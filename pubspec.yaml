name: mm_flutter_app
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.2.0+1

environment:
  sdk: ">=3.7.2 <4.0.0"
  flutter: 3.29.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter

  collection: ^1.17.1
  crop_image: ^1.0.12
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  desktop_drop: ^0.6.0
  device_info_plus: ^11.4.0
  devicelocale: ^0.8.1
  dots_indicator: ^4.0.1
  email_validator: ^3.0.0
  file_picker: ^10.1.9
  firebase_auth: ^5.1.2
  firebase_core: ^3.5.0
  firebase_crashlytics: ^4.1.2
  firebase_messaging: ^15.1.2
  flutter_image_compress: ^2.3.0
  flutter_linkify: ^6.0.0
  flutter_localizations:
    sdk: flutter
  flutter_markdown: ^0.7.3
  flutter_swipe_action_cell: ^3.1.2
  flutter_typeahead: ^5.2.0
  go_router: ^15.1.2
  google_fonts: ^6.1.0
  google_sign_in: ^6.2.1
  gql: ^1.0.0
  graphql_codegen: ^1.2.3
  graphql_flutter: ^5.1.2
  grouped_list: ^6.0.0
  http: ^1.1.0
  image: ^4.1.7
  intl: ^0.19.0
  local_auth: ^2.1.6
  logger: ^2.0.2+1
  package_info_plus: ^8.3.0
  path: ^1.8.3
  path_provider: ^2.1.2
  phone_numbers_parser: ^9.0.3
  provider: ^6.0.5
  retry: ^3.1.2
  shared_preferences: ^2.0.17
  textfield_tags: ^3.0.1
  tuple: ^2.0.2
  url_launcher: ^6.1.12
  uuid: ^4.3.3
  image_picker: ^1.0.8
  permission_handler: ^12.0.0+1
  flutter_facebook_auth: ^7.0.0-dev.5
  carousel_slider: ^5.0.0
  cached_network_image: ^3.3.1
  crypto: ^3.0.3
  firebase_analytics: ^11.2.0
  another_flushbar: ^1.12.30
  connectivity_plus: ^6.1.4
  equatable: ^2.0.5
  webview_flutter: ^4.12.0
  sentry_flutter: ^8.14.2
  scroll_to_index: ^3.0.1
  pointer_interceptor: ^0.10.1+2
  flutter_secure_storage: ^9.2.4
  flutter_timezone: ^4.1.0
  web: ^1.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  # TODO: commenting integration test as some of dependencies are not compatible
  # integration_test:
  #   sdk: flutter

  intl_utils: ^2.8.7

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  analyzer: ^6.4.1

  # widgetbook: ^2.4.1
  # widgetbook_annotation: ^2.1.0
  # widgetbook_generator: ^2.4.1
  build_runner: ^2.3.3
  flutter_launcher_icons: ^0.13.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# https://pub.dev/packages/flutter_launcher_icons
flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/icon/iconcircle.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/icon/iconcircle.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/icon/iconcircle.png"
# The following section is specific to Flutter packages.
flutter:
  # Ensures that the translated files are generated
  generate: true
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  # uses-material-design: true
  fonts:
    - family: MicromentorIcons
      fonts:
        - asset: assets/custom_icons/MicromentorIcons.ttf

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icon/
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

dependency_overrides:
  webview_flutter_wkwebview: 3.17.0

sentry:
  upload_source_maps: true
  upload_sources: true

flutter_intl:
  enabled: true
  class_name: AppLocale
  output_dir: lib/generatedAppLocale
