import 'package:flutter/material.dart';

class ThemeProvider with ChangeNotifier {
  var _selectedPrimaryColor = const Color(0xFF0266FC);
  var _selectedSecondaryColor = const Color(0xFF013C95);

  Color get selectedPrimaryColor => _selectedPrimaryColor;
  Color get selectedSecondaryColor => _selectedSecondaryColor;

  set selectedPrimaryColor(Color color) {
    _selectedPrimaryColor = color;
    notifyListeners();
  }

  set selectedSecondaryColor(Color color) {
    _selectedSecondaryColor = color;
    notifyListeners();
  }

  setEBRDColorScheme() {
    selectedPrimaryColor = const Color(0xff0e5599);
    selectedSecondaryColor = const Color(0xff0e5599);
  }
}
