import 'dart:io';

import 'package:devicelocale/devicelocale.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/widgets/features/welcome/screens/data_use_consent.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../__generated/schema/schema.graphql.dart';
import '../constants/constants.dart';
import '../models/onboarding_model.dart';
import '../utilities/errors/crash_handler.dart';

class LocalDataModel {
  late String _acceptLanguage;
  late String _adminUserId;
  late Enum$IdentityProvider? _identityProvider;
  late String _authToken;
  late String _consumer;
  late String _consumerOs;
  late String _consumerVersion;
  late bool _dataUseConsent;
  late String _deviceUuid;
  late String _locale;
  late OnboardingStage? _onboardingStage;
  late String _pushNotificationToken;
  late String _timezone;
  late String _userId;
  late int _lastSelectedTab;
  late String? _previousRouteOfLoggedInUser;
  late String? _trackId;

  final FlutterSecureStorage _storage = const FlutterSecureStorage();

  Future<bool> showDataUseConsentModal(BuildContext context) async {
    return await showModalBottomSheet(
      elevation: context.theme.d.elevationLevel3,
      isScrollControlled: true,
      context: context,
      useSafeArea: true,
      backgroundColor: context.colorScheme.onPrimary,
      constraints: BoxConstraints(maxHeight: context.mediaQuerySize.height * 0.95),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(context.theme.d.sheetRadius)),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer, // Clip the scrolled content as well
      builder: (context) => const DataUseConsentModal(),
    ).then((result) {
      // Handle gesture dismissals
      if (result is bool) {
        return result;
      } else {
        return false;
      }
    });
  }

  Future<void> init() async {
    final pref = await SharedPreferences.getInstance();
    PackageInfo packageInfo = await PackageInfo.fromPlatform();

    // App info:
    _consumer = 'mm-app';
    _consumerVersion = packageInfo.version;

    // Device info:
    _locale = kIsWeb ? await Devicelocale.currentLocale ?? '' : Platform.localeName;

    _timezone = await FlutterTimezone.getLocalTimezone();
    _acceptLanguage = PlatformDispatcher.instance.locale.toLanguageTag();

    _deviceUuid = pref.getString('deviceUuid') ?? '';
    if (_deviceUuid.isEmpty) {
      _deviceUuid = const Uuid().v1();
      pref.setString('deviceUuid', _deviceUuid);
    }

    _consumerOs = 'unknown';
    if (kIsWeb) {
      _consumerOs = 'web';
    } else if (Platform.isAndroid) {
      _consumerOs = 'android';
    } else if (Platform.isIOS) {
      _consumerOs = 'ios';
    }

    _dataUseConsent = bool.parse(pref.getString('dataUseConsent') ?? 'false');

    // Authentication, push notifications:
    _userId = pref.getString('userId') ?? '';
    _adminUserId = pref.getString('adminUserId') ?? '';
    _pushNotificationToken = pref.getString('pushNotificationToken') ?? '';
    final String? identityProviderName = pref.getString('identityProvider');
    if (identityProviderName?.isNotEmpty == true) {
      _identityProvider = Enum$IdentityProvider.values.byName(identityProviderName!);
    }
    _onboardingStage = OnboardingModel.parseStage(pref.getString('onboardingStage') ?? '');

    _lastSelectedTab = int.parse(pref.getString('lastSelectedTab') ?? '0');
    _previousRouteOfLoggedInUser = pref.getString('previousRouteOfLoggedInUser');
    _trackId = null;

    _authToken = await _storage.read(key: 'authToken') ?? '';
  }

  /// Clears all data except deviceUuid.
  void clear({bool removeTrackId = true}) {
    _authToken = '';
    _storage.delete(key: 'authToken');

    _identityProvider = null;
    _removeFromLocalStorage('identityProvider');

    _dataUseConsent = false;
    _removeFromLocalStorage('dataUseConsent');

    _userId = '';
    _removeFromLocalStorage('userId');
    CrashHandler.updateUserId(_userId);

    _adminUserId = '';
    _removeFromLocalStorage('adminUserId');

    _onboardingStage = null;
    _removeFromLocalStorage('onboardingStage');

    _lastSelectedTab = 0;
    _removeFromLocalStorage('lastSelectedTab');

    _previousRouteOfLoggedInUser = '';
    _removeFromLocalStorage('previousRouteOfLoggedInUser');

    if (removeTrackId) clearTrackId();
  }

  Future<void> _setInLocalStorage(String name, String value) async {
    final pref = await SharedPreferences.getInstance();
    if (value.isEmpty) {
      pref.remove(name);
    } else {
      pref.setString(name, value);
    }
  }

  Future<void> _removeFromLocalStorage(String name) async {
    final pref = await SharedPreferences.getInstance();
    pref.remove(name);
  }

  set authToken(String authToken) {
    _authToken = authToken;
    _storage.write(key: 'authToken', value: authToken);
  }

  set userId(String userId) {
    _userId = userId;
    _setInLocalStorage('userId', userId);
    CrashHandler.updateUserId(userId);
  }

  set adminUserId(String adminUserId) {
    _adminUserId = adminUserId;
    _setInLocalStorage('adminUserId', adminUserId);
  }

  set pushNotificationToken(String pushNotificationToken) {
    _pushNotificationToken = pushNotificationToken;
    _setInLocalStorage('pushNotificationToken', pushNotificationToken);
  }

  set identityProvider(Enum$IdentityProvider? identityProvider) {
    _identityProvider = identityProvider;
    _setInLocalStorage('identityProvider', identityProvider == null ? '' : identityProvider.name);
  }

  set dataUseConsent(bool dataUseConsent) {
    _dataUseConsent = dataUseConsent;
    _setInLocalStorage('dataUseConsent', dataUseConsent.toString());
  }

  set onboardingStage(OnboardingStage? onboardingStage) {
    _onboardingStage = onboardingStage;
    _setInLocalStorage('onboardingStage', onboardingStage?.name ?? '');
  }

  set lastSelectedTab(int tab) {
    _lastSelectedTab = tab;
    _setInLocalStorage('lastSelectedTab', tab.toString());
  }

  set previousRouteOfLoggedInUser(String? routeName) {
    _previousRouteOfLoggedInUser = routeName;
    if (routeName != null) _setInLocalStorage('previousRouteOfLoggedInUser', routeName);
  }

  set trackId(String? trackId) {
    if (trackId != null) {
      _trackId = trackId;
      _setInLocalStorage('trackId', trackId);
    }
    debugPrint('saved trackId locally');
  }

  void clearTrackId() {
    _trackId = null;
    _removeFromLocalStorage('trackId');
  }

  String get acceptLanguage => _acceptLanguage;
  String get adminUserId => _adminUserId;
  Enum$IdentityProvider? get identityProvider => _identityProvider;
  String get authToken => _authToken;
  String get consumer => _consumer;
  String get consumerOs => _consumerOs;
  String get consumerVersion => _consumerVersion;
  bool get dataUseConsent => _dataUseConsent;
  String get deviceUuid => _deviceUuid;
  String get locale => _locale;
  OnboardingStage? get onboardingStage => _onboardingStage;
  String get pushNotificationToken => _pushNotificationToken;
  String get timezone => _timezone;
  String get userId => _userId;
  int get lastSelectedTab => _lastSelectedTab;
  String? get previousRouteOfLoggedInUser => _previousRouteOfLoggedInUser;
  String? get trackId => _trackId;
}
