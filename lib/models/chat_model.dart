import 'dart:async';

import 'package:flutter/material.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/services/graphql/providers/base/operation_result.dart';
import 'package:provider/provider.dart';

import '../__generated/schema/operations_message.graphql.dart';
import '../__generated/schema/schema.graphql.dart';
import '../constants/constants.dart';
import '../services/graphql/providers/providers.dart';
import '../utilities/errors/crash_handler.dart';

class ChatModel extends ChangeNotifier {
  String? channelId;
  final ChannelsProvider _channelsProvider;
  final MessagesProvider _messagesProvider;
  void Function(ChannelMessage)? onNewMessage;
  void Function(List<ChannelMessage>)? onMessagesReceived;
  List<ChannelMessage> _channelMessages = List.empty(growable: true);
  StreamSubscription<QueryResult<ChannelChangedEvent>>? _subscription;
  AsyncState _loadMessageHistoryState = AsyncState.ready;
  AsyncState _loadLatestMessagesState = AsyncState.loading;

  List<ChannelMessage> get channelMessages => _channelMessages;
  AsyncState get loadMessageHistoryState => _loadMessageHistoryState;
  AsyncState get loadLatestMessagesState => _loadLatestMessagesState;

  bool endOfResults = false;

  resetChannelId(String id) {
    channelId = id;
    _channelMessages = List.empty(growable: true);
  }

  ChatModel({required BuildContext context, required this.channelId})
    : _messagesProvider = Provider.of<MessagesProvider>(context, listen: false),
      _channelsProvider = Provider.of<ChannelsProvider>(context, listen: false);

  Future<void> loadChannelMessages() async {
    final bool isLatestBatch = _channelMessages.isEmpty;
    if (isLatestBatch) {
      _loadLatestMessagesState = AsyncState.loading;
    } else {
      _loadMessageHistoryState = AsyncState.loading;
    }
    final result = await _messagesProvider.findChannelMessages(
      fetchFromNetworkOnly: true,
      fetchSkip: _channelMessages.length,
      input: Input$ChannelMessageListFilter(channelId: channelId),
    );
    if (result.gqlQueryResult.hasException) {
      if (isLatestBatch) {
        _loadLatestMessagesState = AsyncState.error;
      } else {
        _loadMessageHistoryState = AsyncState.error;
      }
    } else {
      if (result.response?.isEmpty == true ||
          (result.response?.length ?? 0) < Limits.chatMessagesPageSize) {
        endOfResults = true;
      }
      if (result.response?.isNotEmpty == true) {
        _channelMessages.addAll(result.response!);
        _channelMessages.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        if (isLatestBatch) {
          _loadLatestMessagesState = AsyncState.ready;
        } else {
          _loadMessageHistoryState = AsyncState.ready;
        }

        onMessagesReceived?.call(result.response!);
      }
    }

    if (hasListeners) {
      notifyListeners();
    }
  }

  Future<OperationResult<Query$FindChannelMessages$findChannelMessages>> createChannelMessage({
    required String channelId,
    required String messageText,
    required replyToMessageId,
  }) async {
    final result = await _messagesProvider.createMessage(
      input: Input$ChannelMessageInput(
        channelId: channelId,
        messageText: messageText,
        replyToMessageId: replyToMessageId,
      ),
    );

    if (result.response != null && !result.gqlQueryResult.hasException) {
      _channelMessages.add(result.response!);
    }

    if (hasListeners) {
      notifyListeners();
    }
    return result;
  }

  Future<void> markMessagesAsRead() async {
    final result = await _messagesProvider.markMessageRead(channelId: channelId ?? '');
    if (result.gqlQueryResult.hasException) {
      final String e = result.gqlQueryResult.exception.toString();
      CrashHandler.logCrashReport('Could not mark messages as read: $e');
      return;
    }
  }

  Future<void> _loadChannelMessage(String channelMessageId) async {
    final result = await _messagesProvider.findChannelMessageById(
      channelMessageId: channelMessageId,
    );

    if (result.gqlQueryResult.hasException || result.response == null) {
      final String e = result.gqlQueryResult.exception.toString();
      CrashHandler.logCrashReport('Could not retrieve channel message: $e');
      return;
    }

    final int messageIndex = _channelMessages.indexWhere(
      (element) => element.id == channelMessageId,
    );

    if (messageIndex < 0) {
      _channelMessages.add(result.response!);
      onNewMessage?.call(result.response!);
    } else {
      _channelMessages[messageIndex] = result.response!;
    }

    if (hasListeners) {
      notifyListeners();
    }
  }

  Future<OperationResult> deleteMessage(String channelMessageId) async {
    final result = await _messagesProvider.deleteMessage(
      deletePhysically: false,
      channelMessageId: channelMessageId,
    );
    return result;
  }

  void createChannelSubscription() {
    if (_subscription != null) {
      return;
    }
    _subscription = _channelsProvider.subscribeToChannel(
      channelId: channelId ?? '',
      onSubscriptionEvent: (event) {
        if (event.messageId == null) {
          return;
        }
        final int messageIndex = _channelMessages.indexWhere(
          (element) => element.id == event.messageId,
        );
        switch (event.eventType) {
          case Enum$ChannelChangedEventType.messageCreated:
            if (messageIndex > -1) {
              // Already added to list of messages.
              return;
            }
            _loadChannelMessage(event.messageId!);
            return;
          case Enum$ChannelChangedEventType.messageUpdated:
            _loadChannelMessage(event.messageId!);
            return;
          case Enum$ChannelChangedEventType.messageDeleted:
            if (messageIndex > -1) {
              _channelMessages[messageIndex] =
                  CopyWith$Query$FindChannelMessages$findChannelMessages<ChannelMessage>(
                    _channelMessages[messageIndex],
                    (ChannelMessage msg) => msg,
                  )(deletedAt: DateTime.now().toUtc());
            }
            if (hasListeners) {
              notifyListeners();
            }
            return;
          default:
            return;
        }
      },
    );
  }

  void cancelChannelSubscription() {
    _subscription?.cancel();
    _subscription = null;
  }
}
