import 'package:flutter/material.dart';

class ScaffoldModel extends ChangeNotifier {
  PreferredSizeWidget? _appBar;
  Drawer? _drawer;
  int? _selectedTabIndex;
  bool _hideNavBar = false;
  bool _showDesktopFooter = false;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  PreferredSizeWidget? get appBar => _appBar;
  Drawer? get drawer => _drawer;
  bool get hideNavBar => _hideNavBar;
  int? get selectedTabIndex => _selectedTabIndex;
  bool get showDesktopFooter => _showDesktopFooter;
  GlobalKey<ScaffoldState> get scaffoldKey => _scaffoldKey;

  ScaffoldModel({required BuildContext context});

  void setParams({
    PreferredSizeWidget? appBar,
    Drawer? drawer,
    bool hideNavBar = false,
    int? index,
  }) {
    _appBar = appBar;
    _drawer = drawer;
    _hideNavBar = hideNavBar;
    _selectedTabIndex = index ?? _selectedTabIndex;
    if (hasListeners) {
      notifyListeners();
    }
  }

  void clear() {
    _appBar = null;
    _drawer = null;
    _hideNavBar = false;
    if (hasListeners) {
      notifyListeners();
    }
  }

  void shouldShowFooter(bool value) {
    _showDesktopFooter = value;
    if (hasListeners) {
      notifyListeners();
    }
  }
}
