import 'dart:io';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../__generated/schema/schema.graphql.dart';

class LocaleInfo {
  final String name;
  final String code;

  LocaleInfo(this.name, this.code);
}

class LocaleModel extends ChangeNotifier {
  Locale defaultLocale = const Locale('en');
  Locale? _locale;

  Locale? get locale => _locale;

  Locale getDeviceLanguage() {
    if (kIsWeb) return PlatformDispatcher.instance.locale;
    var locale = Platform.localeName;
    var languageName = locale.split('_').firstOrNull;
    if (languageName != null) {
      Locale? selectedLocale =
          getSupportedLocales()
              .where((element) => element.languageCode == languageName)
              .firstOrNull;
      if (selectedLocale != null) {
        return selectedLocale;
      }
    }
    return defaultLocale;
  }

  static final Map<Locale, LocaleInfo> _supportedLocaleToLanguage = {
    const Locale('en'): LocaleInfo('English', 'en'),
    const Locale('ar'): LocaleInfo('العربية', 'ar'),
    const Locale('es'): LocaleInfo('Español', 'es'),
    const Locale('id'): LocaleInfo('Bahasa Indonesia', 'id'),
    const Locale('ru'): LocaleInfo('Русский', 'ru'),
  };

  void set(Locale locale) {
    _locale = locale;
    notifyListeners();
  }

  void setLanguage(Enum$UiLanguage? language) {
    Locale? selectedLocale =
        getSupportedLocales()
            .where((element) => element.languageCode == language?.name)
            .firstOrNull;
    if (selectedLocale == null) return;
    _locale = selectedLocale;
    notifyListeners();
  }

  List<Locale> getSupportedLocales() {
    return List.from(_supportedLocaleToLanguage.keys);
  }

  String getCurrentLanguageName() {
    return _supportedLocaleToLanguage[_locale]!.name;
  }

  String getCurrentLanguageCode() {
    return _locale?.languageCode ?? getDeviceLanguage().languageCode;
  }

  List<LocaleInfo> getOtherSupportedLanguagesInfo() {
    List<LocaleInfo> result = [];
    for (Locale key in _supportedLocaleToLanguage.keys) {
      if (key == _locale) {
        continue;
      }
      result.add(_supportedLocaleToLanguage[key]!);
    }

    return result;
  }

  bool isArabic({String? text}) {
    if (text == null || text.isEmpty) return _locale?.languageCode == 'ar';
    return Bidi.detectRtlDirectionality(text);
  }

  bool isEnglish({String? text}) {
    if (text == null || text.isEmpty) return _locale?.languageCode == 'en';
    return Bidi.detectRtlDirectionality(text);
  }

  void clear() {
    _locale = null;
    notifyListeners();
  }
}
