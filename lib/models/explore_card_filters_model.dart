import 'dart:async';
import 'dart:math';

import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mm_flutter_app/utilities/errors/error_handler.dart';
import 'package:provider/provider.dart';
import 'package:retry/retry.dart';

import '../__generated/schema/schema.graphql.dart';
import '../constants/constants.dart';
import '../services/graphql/providers/providers.dart';
import '../utilities/errors/crash_handler.dart';
import '../utilities/errors/exceptions.dart';
import '../widgets/widgets.dart';

//Using Equatable mixin to compare objects of this class, This comparison is required while resetting (discarding unapplied) exploire screen filters
// ignore: must_be_immutable
class ExploreCardFiltersModel extends ChangeNotifier with EquatableMixin {
  ExploreCardFiltersModel.empty();

  ExploreCardFiltersModel();

  Set<String> _selectedCountries = {};
  Set<String> _selectedLanguages = {};
  Set<String> _selectedExpertises = {};
  Set<String> _selectedIndustries = {};
  Set<String> _selectedVentureStages = {};
  Enum$UserProfileRole? _selectedUserType;
  String? _selectedKeyword;
  bool isSourceHome = false;

  Set<String> get selectedCountries => _selectedCountries;
  Set<String> get selectedLanguages => _selectedLanguages;
  Set<String> get selectedExpertises => _selectedExpertises;

  Set<String> get selectedIndustries => _selectedIndustries;
  Set<String> get selectedVentureStages => _selectedVentureStages;
  Enum$UserProfileRole? get selectedUserType => _selectedUserType;
  String? get selectedKeyword =>
      (_selectedKeyword == null || (_selectedKeyword?.isEmpty ?? false)) ? null : _selectedKeyword!;

  bool get countryFilterSelected => _selectedCountries.isNotEmpty;
  bool get languageFilterSelected => _selectedLanguages.isNotEmpty;
  bool get expertiseFilterSelected => _selectedExpertises.isNotEmpty;
  bool get industriesFilterSelected => _selectedIndustries.isNotEmpty;
  bool get userFiltersSelected =>
      expertiseFilterSelected ||
      languageFilterSelected ||
      countryFilterSelected ||
      industriesFilterSelected;

  List<UserSearchResult> get searchResults => _searchResults;

  String? get searchId => isSourceHome ? _homeSearchId : _searchId;

  List<UserSearchResult> _searchResults = [];
  String? _searchId;
  String? _homeSearchId;
  int webPageLimit = Limits.searchResultsWebPageSize;

  void setFilters({
    Set<String>? selectedCountries,
    Set<String>? selectedLanguages,
    Set<String>? selectedExpertises,
  }) {
    _selectedCountries = selectedCountries ?? {};
    _selectedLanguages = selectedLanguages ?? {};
    _selectedExpertises = selectedExpertises ?? {};
    clearSearchResult();
    notifyListeners();
  }

  void setAdvancedFilters({
    Set<String>? selectedIndustries,
    Set<String>? selectedVentureStages,
    Enum$UserProfileRole? selectedUserType,
    String? selectedKeyword,
  }) {
    _selectedIndustries = selectedIndustries ?? {};
    _selectedVentureStages = selectedVentureStages ?? {};
    if (selectedUserType != Enum$UserProfileRole.none &&
        selectedUserType != Enum$UserProfileRole.$unknown) {
      _selectedUserType = selectedUserType;
    }
    _selectedKeyword = selectedKeyword;
    clearSearchResult();
    notifyListeners();
  }

  Input$UserSearchInput toUserSearchInput(int maxResultsCount) {
    _selectedUserType ??=
        _userProvider.myUser?.seeksHelp == true
            ? Enum$UserProfileRole.mentor
            : Enum$UserProfileRole.mentee;
    return Input$UserSearchInput(
      filter: Input$UserSearchFilterInput(
        countryTextIds: _selectedCountries.isEmpty ? null : _selectedCountries.toList(),
        expertisesTextIds: _selectedExpertises.isEmpty ? null : _selectedExpertises.toList(),
        languagesTextIds: _selectedLanguages.isEmpty ? null : _selectedLanguages.toList(),
        industriesTextIds: _selectedIndustries.isEmpty ? null : _selectedIndustries.toList(),
        companyStagesTextIds:
            _selectedVentureStages.isEmpty ? null : _selectedVentureStages.toList(),
        offersHelp:
            _selectedUserType == Enum$UserProfileRole.mentor
                ? Enum$UserSearchFieldOption.isTrue
                : Enum$UserSearchFieldOption.any,
        searchText: selectedKeyword,
        seeksHelp:
            _selectedUserType == Enum$UserProfileRole.mentee
                ? Enum$UserSearchFieldOption.isTrue
                : Enum$UserSearchFieldOption.any,
      ),
      maxResultCount: maxResultsCount,
    );
  }

  setSearchId(String id) {
    isSourceHome ? _homeSearchId = id : _searchId = id;
  }

  clearData() {
    _selectedCountries = {};
    _selectedLanguages = {};
    _selectedExpertises = {};
    _selectedIndustries = {};
    _selectedVentureStages = {};
    _selectedUserType = null;
    _selectedKeyword = null;
    clearSearchResult();
  }

  clearSearchResult({bool clearSearchId = true}) {
    if (clearSearchId) isSourceHome ? _homeSearchId = null : _searchId = null;
    _searchResults = [];
    endOfResults = false;
    _resultsAvailable = 0;
  }

  int _resultsAvailable = 0;
  late UserProvider _userProvider;
  bool endOfResults = false;
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  Future<bool> fetchSearchResults(BuildContext context) async {
    _isLoading = true;
    notifyListeners();

    _userProvider = Provider.of<UserProvider>(context, listen: false);
    if (searchId == null) {
      var result = await _userProvider.createUserSearch(
        searchInput: toUserSearchInput(
          isSourceHome ? Limits.homeRecommendedUsersMaxSize : Limits.searchResultsBatchSize,
        ),
        fetchFromNetworkOnly: true,
      );

      setSearchId(result.response?.id ?? '');
    }
    await pollUserSearch();
    // Delay processing the results for long running queries
    await Future.delayed(const Duration(seconds: 2));
    await addSearchResults();
    _isLoading = false;

    notifyListeners();
    return true;
  }

  FutureOr<void> pollUserSearch() async {
    if (searchId?.isEmpty == true) return;
    return CrashHandler.retryOnException<void>(
      () async {
        final userSearchResult = await _userProvider.getUserSearch(
          userSearchId: searchId!,
          fetchFromNetworkOnly: true,
        );
        if (userSearchResult.gqlQueryResult.hasException) {
          String e = userSearchResult.gqlQueryResult.exception.toString();
          CrashHandler.logCrashReport('Failed to retrieve user search: $e');
          final message =
              userSearchResult.gqlQueryResult.exception?.graphqlErrors.firstOrNull?.message ?? '';
          if (message.contains(AppErrorHandler.tooManyRequestsError)) return;
        }
        final runInfos = userSearchResult.response?.runInfos ?? [];
        if (runInfos.isEmpty) {
          throw RetryException(message: 'Waiting for user search to complete...');
        }
        if (runInfos.isNotEmpty) {
          final bool isCompleted = runInfos.last.finishedAt != null;
          if (!isCompleted) {
            throw RetryException(message: 'Waiting for user search to complete...');
          }

          // The max number of results that can be shown is the smallest between
          // the UserSearch batch size and the number of actual matches found
          _resultsAvailable = min(
            Limits.searchResultsBatchSize,
            userSearchResult.response!.runInfos!.last.matchCount,
          );
        }
      },
      retryOptions: const RetryOptions(
        // TODO: reduce this once search is more performant
        // searches are taking 10-20 seconds to complete on average, with no load on the backend
        maxAttempts: 8,
        maxDelay: Duration(seconds: 3),
      ),
      logFailures: false,
    );
  }

  addSearchResults() async {
    try {
      final result = await _userProvider.findUserSearchResults(
        userSearchId: searchId ?? '',
        optionsInput: Input$FindObjectsOptions(
          limit: kIsWeb ? webPageLimit : Limits.searchResultsPageSize,
          skip: _searchResults.length,
        ),
        fetchFromNetworkOnly: true,
      );

      if (!result.gqlQueryResult.hasException &&
          result.response != null &&
          result.response?.isNotEmpty == true) {
        _searchResults.addAll(result.response!);
      }

      if (_searchResults.length >= _resultsAvailable) {
        endOfResults = true;
      }
    } catch (error) {
      debugPrint('$error');
    }
  }

  void initializeFilters(BuildContext context) {
    _userProvider = Provider.of<UserProvider>(context, listen: false);

    final profileUtils = ProfileUtility.fromFindId(_userProvider.myUser);

    List<String>? selectedExpertises;
    if (_userProvider.myUser?.seeksHelp == true) {
      selectedExpertises =
          profileUtils.menteeGroupMembership?.soughtExpertises.map((e) => e.textId).toList();
    } else {
      selectedExpertises =
          profileUtils.mentorGroupMembership?.expertises.map((e) => e.textId).toList();
    }
    setFilters(selectedExpertises: {...?selectedExpertises});
    setAdvancedFilters();
  }

  @override
  List<Object?> get props => [
    _selectedCountries,
    _selectedLanguages,
    _selectedExpertises,
    _selectedIndustries,
    _selectedVentureStages,
    _selectedUserType,
    _selectedKeyword,
  ];
}
