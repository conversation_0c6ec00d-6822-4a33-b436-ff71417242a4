import '../constants/constants.dart';

class StageDef {
  final OnboardingStage stage;
  final AppRoute route;
  final bool forMentee;
  final bool forMentor;
  final bool enabled;
  final bool inSequence;

  StageDef({
    required this.stage,
    required this.route,
    this.forMentee = true,
    this.forMentor = true,
    this.enabled = true,
    this.inSequence = true,
  });
}

class OnboardingModel {
  final bool enableGender;
  final bool enableBirthYear;
  final bool enableLanguage;
  final bool enableProfileRole;
  final bool enableExpertises;
  final bool enableVentureName;
  final bool enableVentureStage;
  final bool enableMentorInfo;
  final bool enableReasonToJoin;
  final bool enableNotifications;
  GroupIdent groupIdent;
  String? groupIdentName;
  UserCms? userCms;

  OnboardingModel({
    this.enableGender = true,
    this.enableBirthYear = true,
    this.enableLanguage = true,
    this.enableProfileRole = true,
    this.enableExpertises = true,
    this.enableVentureName = true,
    this.enableVentureStage = true,
    this.enableMentorInfo = true,
    this.enableReasonToJoin = true,
    this.enableNotifications = false,
    this.groupIdent = GroupIdent.mentees,
    this.groupIdentName,
  });

  List<StageDef> get stageDefs =>
      [
        StageDef(stage: OnboardingStage.authType, route: AppRoutes.signup),
        StageDef(stage: OnboardingStage.createAccount, route: AppRoutes.signupCreateAccount),
        if (enableNotifications)
          StageDef(stage: OnboardingStage.notifications, route: AppRoutes.signupNotifications),
        if (isIQLAAGroup)
          StageDef(stage: OnboardingStage.iqlaaConsent, route: AppRoutes.iqlaaConsentPage),
        if (userCms?.groupCms?.onboarding == null ||
            userCms?.groupCms?.onboarding?.showLocationPage != false)
          StageDef(stage: OnboardingStage.location, route: AppRoutes.signupLocation),
        if (isIQLAAGroup)
          StageDef(stage: OnboardingStage.iqlaaNationality, route: AppRoutes.iqlaaNationalityPage),
        if (userCms?.groupCms?.onboarding == null ||
            userCms?.groupCms?.onboarding?.showPhoneNumberPage != false)
          StageDef(stage: OnboardingStage.phoneNumber, route: AppRoutes.signupPhoneNumber),
        if (userCms?.groupCms?.onboarding == null ||
            userCms?.groupCms?.onboarding?.showGenderPage != false)
          StageDef(
            stage: OnboardingStage.gender,
            route: AppRoutes.signupGender,
            enabled: enableGender,
          ),
        if (userCms?.groupCms?.onboarding == null ||
            userCms?.groupCms?.onboarding?.showBirthYearPage != false)
          StageDef(
            stage: OnboardingStage.birthYear,
            route: AppRoutes.signupBirthYear,
            enabled: enableBirthYear,
          ),
        if (userCms?.groupCms?.onboarding == null ||
            userCms?.groupCms?.onboarding?.showPreferredLanguagePage != false)
          StageDef(
            stage: OnboardingStage.language,
            route: AppRoutes.signupLanguage,
            enabled: enableLanguage,
          ),
        if (userCms?.groupCms?.onboarding == null ||
            userCms?.groupCms?.onboarding?.showProfileRolePage != false)
          StageDef(
            stage: OnboardingStage.profileRole,
            route: AppRoutes.signUpProfileRole,
            enabled: enableProfileRole,
          ),
        if (isMastercardGroup)
          StageDef(
            stage: OnboardingStage.mastercardBankDetails,
            route: AppRoutes.signupMastercardBankStage,
            forMentor: false,
          ),
        if (isStriveIndonesiaGroup)
          StageDef(
            stage: OnboardingStage.striveIndonesiaVentureStartDate,
            route: AppRoutes.signupVentureYear,
            forMentor: false,
          ),
        if (userCms?.groupCms?.onboarding == null ||
            userCms?.groupCms?.onboarding?.showExpertisesPage != false)
          StageDef(
            stage: OnboardingStage.expertises,
            route: AppRoutes.signupExpertises,
            enabled: enableExpertises,
          ),
        if (userCms?.groupCms?.onboarding == null ||
            userCms?.groupCms?.onboarding?.showIndustryPage != false)
          StageDef(stage: OnboardingStage.industries, route: AppRoutes.signUpIndustries),
        if (userCms?.groupCms?.onboarding == null ||
            userCms?.groupCms?.onboarding?.showVentureNamePage != false)
          StageDef(
            stage: OnboardingStage.ventureName,
            forMentor: false,
            route: AppRoutes.signupVentureName,
            enabled: enableVentureName,
          ),
        if (userCms?.groupCms?.onboarding == null ||
            userCms?.groupCms?.onboarding?.showVentureStagePage != false)
          StageDef(
            stage: OnboardingStage.ventureStage,
            forMentor: false,
            route: AppRoutes.signupVentureStage,
            enabled: enableVentureStage,
          ),
        if (userCms?.groupCms?.onboarding == null ||
            userCms?.groupCms?.onboarding?.showMentorRolePage != false)
          StageDef(
            stage: OnboardingStage.mentorInfo,
            forMentee: false,
            route: AppRoutes.signupMentorInfo,
            enabled: enableMentorInfo,
          ),
        if (userCms?.groupCms?.onboarding == null ||
            userCms?.groupCms?.onboarding?.showReasonToJoinPage != false)
          StageDef(
            stage: OnboardingStage.reasonToJoin,
            route: AppRoutes.signupReasonToJoin,
            enabled: enableReasonToJoin,
          ),
        StageDef(
          stage: OnboardingStage.howICanHelpMentees,
          route: AppRoutes.signUpHowICanHelpMentees,
          forMentee: false,
        ),
        if (isIQLAAGroup)
          StageDef(
            stage: OnboardingStage.iqlaaHomeBasedBusiness,
            forMentor: false,
            route: AppRoutes.iqlaaHomeBasedBusiness,
          ),
        if (isIQLAAGroup)
          StageDef(
            stage: OnboardingStage.iqlaaBusinessRegistration,
            forMentor: false,
            route: AppRoutes.iqlaaBusinessRegistration,
          ),
        if (isIQLAAGroup || isStriveIndonesiaGroup)
          StageDef(
            stage: OnboardingStage.iqlaaEmployeeNumber,
            forMentor: false,
            route: AppRoutes.iqlaaEmployeeNumber,
          ),
        StageDef(
          stage: OnboardingStage.businessChallenge,
          forMentor: false,
          route: AppRoutes.signUpBusinessChallenge,
        ),
        StageDef(
          stage: OnboardingStage.howCanMentorSupportMe,
          forMentor: false,
          route: AppRoutes.signUpHowCanMentorSupportMe,
        ),
        if (userCms?.groupCms?.onboarding == null ||
            userCms?.groupCms?.onboarding?.showAcceptTermsPage != false)
          StageDef(stage: OnboardingStage.acceptTerms, route: AppRoutes.signupAcceptTerms),
        StageDef(stage: OnboardingStage.finished, route: AppRoutes.home),
      ].where((s) => s.enabled).toList();

  List<StageDef> get stageDefsForMentee =>
      stageDefs.where((sd) => sd.inSequence && sd.forMentee).toList();
  List<StageDef> get stageDefsForMentor =>
      stageDefs.where((sd) => sd.inSequence && sd.forMentor).toList();
  List<OnboardingStage> get stagesForMentee => stageDefsForMentee.map((sd) => sd.stage).toList();
  List<OnboardingStage> get stagesForMentor => stageDefsForMentor.map((sd) => sd.stage).toList();

  static bool isValidStage(String? stage) {
    if (stage == null || stage == '') {
      return false;
    }
    try {
      OnboardingStage.values.byName(stage);
      return true;
    } catch (e) {
      return false;
    }
  }

  static OnboardingStage? parseStage(String? stage) {
    if (stage == null || stage == '') {
      return null;
    }
    try {
      return OnboardingStage.values.byName(stage);
    } catch (e) {
      return null;
    }
  }

  OnboardingStage firstStage({bool isMentee = true}) {
    final stagesForThisRole = isMentee ? stagesForMentee : stagesForMentor;
    return stagesForThisRole[2];
  }

  OnboardingStage createAccountStage({bool isMentee = true}) {
    final stagesForThisRole = isMentee ? stagesForMentee : stagesForMentor;
    return stagesForThisRole[1];
  }

  AppRoute firstRoute({isMentee = true}) {
    final stageDefsForThisRole = isMentee ? stageDefsForMentee : stageDefsForMentor;
    return stageDefsForThisRole[2].route;
  }

  OnboardingStage? getNextStage({required OnboardingStage? curStage, bool isMentee = true}) {
    if (curStage == null) {
      return null;
    }

    final stagesForThisRole = isMentee ? stagesForMentee : stagesForMentor;
    int index = stagesForThisRole.indexOf(curStage);

    if (index < 0 || index > stageDefs.length - 1) {
      return null;
    }

    return stagesForThisRole[index + 1];
  }

  OnboardingStage? getPreviousStage({required OnboardingStage? curStage, isMentee = true}) {
    if (curStage == null) {
      return null;
    }

    final stagesForThisRole = isMentee ? stagesForMentee : stagesForMentor;
    int index = stagesForThisRole.indexOf(curStage);

    if (index < 1 || index > stageDefs.length) {
      return null;
    }

    final OnboardingStage prevStage = stagesForThisRole[index - 1];

    return prevStage;
  }

  bool isCompleted(OnboardingStage? curStage) => curStage == OnboardingStage.finished;

  StageDef getStageDefFromStage(OnboardingStage stage) {
    return stageDefs.firstWhere((sd) => sd.stage == stage);
  }

  AppRoute getRouteFromStage(OnboardingStage stage) {
    final stageDef = getStageDefFromStage(stage);
    return stageDef.route;
  }

  AppRoute? getNextRouteFromStage(OnboardingStage? stage, {bool isMentee = true}) {
    if (stage == null) {
      return null;
    }

    final nextStage = getNextStage(curStage: stage);
    if (nextStage == null) {
      return null;
    }

    return getStageDefFromStage(nextStage).route;
  }

  AppRoute? getPreviousRouteFromStage(OnboardingStage? stage, {bool isMentee = true}) {
    if (stage == null) {
      return null;
    }

    final prevStage = getPreviousStage(curStage: stage, isMentee: isMentee);
    if (prevStage == null) {
      return null;
    }

    return getStageDefFromStage(prevStage).route;
  }

  double getProgress({required OnboardingStage? curStage, bool isMentee = true}) {
    final stagesForThisRole = isMentee ? stagesForMentee : stagesForMentor;
    int index = curStage == null ? -1 : stagesForThisRole.indexOf(curStage);
    final val = index / (stagesForThisRole.length - 2);
    if (val < .01) {
      return 0.05;
    }
    return val;
  }

  setGroupIdent(GroupIdent group) {
    groupIdent = group;
  }

  setGroupIdentName(String name) {
    groupIdentName = name;
  }

  bool get isIQLAAGroup => groupIdent == GroupIdent.iqlaa;

  bool get isStriveIndonesiaGroup =>
      groupIdent == GroupIdent.striveIndonesia || groupIdentName == GroupIdent.striveIndonesia.name;

  bool get isMastercardGroup =>
      groupIdent == GroupIdent.mastercard || groupIdentName == GroupIdent.mastercard.name;

  bool get isRegularUser =>
      ((groupIdent == GroupIdent.mentees || groupIdent == GroupIdent.mentors) &&
          groupIdentName == null);

  bool get belongsToEBRDGroup =>
      groupIdent == GroupIdent.ebrdCentralAsia ||
      groupIdent == GroupIdent.kmf ||
      groupIdent == GroupIdent.hamkor;
}
