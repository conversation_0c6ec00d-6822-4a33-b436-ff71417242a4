import 'dart:io';

import 'package:flutter/foundation.dart';

enum Flavor { prod, stag, dev, local }

class FlavorValues {
  FlavorValues({required this.domain, required this.mm2Domain});

  String domain;
  String mm2Domain;
}

class FlavorConfig {
  final Flavor flavor;
  final String name;
  final FlavorValues values;
  static late FlavorConfig _instance;

  static String prodDomain = 'mmdata.micromentor.org';
  static String stagDomain = 'mmdata-stag.micromentor.org';
  static String devDomain = 'mmdata-dev.micromentor.org';
  static String prodMm2Domain = 'https://classic.micromentor.org';
  static String devMm2Domain = 'https://development.micromentor.org';
  // Temporarily using the dev domain for staging. The MM3 staging
  // database is really slow, so we're using the MM3 dev DB
  // and the MM2 dev env as a temporary measure.
  static String stagMm2Domain = devMm2Domain;
  // static String stagMm2Domain = 'https://stag.micromentor.org';
  static String localDomain = '127.0.0.1:3000';
  static String localDomainAndroid = '********:3000';
  static String localDomainDevice =
      '***********:3000'; //add router-assigned ip address for local backend here to run Flutter on real device with a local server
  static String localMm2Domain = 'http://127.0.0.1:8000';
  static String localMm2DomainAndroid = 'http://********:8000';

  factory FlavorConfig({required String appFlavor, bool? isRealDevice}) {
    Flavor? flavor = Flavor.values.where((e) => e.name == appFlavor).firstOrNull ?? Flavor.prod;
    FlavorValues flavorValues = FlavorValues(domain: '', mm2Domain: '');
    switch (flavor) {
      case Flavor.local:
        flavorValues.mm2Domain = localMm2Domain;
        if (isRealDevice ?? false) {
          flavorValues.domain = localDomainDevice;
          break;
        }
        if (kIsWeb) {
          flavorValues.domain = localDomain;
          break;
        }
        if (Platform.isAndroid) {
          flavorValues.mm2Domain = localMm2DomainAndroid;
          flavorValues.domain = localDomainAndroid;
          break;
        }
        flavorValues.domain = localDomain;
        break;
      case Flavor.dev:
        flavorValues.mm2Domain = devMm2Domain;
        flavorValues.domain = devDomain;
        break;
      case Flavor.stag:
        flavorValues.mm2Domain = stagMm2Domain;
        flavorValues.domain = stagDomain;
        break;
      case Flavor.prod:
        flavorValues.mm2Domain = prodMm2Domain;
        flavorValues.domain = prodDomain;
        break;
    }
    _instance = FlavorConfig._internal(flavor, flavor.name, flavorValues);
    return _instance;
  }

  FlavorConfig._internal(this.flavor, this.name, this.values);

  static FlavorConfig get instance {
    return _instance;
  }

  static String getEnvironmentName() {
    switch (_instance.flavor) {
      case Flavor.local:
        return 'local';
      case Flavor.dev:
        return 'dev';
      case Flavor.stag:
        return 'stag';
      case Flavor.prod:
        return 'prod';
    }
  }

  String graphUrl() {
    var protocol = 'https';
    if (_instance.flavor == Flavor.local) {
      protocol = 'http';
    }
    return '$protocol://${instance.values.domain}/mmdata/api/graphql';
  }

  String subscriptionUrl() {
    var protocol = 'wss';
    if (_instance.flavor == Flavor.local) {
      protocol = 'ws';
    }
    return '$protocol://${instance.values.domain}/mmdata/api/graphql';
  }

  String assetUploadUrl() {
    var protocol = 'https';
    if (_instance.flavor == Flavor.local) {
      protocol = 'http';
    }
    return '$protocol://${instance.values.domain}/mmdata/file-upload';
  }

  static String sentryDsn() {
    return 'https://<EMAIL>/4507968870350848';
  }

  String getMM2Domain() {
    switch (_instance.flavor) {
      case Flavor.local:
        if (kIsWeb || !Platform.isAndroid) {
          return localMm2Domain;
        } else {
          return localMm2DomainAndroid;
        }
      case Flavor.dev:
        return devMm2Domain;
      case Flavor.stag:
        return stagMm2Domain;
      case Flavor.prod:
        return prodMm2Domain;
    }
  }
}
