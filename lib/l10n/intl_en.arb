{"@@locale": "en", "accessibilityMMLogo": "Micromentor logo: A blue stylized 'M' followed by the name Micromentor", "accessibilityWelcomePageIndicator": "Dot button indicating page {pageIndex} of {totalPages} in the image carousel", "accessibilityWelcomeSlide1": "2 women connecting in front of web screens", "accountHeader": "Account settings", "accountSettingConfirmNewPasswordInputLabel": "Confirm new password", "accountSettingCurrentPasswordInputLabel": "Current password", "accountSettingEditAppLanguageMsg": "This is the language you will use in the Micromentor app.", "accountSettingEditBirthYearMsg": "Enter your year of birth", "accountSettingEditBlockUsersMsg": "These users have been blocked by you. If you wish to unblock anyone, select the icon on your right.", "accountSettingEditEmailMsg": "Please enter your email", "accountSettingEditEmailTitle": "Email", "accountSettingEditGenderMsg": "We use this to better understand our community and it is never shared", "accountSettingEditNameHint": "ex. <PERSON>", "accountSettingEditNameMsg": "Please enter your name", "accountSettingEditPasswordMsg": "If you wish to change your current password, please enter your new password in the designated box below", "accountSettingEditPhoneNumberHint": "+1**************", "accountSettingEditPhoneNumberMsg": "We never share your phone number outside of Micromentor’s programs", "accountSettingEditRoleConfirmationTitle": "Switch role?", "accountSettingEditRoleNote": "Switching roles will clear some of your profile information. You will not lose any of your existing conversations and you can switch back any time.", "accountSettingEditRoleToMenteeButtonTitle": "Switch to become an entrepreneur", "accountSettingEditRoleToMenteeConfirmMessage": "Are you sure that you want to become an entrepreneur? Switching roles will clear some of your profile information.", "accountSettingEditRoleToMenteeNote": "You are currently a mentor on Micromentor. Want to switch to become an entrepreneur?", "accountSettingEditRoleToMentorButtonTitle": "Switch to become a mentor", "accountSettingEditRoleToMentorConfirmMessage": "Are you sure that you want to become a mentor? Switching roles will clear some of your profile information.", "accountSettingEditRoleToMentorNote": "You are currently an entrepreneur on Micromentor. Want to switch to become a mentor?", "accountSettingFirstNameInputHint": "ex. <PERSON>", "accountSettingFirstNameInputLabel": "First name", "accountSettingLastNameInputHint": "ex. <PERSON>", "accountSettingLastNameInputLabel": "Last name", "accountSettingLegalPrivacyPolicy": "Privacy Policy", "accountSettingLegalTermsOfUse": "Terms of Use", "accountSettingLegalTitle": "Legal", "accountSettingNewPasswordInputLabel": "New password", "accountSettingNoBlockedUserMsg": "You are currently not blocking any users.", "actionAccept": "Accept", "actionApply": "Apply", "actionBlock": "Block", "actionCancel": "Cancel", "actionClear": "Clear", "actionClearImpersonation": "Clear Impersonation", "actionConfirm": "Confirm", "actionContinue": "Continue", "actionDecline": "Decline", "actionImpersonate": "Impersonate", "actionLogOut": "Log out", "actionNext": "Next", "actionPrevious": "Previous", "actionReport": "Report", "actionSend": "Send", "actionSubmit": "Submit", "actionUnblock": "Unblock", "alreadyMember": "Already a member?", "alternatePhoneNumber": "Alternate phone number", "appLanguageAccountSettingMessage": "To change your Micromentor app language, [visit your Account Settings page by clicking here]({accountSettingsPageRoute})", "appLanguageHeading": "App language", "appLanguageMessage": "To change your preferred language to communicate in and other languages, [visit your Edit Profile page by clicking here]({editProfilePageRoute})", "blockedUsersHeading": "Blocked users", "blockedUsersIndicator": "You have blocked this user", "businessChallengeInputHint": "ex. I’ve launched my online store, but I haven’t made any sales yet.", "businessChallengeSubtitle": "Briefly describe the main problem you want to solve in the short term.", "businessChallengeTitle": "My biggest business challenge", "chatArchivedTitle": "Chat archived", "classicAdminDashboard": "Classic admin dashboard", "countryCodeInputHint": "US +1", "countryCodeOptional": "Country code (optional)", "countryValidationError": "Sharing your current country is required", "currentPasswordHeading": "Current password", "date": "Date", "dateHint": "DD", "dateLastMonth": "last month", "dateLastWeek": "last week", "dateLastYear": "last year", "datePastMonths": "{numMonths} months ago", "datePastWeeks": "{numWeeks} weeks ago", "datePastYears": "{numYears} years ago", "datePresent": "present", "dateToday": "today", "dateYesterday": "yesterday", "defaultValueLocation": "Unknown location", "delete": "Delete", "deleteAccountDialogConfirmInputLabel": "Enter 'Delete Account'", "deleteAccountDialogConfirmInstructions": "Please type the words 'Delete Account' to confirm.", "deleteAccountDialogHeading": "Delete My Account", "deleteAccountDialogSubtitle": "Are you sure you want to permanently delete your account? All your data will be lost. This cannot be undone.", "deleteAccountHeading": "Delete account", "deleteEducationDialogHeading": "Delete Education", "deleteEducationTitle": "Are you sure you want to delete this education?", "deleteExperienceDialogHeading": "Delete Experience", "deleteExperienceTitle": "Are you sure you want to delete this experience?", "desktopBannerCodeOfConductLabel": "Code of Conduct", "desktopBannerHelpCenterLabel": "Help center", "desktopBannerMincromentorName": "© {currentYear} Micromentor", "discardChangesTitle": "Discard unsaved changes?", "district": "District", "districtHint": "ex. Al-Aqaba", "doneTitle": "Done", "download": "Download", "downloadCertificate": "Download certificate", "downloadCertificateDialogMsg": "You did it! Celebrate your achievement—download your certificate and show off your success", "editPicture": "Edit picture", "editProfileExperienceLocationHint": "ex. Nairobi, Kenya", "editPronounsLabel": "Shown on your profile as:", "emptyLocationMessage": "We can’t find that location", "endorsement": "Endorsement", "entrepreneurHeading": "Entrepreneur", "errorAlreadyEntered": "Already entered", "errorEmptyField": "Cannot be empty.", "errorInvalidChoice": "Invalid choice", "errorMessageFailedToConnect": "Failed to connect. Please make sure this device has good connectivity, then try again.", "errorMessageUnknownError": "Unknown error occurred. Please try again.", "exit": "Exit", "exitAppDialogContinueUsingLabel": "Cancel", "exitAppDialogExitLabel": "Exit", "exitAppDialogMessage": "Are you sure you want to exit the app?", "exitAppDialogTitle": "Exit App", "exitContinueSignUp": "Continue sign up", "exitSignUpMessage": "Are you sure you would like to exit sign up without completing your account?", "exitSignUpTitle": "Exit sign up?", "expandableContainerViewLess": "View less", "expandableContainerViewMore": "View more", "exploreAdvanceFilterUserTypeEntrepreneur": "Entrepreneur", "exploreAdvanceFilterUserTypeMentor": "Mentor", "exploreMoreTitle": "Explore more", "exploreResultEndorsements": "{numEndorsements} endorsements", "exploreSearchExpertiseHintEntrepreneur": "What are you looking for help with?", "exploreSearchExpertiseHintMentor": "What are you looking to help with?", "exploreSearchFilterAdvancedFilters": "Advanced filters", "exploreSearchFilterAdvancedTitle": "Advanced filters", "exploreSearchFilterAll": "All", "exploreSearchFilterCountries": "{country, select, USA{USA} other{Unknown}}", "exploreSearchFilterDiscardFiltersMsg": "You've selected specific search filters, but it looks like you're leaving without applying them. Are you sure you want to discard your changes?", "exploreSearchFilterDiscardFiltersTitle": "Discard changes?", "exploreSearchFilterExpertise": "Expertise", "exploreSearchFilterHeadingCountries": "Country", "exploreSearchFilterHeadingLanguage": "Language", "exploreSearchFilterHideFilterMsg": "Hide advanced filters", "exploreSearchFilterHintSelect1": "Select one", "exploreSearchFilterHintSelectUpTo3": "Select up to three", "exploreSearchFilterHintTypeToSelectUpTo3": "Type to select up to three", "exploreSearchFilterIndustries": "{industry, select, Programming{Programming} Farming{Farming} Plumbing{Plumbing} other{Unknown}}", "exploreSearchFilterIndustry": "Industry", "exploreSearchFilterKeyword": "Keyword", "exploreSearchFilterKeywordHint": "First Name, Last Name, Company Name", "exploreSearchFilterLabel": "All filters", "exploreSearchFilterLanguages": "{language, select, English{English} Urdu{Urdu} Hindi{Hindi} other{Unknown}}", "exploreSearchFilterReset": "Reset all", "exploreSearchFilterShowFilterMsg": "Show advanced filters", "exploreSearchFilterSkills": "{skill, select, Marketing{Marketing} Operations{Operations} StartingUp{Starting Up} other{Unknown}}", "exploreSearchFilterTitle": "Search filters", "exploreSearchFilterUserType": "User type", "exploreSearchFilterUserTypes": "{userType, select, mentor{<PERSON><PERSON>} mentee{Entrepreneur} other{Unknown}}", "exploreSearchFilterVentureStage": "Business Stage", "exploreSearchLanguageLocationHint": "Any language • Any location", "exploreSearchNoResultsSubtitle": "Try removing some filters to expand your search", "exploreSearchNoResultsTitle": "No results found", "exploreSearchSeeMore": "See More", "exploreSendInviteButton": "Send {selectedCount} invites", "exploreSendInviteButtonDisabled": "Select profiles to send invites", "exploreTipsEntrepreneurSubtitle": "Connect with 3+ mentors for your best chance at finding the right match.", "exploreTipsEntrepreneurTitle": "Supercharge your success!", "exploreTipsMentorSubtitle": "Connect with three entrepreneurs to maximize your impact.", "exploreTipsMentorTitle": "Maximize your impact!", "familyName": "Family name", "fathersName": "Father's name", "findMyResources": "Find My Resources", "gender": "Gender", "governorate": "Governorate", "governorateHint": "ex. Aqaba", "helpCenterTitle": "Help center", "homeBetaBannerMsgPart1": "Welcome to the new Micromentor experience! ✨", "homeBetaBannerMsgPart2": "We’re here to help—please let us know if you have any feedback or need support: ", "homeBetaBannerMsgPart3": "Get in touch", "homeGreetingAfternoon": "Good afternoon", "homeGreetingEvening": "Good evening", "homeGreetingMorning": "Good morning", "homeInvitationSectionTitle": "Your new invitations", "homePercentageProfileCompletion": "{profileCompletionPercentage}%", "homeRecommendedEntrepreneurTitle": "Mentors for you", "homeRecommendedExpertiseEntrepreneur": "Seeking expertise in:", "homeRecommendedExpertiseMentor": "Expertises:", "homeRecommendedMentorTitle": "Entrepreneurs for you", "homeRecommendedSubtitle": "Recommendations are based on profile", "homeReminderBannerProfileCompletePrompt": "Complete profile", "homeReminderBannerProfileCompleteSubtitle": "Provide more details for better recommendations", "homeReminderBannerProfileCompleteTitle": "Your profile is almost complete", "homeReminderBannerProfileUpdatePrompt": "Update profile", "homeReminderBannerProfileUpdateSubtitle": "Provide more details for better recommendations", "homeReminderBannerProfileUpdateTitle": "Update your profile", "homeResourcesItemBuildingFuture": "Training: Building a Sustainable Future", "homeResourcesItemMarketingBusiness": "Marketing Your Business 101", "homeResourcesItemMentoringFirstSteps": "Training: Mentoring First Steps", "homeResourcesTitle": "More resources", "howCanMentorSupportMeInputHint": "ex. I’d like advice on how to promote my business and reach more customers online.", "howCanMentorSupportMeSubtitle": "Tell us what kind of support or guidance you’re looking for.", "howCanMentorSupportMeTitle": "How a mentor can help?", "howYouCanHelpInputHint": "ex. With 15 years of experience in finance, I want to help entrepreneurs avoid common financial pitfalls and build strong, sustainable businesses.", "howYouCanHelpSubtitle": "Share your experience and motivation so entrepreneurs understand how they can benefit from your guidance.", "howYouCanHelpTitle": "How you can help?", "identityProviderApple": "Apple", "identityProviderFacebook": "Facebook", "identityProviderGoogle": "Google", "identityProviderLinkedIn": "LinkedIn", "identityProviderOwn": "Email", "identityProviderTelegram": "Telegram", "identityProviderTrackingCaveat": "Update the permissions in your [device settings]({deviceSettings}) to log in using Facebook.", "inboxArchivedChatsTitle": "Archived chats", "inboxChatsEmptyState": "You do not have any chats.", "inboxChatsNoChatSelectedMsg": "No chat selected", "inboxChatsTitle": "Chats", "inboxInvitesActionWithdraw": "Withdraw invite", "inboxInvitesDateSent": "<PERSON><PERSON>", "inboxInvitesEmptyState": "There are no requests.", "inboxInvitesReceived": "Received invites", "inboxInvitesReceivedMessage": "You have a new invitation to connect", "inboxInvitesReceivedTitle": "New invite", "inboxInvitesSent": "<PERSON><PERSON> invites", "inboxInvitesSentFooter": "Your invite is currently pending", "inboxInvitesSentTitle": "Sent invite", "inboxInvitesTitle": "<PERSON><PERSON><PERSON>", "inboxMessagesActionDelete": "Delete", "inboxMessagesActionEdit": "Edit", "inboxMessagesInputHint": "Write a message...", "inboxMessagesStatusDeletedByMe": "You deleted this message.", "inboxMessagesStatusDeletedByOne": "", "inboxMessagesStatusDeletedByOther": "This message was deleted.", "inboxMessagesStatusEdited": "Edited {timestamp}", "inboxTitle": "Inbox", "invalidUrlMessage": "This url cannot be launched. Please enter valid url.", "invalidUrlTitle": "Invalid Company Website", "inviteCreateCustomizeMessagePrompt": "Customize your message", "inviteCreateMessageDefaultToEntrepreneur": "Hi {user<PERSON><PERSON>},\nI saw your profile and I would love to chat about some of your challenges. Would you like to connect?", "inviteCreateMessageDefaultToMentor": "Hi {user<PERSON><PERSON>},\nI saw your profile and think you could be a great mentor for my business. Can we connect? I admire your experience and would love to learn from you.\nThanks!", "inviteCreateMessagePlaceholder": "Enter invite message", "inviteCreateMessageTipsSubtitle": "• Make your message specific\n• Keep it concise and simple\n• Be polite and respectful", "inviteCreateMessageTipsTitle": "Message Tips:", "inviteCreateTitle": "Invite to Connect", "inviteDeclineReasonFakeProfile": "Profile is fake, spam, or a scammer", "inviteDeclineReasonInappropriate": "Inappropriate behavior or content", "inviteDeclineReasonNoReason": "No reason", "inviteDeclineReasonNotGoodFit": "I'm not a good fit", "inviteDeclineReasonTooBusy": "I'm too busy right now", "inviteDeclineSubtitle": "Your reason is private and will not be shared with {name}.", "inviteDeclineTitle": "Why would you like to decline?", "iqlaaBusinessRegistrationOptionYesDisclamer": "If yes, please enter commercial registration number in the box below:", "iqlaaBusinessRegistrationSubtitle": "This information will not be shared outside of Micromentor/Mercy Corps and USAID programs.", "iqlaaBusinessRegistrationTitle": "Is your business or project registered in the Companies Control Department - Ministry of Industries and Trading?", "iqlaaConsentTitle": "<PERSON><PERSON><PERSON><PERSON> consent", "iqlaaEmployeeNumberTextTieldHint": "ex. 6", "iqlaaEmployeeNumberTextTieldLabel": "Total number of employees", "iqlaaEmployeeNumberTitle": "Number of employees", "iqlaaHomeBasedBusinessDisclamer": "This information will not be shared outside of Micromentor/Mercy Corps and USAID programs.", "iqlaaSignupConsentOption1": "I understand that after filling in this form, my business/project/idea will be registered at Iqlaa program, I will be contacted only if my business/project/idea meets the selection criteria of Iqlaa program.", "iqlaaSignupConsentOption2": "I agree to provide full information about me and my project/business/idea and understand that my personal information will not be shared with any party, and it will be kept safe within the Iqlaa program.", "iqlaaSignupConsentOption3": "I am aware that any information shared with the government will not contain my personal information, including my name, ID number, and other information that could identify me. and in case of the need to share such information with the government, I will be contacted for a concents in advance.", "iqlaaSignupDataConsentConfirmationMessage": "I certify that I have read, and understood the consent statement and agree to complete this form. (y/n)", "iqlaaSignupDataConsentDeclinationMsg": "Without consenting to the above statement, you cannot join the Iqlaa community. You will be redirected to the general Micromentor community instead", "iqlaaSignupDataConsentMustAgreeWarning": "You must agree to and check all 3 statements and agree to the statement to proceed.", "iqlaaSignupHomeBasedBusinessTitle": "Is your business a home-based business?", "iqlaaSignupReasonEntrepreneurTitle": "Why did you start your business?", "lessonPlan": "Lesson Plan:", "listSeeAll": "See all", "listSeeMore": "See more", "listSeparator": ", ", "logOutDialogHeading": "Log out?", "logOutSubtitle": "Once logged out, you will not receive notifications for new chats, invites, reminders, or recommendations.", "login": "<PERSON><PERSON>", "mastercardBankDetailsSubtitle": "Please share the following information about the financial services you currently use. The data you provide is protected by our privacy policy and will allow us to understand how best to continue to bring financial services to women entrepreneurs.", "mastercardBankDetailsTitle": "Mastercard has partnered with Micromentor to bring you free mentorship as a client of a LAC Financial Institution.", "mastercardBusinessCardTitle": "What type of small business card do you use?", "mastercardPersonalCardTitle": "What type of personal card do you use?", "mastercardSelectBankHint": "Ex: Republic Bank EC Limited", "mastercardSelectBankTitle": "Who do you bank with today for your business needs?", "mastercardTypeCredit": "Credit", "mastercardTypeDebit": "Debit", "mastercardTypeNone": "None", "mastercardTypeNotProvided": "I prefer not to say", "mastercardTypePrepaid": "Prepaid", "maximumViewableChatHistoryMessage": "You have reached the maximum allowed viewable chat history. Please bear with us as we work on upgrading our systems to access your entire chat history.", "message": "Message", "microMentorsAgreement": "I agree to Micromentor´s Terms of Use and Privacy Policy", "month": "Month", "monthHint": "MM", "multiSelectDropdownLanguageNotFoundMsg": "We can’t find that language", "myProfile": "My Profile", "myTraningTitle": "My Trainings", "name": "Name", "navTabExplore": "Explore", "navTabHome": "Home", "navTabInbox": "Inbox", "navTabProfile": "Profile", "navTabTrainings": "Trainings", "no": "No", "noPreviewAvaliable": "No image preview available", "notificationEmail": "Email Permissions", "notificationHeading": "Notification Permissions", "notificationNewMsgInvite": "New messages & invitations", "notificationPush": "Push Notifications", "notificationRecommendTip": "Recommendations & Tips", "notificationRecommendation": "Recommendations", "notificationScreenSubtitle": "Turn on notifications to get the most out of Micromentor", "notificationTipAdviceUpdate": "Tips, advice, & updates from the community", "notificationsHeading": "Notifications", "okTitle": "OK", "or": "or", "passwordAndSecurityHeading": "Password & security", "permissionAccessDescription": "Allow Micrometor to access photos on your device.", "permissionAccessTitle": "\"Micromentor\" would like to access your photos", "personalDetails": "Personal details", "phone": "Phone", "phoneNumberInputCountryLabel": "Country code", "phoneNumberInputHint": "XXX-XXX-XXXX", "phoneNumberInputLabel": "Phone number", "phoneNumberOptional": "Phone number (optional)", "profileEditMainAboutCurrentCountrySection": "Current country", "profileEditMainAboutCurrentLocationSection": "Current city", "profileEditMainAboutHeader": "About me", "profileEditMainAboutLanguageOthersSection": "Other languages", "profileEditMainAboutLanguagePreferredSection": "Preferred language", "profileEditMainAboutLinkedInConfirmationSection": "LinkedIn connected", "profileEditMainAboutLinkedInPromptSection": "Connect your LinkedIn", "profileEditMainAboutOriginLocationSection": "Where are you from?", "profileEditMainAboutPronounsSection": "Pronouns", "profileEditMainBusinessHeader": "About my business", "profileEditMainBusinessIndustrySection": "Industry", "profileEditMainBusinessLocationSection": "Business location", "profileEditMainBusinessMissionSection": "Business mission", "profileEditMainBusinessMotivationSection": "Why I started this business", "profileEditMainBusinessNameSection": "Business name", "profileEditMainBusinessPhotosSection": "Photos", "profileEditMainBusinessStageSection": "Business stage", "profileEditMainBusinessTopicsAdditionalHint": "Select up to three additional topics", "profileEditMainBusinessTopicsHint": "Select top three topics", "profileEditMainBusinessTopicsSection": "I am looking for help with:", "profileEditMainBusinessWebsiteSection": "Website", "profileEditMainEducationAddSection": "+ Add additional education", "profileEditMainEducationHeader": "My education", "profileEditMainExperienceAddSection": "+ Add additional experience", "profileEditMainExperienceHeader": "My experience", "profileEditMainMentorExpertisesAdditionalHint": "Select up to three additional topics", "profileEditMainMentorExpertisesHint": "Select top three topics", "profileEditMainMentorExpertisesSection": "My expertises", "profileEditMainMentorHeader": "How can I help", "profileEditMainMentorIndustriesSection": "My industries", "profileEditMainMentorPreferencesSection": "My mentoring preferences", "profileEditMainMentorWhyIMentor": "Why I mentor", "profileEditMainMentorWhyIMentorInputHint": "ex. I started my business three years ago after more than 10 years in the coffee business. I am passionate about coffee and I love helping other entreprneurs with their coffee related businesses.", "profileEditOtherLanguageInputLabel": "Other languages", "profileEditPictureLabel": "Edit picture", "profileEditPreferredLanguageErrorMsg": "You must select a preferred language.", "profileEditPreferredLanguageInputLabel": "Preferred language", "profileEditSectionAboutCurrentLocationInputHint": "Type to select", "profileEditSectionAboutCurrentLocationInputLabel": "Find your current city", "profileEditSectionAboutCurrentLocationTitle": "Current city", "profileEditSectionAboutLanguageOthersSubtitle": "What other languages do you speak fluently? (optional)", "profileEditSectionAboutLanguageOthersTitle": "Other languages", "profileEditSectionAboutLanguagePreferredSubtitle": "What's your preferred language to communicate in? Select 1", "profileEditSectionAboutLanguagePreferredTitle": "Preferred language", "profileEditSectionAboutLinkedInInputHint": "ex: www.linkedin.com/in/jperez", "profileEditSectionAboutLinkedInInputLabel": "LinkedIn URL", "profileEditSectionAboutLinkedInTitle": "Connect LinkedIn", "profileEditSectionAboutOriginLocationInputHint": "Type to select", "profileEditSectionAboutOriginLocationInputLabel": "Find city you are from", "profileEditSectionAboutOriginLocationTitle": "Where are you from?", "profileEditSectionAboutPronounsSubtitle": "This helps other users know how you would like to be referred to. Select all that apply. (optional)", "profileEditSectionAboutPronounsTitle": "Pronouns", "profileEditSectionBusinessIndustrySubtitle": "What industry is your business in? Select one", "profileEditSectionBusinessIndustryTitle": "Industry", "profileEditSectionBusinessLocationInputHint": "ex: Nairobi, Kenya", "profileEditSectionBusinessLocationInputLabel": "Where is your business located?", "profileEditSectionBusinessLocationTitle": "Business location", "profileEditSectionBusinessMissionInputHint": "ex. I started my business three years ago after more than 10 years in the coffee business. I am passionate about coffee and I love creating a space for my community to come together.", "profileEditSectionBusinessMissionTitle": "Business mission", "profileEditSectionBusinessNameInputHint": "ex. Cafe Orchid", "profileEditSectionBusinessNameInputLabel": "What is your business name?", "profileEditSectionBusinessNameTitle": "Business name", "profileEditSectionBusinessReasonInputHint": "ex. I started my business three years ago after more than 10 years in the coffee business. I am passionate about coffee and I love creating a space for my community to come together.", "profileEditSectionBusinessReasonTitle": "Why I started this business", "profileEditSectionBusinessStageCard1Description": "I do not yet have a working prototype or customers and am not operational.", "profileEditSectionBusinessStageCard1Title": "Idea stage", "profileEditSectionBusinessStageCard2Description": "I have a product or service offering but I have not yet earned revenue.", "profileEditSectionBusinessStageCard2Title": "Operational", "profileEditSectionBusinessStageCard3Description": "I am earning revenue but I am not yet profitable.", "profileEditSectionBusinessStageCard3Title": "Revenue earning", "profileEditSectionBusinessStageCard4Description": "I am making a profit and am ready to grow my business.", "profileEditSectionBusinessStageCard4Title": "Profitable and Scaling", "profileEditSectionBusinessStageSubtitle": "What stage is your business currently in?", "profileEditSectionBusinessStageTitle": "Business stage", "profileEditSectionBusinessTopicsAdditionalSubtitle": "Select up to three additional topics you would like to receive mentoring in", "profileEditSectionBusinessTopicsAdditionalTitle": "Additional topics", "profileEditSectionBusinessTopicsTopSubtitle": "Select the top three topics that you would like to receive mentoring in", "profileEditSectionBusinessTopicsTopTitle": "Top three topics", "profileEditSectionBusinessWebsiteInputHint": "ex. www.cafeorchid.com", "profileEditSectionBusinessWebsiteInputLabel": "What is your website URL?", "profileEditSectionBusinessWebsiteTitle": "Website", "profileEditSectionEducationAddTitle": "Add education", "profileEditSectionEducationDateEndInputLabel": "End date", "profileEditSectionEducationDateInputHint": "YYYY", "profileEditSectionEducationDateStartInputLabel": "Start date", "profileEditSectionEducationDegreeInputHint": "ex. Bachelor of Science", "profileEditSectionEducationDegreeInputLabel": "Degree", "profileEditSectionEducationEditTitle": "Edit education", "profileEditSectionEducationSchoolInputHint": "ex. University of Delhi", "profileEditSectionEducationSchoolInputLabel": "School*", "profileEditSectionEducationShowInHeaderInputLabel": "Show in header (limited to one educational experience)", "profileEditSectionEducationStudyFieldInputHint": "ex. Organic Chemistry", "profileEditSectionEducationStudyFieldInputLabel": "Field of study", "profileEditSectionExperienceAddTitle": "Add experience", "profileEditSectionExperienceCompanyInputHint": "ex. Cafe Orchid", "profileEditSectionExperienceCompanyInputLabel": "Company name*", "profileEditSectionExperienceDateEndInputLabel": "End date", "profileEditSectionExperienceDateEndPresentInputLabel": "I am currently working in this role", "profileEditSectionExperienceDateInputHint": "YYYY", "profileEditSectionExperienceDateStartInputLabel": "Start date", "profileEditSectionExperienceEditTitle": "Edit experience", "profileEditSectionExperienceLocationInputHint": "Type to select", "profileEditSectionExperienceLocationInputLabel": "Location", "profileEditSectionExperienceRoleInputHint": "ex. Executive Director", "profileEditSectionExperienceRoleInputLabel": "Role*", "profileEditSectionExperienceShowInHeaderInputLabel": "Use as current role and company header", "profileEditSectionMentorExpertisesAdditionalSubtitle": "Select up to three more topics you would like to mentor on", "profileEditSectionMentorExpertisesAdditionalTitle": "Additional expertises", "profileEditSectionMentorExpertisesTopSubtitle": "Select the top three topics you would like to mentor on", "profileEditSectionMentorExpertisesTopTitle": "Top three expertises", "profileEditSectionMentorIndustrySubtitle": "What industry do you have experience in? Select up to three", "profileEditSectionMentorIndustryTitle": "Industry", "profileEditSectionMentorPreferencesOption1": "Weekly check-ins", "profileEditSectionMentorPreferencesOption2": "Monthly check-ins", "profileEditSectionMentorPreferencesOption3": "One-off sessions", "profileEditSectionMentorPreferencesOption4": "Informal chats", "profileEditSectionMentorPreferencesOption5": "Formal meetings", "profileEditSectionMentorPreferencesOption6": "Long term mentoring", "profileEditSectionMentorPreferencesSubtitle": "Select all that apply", "profileEditSectionMentorPreferencesTitle": "Mentoring preferences", "profileEditTitle": "Edit profile", "profilePhoto": "Profile Picture", "profilePhotoTake": "Take photo", "profilePhotoUpload": "Upload photo", "profileQuickCardMyExpertises": "My expertises:", "profileViewAboutBusiness": "About my business", "profileViewAboutBusinessStage": "{stageDescriptor} stage", "profileViewAboutFrom": "From {location}", "profileViewAboutMe": "About me", "profileViewEditButtonLabel": "Edit Profile", "profileViewEducation": "My education", "profileViewExperience": "My experience", "profileViewHeaderImpersonate": "Impersonate", "profileViewHeaderInvite": "Invite {firstName} to Connect", "profileViewHeaderWithdraw": "Cancel pending request", "profileViewHelpWith": "I am looking for help with:", "profileViewHowCanIHelp": "How can I help", "profileViewHowCanIHelpExpertises": "My expertises:", "profileViewHowCanIHelpIndustries": "My industries:", "profileViewHowCanIHelpPreferences": "My mentoring preferences:", "profileViewHowCanIHelpWhyIMentor": "Why I mentor:", "profileViewMentorViewLess": "View less", "profileViewMentorViewMore": "View more", "profileViewMission": "Business mission", "profileViewMotivation": "Why I started this business", "profileViewMyLanguages": "My languages:", "profileViewOverview": "Overview", "profileViewPreferredLanguage": "preferred", "profileViewUserTypeEntrepreneur": "ENTREPRENEUR", "profileViewUserTypeMentor": "MENTOR", "profileViewVacationBanner": "I am on vacation so my responses may be slow.", "removeProfilePictureDialogButtonLabel": "Remove", "removeProfilePictureDialogDescription": "Are you sure you want to remove this profile picture?", "removeProfilePictureDialogTitle": "Remove Profile Picture", "resendCodeInSeconds": "(new code available in {secondsRemaining})", "resendCodeTitle": "Send new code", "resetPasswordCompletedSubtitle": "Your password has been reset successfully.", "resetPasswordCompletedTitle": "You're all done!", "resetPasswordEmail": "EMAIL", "resetPasswordEmailNotFound": "Please double-check the email address you entered and try again.", "resetPasswordEnterCodeHint": "------", "resetPasswordEnterCodeLabel": "Enter code", "resetPasswordEnterCodeSubtitle": "We've sent a code on your email. Please enter it below:", "resetPasswordEnterCodeTitle": "Check your email", "resetPasswordEnterNewPasswordSubtitle": "If you have not received a code to the email you provided, <NAME_EMAIL>", "resetPasswordEnterNewPasswordTitle": "Enter new password", "resetPasswordFailedToSendNotification": "We could not send out the code to you. Please contact customer support.", "resetPasswordSubtitle": "Please enter the email you saved to your Micromentor profile.", "resetPasswordTitle": "Reset password", "resetPasswordTokenMismatchError": "The code you entered could not be verified. Please check your email and try again.", "reviewTitle": "Review", "role": "Role", "routerErrorMessage": "Sorry, <PERSON> Not Found.", "runAdminTaskTitle": "Run Admin Task", "saveAction": "Save", "saveChangeDialogDiscardLabel": "Discard", "saveChangeDialogMessage": "You have unsaved changes. Would you like to save them before exiting?", "saveChangeDialogSaveLabel": "Save", "saveChangeDialogTitle": "Save changes?", "selectLanguageTitle": "Select Language", "selectPreferencesHeading": "Select preferences", "sentInviteSuccessMessage": "While you wait for a response, browse for more {users}", "sentInviteSuccessTitle": "Your invitation has been sent", "settings": "Settings", "signUpBusinessSectorSubtitle": "Select one. This information will be shared on your profile.", "signUpBusinessSectorTitle": "Business sector", "signinDontHaveAccountPrompt": "Don't have an account? Sign up", "signinEmailInputLabel": "Email address", "signinFailed": "Invalid email or password. Please check your login details and try again.", "signinForgotPasswordPrompt": "Forgot password?", "signinPasswordInputLabel": "Password", "signinSsoDivider": "or login with", "signinSubmitButton": "Sign in", "signinSubtitle": "Please sign into your account with", "signinTitle": "Welcome!", "signupAgreement": "By signing up, you are agreeing to our [Terms of Use]({termsOfUse})", "signupBirthInputHint": "YYYY", "signupBirthInputLabel": "Year of birth", "signupBirthSubtitle": "You must be over 18 years old to join Micromentor", "signupBirthTitle": "What year were you born?", "signupBusinessNameInputHint": "ex. Cafe Orchid", "signupBusinessNameInputLabel": "Business name", "signupBusinessNameSubtitle": "This will be shared on your profile", "signupBusinessNameTitle": "What is the name of your business?", "signupBusinessStageCard1Description": "I do not yet have a working prototype or customers and am not operational.", "signupBusinessStageCard1Title": "Idea stage", "signupBusinessStageCard2Description": "I have a product or service offering but I have not yet earned revenue.", "signupBusinessStageCard2Title": "Operational", "signupBusinessStageCard3Description": "I am earning revenue but I am not yet profitable.", "signupBusinessStageCard3Title": "Revenue earning", "signupBusinessStageCard4Description": "I am making a profit and am ready to grow my business.", "signupBusinessStageCard4Title": "Profitable and scaling", "signupBusinessStageSubtitle": "We use this to better understand your needs. It will be shared on your profile.", "signupBusinessStageTitle": "What stage is your business?", "signupBusinessStartYearTitle": "Business start year", "signupCompletedContent1": "Here are some ground rules we ask you to follow to keep Micromentor a safe and inclusive community:", "signupCompletedContent2": "Entrepreneurs may not ask for money.", "signupCompletedContent2Icon": "🚫", "signupCompletedContent3": "Mentors may not charge for services.", "signupCompletedContent3Icon": "💰", "signupCompletedContent4": "Report any inappropriate, offensive, or bullying behavior.", "signupCompletedContent4Icon": "🚨", "signupCompletedContent5Entrepreneur": "Ready to start connecting with mentors?", "signupCompletedContent5Mentor": "Ready to start connecting with entrepreneurs?", "signupCompletedEntrepreneurAction": "Find mentors", "signupCompletedMentorAction": "Find entrepreneurs", "signupCompletedTitle": "Welcome!", "signupCredentialsEmailInputHint": "ex. <EMAIL>", "signupCredentialsEmailInputLabel": "Email address", "signupCredentialsNameFirstInputHint": "ex. <PERSON>", "signupCredentialsNameFirstInputLabel": "First name", "signupCredentialsNameLastInputHint": "ex. <PERSON>", "signupCredentialsNameLastInputLabel": "Last name", "signupCredentialsNewsCheckboxLabel": "Receive updates and news from Micromentor", "signupCredentialsPasswordConfirmInputLabel": "Confirm password", "signupCredentialsPasswordInputLabel": "Password", "signupCredentialsPasswordLengthRequirementError": "Password must contain at least 8 characters.", "signupCredentialsSubTitle": "Create an account with your email", "signupCredentialsTitle": "Email sign up", "signupDataUseConsentAcceptInputLabel": "I acknowledge that I have read and understood the Privacy Policy and consent to the data practices described.", "signupDataUseConsentBody": "By checking the box below, you consent to Micromentor, an initiative of Mercy Corps, collecting, storing, and using the personal data entered in this form. We will use your data exclusively to manage your account, provide the services you request, and improve our services. We will not share your personal data with third parties without your explicit consent. For more details, please read our [Privacy Policy]({privacyPolicy}).", "signupDataUseConsentDeclinationStatement": "Without consenting to Micromentor processing your personal data, you will be unable to participate in our community.", "signupDataUseConsentDeclineInputLabel": "I do not consent to Micromentor processing my personal data", "signupDataUseConsentTitle": "Data consent", "signupDataUseConsentWithdrawlClause": "You can withdraw your consent at any time by contacting <NAME_EMAIL>.", "signupExpertisesEntrepreneurSubtitle": "We will use this to help you find the right mentor connection.", "signupExpertisesEntrepreneurTitle": "Select three areas where you would like help", "signupExpertisesMentorTitle": "Select three areas of expertise where you would like to help", "signupGenderSubtitle": "We use this to better understand our community and it is never shared", "signupGenderTitle": "What's your gender?", "signupIqlaaLocationText": "Joining Micromentor from outside of Jordan? [Click here]().", "signupIqlaaNationalitySubtitle": "Select one. We never share your nationality outside of Micromentor and Mercy Corps programs.", "signupIqlaaNationalityTitle": "What is your nationality?", "signupLanguageInputHint": "Type to select one", "signupLanguageInputLabel": "Preferred language", "signupLanguageSubtitle": "We use this to connect you with others who speak the same language as you", "signupLanguageTitle": "What's your preferred language to communicate in?", "signupLocationCityInputLabel": "Find your current city", "signupLocationCountryInputLabel": "Find your current country", "signupLocationInputHint": "Type to select", "signupLocationSubtitle": "We share this information on your profile", "signupLocationTitle": "Where do you live?", "signupMethodSubtitle": "I, {firstName} {lastName}, agree to Micromentor’s [Code of Conduct]({codeOfConduct})", "signupMethodTitle": "Sign up", "signupPhoneSubtitle": "We never share your phone number outside of Micromentor’s programs", "signupPhoneTitle": "What's your phone number?", "signupProfileRoleEntrepreneur": "I'm an entrepreneur", "signupProfileRoleEntrepreneurDescription": "I own a business and I am looking for a mentor", "signupProfileRoleMentor": "I'm a mentor", "signupProfileRoleMentorDescription": "I am an experienced professional and I want to share my knowledge", "signupProfileRoleTitle": "What brings you to Micromentor?", "signupReasonEntrepreneurInputHint": "ex. I started my business three years ago after more than 10 years in the coffee business. I am passionate about coffee and I love creating a space for my community to come together.", "signupReasonEntrepreneurSubtitle": "Tell mentors more about what motivates you. This will be shared on your profile.", "signupReasonEntrepreneurTitle": "Almost done! Why did you start your business?", "signupReasonMentorInputHint": "ex. I worked with a mentor and she made all the difference. I am excited to share my experience and connect with other entrepreneurs from all over the world.", "signupReasonMentorSubtitle": "Tell entrepreneurs more about what motivates you to be a mentor. This will be shared on your profile.", "signupReasonMentorTitle": "Almost done! Why do you want to mentor?", "signupRoleCompanyInputHint": "ex. Evergreen Ventures", "signupRoleCompanyInputLabel": "Company or organization name", "signupRoleJobTitleInputHint": "ex. Co-founder and CEO", "signupRoleJobTitleInputLabel": "Title", "signupRoleSubtitle": "This will be shared on your profile", "signupRoleTitle": "What is your current role?", "signupSubTitle": "By signing up, you are agreeing to our [Terms of Use]({termsOfUse}) and [Privacy Policy]({privacyPolicy})", "signupText": "Sign up", "signupYearTitle": "Year", "ssoWhatsapp": "WhatsApp", "startTitle": "Start", "striveIndonesiaDistrict": "District", "striveIndonesiaDistrictHint": "ex. <PERSON><PERSON>", "striveIndonesiaProvince": "Province", "striveIndonesiaProvinceHint": "ex. <PERSON><PERSON>", "striveUserEntrepreneurTitle": "I own a business and want to join STRIVE", "switchOffLabel": "OFF", "switchOnLabel": "ON", "titleEntrepreneurs": "Entrepreneurs", "titleMentors": "Mentors", "trainingCourse": "Training course", "trainingCourses": "Training courses", "trainingDownloadCertificatesOpenSettingsLabel": "Open Settings", "trainingDownloadCertificatesPermissionDeniedMsg": "Storage permission is permanently denied. Please enable it from the app settings.", "trainingDownloadCertificatesPermissionLabel": "Permission Required", "trainingDownloadCertificatesStoragePermissionRequest": "This app requires storage access to download files. Please grant the permission.", "trainingFailMessage": "You did not pass the training. Please try again.", "trainingLearningTitle": "What you’ll learn", "trainingPassMessage": "Congrats! You passed the training.", "trainingStatusCompleted": "Completed", "trainingStatusInProgress": "In progress", "trainingStatusRecommended": "Recommended for you", "undoTitle": "Undo", "updateAvatarCropImageDoneAction": "Done", "updateAvatarDragDropMessage": "Drag and drop your picture here", "updateAvatarGotItText": "Got it", "updateAvatarInvalidFileTypeError": "Please select a valid image type", "updateAvatarOR": "or", "updateAvatarRemovePictureTitle": "Remove profile picture", "updateAvatarSavePictureTitle": "Save picture", "updateAvatarSelectPictureTitle": "Choose from library", "updateAvatarSuccessMsg": "Your picture has been saved successfully. It will take a little while to process before it will appear as your new profile picture", "updateAvatarSuccessTitle": "Success!", "updateAvatarTakePicture": "Take a picture", "userBlockErrorMsg": "Error occurred while blocking the user {fullName}.", "userBlockSubtitle": "This action will block {fullName} and prevent them from messaging you or viewing your profile.", "userBlockSuccessMsg": "You blocked {fullName}.", "userBlockTitle": "Block {fullName}", "userCardEntrepreneurTitle": "Looking for help with:", "userOverflowActionArchive": "Archive chat", "userOverflowActionBlock": "Block user", "userOverflowActionReport": "Report user", "userOverflowActionUnarchive": "Unarchive chat", "userOverflowActionUnblockUser": "Unblock user", "userReportErrorMsg": "Error occurred while reporting the user {fullName}.", "userReportReasonOne": "", "userReportReasonOther": "Other", "userReportSubtitle": "Your identity will not be shared.", "userReportSuccessMsg": "Success! Your report has been received.", "userReportTitle": "Report {fullName}", "userUnblockErrorMsg": "Error occurred while unblocking the user {fullName}.", "userUnblockSubtitle": "You blocked {fullName}.They cannot send you messages or view your profile. Do you want to remove this block?", "userUnblockSuccessMsg": "You unblocked {fullName}.", "userUnblockTitle": "Unblock {fullName}", "vacationModeOff": "Vacation mode is currently off", "vacationModeOn": "Vacation mode is currently on", "vacationModeTitle": "Vacation mode", "vacationOffModeSubTitle": "Your profile is being shown in recommendations and on the explore page.", "vacationOnModeSubTitle": "Your profile is not shown in recommendations or on the explore page, however, existing connections can still contact you.", "vacationPromptDialogActionButtonTitle": "Change to vacation mode", "vacationPromptDialogCancelButtonTitle": "Not right now", "vacationPromptDialogMsg": "Change your profile to vacation mode to no longer show up in search results.", "vacationPromptDialogTitle": "Need a break from new invites?", "validation2faCodeNoMatch": "We could not verify the code you entered. Please try again.", "validationBirthDateInvalid": "Please enter a valid 2-digit date between 1–31 to continue.", "validationBirthMonthInvalid": "Please enter a valid 2-digit month between 1–12 to continue. ", "validationBirthYearInvalid": "Please enter a 4-digit year (ex: 1989) to continue.", "validationBirthYearInvalidAge": "You must be at least 18 years old to join Micromentor.", "validationDateInvalidRange": "Entered date should not be in future", "validationEmailEmpty": "Please enter an email address.", "validationEmailInvalid": "Please enter a valid email <NAME_EMAIL>.", "validationEmailNotAvailable": "This email belongs to an existing account. Please log into this account, or use a different email address.", "validationFirstNameEmpty": "Please enter your first name.", "validationFullNameEmpty": "Please enter a valid full name.", "validationFullNameLastNameMissing": "Please enter your first and last name.", "validationIncorrect2faCode": "Please enter a 6 digit code.", "validationLastNameEmpty": "Please enter your last name.", "validationLinkedinUriInvalid": "Please enter a valid LinkedIn public profile URL (i.e. www.linkedin.com/in/jperez)", "validationPasswordInvalid": "Password must contain at least 8 characters.", "validationPasswordInvalidTooLong": "Password must contain less than 100 characters.", "validationPasswordNoMatch": "Passwords do not match. Please re-enter password again.", "validationPhoneNumberInvalid": "Please provide a valid phone number", "validationPhoneNumberNotAvailable": "This phone number belongs to an existing account. Please log into this account, or use a different phone number.", "validationRequiredInput": "Required", "validationUriInvalid": "Please enter a valid web address (i.e. www.somewhere.com)", "validationUrlInvalid": "Please enter a valid web address (i.e. https://www.somewhere.com)", "validationYear": "Enter a valid year(YYYY)", "validationYearCompare": "Start date must be before end date", "validationYearInvalidRange": "Entered year should be within a reasonable range", "view": "View", "viewBlockedUserHeading": "View all blocked users", "viewProfile": "View Profile", "view_invitation": "View Invitation", "welcomeButtonLabel": "Get Started", "welcomeLoginPrompt": "Have an account? Sign in", "welcomePreviewSlide1": "Micromentor is a global community of small business owners and volunteer mentors.", "welcomePreviewSlide2": "We are 100% free—our community of volunteer mentors work with entrepreneurs from around the world.", "welcomePreviewSlide3": "Access strategic advice for your business across industries, regions, and business stages.", "welcomeSubtitle": "It only takes a few minutes.", "welcomeTitle": "Let's get started", "withSupportFrom": "with support from", "yes": "Yes"}