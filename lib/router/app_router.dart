import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/main/app.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../main/splash_screen.dart';
import '../models/models.dart';
import '../services/firebase/analytic_service.dart';
import '../services/graphql/providers/providers.dart';
import '../widgets/features/account_setting/providers/account_setting_provider.dart';
import '../widgets/shared/register_webview.dart' as web_html;
import '../widgets/widgets.dart';

part 'routes/account_settings_routes.dart';
part 'routes/admin_routes.dart';
part 'routes/data_consent_route.dart';
part 'routes/explore_tab_routes.dart';
part 'routes/home_tab_routes.dart';
part 'routes/inbox_tab_routes.dart';
part 'routes/invite_to_connect_route.dart';
part 'routes/profile_tab_routes.dart';
part 'routes/reset_password_route.dart';
part 'routes/root_route.dart';
part 'routes/sign_in_route.dart';
part 'routes/sign_up_routes.dart';
part 'routes/training_routes.dart';
part 'routes/welcome_route.dart';

class AppRouter {
  static GoRouter createRouter(BuildContext context) {
    return GoRouter(
      initialLocation: AppRoutes.root.path,
      navigatorKey: navigatorKey,
      redirect: (BuildContext context, GoRouterState state) async {
        final localDataModel = context.read<LocalDataModel>();
        final userProvider = context.read<UserProvider>();
        final onboardingModel = context.read<OnboardingModel>();

        final currentPath = state.uri.path;

        checkDomainForGroup(onboardingModel, state);
        bool setUserLocale = false;

        if (localDataModel.userId.isNotEmpty && userProvider.myUser == null) {
          await userProvider.loadUser();
          setUserLocale = true;
        }

        if (userProvider.myUser != null) {
          if (userProvider.isOnboardingCompleted(onboardingModel) &&
              (localDataModel.previousRouteOfLoggedInUser ?? '').isNotEmpty) {
            setUserLocale = false;
            if (context.mounted) AppUtility.setLocaleValues(context, userProvider.myUser);
            if (context.mounted) fetchContentProviderData(context);
            return await _handleLoggedInUsersRouting(
              state,
              userProvider,
              localDataModel.previousRouteOfLoggedInUser,
            );
          }
          return null;
        }

        // Allow navigation to pre authentication route(routes for unauthenticated users)
        if (_isPreAuthRoute(currentPath)) {
          // could recieve trackId signup route.(/signup?t=<trackId>)
          String trackId = state.uri.queryParameters[RouteParams.trackId] ?? '';
          final groupIdent = state.uri.queryParameters[RouteParams.groupIdent];

          if (trackId.isNotEmpty) {
            localDataModel.trackId = trackId;
          }

          if (context.mounted) {
            final onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
            if (groupIdent != null) {
              onboardingModel.setGroupIdentName(groupIdent);
            }

            if (!onboardingModel.isRegularUser) {
              // Fetch group
              final groupProvider = Provider.of<GroupProvider>(context, listen: false);
              final localeModel = Provider.of<LocaleModel>(context, listen: false);
              final group = await groupProvider.getGroup(
                groupIdent ?? onboardingModel.groupIdent.name,
              );

              if (group?.languageTextId != null) {
                setUserLocale = false;
                localeModel.set(Locale(group?.languageTextId ?? ''));
                if (context.mounted) {
                  Provider.of<ContentProvider>(context, listen: false).clear();
                }
              }
              return null;
            }
          }

          if (setUserLocale && context.mounted) {
            AppUtility.setLocaleValues(context, userProvider.myUser);
          }

          if (context.mounted) fetchContentProviderData(context);

          if (trackId.isNotEmpty || groupIdent != null) {
            return null;
          }

          final allowedSignupPaths = [
            AppRoutes.signupCreateAccount.path,
            AppRoutes.iqlaaSignup.path,
            AppRoutes.ebrdSignup.path,
            AppRoutes.ebrdKmfSignup.path,
            AppRoutes.ebrdHamkorBankSignup.path,
            AppRoutes.striveIndonesiaSignup.path,
          ];

          if (localDataModel.userId.isEmpty &&
              (!allowedSignupPaths.contains(currentPath) &&
                  currentPath.startsWith(AppRoutes.signup.path))) {
            return AppRoutes.signup.path;
          }
          return null;
        }

        if (localDataModel.userId.isEmpty &&
            currentPath != AppRoutes.welcome.path &&
            currentPath != AppRoutes.root.path) {
          return AppRoutes.welcome.path;
        }

        AnalyticService.pageTracking(currentPath);
        return null;
      },
      routes: [
        root,
        welcome,
        dataConsent,
        signIn,
        iqlaaSignInRoute,
        ebrdSignInRoute,
        ebrdKmfSignInRoute,
        ebrdHamkorBankSignInRoute,
        striveIndonesiaSignInRoute,
        signUpShellRoute,
        resetPasswordShellRoute,
        _createNavigationShellRoute(context),
        ...adminRoutes,
      ],
      observers: [AnalyticService.observer()],
      errorBuilder: (BuildContext context, GoRouterState state) {
        return const ErrorScreen();
      },
    );
  }

  static popUntilRoot(BuildContext context) {
    final router = GoRouter.of(context);
    while (router.canPop()) {
      router.pop();
    }
  }

  static String? getSecondLastSubroutePath(BuildContext context) {
    var routeMatchList = getRoutesList(context);
    if (routeMatchList.length - 2 < 0) return null;
    return routeMatchList[routeMatchList.length - 2];
  }

  static String? getLastSubroutePath(BuildContext context) {
    return getRoutesList(context).lastOrNull;
  }

  static List getRoutesList(BuildContext context) {
    final routesList = GoRouter.of(context).routerDelegate.currentConfiguration.matches;

    var routeMatchList = [];
    for (var element in routesList) {
      if (element is ShellRouteMatch) {
        for (var e in element.matches) {
          routeMatchList.add(e.matchedLocation);
        }
      } else {
        routeMatchList.add(element.matchedLocation);
      }
    }
    return routeMatchList;
  }

  static String? getLastRoutePath(BuildContext context) {
    return GoRouter.of(context).routerDelegate.currentConfiguration.matches.last.matchedLocation;
  }
}

_createNavigationShellRoute(BuildContext context) {
  return ShellRoute(
    observers: [Provider.of<RouteObserver<PageRoute>>(context)],
    pageBuilder: (BuildContext context, GoRouterState state, child) {
      return MaterialPage(key: state.pageKey, maintainState: true, child: AppWrapper(child: child));
    },
    redirect: (BuildContext context, GoRouterState state) async {
      final localDataModel = context.read<LocalDataModel>();
      localDataModel.previousRouteOfLoggedInUser = state.uri.path;
      return null;
    },
    routes: [
      home,
      explore,
      exploreFilters,
      ...inboxRoutes,
      ...editProfileRoutes,
      inviteToConnect,
      accountSettingsRoutes,
      ...trainingRoutes,
      webView,
    ],
  );
}

_handleLoggedInUsersRouting(
  GoRouterState state,
  UserProvider userProvider,
  String? previousRouteOfLoggedInUser,
) async {
  final unauthorizedUsersRoutes = [
    AppRoutes.signin.path,
    AppRoutes.welcome.path,
    AppRoutes.signup.path,
    AppRoutes.resetpassword.path,
    AppRoutes.dataUseConsent.path,
  ];

  if (unauthorizedUsersRoutes.any((path) => state.uri.path.startsWith(path))) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      GoRouter.of(navigatorKey.currentContext!).refresh();
    });
    if (navigatorKey.currentContext != null) {
      final wantsToLogout = await showDialog<bool>(
        context: navigatorKey.currentContext!,
        builder: (dialogContext) {
          return LogoutDialog(
            isDesktop: AppUtility.displayDesktopUI(dialogContext),
            onConfirm: () async {
              await userProvider.signOutUser(dialogContext, pathName: state.fullPath);
              if (dialogContext.mounted) {
                Navigator.of(dialogContext).pop(true);
              }
            },
          );
        },
      );

      return wantsToLogout == true ? null : previousRouteOfLoggedInUser;
    } else {
      return previousRouteOfLoggedInUser;
    }
  }
  return null;
}

bool _isPreAuthRoute(String path) {
  return path.contains(AppRoutes.signin.path) ||
      path.startsWith(AppRoutes.signup.path) ||
      path.contains(AppRoutes.resetpassword.path) ||
      path.contains(AppRoutes.dataUseConsent.path);
}

checkDomainForGroup(OnboardingModel onboardingModel, GoRouterState state) {
  final domainName = kIsWeb ? web_html.getDomainName() : state.uri.host;
  final path = state.fullPath ?? '';
  final queryParameters = state.uri.queryParameters;
  if (queryParameters.containsKey(RouteParams.groupIdent)) {
    return;
  }
  if (!path.contains(AppRoutes.signup.path) && !path.contains(AppRoutes.signin.path)) {
    return;
  }
  if ((path.contains(AppRoutes.signup.path) && path != AppRoutes.signup.path) ||
      path.contains(AppRoutes.signin.path) && path != AppRoutes.signin.path) {
    return;
  }
  if (domainName.contains(GroupIdent.iqlaa.name)) {
    onboardingModel.setGroupIdent(GroupIdent.iqlaa);
  } else if (domainName.contains('ebrd')) {
    onboardingModel.setGroupIdent(GroupIdent.ebrdCentralAsia);
  } else if (domainName.contains(GroupIdent.kmf.name)) {
    onboardingModel.setGroupIdent(GroupIdent.kmf);
  } else if (domainName.contains(GroupIdent.hamkor.name)) {
    onboardingModel.setGroupIdent(GroupIdent.hamkor);
  } else if (domainName.contains('striveid')) {
    onboardingModel.setGroupIdent(GroupIdent.striveIndonesia);
  } else if (domainName.contains('mastercard')) {
    onboardingModel.setGroupIdent(GroupIdent.mastercard);
  } else if (!domainName.contains('dev') &&
      !domainName.contains('app') &&
      !domainName.contains('localhost')) {
    final hostName = domainName.split('.').firstOrNull;
    if (hostName != null && hostName.isNotEmpty) onboardingModel.setGroupIdentName(hostName);
  }
}

fetchContentProviderData(BuildContext context) async {
  final contentProvider = Provider.of<ContentProvider>(context, listen: false);
  if (contentProvider.isFetchingData || contentProvider.countryOptions?.isEmpty == false) return;
  final localeModel = Provider.of<LocaleModel>(context, listen: false);
  await contentProvider.findAllOptionsByType(
    fetchPolicy: FetchPolicy.networkOnly,
    fallbackUiLanguage: Enum$UiLanguage.values.byName(localeModel.getCurrentLanguageCode()),
  );
}
