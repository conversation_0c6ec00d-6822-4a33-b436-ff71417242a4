part of '../app_router.dart';

var adminRoutes = [admin, adminTask, impersonate];

GoRoute admin = GoRoute(
  path: AppRoutes.admin.path,
  name: AppRoutes.admin.name,
  builder: (context, state) {
    return const AdminTaskScreen();
  },
);

GoRoute adminTask = GoRoute(
  path: AppRoutes.adminTask.path,
  name: AppRoutes.adminTask.name,
  builder: (context, state) {
    return const AdminTaskScreen();
  },
);

GoRoute impersonate = GoRoute(
  path: AppRoutes.impersonate.path,
  name: AppRoutes.impersonate.name,
  builder: (context, state) {
    final String userId = state.pathParameters[RouteParams.userId] ?? '';
    return ImpersonateScreen(userId: userId);
  },
);
