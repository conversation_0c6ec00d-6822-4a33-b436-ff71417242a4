part of '../app_router.dart';

var inboxRoutes = [
  inboxChats,
  inboxChatsChannelId,
  inboxInvitesReceived,
  inboxReceivedInvitationDetails,
  inboxInvitesSent,
  inboxSentInvitationDetails,
  inboxArchived,
  channelScreen,
  newInviteReceivedWeb,
  inviteSentWeb,
  inboxAcceptInvitationRoute,
];

GoRoute inboxChats = GoRoute(
  path: AppRoutes.inboxChats.path,
  name: AppRoutes.inboxChats.name,
  redirect: (BuildContext context, GoRouterState state) {
    //Set the selected navigationIcon (Explore) when launching the screen using routes in the web application.
    if (kIsWeb) {
      AppUtility.setNavigationIconSelection(context, Tabs.inbox);
    }
    return null;
  },
  builder: (context, state) {
    return AppUtility.displayDesktopUI(context, isChatScreen: true)
        ? const InboxWebScreensWrapper()
        : const InboxScreensWrapper();
  },
);

GoRoute newInviteReceivedWeb = GoRoute(
  path: AppRoutes.newInviteReceivedWeb.path,
  name: AppRoutes.newInviteReceivedWeb.name,
  builder: (context, state) {
    String? receivedInvitationId;
    if (state.extra != null) {
      if (state.extra is String) receivedInvitationId = state.extra as String;
    }
    return AppUtility.displayDesktopUI(context, isChatScreen: true)
        ? InboxWebScreensWrapper(
          selectedInviteId: receivedInvitationId,
          selectedInboxMenu: InboxDrawersMenu.invites,
          inviteTab: InviteTab.received,
        )
        : InboxInvitationDetailScreen(
          channelInvitationId: receivedInvitationId!,
          invitationDirection: MessageDirection.received,
        );
  },
);

GoRoute inviteSentWeb = GoRoute(
  path: AppRoutes.inviteSentWeb.path,
  name: AppRoutes.inviteSentWeb.name,
  builder: (context, state) {
    String? sentInvitationId;
    if (state.extra != null) {
      if (state.extra is String) sentInvitationId = state.extra as String;
    }
    return AppUtility.displayDesktopUI(context, isChatScreen: true)
        ? InboxWebScreensWrapper(
          selectedInviteId: sentInvitationId,
          selectedInboxMenu: InboxDrawersMenu.invites,
          inviteTab: InviteTab.sent,
        )
        : InboxInvitationDetailScreen(
          channelInvitationId: sentInvitationId!,
          invitationDirection: MessageDirection.sent,
        );
  },
);

GoRoute inboxChatsChannelId = GoRoute(
  path: AppRoutes.inboxChatsChannelId.path,
  name: AppRoutes.inboxChatsChannelId.name,
  onExit: (context, _) {
    final scaffoldModel = Provider.of<ScaffoldModel>(context, listen: false);
    if (AppRouter.getLastRoutePath(context) == AppRoutes.home.path &&
        scaffoldModel.selectedTabIndex != Tabs.home.index) {
      scaffoldModel.setParams(index: Tabs.home.index);
    }
    return true;
  },
  builder: (context, state) {
    final String channelId = state.pathParameters[RouteParams.channelId] ?? '';
    return AppUtility.displayDesktopUI(context, isChatScreen: true)
        ? InboxWebScreensWrapper(channelId: channelId)
        : ChannelScreen(channelId: channelId, isArchivedForUser: false);
  },
);

GoRoute inboxInvitesReceived = GoRoute(
  path: AppRoutes.inboxInvitesReceived.path,
  name: AppRoutes.inboxInvitesReceived.name,
  builder: (context, state) {
    return const InboxInvitesReceivedScreen();
  },
);

GoRoute inboxReceivedInvitationDetails = GoRoute(
  path: AppRoutes.inboxInvitesReceivedId.path,
  name: AppRoutes.inboxInvitesReceivedId.name,
  builder: (context, state) {
    final channelInvitationId = state.pathParameters[RouteParams.channelInvitationId] ?? '';
    return AppUtility.displayDesktopUI(context, isChatScreen: true)
        ? InboxWebScreensWrapper(
          selectedInviteId: channelInvitationId,
          inviteTab: InviteTab.received,
        )
        : InboxInvitationDetailScreen(
          channelInvitationId: channelInvitationId,
          invitationDirection: MessageDirection.received,
        );
  },
);

GoRoute inboxAcceptInvitationRoute = GoRoute(
  path: AppRoutes.inboxAcceptInvitation.path,
  name: AppRoutes.inboxAcceptInvitation.name,
  builder: (context, state) {
    final channelInvitationId = state.pathParameters[RouteParams.channelInvitationId] ?? '';
    return AppUtility.displayDesktopUI(context, isChatScreen: true)
        ? InboxWebScreensWrapper(
          selectedInviteId: channelInvitationId,
          inviteTab: InviteTab.received,
          autoAccept: true,
        )
        : InboxInvitationDetailScreen(
          channelInvitationId: channelInvitationId,
          invitationDirection: MessageDirection.received,
          autoAccept: true,
        );
  },
);

GoRoute inboxInvitesSent = GoRoute(
  path: AppRoutes.inboxInvitesSent.path,
  name: AppRoutes.inboxInvitesSent.name,
  builder: (context, state) {
    return const InboxInvitesSentScreen();
  },
);

GoRoute inboxSentInvitationDetails = GoRoute(
  path: AppRoutes.inboxInvitesSentId.path,
  name: AppRoutes.inboxInvitesSentId.name,
  builder: (context, state) {
    final String channelInvitationId = state.pathParameters[RouteParams.channelInvitationId] ?? '';
    return AppUtility.displayDesktopUI(context, isChatScreen: true)
        ? InboxWebScreensWrapper(
          selectedInviteId: channelInvitationId,
          selectedInboxMenu: InboxDrawersMenu.invites,
          inviteTab: InviteTab.sent,
        )
        : InboxInvitationDetailScreen(
          channelInvitationId: channelInvitationId,
          invitationDirection: MessageDirection.sent,
        );
  },
);

GoRoute inboxArchived = GoRoute(
  path: AppRoutes.inboxArchived.path,
  name: AppRoutes.inboxArchived.name,
  builder: (context, state) {
    return const InboxChatListScreen(isArchivedForUser: true);
  },
);

GoRoute channelScreen = GoRoute(
  path: AppRoutes.inboxArchivedChannelId.path,
  name: AppRoutes.inboxArchivedChannelId.name,
  builder: (context, state) {
    final String channelId = state.pathParameters[RouteParams.channelId] ?? '';
    return AppUtility.displayDesktopUI(context, isChatScreen: true)
        ? InboxWebScreensWrapper(
          channelId: channelId,
          selectedInboxMenu: InboxDrawersMenu.archivedChats,
        )
        : ChannelScreen(channelId: channelId, isArchivedForUser: true);
  },
);
