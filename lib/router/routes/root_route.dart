part of '../app_router.dart';

GoRoute root = GoRoute(
  path: AppRoutes.root.path,
  name: AppRoutes.root.name,
  builder: (context, state) {
    AppUtility.setTrackId(context, state);
    final channelId = state.uri.queryParameters[RouteParams.channelId];
    return SplashScreen(
      nextRouteName: state.uri.queryParameters[RouteParams.nextRouteName] ?? AppRoutes.home.name,
      pathParameters: (channelId == null) ? {} : {RouteParams.channelId: channelId},
    );
  },
);
