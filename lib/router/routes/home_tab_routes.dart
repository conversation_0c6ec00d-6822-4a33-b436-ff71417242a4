part of '../app_router.dart';

GoRoute home = GoRoute(
  path: AppRoutes.home.path,
  name: AppRoutes.home.name,
  redirect: (BuildContext context, GoRouterState state) {
    //Set the selected navigationIcon (Home) when launching the screen using routes in the web application.
    if (kIsWeb) {
      AppUtility.setNavigationIconSelection(context, Tabs.home);
    }
    return null;
  },
  builder: (context, state) {
    return const HomeScreen();
  },
);

GoRoute webView = GoRoute(
  path: AppRoutes.webView.path,
  name: AppRoutes.webView.name,
  builder: (context, state) {
    final String targetUrl = state.pathParameters[RouteParams.targetUrl] ?? '';
    final String webviewTitle = state.pathParameters[RouteParams.webviewTitle] ?? '';
    if (kIsWeb) {
      return WebEmbedWidget(targetUrl: targetUrl, webviewTitle: webviewTitle);
      // just show the url and title for now, testing
      // return Scaffold(
      //   appBar: AppBar(
      //     title: Text("Web: " + webviewTitle),
      //   ),
      //   body: Center(
      //     child: Column(
      //       mainAxisAlignment: MainAxisAlignment.center,
      //       children: [
      //         Text('URL: $targetUrl'),
      //         Text('Title: $webviewTitle'),
      //       ],
      //     ),
      //   ),
      // );
    } else {
      return MobileEmbedWidget(targetUrl: targetUrl, webviewTitle: webviewTitle);
      // just show the url and title for now, testing
      // return Scaffold(
      //   appBar: AppBar(
      //     title: Text("Mobile: " + webviewTitle),
      //   ),
      //   body: Center(
      //     child: Column(
      //       mainAxisAlignment: MainAxisAlignment.center,
      //       children: [
      //         Text('URL: $targetUrl'),
      //         Text('Title: $webviewTitle'),
      //       ],
      //     ),
      //   ),
      // );
    }
  },
);
