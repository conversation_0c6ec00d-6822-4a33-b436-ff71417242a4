part of '../app_router.dart';

GoRoute signIn = GoRoute(
  path: AppRoutes.signin.path,
  name: AppRoutes.signin.name,
  builder: (context, state) {
    return SignInScreen(nextRouteName: AppRoutes.home.name);
  },
);

GoRoute iqlaaSignInRoute = GoRoute(
  path: AppRoutes.iqlaaSignin.path,
  name: AppRoutes.iqlaaSignin.name,
  builder: (context, state) {
    Provider.of<OnboardingModel>(context, listen: false).setGroupIdent(GroupIdent.iqlaa);
    return SignInScreen(nextRouteName: AppRoutes.home.name);
  },
);

GoRoute ebrdSignInRoute = GoRoute(
  path: AppRoutes.ebrdSignIn.path,
  name: AppRoutes.ebrdSignIn.name,
  builder: (context, state) {
    // NOTE: the URL is /ebrd, but the groupIdent is ebrdCentralAsia. This differs from most routes.
    Provider.of<OnboardingModel>(context, listen: false).setGroupIdent(GroupIdent.ebrdCentralAsia);
    return SignInScreen(nextRouteName: AppRoutes.home.name);
  },
);

GoRoute ebrdKmfSignInRoute = GoRoute(
  path: AppRoutes.ebrdKmfSignIn.path,
  name: AppRoutes.ebrdKmfSignIn.name,
  builder: (context, state) {
    Provider.of<OnboardingModel>(context, listen: false).setGroupIdent(GroupIdent.kmf);
    return SignInScreen(nextRouteName: AppRoutes.home.name);
  },
);

GoRoute ebrdHamkorBankSignInRoute = GoRoute(
  path: AppRoutes.ebrdHamkorBankSignIn.path,
  name: AppRoutes.ebrdHamkorBankSignIn.name,
  builder: (context, state) {
    Provider.of<OnboardingModel>(context, listen: false).setGroupIdent(GroupIdent.hamkor);
    return SignInScreen(nextRouteName: AppRoutes.home.name);
  },
);
GoRoute striveIndonesiaSignInRoute = GoRoute(
  path: AppRoutes.striveIndonesiaSignIn.path,
  name: AppRoutes.striveIndonesiaSignIn.name,
  builder: (context, state) {
    Provider.of<OnboardingModel>(context, listen: false).setGroupIdent(GroupIdent.striveIndonesia);
    return SignInScreen(nextRouteName: AppRoutes.home.name);
  },
);
