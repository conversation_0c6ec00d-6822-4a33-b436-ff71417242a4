part of '../app_router.dart';

final accountSettingsRoutes = ShellRoute(
  builder: (c, state, child) {
    return ChangeNotifierProvider(
      create: (context) {
        final provider = AccountSettingsProvider(
          userProvider: c.read<UserProvider>(),
          contentProvider: c.read<ContentProvider>(),
        );
        provider.addUserProviderListener();
        return provider;
      },
      child: child,
    );
  },
  routes: [
    accountSettings,
    accountSettingAppLanguage,
    accountSettingBirthYear,
    accountSettingEmail,
    accountSettingGender,
    accountSettingName,
    accountSettingPassword,
    accountSettingPhone,
    accountSettingRole,
    accountSettingViewBlockedUsers,
    accountSettingViewNotification,
  ],
);

GoRoute accountSettings = GoRoute(
  path: AppRoutes.accountSettings.path,
  name: AppRoutes.accountSettings.name,
  builder: (context, state) {
    return const AccountSetting();
  },
);

GoRoute accountSettingAppLanguage = GoRoute(
  path: AppRoutes.accountSettingAppLanguage.path,
  name: AppRoutes.accountSettingAppLanguage.name,
  builder: (context, state) {
    return (AppUtility.displayDesktopUI(context))
        ? const AccountSetting(editScreenOption: EditAccountSettingOptions.appLanguage)
        : const EditAccountSettingAppLanguage();
  },
);

GoRoute accountSettingBirthYear = GoRoute(
  path: AppRoutes.accountSettingBirthYear.path,
  name: AppRoutes.accountSettingBirthYear.name,
  builder: (context, state) {
    return (AppUtility.displayDesktopUI(context))
        ? const AccountSetting(editScreenOption: EditAccountSettingOptions.birthYear)
        : const EditAccountSettingBirthYear();
  },
);

GoRoute accountSettingEmail = GoRoute(
  path: AppRoutes.accountSettingEmail.path,
  name: AppRoutes.accountSettingEmail.name,
  builder: (context, state) {
    return (AppUtility.displayDesktopUI(context))
        ? const AccountSetting(editScreenOption: EditAccountSettingOptions.emailAddress)
        : const EditAccountSettingEmail();
  },
);

GoRoute accountSettingGender = GoRoute(
  path: AppRoutes.accountSettingGender.path,
  name: AppRoutes.accountSettingGender.name,
  builder: (context, state) {
    return (AppUtility.displayDesktopUI(context))
        ? const AccountSetting(editScreenOption: EditAccountSettingOptions.gender)
        : const EditAccountSettingGender();
  },
);

GoRoute accountSettingName = GoRoute(
  path: AppRoutes.accountSettingName.path,
  name: AppRoutes.accountSettingName.name,
  builder: (context, state) {
    return (AppUtility.displayDesktopUI(context))
        ? const AccountSetting(editScreenOption: EditAccountSettingOptions.name)
        : const EditAccountSettingName();
  },
);

GoRoute accountSettingPassword = GoRoute(
  path: AppRoutes.accountSettingPassword.path,
  name: AppRoutes.accountSettingPassword.name,
  builder: (context, state) {
    return (AppUtility.displayDesktopUI(context))
        ? const AccountSetting(editScreenOption: EditAccountSettingOptions.passwordAndSecurity)
        : const EditAccountSettingPassword();
  },
);

GoRoute accountSettingPhone = GoRoute(
  path: AppRoutes.accountSettingPhone.path,
  name: AppRoutes.accountSettingPhone.name,
  builder: (context, state) {
    return (AppUtility.displayDesktopUI(context))
        ? const AccountSetting(editScreenOption: EditAccountSettingOptions.phoneNumber)
        : const EditAccountSettingPhone();
  },
);

GoRoute accountSettingViewBlockedUsers = GoRoute(
  path: AppRoutes.accountSettingViewBlockedUsers.path,
  name: AppRoutes.accountSettingViewBlockedUsers.name,
  builder: (context, state) {
    return (AppUtility.displayDesktopUI(context))
        ? const AccountSetting(editScreenOption: EditAccountSettingOptions.blockedUsers)
        : const EditAccountSettingViewBlockedUsers();
  },
);

GoRoute accountSettingViewNotification = GoRoute(
  path: AppRoutes.accountSettingViewNotification.path,
  name: AppRoutes.accountSettingViewNotification.name,
  builder: (context, state) {
    return (AppUtility.displayDesktopUI(context))
        ? const AccountSetting(editScreenOption: EditAccountSettingOptions.notifications)
        : const EditAccountSettingViewNotificationPermission();
  },
);

GoRoute accountSettingRole = GoRoute(
  path: AppRoutes.accountSettingRole.path,
  name: AppRoutes.accountSettingRole.name,
  builder: (context, state) {
    return (AppUtility.displayDesktopUI(context))
        ? const AccountSetting(editScreenOption: EditAccountSettingOptions.profileRole)
        : const EditAccountSettingRole();
  },
);
