part of '../app_router.dart';

GoRoute explore = GoRoute(
  path: AppRoutes.explore.path,
  name: AppRoutes.explore.name,
  redirect: (BuildContext context, GoRouterState state) {
    //Set the selected navigationIcon (Explore) when launching the screen using routes in the web application.
    if (kIsWeb) {
      AppUtility.setNavigationIconSelection(context, Tabs.explore);
    }
    return null;
  },
  builder: (context, state) {
    return const ExploreScreen();
  },
);

GoRoute exploreFilters = GoRoute(
  path: AppRoutes.exploreFilters.path,
  name: AppRoutes.exploreFilters.name,
  builder: (context, state) {
    return const ExploreFiltersScreen();
  },
);
