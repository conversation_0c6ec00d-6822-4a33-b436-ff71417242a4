part of '../app_router.dart';

var signUpShellRoute = ShellRoute(
  pageBuilder: (context, state, child) {
    return MaterialPage(key: state.pageKey, maintainState: true, child: child);
  },
  routes: [
    signUpBirthYear,
    signUpCreateAccount,
    signUpNotification,
    signUpExpertise,
    signUpLanding,
    signUpGender,
    signUpLanguage,
    signUpLocation,
    signUpMentor,
    signUpPhoneNumber,
    signUpProfileRole,
    signUpReason,
    signUpTerms,
    signUpVentureName,
    signupVentureYear,
    signUpVentureStage,
    iqlaaSignUpRoute,
    iqlaaConsentPage,
    iqlaaNumberOfEmployees,
    iqlaaHomeBasedBusiness,
    iqlaaNationalityPage,
    signUpIndustries,
    iqlaaBusinessRegistration,
    ebrdSignUpRoute,
    ebrdKmfSignupRoute,
    ebrdHamkorBankSignupRoute,
    striveIndonesiaSignupRoute,
    signUpMastercardBankStage,
    signUpBusinessChallengeRoute,
    signUpHowCanMentorSupportMeRoute,
    signUpHowICanHelpMenteesRoute,
  ],
);

GoRoute signUpBirthYear = GoRoute(
  path: AppRoutes.signupBirthYear.path,
  name: AppRoutes.signupBirthYear.name,
  builder: (context, state) {
    return const SignUpBirthYearScreen();
  },
);

GoRoute signUpCreateAccount = GoRoute(
  path: AppRoutes.signupCreateAccount.path,
  name: AppRoutes.signupCreateAccount.name,
  builder: (context, state) {
    return const SignUpCreateAccountScreen();
  },
);

GoRoute iqlaaSignUpRoute = GoRoute(
  path: AppRoutes.iqlaaSignup.path,
  name: AppRoutes.iqlaaSignup.name,
  builder: (context, state) {
    Provider.of<OnboardingModel>(context, listen: false).setGroupIdent(GroupIdent.iqlaa);
    return const SignUpMethodScreen();
  },
);

GoRoute iqlaaConsentPage = GoRoute(
  path: AppRoutes.iqlaaConsentPage.path,
  name: AppRoutes.iqlaaConsentPage.name,
  builder: (context, state) {
    return const IqlaaConsentScreen();
  },
);

GoRoute iqlaaNationalityPage = GoRoute(
  path: AppRoutes.iqlaaNationalityPage.path,
  name: AppRoutes.iqlaaNationalityPage.name,
  builder: (context, state) {
    return const IqlaaNationalityScreen();
  },
);

GoRoute iqlaaHomeBasedBusiness = GoRoute(
  path: AppRoutes.iqlaaHomeBasedBusiness.path,
  name: AppRoutes.iqlaaHomeBasedBusiness.name,
  builder: (context, state) {
    return const IqlaaHomeBaseBusinessScreen();
  },
);

GoRoute iqlaaBusinessRegistration = GoRoute(
  path: AppRoutes.iqlaaBusinessRegistration.path,
  name: AppRoutes.iqlaaBusinessRegistration.name,
  builder: (context, state) {
    return const IqlaaBusinessRegistrationScreen();
  },
);

GoRoute iqlaaNumberOfEmployees = GoRoute(
  path: AppRoutes.iqlaaEmployeeNumber.path,
  name: AppRoutes.iqlaaEmployeeNumber.name,
  builder: (context, state) {
    return const IqlaaEmployeeNumberScreen();
  },
);

GoRoute signUpIndustries = GoRoute(
  path: AppRoutes.signUpIndustries.path,
  name: AppRoutes.signUpIndustries.name,
  builder: (context, state) {
    return const SignUpIndustriesScreen();
  },
);

GoRoute signUpNotification = GoRoute(
  path: AppRoutes.signupNotifications.path,
  name: AppRoutes.signupNotifications.name,
  builder: (context, state) {
    return const SignUpNotificationScreen();
  },
);

GoRoute signUpExpertise = GoRoute(
  path: AppRoutes.signupExpertises.path,
  name: AppRoutes.signupExpertises.name,
  builder: (context, state) {
    return const SignUpExpertisesScreen();
  },
);

GoRoute signUpGender = GoRoute(
  path: AppRoutes.signupGender.path,
  name: AppRoutes.signupGender.name,
  builder: (context, state) {
    return const SignUpGenderScreen();
  },
);

GoRoute signUpLanding = GoRoute(
  path: AppRoutes.signup.path,
  name: AppRoutes.signup.name,
  onExit: (context, state) {
    if (!kIsWeb) return true;
    // For group users as we launch directly on signup screen don't need to check past routes.
    if (!Provider.of<OnboardingModel>(context, listen: false).isRegularUser) return true;
    // This code is to handle browser's back button to show exit dialog to exit signup flow.
    if (AppRouter.getLastRoutePath(context) == AppRoutes.signin.path ||
        AppRouter.getLastRoutePath(context) == AppRoutes.root.path) {
      return true;
    }
    if (AppRouter.getSecondLastSubroutePath(context) == AppRoutes.signin.path ||
        AppRouter.getSecondLastSubroutePath(context) == AppRoutes.signup.path) {
      AppUtility.onboardingExitDialog(context);
      return false;
    }
    return true;
  },
  builder: (context, state) {
    return const SignUpMethodScreen();
  },
);

GoRoute signUpLanguage = GoRoute(
  path: AppRoutes.signupLanguage.path,
  name: AppRoutes.signupLanguage.name,
  builder: (context, state) {
    return const SignupLanguageScreen();
  },
);

GoRoute signUpLocation = GoRoute(
  path: AppRoutes.signupLocation.path,
  name: AppRoutes.signupLocation.name,
  builder: (context, state) {
    return const SignupLocationScreen();
  },
);

GoRoute signUpMentor = GoRoute(
  path: AppRoutes.signupMentorInfo.path,
  name: AppRoutes.signupMentorInfo.name,
  builder: (context, state) {
    return const SignupMentorInfoScreen();
  },
);

GoRoute signUpPhoneNumber = GoRoute(
  path: AppRoutes.signupPhoneNumber.path,
  name: AppRoutes.signupPhoneNumber.name,
  builder: (context, state) {
    return const SignUpPhoneScreen();
  },
);

GoRoute signUpProfileRole = GoRoute(
  path: AppRoutes.signUpProfileRole.path,
  name: AppRoutes.signUpProfileRole.name,
  builder: (context, state) {
    return const SignUpProfileRoleScreen();
  },
);

GoRoute signUpReason = GoRoute(
  path: AppRoutes.signupReasonToJoin.path,
  name: AppRoutes.signupReasonToJoin.name,
  builder: (context, state) {
    return const SignUpReasonScreen();
  },
);

GoRoute signUpTerms = GoRoute(
  path: AppRoutes.signupAcceptTerms.path,
  name: AppRoutes.signupAcceptTerms.name,
  builder: (context, state) {
    return const SignUpAcceptTermsScreen();
  },
);

GoRoute signUpVentureName = GoRoute(
  path: AppRoutes.signupVentureName.path,
  name: AppRoutes.signupVentureName.name,
  builder: (context, state) {
    return const SignUpVentureNameScreen();
  },
);

GoRoute signupVentureYear = GoRoute(
  path: AppRoutes.signupVentureYear.path,
  name: AppRoutes.signupVentureYear.name,
  builder: (context, state) {
    return const StriveIndonesiaSignupVentureStartDateScreen();
  },
);

GoRoute signUpVentureStage = GoRoute(
  path: AppRoutes.signupVentureStage.path,
  name: AppRoutes.signupVentureStage.name,
  builder: (context, state) {
    return const SignUpVentureStageScreen();
  },
);

GoRoute signUpMastercardBankStage = GoRoute(
  path: AppRoutes.signupMastercardBankStage.path,
  name: AppRoutes.signupMastercardBankStage.name,
  builder: (context, state) {
    return const SignUpBankDetailsScreen();
  },
);

GoRoute ebrdSignUpRoute = GoRoute(
  path: AppRoutes.ebrdSignup.path,
  name: AppRoutes.ebrdSignup.name,
  builder: (context, state) {
    Provider.of<OnboardingModel>(context, listen: false).setGroupIdent(GroupIdent.ebrdCentralAsia);
    return const SignUpMethodScreen();
  },
);

GoRoute ebrdKmfSignupRoute = GoRoute(
  path: AppRoutes.ebrdKmfSignup.path,
  name: AppRoutes.ebrdKmfSignup.name,
  builder: (context, state) {
    Provider.of<OnboardingModel>(context, listen: false).setGroupIdent(GroupIdent.kmf);
    return const SignUpMethodScreen();
  },
);

GoRoute ebrdHamkorBankSignupRoute = GoRoute(
  path: AppRoutes.ebrdHamkorBankSignup.path,
  name: AppRoutes.ebrdHamkorBankSignup.name,
  builder: (context, state) {
    Provider.of<OnboardingModel>(context, listen: false).setGroupIdent(GroupIdent.hamkor);
    return const SignUpMethodScreen();
  },
);

GoRoute striveIndonesiaSignupRoute = GoRoute(
  path: AppRoutes.striveIndonesiaSignup.path,
  name: AppRoutes.striveIndonesiaSignup.name,
  builder: (context, state) {
    Provider.of<OnboardingModel>(context, listen: false).setGroupIdent(GroupIdent.striveIndonesia);
    return const SignUpMethodScreen();
  },
);

GoRoute signUpBusinessChallengeRoute = GoRoute(
  path: AppRoutes.signUpBusinessChallenge.path,
  name: AppRoutes.signUpBusinessChallenge.name,
  builder: (context, state) {
    return const SignUpBusinessChallengeScreen();
  },
);

GoRoute signUpHowCanMentorSupportMeRoute = GoRoute(
  path: AppRoutes.signUpHowCanMentorSupportMe.path,
  name: AppRoutes.signUpHowCanMentorSupportMe.name,
  builder: (context, state) {
    return const SignUpHowCanMentorSupportMeScreen();
  },
);

GoRoute signUpHowICanHelpMenteesRoute = GoRoute(
  path: AppRoutes.signUpHowICanHelpMentees.path,
  name: AppRoutes.signUpHowICanHelpMentees.name,
  builder: (context, state) {
    return const SignUpHowICanHelpMenteesScreen();
  },
);
