part of '../app_router.dart';

var resetPasswordShellRoute = ShellRoute(
  pageBuilder: (BuildContext context, GoRouterState state, child) {
    return MaterialPage(key: state.pageKey, maintainState: true, child: child);
  },
  routes: [
    resetpasswordEnterEmail,
    resetPasswordEnterCode,
    resetPasswordEnterNewPassword,
    resetPasswordCompleted,
  ],
);

GoRoute resetpasswordEnterEmail = GoRoute(
  path: AppRoutes.resetpassword.path,
  name: AppRoutes.resetpassword.name,
  builder: (context, state) {
    return ResetPasswordEnterEmailScreen(enteredEmail: state.extra as String?);
  },
);

GoRoute resetPasswordEnterCode = GoRoute(
  path: AppRoutes.resetPasswordEnterCode.path,
  name: AppRoutes.resetPasswordEnterCode.name,
  builder: (context, state) {
    return ResetPasswordEnterCodeScreen(actionId: state.extra as String);
  },
);

GoRoute resetPasswordEnterNewPassword = GoRoute(
  path: AppRoutes.resetPasswordEnterNewPassword.path,
  name: AppRoutes.resetPasswordEnterNewPassword.name,
  builder: (context, state) {
    return ResetPasswordEnterNewPasswordScreen(
      actionId: (state.extra as Map<String, dynamic>)['actionId'],
      email: (state.extra as Map<String, dynamic>)['email'],
    );
  },
);

GoRoute resetPasswordCompleted = GoRoute(
  path: AppRoutes.resetPasswordCompleted.path,
  name: AppRoutes.resetPasswordCompleted.name,
  builder: (context, state) {
    return const ResetPasswordCompletedScreen();
  },
);
