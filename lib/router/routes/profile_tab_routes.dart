part of '../app_router.dart';

var editProfileRoutes = [
  profile,
  profileEdit,
  profileEditPronouns,
  profileEditLinkedin,
  profileEditCurrentLocation,
  profileEditOriginLocation,
  profileEditLanguagePreferred,
  profileEditLanguageOthers,
  profileEditEducation,
  profileAddEducation,
  profileEditExperience,
  profileAddExperience,
  profileEditExpertisesTop,
  profileEditExpertisesAdditional,
  profileEditIndustries,
  profileEditMentoringPreferences,
  profileEditReasonsForMentoring,
  profileEditHowICanHelpMentees,
  profileEditCompanyStage,
  profileEditCompanyName,
  profileEditCompanyWebsite,
  profileEditCompanyLocation,
  profileEditCompanyMission,
  profileEditCompanyReason,
  profileEditBusinessChallenge,
  profileEditHowCanMentorSupportMe,
  profileScreen,
];

GoRoute profile = GoRoute(
  path: AppRoutes.profile.path,
  name: AppRoutes.profile.name,
  builder: (context, state) {
    return const ProfileScreen();
  },
);

GoRoute profileEdit = GoRoute(
  path: AppRoutes.profileEdit.path,
  name: AppRoutes.profileEdit.name,
  builder: (context, state) {
    return const EditProfileScreen();
  },
);

GoRoute profileEditPronouns = GoRoute(
  path: AppRoutes.profileEditPronouns.path,
  name: AppRoutes.profileEditPronouns.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.pronouns);
    }
    return const EditProfilePronounsScreen();
  },
);

GoRoute profileEditLinkedin = GoRoute(
  path: AppRoutes.profileEditLinkedin.path,
  name: AppRoutes.profileEditLinkedin.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.linkedin);
    }
    return const EditConnectLinkedInScreen();
  },
);

GoRoute profileEditCurrentLocation = GoRoute(
  path: AppRoutes.profileEditCurrentLocation.path,
  name: AppRoutes.profileEditCurrentLocation.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.country);
    }
    return const EditCurrentLocationScreen();
  },
);

GoRoute profileEditOriginLocation = GoRoute(
  path: AppRoutes.profileEditOriginLocation.path,
  name: AppRoutes.profileEditOriginLocation.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.originLocation);
    }
    return const EditOriginLocationScreen();
  },
);

GoRoute profileEditLanguagePreferred = GoRoute(
  path: AppRoutes.profileEditLanguagePreferred.path,
  name: AppRoutes.profileEditLanguagePreferred.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.prefferedLanguages);
    }
    return const EditPreferredLanguageScreen();
  },
);

GoRoute profileEditLanguageOthers = GoRoute(
  path: AppRoutes.profileEditLanguageOthers.path,
  name: AppRoutes.profileEditLanguageOthers.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.otherLanguages);
    }
    return const EditOtherLanguagesScreen();
  },
);

GoRoute profileEditEducation = GoRoute(
  path: AppRoutes.profileEditEducationIndex.path,
  name: AppRoutes.profileEditEducationIndex.name,
  builder: (context, state) {
    final int experienceIndex = int.parse(state.pathParameters[RouteParams.experienceIndex]!);
    if (AppUtility.displayDesktopUI(context)) {
      return EditProfileScreen(
        editOption: EditProfileOptions.editEducation,
        educationExperienceIndex: experienceIndex,
      );
    }
    return EditEducationScreen(experienceIndex: experienceIndex);
  },
);

GoRoute profileAddEducation = GoRoute(
  path: AppRoutes.profileEditEducationNew.path,
  name: AppRoutes.profileEditEducationNew.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.newEducation);
    }
    return const EditEducationScreen();
  },
);

GoRoute profileEditExperience = GoRoute(
  path: AppRoutes.profileEditExperienceIndex.path,
  name: AppRoutes.profileEditExperienceIndex.name,
  builder: (context, state) {
    final int experienceIndex = int.parse(state.pathParameters[RouteParams.experienceIndex]!);
    if (AppUtility.displayDesktopUI(context)) {
      return EditProfileScreen(
        editOption: EditProfileOptions.editExperience,
        educationExperienceIndex: experienceIndex,
      );
    }
    return EditExperienceScreen(experienceIndex: experienceIndex);
  },
);

GoRoute profileAddExperience = GoRoute(
  path: AppRoutes.profileEditExperienceNew.path,
  name: AppRoutes.profileEditExperienceNew.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.newExperience);
    }
    return const EditExperienceScreen();
  },
);

GoRoute profileEditExpertisesTop = GoRoute(
  path: AppRoutes.profileEditExpertisesTop.path,
  name: AppRoutes.profileEditExpertisesTop.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.expertiseTop);
    }
    return const EditExpertisesScreen(isTopExpertises: true);
  },
);

GoRoute profileEditExpertisesAdditional = GoRoute(
  path: AppRoutes.profileEditExpertisesAdditional.path,
  name: AppRoutes.profileEditExpertisesAdditional.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.expertiseAdditional);
    }
    return const EditExpertisesScreen(isTopExpertises: false);
  },
);

GoRoute profileEditIndustries = GoRoute(
  path: AppRoutes.profileEditIndustries.path,
  name: AppRoutes.profileEditIndustries.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.industryName);
    }
    return const EditIndustriesScreen();
  },
);

GoRoute profileEditMentoringPreferences = GoRoute(
  path: AppRoutes.profileEditMentoringPreferences.path,
  name: AppRoutes.profileEditMentoringPreferences.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.mentoringPreference);
    }
    return const EditMentoringPreferencesScreen();
  },
);

GoRoute profileEditReasonsForMentoring = GoRoute(
  path: AppRoutes.profileEditReasonsForMentoring.path,
  name: AppRoutes.profileEditReasonsForMentoring.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.mentoringReason);
    }
    return const EditReasonsForMentoringScreen();
  },
);

GoRoute profileEditHowICanHelpMentees = GoRoute(
  path: AppRoutes.profileEditHowICanHelpMentees.path,
  name: AppRoutes.profileEditHowICanHelpMentees.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.howICanHelpMentees);
    }
    return const EditHowICanHelpMenteesScreen();
  },
);

GoRoute profileEditCompanyStage = GoRoute(
  path: AppRoutes.profileEditCompanyStage.path,
  name: AppRoutes.profileEditCompanyStage.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.companyStage);
    }
    return const EditCompanyStageScreen();
  },
);

GoRoute profileEditCompanyName = GoRoute(
  path: AppRoutes.profileEditCompanyName.path,
  name: AppRoutes.profileEditCompanyName.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.companyName);
    }
    return const EditCompanyNameScreen();
  },
);

GoRoute profileEditCompanyWebsite = GoRoute(
  path: AppRoutes.profileEditCompanyWebsite.path,
  name: AppRoutes.profileEditCompanyWebsite.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.companyWebsite);
    }
    return const EditCompanyWebsiteScreen();
  },
);

GoRoute profileEditCompanyLocation = GoRoute(
  path: AppRoutes.profileEditCompanyLocation.path,
  name: AppRoutes.profileEditCompanyLocation.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.companyLocation);
    }
    return const EditCompanyLocationScreen();
  },
);

GoRoute profileEditCompanyMission = GoRoute(
  path: AppRoutes.profileEditCompanyMission.path,
  name: AppRoutes.profileEditCompanyMission.name,
  builder: (context, state) {
    return const EditCompanyMissionScreen();
  },
);

GoRoute profileEditCompanyReason = GoRoute(
  path: AppRoutes.profileEditCompanyReason.path,
  name: AppRoutes.profileEditCompanyReason.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.menteeBusinessReason);
    }
    return const EditCompanyReasonScreen();
  },
);

GoRoute profileEditBusinessChallenge = GoRoute(
  path: AppRoutes.profileEditBusinessChallenge.path,
  name: AppRoutes.profileEditBusinessChallenge.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.businessChallenge);
    }
    return const EditBusinessChallengeScreen();
  },
);

GoRoute profileEditHowCanMentorSupportMe = GoRoute(
  path: AppRoutes.profileEditHowCanMentorSupportMe.path,
  name: AppRoutes.profileEditHowCanMentorSupportMe.name,
  builder: (context, state) {
    if (AppUtility.displayDesktopUI(context)) {
      return const EditProfileScreen(editOption: EditProfileOptions.howCanMentorSupportMe);
    }
    return const EditHowCanMentorSupportMeScreen();
  },
);

GoRoute profileScreen = GoRoute(
  path: AppRoutes.profileId.path,
  name: AppRoutes.profileId.name,
  builder: (context, state) {
    final String userId = state.pathParameters[RouteParams.userId] ?? '';
    return ProfileScreen(userId: userId);
  },
);
