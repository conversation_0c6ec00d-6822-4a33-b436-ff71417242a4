part of '../app_router.dart';

var trainingRoutes = [trainings, trainingSeries, individualTraining];

GoRoute trainings = GoRoute(
  path: AppRoutes.trainings.path,
  name: AppRoutes.trainings.name,
  redirect: (BuildContext context, GoRouterState state) {
    if (kIsWeb) {
      AppUtility.setNavigationIconSelection(context, Tabs.trainings);
    }
    return null;
  },
  builder: (context, state) {
    return AppUtility.displayDesktopUI(context)
        ? const TrainingsLandingWebScreen()
        : const TrainingsLandingScreen();
  },
);

GoRoute trainingSeries = GoRoute(
  path: AppRoutes.trainingSeries.path,
  name: AppRoutes.trainingSeries.name,
  builder: (context, state) {
    final String trainingSeriesId = state.pathParameters[RouteParams.trainingSeriesId] ?? '';

    return TrainingSeriesLandingScreen(trainingSeriesId: trainingSeriesId);
  },
);

GoRoute individualTraining = GoRoute(
  path: AppRoutes.individualTraining.path,
  name: AppRoutes.individualTraining.name,
  builder: (context, state) {
    final String trainingId = state.pathParameters[RouteParams.individualTrainingId] ?? '';
    return IndividualTrainingLandingScreen(trainingId: trainingId);
  },
);
