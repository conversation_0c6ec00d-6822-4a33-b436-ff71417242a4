import 'dart:ui';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:phone_numbers_parser/phone_numbers_parser.dart';
import 'package:mm_flutter_app/utilities/utility.dart';

class ThemeValidator extends ThemeExtension<ThemeValidator> {
  final int minimumAge;
  final bool requireUniqueEmailAddresses;
  final bool requireUniquePhoneNumbers;

  const ThemeValidator({
    required this.minimumAge,
    required this.requireUniqueEmailAddresses,
    required this.requireUniquePhoneNumbers,
  });

  static const defaultThemeValidator = ThemeValidator(
    minimumAge: 18,
    requireUniqueEmailAddresses: true,
    requireUniquePhoneNumbers: false,
  );

  @override
  ThemeExtension<ThemeValidator> copyWith({
    int? minimumAge,
    bool? requireUniqueEmailAddresses,
    bool? requireUniquePhoneNumbers,
  }) {
    return ThemeValidator(
      minimumAge: minimumAge ?? this.minimumAge,
      requireUniqueEmailAddresses:
          requireUniqueEmailAddresses == true || requireUniqueEmailAddresses == false
              ? requireUniqueEmailAddresses!
              : this.requireUniqueEmailAddresses,
      requireUniquePhoneNumbers:
          requireUniquePhoneNumbers == true || requireUniquePhoneNumbers == false
              ? requireUniquePhoneNumbers!
              : this.requireUniquePhoneNumbers,
    );
  }

  @override
  ThemeExtension<ThemeValidator> lerp(covariant ThemeExtension<ThemeValidator>? other, double t) {
    if (other is! ThemeValidator) {
      return this;
    }
    return ThemeValidator(
      minimumAge: lerpDouble(minimumAge, other.minimumAge, t)?.round() ?? 0,
      requireUniqueEmailAddresses: requireUniqueEmailAddresses,
      requireUniquePhoneNumbers: requireUniquePhoneNumbers,
    );
  }

  // ------------------------------------------------------------------------------
  // Validators:
  String? validate2faCode(
    String? value, {
    bool? matched,
    String? errorMessage,
    String? noMatchErrorMessage,
  }) {
    if (value?.isEmpty == true) {
      return null;
    }

    String pattern = r'^[0-9]{6}$';
    RegExp regExp = RegExp(pattern);
    if (!regExp.hasMatch(value!)) {
      return errorMessage ?? AppLocale.current.validationIncorrect2faCode;
    }

    if (matched == false) {
      return noMatchErrorMessage ?? AppLocale.current.validation2faCodeNoMatch;
    }
    return null;
  }

  String? validateBirthYear(String? value, {String? errorMessage}) {
    if (value?.isNotEmpty != true) {
      return null;
    }
    if (value != null && value.length < 4) {
      return errorMessage ?? AppLocale.current.validationBirthYearInvalid;
    }
    try {
      final enteredYear = int.parse(value!);
      final int age = DateTime.now().year - enteredYear;
      if (age < 18 || age > 110) {
        return errorMessage ?? AppLocale.current.validationBirthYearInvalidAge;
      }
    } catch (_) {
      return errorMessage ?? AppLocale.current.validationBirthYearInvalid;
    }
    return null;
  }

  String? validateBirthDate(String? value, {String? errorMessage}) {
    if (value?.isNotEmpty != true) {
      return null;
    }
    if (value != null && value.length < 2) {
      return errorMessage ?? AppLocale.current.validationBirthDateInvalid;
    }
    try {
      final enteredDate = int.parse(value!);
      if (enteredDate <= 0 || enteredDate > 31) {
        return errorMessage ?? AppLocale.current.validationBirthDateInvalid;
      }
    } catch (_) {
      return errorMessage ?? AppLocale.current.validationBirthYearInvalid;
    }
    return null;
  }

  String? validateBirthMonth(String? value, {String? errorMessage}) {
    if (value?.isNotEmpty != true) {
      return null;
    }
    if (value != null && value.length < 2) {
      return errorMessage ?? AppLocale.current.validationBirthMonthInvalid;
    }
    try {
      final enteredMonth = int.parse(value!);
      if (enteredMonth <= 0 || enteredMonth > 12) {
        return errorMessage ?? AppLocale.current.validationBirthMonthInvalid;
      }
    } catch (_) {
      return errorMessage ?? AppLocale.current.validationBirthYearInvalid;
    }
    return null;
  }

  String? validateEmail(
    String? value, {
    String? errorMessage,
    String? notAvailableErrorMessage,
    bool? isAvailable,
  }) {
    if (value?.isNotEmpty != true) {
      return errorMessage ?? AppLocale.current.validationEmailEmpty;
    }
    if (!EmailValidator.validate(value!)) {
      return errorMessage ?? AppLocale.current.validationEmailInvalid;
    }
    if (requireUniqueEmailAddresses && isAvailable == false) {
      return notAvailableErrorMessage ?? AppLocale.current.validationEmailNotAvailable;
    }

    return null;
  }

  String? validateName(String? value, {String? errorMessage, bool isFullNameValidation = false}) {
    if (value == null || value.trim().isEmpty) {
      return errorMessage ?? AppLocale.current.validationFullNameEmpty;
    }

    if (isFullNameValidation) {
      // Check that there is a minimum of 2 words:
      List<String> words = (value.trim()).split(' ');
      if (words.length < 2) {
        return errorMessage ?? AppLocale.current.validationFullNameLastNameMissing;
      }
    }

    return null;
  }

  String? validatePassword(
    String? value, {
    String? matchValue,
    String? errorMessage,
    String? errorMessageForNoMatch,
    int minLength = 8,
    // MM2/Django had no max length for password. We'll set it to 99 for MM3.
    int maxLength = 99,
  }) {
    if (value?.isNotEmpty != true) {
      return null;
    }
    if (value!.length < minLength) {
      return errorMessage ?? AppLocale.current.validationPasswordInvalid;
    }
    if (value.length > maxLength) {
      return errorMessage ?? AppLocale.current.validationPasswordInvalidTooLong;
    }
    if (matchValue?.isNotEmpty == true && value != matchValue) {
      return errorMessageForNoMatch ?? AppLocale.current.validationPasswordNoMatch;
    }
    return null;
  }

  String? validatePhoneNumber(String? countryCode, String? phoneNumber, {String? errorMessage}) {
    if (countryCode?.trim().isEmpty == true || phoneNumber?.trim().isEmpty == true) {
      return errorMessage ?? AppLocale.current.validationPhoneNumberInvalid;
    }

    try {
      final contactNumber = AppUtility.formatPhoneNumber(countryCode, phoneNumber);
      if (!PhoneNumber.parse(contactNumber).isValid()) {
        return errorMessage ?? AppLocale.current.validationPhoneNumberInvalid;
      }
    } catch (_) {
      return errorMessage ?? AppLocale.current.validationPhoneNumberInvalid;
    }

    return null;
  }

  // see https://www.appsloveworld.com/flutter/100/3/dart-flutter-validating-a-string-for-url
  String? validateUri(String? value, {String? errorMessage, String? host}) {
    if (value?.isNotEmpty != true) {
      return null;
    }
    final sanitizedValue = AppUtility.sanitizeUri(value!);

    final uri = Uri.tryParse(sanitizedValue);
    if (uri == null || !uri.hasAbsolutePath || !uri.scheme.startsWith('https')) {
      return errorMessage ?? AppLocale.current.validationUriInvalid;
    }

    if (host?.isNotEmpty == true && uri.host.toLowerCase() != host?.toLowerCase()) {
      return errorMessage ?? AppLocale.current.validationUriInvalid;
    }

    // var pattern = r'^((?:.|\n)*?)((http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)([-a-z0-9.]+)(/[-a-z0-9+&@#/%=~_|!:,.;]*)?(\?[a-z0-9+&@#/%=~_|!:‌​,.;]*)?)';
    // RegExp regExp = RegExp(pattern);
    // if (!regExp.hasMatch(sanitizedValue)) {
    //   return errorMessage ?? _l10n?.validationUriInvalid;
    // }

    return null;
  }

  String? validateLinkedinUri(String? value, {String? errorMessage, String? host}) {
    if (value?.isNotEmpty != true) {
      return null;
    }

    final sanitizedValue = AppUtility.sanitizeUri(value!);
    final uri = Uri.tryParse(sanitizedValue);

    if (uri == null || !uri.hasAbsolutePath || !uri.scheme.startsWith('https')) {
      return errorMessage ?? AppLocale.current.validationUriInvalid;
    }

    if (host?.isNotEmpty == true && uri.host.toLowerCase() != host?.toLowerCase()) {
      return errorMessage ?? AppLocale.current.validationUriInvalid;
    }

    final linkedInRegExp = RegExp(
      r'https?:\/\/(www\.)?linkedin\.com\/(in|pub|company|school)\/[A-z0-9_-]+\/?$',
    );

    if (!linkedInRegExp.hasMatch(sanitizedValue)) {
      return errorMessage ?? AppLocale.current.validationUriInvalid;
    }

    return null;
  }

  String? validateUrl(String? value) {
    if (value?.trim().isEmpty == true) return null;

    RegExp regex = RegExp(
      r'^(https?://)?((www\.[a-zA-Z0-9-]+\.([a-zA-Z0-9-]+\.)*[a-z]{2,})|((?!www\.)[a-zA-Z0-9-]+\.[a-z]{2,}))(?:/)?$',
      caseSensitive: false,
      multiLine: false,
    );
    if (!regex.hasMatch(value ?? '')) {
      return AppLocale.current.validationUrlInvalid;
    }
    return null;
  }

  String? validateYear(
    String? value, {
    String? notBefore,
    String? notAfter,
    String? notBeforeErrorMessage,
    String? notAfterErrorMessage,
    String? notBeforeAndAfterErrorMessage,
    String? maxRange,
  }) {
    if (value?.isNotEmpty != true) {
      return null;
    }
    final RegExp yearRegExp = RegExp(r'^\d{4}$');
    if (value != null && !yearRegExp.hasMatch(value)) {
      return AppLocale.current.validationYear;
    }

    if (value?.isNotEmpty == true) {
      final enteredYear = int.tryParse(value!);
      final currentYear = DateTime.now().year;
      if (enteredYear != null && (enteredYear > currentYear || currentYear - enteredYear > 100)) {
        return AppLocale.current.validationYearInvalidRange;
      }
    }

    if (value?.isNotEmpty == true &&
        (notBefore?.isNotEmpty == true || notAfter?.isNotEmpty == true)) {
      final enteredYear = int.parse(value!);
      if (notBefore?.isNotEmpty == true && enteredYear < int.parse(notBefore!)) {
        return notBeforeErrorMessage ??
            notBeforeAndAfterErrorMessage ??
            AppLocale.current.validationYearCompare;
      }
      if (notAfter?.isNotEmpty == true && enteredYear > int.parse(notAfter!)) {
        return notAfterErrorMessage ??
            notBeforeAndAfterErrorMessage ??
            AppLocale.current.validationYearCompare;
      }

      if (maxRange?.isNotEmpty == true) {
        final yearToCompare = int.tryParse(notBefore ?? '') ?? int.tryParse(notAfter ?? '');
        if (yearToCompare != null &&
            (enteredYear - yearToCompare).abs() > (int.tryParse(maxRange!) ?? 100)) {
          return AppLocale.current.validationYearInvalidRange;
        }
      }
    }
    return null;
  }

  String? validateDate(DateTime? dateTimeValue) {
    if (dateTimeValue == null) return null;

    DateTime currentDate = DateTime.now();
    if (dateTimeValue.isAfter(currentDate)) {
      return AppLocale.current.validationDateInvalidRange;
    }
    return null;
  }
}
