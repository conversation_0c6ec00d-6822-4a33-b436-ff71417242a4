import 'package:flutter/material.dart';

import '../../../../services/extensions.dart';

class CustomStyles extends ThemeExtension<CustomStyles> {
  final Size largeButtonMinSize;
  final Size largeButtonMaxSize;

  const CustomStyles({required this.largeButtonMinSize, required this.largeButtonMaxSize});

  static const defaultCustomStyles = CustomStyles(
    largeButtonMinSize: Size(180.0, 48.0),
    largeButtonMaxSize: Size(288.0, 48.0),
  );

  @override
  ThemeExtension<CustomStyles> copyWith({Size? largeButtonMinSize, Size? largeButtonMaxSize}) {
    return CustomStyles(
      largeButtonMinSize: largeButtonMinSize ?? this.largeButtonMinSize,
      largeButtonMaxSize: largeButtonMaxSize ?? this.largeButtonMaxSize,
    );
  }

  @override
  ThemeExtension<CustomStyles> lerp(covariant ThemeExtension<CustomStyles>? other, double t) {
    if (other is! CustomStyles) {
      return this;
    }
    return CustomStyles(
      largeButtonMinSize: Size.lerp(largeButtonMinSize, other.largeButtonMinSize, t)!,
      largeButtonMaxSize: Size.lerp(largeButtonMaxSize, other.largeButtonMaxSize, t)!,
    );
  }

  // ------------------------------------------------------------------------
  // Styles:
  ButtonStyle primaryRoundedRectangleButton(BuildContext context) {
    final dimensions = context.theme.d;
    return ElevatedButton.styleFrom(
      backgroundColor: context.theme.colorScheme.primary,
      minimumSize: context.theme.d.buttonSizeSmall,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(dimensions.roundedRectRadiusXxLarge),
      ),
    );
  }

  ButtonStyle primaryTextButton(BuildContext context) {
    return TextButton.styleFrom(
      elevation: context.theme.d.elevationLevel0,
      minimumSize: context.theme.d.buttonSizeSmall,
    );
  }

  ButtonStyle primaryOutlineButton(BuildContext context) {
    return OutlinedButton.styleFrom(minimumSize: context.theme.d.buttonSizeSmall);
  }

  ButtonStyle secondaryRoundedRectangleButton(BuildContext context) {
    return ElevatedButton.styleFrom(
      minimumSize: largeButtonMinSize,
      maximumSize: largeButtonMaxSize,
      backgroundColor: context.theme.colorScheme.secondaryContainer,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusSmall),
      ),
    );
  }

  ButtonStyle secondaryRoundedRectangleButtonLarge(BuildContext context) {
    return ElevatedButton.styleFrom(
      minimumSize: largeButtonMinSize,
      maximumSize: largeButtonMaxSize,
      backgroundColor: context.theme.colorScheme.secondaryContainer,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
      ),
    );
  }

  ButtonStyle primaryRoundedRectangleButtonLarge(BuildContext context) {
    return ElevatedButton.styleFrom(
      minimumSize: largeButtonMinSize,
      maximumSize: largeButtonMaxSize,
      backgroundColor: context.theme.colorScheme.inversePrimary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
      ),
    );
  }
}
