import 'package:flutter/material.dart';

class AppFeatures extends ThemeExtension<AppFeatures> {
  final bool appLanguage;
  final bool cityOfOrigin;
  final bool cityOfResidence;
  final bool countryOfOrigin;
  final bool countryOfResidence;
  final bool currentRoleToggle;
  final bool darkTheme;
  final bool notificationPermission;
  final bool showOnlyEmailPushNotificationPreference;
  final bool regionOfOrigin;
  final bool regionOfResidence;
  final bool switchProfileRole;

  const AppFeatures({
    required this.appLanguage,
    required this.cityOfOrigin,
    required this.cityOfResidence,
    required this.countryOfOrigin,
    required this.countryOfResidence,
    required this.currentRoleToggle,
    required this.darkTheme,
    required this.notificationPermission,
    required this.showOnlyEmailPushNotificationPreference,
    required this.regionOfOrigin,
    required this.regionOfResidence,
    required this.switchProfileRole,
  });

  static const defaultAppFeatures = AppFeatures(
    appLanguage: true,
    cityOfOrigin: false,
    cityOfResidence: false,
    countryOfOrigin: false,
    countryOfResidence: true,
    currentRoleToggle: false,
    darkTheme: false,
    notificationPermission: true,
    showOnlyEmailPushNotificationPreference: true,
    regionOfOrigin: false,
    regionOfResidence: false,
    switchProfileRole: false,
  );

  @override
  ThemeExtension<AppFeatures> copyWith({
    bool? appLanguage,
    bool? businessWebsiteLink,
    bool? cityOfOrigin,
    bool? cityOfResidence,
    bool? countryOfOrigin,
    bool? countryOfResidence,
    bool? currentRoleToggle,
    bool? darkTheme,
    bool? notificationPermission,
    bool? showOnlyEmailPushNotificationPreference,
    bool? regionOfOrigin,
    bool? regionOfResidence,
    bool? switchProfileRole,
    bool? userLocation,
  }) {
    return AppFeatures(
      appLanguage: appLanguage ?? this.appLanguage,
      cityOfOrigin: cityOfOrigin ?? this.cityOfOrigin,
      cityOfResidence: cityOfResidence ?? this.cityOfResidence,
      countryOfOrigin: countryOfOrigin ?? this.countryOfOrigin,
      countryOfResidence: countryOfResidence ?? this.countryOfResidence,
      currentRoleToggle: currentRoleToggle ?? this.currentRoleToggle,
      darkTheme: darkTheme ?? this.darkTheme,
      notificationPermission: notificationPermission ?? this.notificationPermission,
      showOnlyEmailPushNotificationPreference:
          showOnlyEmailPushNotificationPreference ?? this.showOnlyEmailPushNotificationPreference,
      regionOfOrigin: regionOfOrigin ?? this.regionOfOrigin,
      regionOfResidence: regionOfResidence ?? this.regionOfResidence,
      switchProfileRole: switchProfileRole ?? this.switchProfileRole,
    );
  }

  @override
  ThemeExtension<AppFeatures> lerp(covariant ThemeExtension<AppFeatures>? other, double t) {
    if (other is! AppFeatures) {
      return this;
    }
    return AppFeatures(
      appLanguage: appLanguage,
      cityOfOrigin: cityOfOrigin,
      cityOfResidence: cityOfResidence,
      countryOfOrigin: countryOfOrigin,
      countryOfResidence: countryOfResidence,
      currentRoleToggle: currentRoleToggle,
      darkTheme: darkTheme,
      notificationPermission: notificationPermission,
      showOnlyEmailPushNotificationPreference: showOnlyEmailPushNotificationPreference,
      regionOfOrigin: regionOfOrigin,
      regionOfResidence: regionOfResidence,
      switchProfileRole: switchProfileRole,
    );
  }
}
