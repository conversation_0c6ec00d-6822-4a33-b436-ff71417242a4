import 'dart:ui';

import 'package:flutter/material.dart';

class Dimensions extends ThemeExtension<Dimensions> {
  final double layoutBreakpointCompactWidthMax;
  final double layoutBreakpointMediumWidthMin;
  final double layoutBreakpointMediumWidthMax;
  final double layoutBreakpointExpandedWidthMin;
  final double layoutBreakpointCompactHeightMax;
  final double layoutBreakpointMediumHeightMin;
  final double layoutBreakpointMediumHeightMax;
  final double layoutBreakpointExpandedHeightMin;

  final double appEdgePaddingCompact;
  final double appEdgePaddingMedium;
  final double appEdgePaddingExpanded;

  final double paddingXxSmall;
  final double paddingXSmall;
  final double paddingSmall;
  final double chipPadding;
  final double paddingMedium;
  final double paddingSmallMedium;
  final double paddingLarge;
  final double paddingXLarge;
  final double padding15;

  final double profilePhotoRadiusSmall;
  final double profilePhotoRadiusMedium;
  final double profilePhotoRadiusLarge;

  final double roundedRectRadiusXSmall;
  final double roundedRectRadiusSmall;
  final double roundedRectRadiusMedium;
  final double roundedRectRadiusLarge;
  final double roundedRectRadiusXLarge;
  final double roundedRectRadiusXxLarge;

  final double elevationLevel0;
  final double elevationLevel1;
  final double elevationLevel2;
  final double elevationLevel3;
  final double elevationLevel4;
  final double elevationLevel5;

  final double borderWidthXSmall;
  final double borderWidthSmall;
  final double borderWidthRegular;
  final double borderWidthMedium;

  final double drawerHeaderHeight;
  final double dropdownHeight;
  final double highlightBorderWidth;
  final double customToolbarHeight;
  final Size buttonSizeSmall;
  final Size buttonSizeMedium;
  final Size buttonSizeLarge;
  final Size buttonSizeXLarge;
  final Size socialSigninButtonSize;
  final Size loadingWidgetSize;

  final Size imageGalleryTileSize;
  final Size imageGalleryImageSize;

  final double fontSizeXSmall;
  final double fontSizeSmall;
  final double fontSizeMedium;
  final double fontSizeMedium18;
  final double fontSizeMediumLarge;
  final double fontSizeLarge;
  final double fontSizeXLarge;

  final double iconSizeXSmall;
  final double iconSizeSmall;
  final double iconSizeMedium;
  final double iconSizeLarge;
  final double iconSizeXLarge;
  final double iconSizeXxLarge;

  final double imageSizeSmall;
  final double imageSizeMedium;
  final double imageSizeLarge;
  final double imageSizeXLarge;

  final double zero;
  final double boxSizeSmall;
  final double boxSizeMedium;
  final double boxSizeMediumLarge;
  final double boxSizeLarge;
  final double boxSizeXLarge;
  final double boxSizeXXLarge;
  final double boxSizeXxLarge;
  final double recommandedCardHeightMin;
  final double welcomePreviewSlide;
  final double messageMenuWidth;
  final double messageChannelMinWidth;
  final double messageChannelMaxWidth;
  final double countryCodeWidth;

  final double lineHeightX;
  final double boxWidth;
  final double boxHeight;
  final double imageRadius;
  final double popupYOffset;
  final double homeToolbarHeight;
  final double radioButtonPadding;

  final double sheetRadius;
  final double textFieldRadius;
  final double vacationDialogImageHeight;
  final double vacationDialogContentHeight;
  final double vacationDialogContentWidth;
  final Size welcomeCarouselIndicatorSize;

  final double maxDesktopAppWidth;
  final double desktopAppBreakpoint;
  final double desktopAppBreakpointChat;
  final double desktopAppBreakpointInviteTab;
  final double desktopAppBreakpointTrainingDashboard;
  final double desktopOnboardingExtraScrollingSpace;
  final double imageAspectRatioThreshold;
  final double carousalImageAspectRatio;
  final double dialogWidthWeb;
  final double dialogSmallWidthWeb;
  final double desktopTrainingCardHeight;

  final double mobileUserCardWidth;
  final Size webUserCardSize;
  final Size webArchiveMessageCardSize;

  final double webTrainingCardImageWidth;
  final Size trainingSourceSize;

  final double nestedListViewIndent;
  final double footerHeight;
  final double inviteToConnectDialogHeight;

  final double tabIconHeight;

  static const defaultDimensions = Dimensions(
    layoutBreakpointCompactWidthMax: 600.0,
    layoutBreakpointMediumWidthMin: 600.0,
    layoutBreakpointMediumWidthMax: 840.0,
    layoutBreakpointExpandedWidthMin: 840.0,
    layoutBreakpointCompactHeightMax: 480.0,
    layoutBreakpointMediumHeightMin: 480.0,
    layoutBreakpointMediumHeightMax: 900.0,
    layoutBreakpointExpandedHeightMin: 900.0,
    appEdgePaddingCompact: 16.0,
    appEdgePaddingMedium: 24.0,
    appEdgePaddingExpanded: 24.0,
    paddingXxSmall: 4.0,
    paddingXSmall: 6.0,
    paddingSmall: 8.0,
    chipPadding: 10.0,
    paddingMedium: 16.0,
    paddingSmallMedium: 12.0,
    paddingLarge: 24.0,
    paddingXLarge: 32.0,
    padding15: 15.0,
    profilePhotoRadiusSmall: 24.0,
    profilePhotoRadiusMedium: 40.0,
    profilePhotoRadiusLarge: 48.0,
    roundedRectRadiusXSmall: 4.0,
    roundedRectRadiusSmall: 8.0,
    roundedRectRadiusMedium: 12.0,
    roundedRectRadiusLarge: 16.0,
    roundedRectRadiusXLarge: 20.0,
    roundedRectRadiusXxLarge: 24.0,
    elevationLevel0: 0.0,
    elevationLevel1: 1.0,
    elevationLevel2: 3.0,
    elevationLevel3: 6.0,
    elevationLevel4: 8.0,
    elevationLevel5: 12.0,
    borderWidthXSmall: 0.2,
    borderWidthSmall: 0.4,
    borderWidthRegular: 1,
    borderWidthMedium: 1.5,
    drawerHeaderHeight: 64.0,
    dropdownHeight: 200.0,
    highlightBorderWidth: 2.0,
    customToolbarHeight: 80.0,
    buttonSizeSmall: Size(80.0, 40.0),
    buttonSizeMedium: Size(108.0, 44.0),
    buttonSizeLarge: Size(180.0, 50.0),
    buttonSizeXLarge: Size(180.0, 52.0),
    socialSigninButtonSize: Size(300, 56),
    loadingWidgetSize: Size(35, 35),
    imageGalleryTileSize: Size(88.0, 168.0),
    imageGalleryImageSize: Size(88.0, 72.0),
    fontSizeXSmall: 10.0,
    fontSizeSmall: 12.0,
    fontSizeMedium: 16.0,
    fontSizeMedium18: 18.0,
    fontSizeMediumLarge: 20.0,
    fontSizeLarge: 22.0,
    fontSizeXLarge: 24.0,

    //Icon Sizes
    iconSizeXSmall: 12.0,
    iconSizeSmall: 16.0,
    iconSizeMedium: 20.0,
    iconSizeLarge: 24.0,
    iconSizeXLarge: 28.0,
    iconSizeXxLarge: 32.0,

    //Image Sizes
    imageSizeSmall: 48.0,
    imageSizeMedium: 56.0,
    imageSizeLarge: 80.0,
    imageSizeXLarge: 96.0,

    //Box Sizes
    zero: 0.0,
    boxSizeSmall: 44.0,
    boxSizeMedium: 64.0,
    boxSizeMediumLarge: 68.0,
    boxSizeLarge: 88.0,
    boxSizeXLarge: 104.0,
    boxSizeXXLarge: 200.0,
    boxSizeXxLarge: 240.0,
    recommandedCardHeightMin: 252.0,
    welcomePreviewSlide: 320.0,
    mobileUserCardWidth: 360.0,
    messageMenuWidth: 128.0,
    messageChannelMinWidth: 120.0,
    messageChannelMaxWidth: 700.0,
    countryCodeWidth: 125.0,
    //Text Height
    lineHeightX: 1.5,
    boxWidth: 5,
    boxHeight: 14,
    imageRadius: 50,

    //Popup dialog y-axis offset
    popupYOffset: 40,
    homeToolbarHeight: 76,
    sheetRadius: 25,
    textFieldRadius: 30,
    radioButtonPadding: 56.0,
    vacationDialogImageHeight: 140.0,
    vacationDialogContentHeight: 120.0,
    vacationDialogContentWidth: 400.0,
    welcomeCarouselIndicatorSize: Size(12, 12),
    maxDesktopAppWidth: 710,
    desktopAppBreakpoint: 767,
    desktopAppBreakpointChat: 1025,
    desktopAppBreakpointInviteTab: 1150,
    desktopAppBreakpointTrainingDashboard: 1025,
    desktopOnboardingExtraScrollingSpace: 90,
    imageAspectRatioThreshold: 10,
    carousalImageAspectRatio: 2.6,
    dialogWidthWeb: 680,
    dialogSmallWidthWeb: 480,
    desktopTrainingCardHeight: 430,
    webUserCardSize: Size(400.0, 500.0),
    webArchiveMessageCardSize: Size(230.0, 50.0),
    webTrainingCardImageWidth: 424,
    trainingSourceSize: Size(424, 118),
    nestedListViewIndent: 16.0,
    footerHeight: 52.0,
    inviteToConnectDialogHeight: 450.0,
    tabIconHeight: 25.0,
  );

  const Dimensions({
    required this.layoutBreakpointCompactWidthMax,
    required this.layoutBreakpointMediumWidthMin,
    required this.layoutBreakpointMediumWidthMax,
    required this.layoutBreakpointExpandedWidthMin,
    required this.layoutBreakpointCompactHeightMax,
    required this.layoutBreakpointMediumHeightMin,
    required this.layoutBreakpointMediumHeightMax,
    required this.layoutBreakpointExpandedHeightMin,
    required this.appEdgePaddingCompact,
    required this.appEdgePaddingMedium,
    required this.appEdgePaddingExpanded,
    required this.paddingXxSmall,
    required this.paddingXSmall,
    required this.paddingSmall,
    required this.chipPadding,
    required this.paddingMedium,
    required this.paddingSmallMedium,
    required this.paddingLarge,
    required this.paddingXLarge,
    required this.padding15,
    required this.radioButtonPadding,
    required this.profilePhotoRadiusSmall,
    required this.profilePhotoRadiusMedium,
    required this.profilePhotoRadiusLarge,
    required this.roundedRectRadiusXSmall,
    required this.roundedRectRadiusSmall,
    required this.roundedRectRadiusMedium,
    required this.roundedRectRadiusLarge,
    required this.roundedRectRadiusXLarge,
    required this.roundedRectRadiusXxLarge,
    required this.elevationLevel0,
    required this.elevationLevel1,
    required this.elevationLevel2,
    required this.elevationLevel3,
    required this.elevationLevel4,
    required this.elevationLevel5,
    required this.borderWidthXSmall,
    required this.borderWidthSmall,
    required this.borderWidthRegular,
    required this.borderWidthMedium,
    required this.drawerHeaderHeight,
    required this.dropdownHeight,
    required this.highlightBorderWidth,
    required this.customToolbarHeight,
    required this.buttonSizeSmall,
    required this.buttonSizeMedium,
    required this.buttonSizeLarge,
    required this.buttonSizeXLarge,
    required this.socialSigninButtonSize,
    required this.loadingWidgetSize,
    required this.imageGalleryTileSize,
    required this.imageGalleryImageSize,
    required this.fontSizeXSmall,
    required this.fontSizeSmall,
    required this.fontSizeMedium,
    required this.fontSizeMedium18,
    required this.fontSizeMediumLarge,
    required this.fontSizeLarge,
    required this.fontSizeXLarge,
    required this.iconSizeXSmall,
    required this.iconSizeSmall,
    required this.iconSizeMedium,
    required this.iconSizeLarge,
    required this.iconSizeXLarge,
    required this.iconSizeXxLarge,
    required this.imageSizeSmall,
    required this.imageSizeMedium,
    required this.imageSizeLarge,
    required this.imageSizeXLarge,
    required this.zero,
    required this.boxSizeSmall,
    required this.boxSizeMedium,
    required this.boxSizeMediumLarge,
    required this.boxSizeLarge,
    required this.boxSizeXLarge,
    required this.boxSizeXXLarge,
    required this.boxSizeXxLarge,
    required this.recommandedCardHeightMin,
    required this.welcomePreviewSlide,
    required this.mobileUserCardWidth,
    required this.messageMenuWidth,
    required this.messageChannelMinWidth,
    required this.messageChannelMaxWidth,
    required this.countryCodeWidth,
    required this.lineHeightX,
    required this.boxWidth,
    required this.boxHeight,
    required this.imageRadius,
    required this.popupYOffset,
    required this.homeToolbarHeight,
    required this.sheetRadius,
    required this.textFieldRadius,
    required this.vacationDialogImageHeight,
    required this.vacationDialogContentHeight,
    required this.vacationDialogContentWidth,
    required this.welcomeCarouselIndicatorSize,
    required this.maxDesktopAppWidth,
    required this.desktopAppBreakpoint,
    required this.desktopAppBreakpointChat,
    required this.desktopAppBreakpointInviteTab,
    required this.desktopAppBreakpointTrainingDashboard,
    required this.desktopOnboardingExtraScrollingSpace,
    required this.imageAspectRatioThreshold,
    required this.carousalImageAspectRatio,
    required this.dialogWidthWeb,
    required this.dialogSmallWidthWeb,
    required this.desktopTrainingCardHeight,
    required this.webUserCardSize,
    required this.webArchiveMessageCardSize,
    required this.webTrainingCardImageWidth,
    required this.trainingSourceSize,
    required this.nestedListViewIndent,
    required this.footerHeight,
    required this.inviteToConnectDialogHeight,
    required this.tabIconHeight,
  });

  @override
  ThemeExtension<Dimensions> copyWith({
    double? layoutBreakpointCompactWidthMax,
    double? layoutBreakpointMediumWidthMin,
    double? layoutBreakpointMediumWidthMax,
    double? layoutBreakpointExpandedWidthMin,
    double? layoutBreakpointCompactHeightMax,
    double? layoutBreakpointMediumHeightMin,
    double? layoutBreakpointMediumHeightMax,
    double? layoutBreakpointExpandedHeightMin,
    double? appEdgePaddingCompact,
    double? appEdgePaddingMedium,
    double? appEdgePaddingExpanded,
    double? paddingXxSmall,
    double? paddingXSmall,
    double? paddingSmall,
    double? chipPadding,
    double? paddingMedium,
    double? paddingSmallMedium,
    double? paddingLarge,
    double? paddingXLarge,
    double? padding15,
    double? profilePhotoRadiusSmall,
    double? profilePhotoRadiusMedium,
    double? profilePhotoRadiusLarge,
    double? roundedRectRadiusXSmall,
    double? roundedRectRadiusSmall,
    double? roundedRectRadiusMedium,
    double? roundedRectRadiusLarge,
    double? roundedRectRadiusXLarge,
    double? roundedRectRadiusXxLarge,
    double? elevationLevel0,
    double? elevationLevel1,
    double? elevationLevel2,
    double? elevationLevel3,
    double? elevationLevel4,
    double? elevationLevel5,
    double? borderWidthXSmall,
    double? borderWidthSmall,
    double? borderWidthRegular,
    double? borderWidthMedium,
    double? drawerHeaderHeight,
    double? dropdownHeight,
    double? highlightBorderWidth,
    double? customToolbarHeight,
    double? buttonHeight,
    Size? buttonSizeSmall,
    Size? buttonSizeMedium,
    Size? buttonSizeLarge,
    Size? buttonSizeXLarge,
    Size? socialSigninButtonSize,
    Size? loadingWidgetSize,
    Size? imageGalleryTileSize,
    Size? imageGalleryImageSize,
    double? fontSizeXSmall,
    double? fontSizeSmall,
    double? fontSizeMedium,
    double? fontSizeMedium18,
    double? fontSizeMediumLarge,
    double? fontSizeLarge,
    double? fontSizeXLarge,
    double? iconSizeXSmall,
    double? iconSizeSmall,
    double? iconSizeMedium,
    double? iconSizeLarge,
    double? iconSizeXLarge,
    double? iconSizeXxLarge,
    double? imageSizeSmall,
    double? imageSizeMedium,
    double? imageSizeLarge,
    double? imageSizeXLarge,
    double? zero,
    double? boxSizeSmall,
    double? boxSizeMedium,
    double? boxSizeMediumLarge,
    double? boxSizeLarge,
    double? boxSizeXLarge,
    double? boxSizeXXLarge,
    double? boxSizeXxLarge,
    double? recommandedCardHeightMin,
    double? welcomePreviewSlide,
    double? mobileUserCardWidth,
    double? messageMenuWidth,
    double? messageChannelMinWidth,
    double? messageChannelMaxWidth,
    double? countryCodeWidth,
    double? lineHeightX,
    double? boxWidth,
    double? boxHeight,
    double? imageRadius,
    double? popupYOffset,
    double? homeToolbarHeight,
    double? sheetRadius,
    double? textFieldRadius,
    double? vacationDialogImageHeight,
    double? vacationDialogContentHeight,
    double? vacationDialogContentWidth,
    Size? welcomeCarouselIndicatorSize,
    double? radioButtonPadding,
    double? maxDesktopAppWidth,
    double? desktopAppBreakpoint,
    double? desktopAppBreakpointChat,
    double? desktopAppBreakpointInviteTab,
    double? desktopAppBreakpointTrainingDashboard,
    double? desktopOnboardingExtraScrollingSpace,
    double? imageAspectRatioThreshold,
    double? carousalImageAspectRatio,
    double? dialogWidthWeb,
    double? dialogSmallWidthWeb,
    double? desktopTrainingCardHeight,
    Size? webUserCardSize,
    Size? webArchiveMessageCardSize,
    double? webTrainingCardImageWidth,
    Size? trainingSourceSize,
    double? nestedListViewIndent,
    double? footerHeight,
    double? inviteToConnectDialogHeight,
    double? tabIconHeight,
  }) {
    return Dimensions(
      layoutBreakpointCompactWidthMax:
          layoutBreakpointCompactWidthMax ?? this.layoutBreakpointCompactWidthMax,
      layoutBreakpointMediumWidthMin:
          layoutBreakpointMediumWidthMin ?? this.layoutBreakpointMediumWidthMin,
      layoutBreakpointMediumWidthMax:
          layoutBreakpointMediumWidthMax ?? this.layoutBreakpointMediumWidthMax,
      layoutBreakpointExpandedWidthMin:
          layoutBreakpointExpandedWidthMin ?? this.layoutBreakpointExpandedWidthMin,
      layoutBreakpointCompactHeightMax:
          layoutBreakpointCompactHeightMax ?? this.layoutBreakpointCompactHeightMax,
      layoutBreakpointMediumHeightMin:
          layoutBreakpointMediumHeightMin ?? this.layoutBreakpointMediumHeightMin,
      layoutBreakpointMediumHeightMax:
          layoutBreakpointMediumHeightMax ?? this.layoutBreakpointMediumHeightMax,
      layoutBreakpointExpandedHeightMin:
          layoutBreakpointExpandedHeightMin ?? this.layoutBreakpointExpandedHeightMin,
      appEdgePaddingCompact: appEdgePaddingCompact ?? this.appEdgePaddingCompact,
      appEdgePaddingMedium: appEdgePaddingMedium ?? this.appEdgePaddingMedium,
      appEdgePaddingExpanded: appEdgePaddingExpanded ?? this.appEdgePaddingExpanded,
      paddingXxSmall: paddingXxSmall ?? this.paddingXxSmall,
      paddingXSmall: paddingXSmall ?? this.paddingXSmall,
      paddingSmall: paddingSmall ?? this.paddingSmall,
      chipPadding: chipPadding ?? this.chipPadding,
      paddingMedium: paddingMedium ?? this.paddingMedium,
      paddingSmallMedium: paddingSmallMedium ?? this.paddingSmallMedium,
      paddingLarge: paddingLarge ?? this.paddingLarge,
      paddingXLarge: paddingXLarge ?? this.paddingXLarge,
      padding15: padding15 ?? this.padding15,
      profilePhotoRadiusSmall: profilePhotoRadiusSmall ?? this.profilePhotoRadiusSmall,
      profilePhotoRadiusMedium: profilePhotoRadiusMedium ?? this.profilePhotoRadiusMedium,
      profilePhotoRadiusLarge: profilePhotoRadiusLarge ?? this.profilePhotoRadiusLarge,
      roundedRectRadiusXSmall: roundedRectRadiusXSmall ?? this.roundedRectRadiusXSmall,
      roundedRectRadiusSmall: roundedRectRadiusSmall ?? this.roundedRectRadiusSmall,
      roundedRectRadiusMedium: roundedRectRadiusMedium ?? this.roundedRectRadiusMedium,
      roundedRectRadiusLarge: roundedRectRadiusLarge ?? this.roundedRectRadiusLarge,
      roundedRectRadiusXLarge: roundedRectRadiusXLarge ?? this.roundedRectRadiusXLarge,
      roundedRectRadiusXxLarge: roundedRectRadiusXxLarge ?? this.roundedRectRadiusXxLarge,
      elevationLevel0: elevationLevel0 ?? this.elevationLevel0,
      elevationLevel1: elevationLevel1 ?? this.elevationLevel1,
      elevationLevel2: elevationLevel2 ?? this.elevationLevel2,
      elevationLevel3: elevationLevel3 ?? this.elevationLevel3,
      elevationLevel4: elevationLevel4 ?? this.elevationLevel4,
      elevationLevel5: elevationLevel5 ?? this.elevationLevel5,
      borderWidthXSmall: borderWidthXSmall ?? this.borderWidthXSmall,
      borderWidthSmall: borderWidthSmall ?? this.borderWidthSmall,
      borderWidthRegular: borderWidthRegular ?? this.borderWidthRegular,
      borderWidthMedium: borderWidthMedium ?? this.borderWidthMedium,
      drawerHeaderHeight: drawerHeaderHeight ?? this.drawerHeaderHeight,
      dropdownHeight: dropdownHeight ?? this.dropdownHeight,
      highlightBorderWidth: highlightBorderWidth ?? this.highlightBorderWidth,
      customToolbarHeight: customToolbarHeight ?? this.customToolbarHeight,
      buttonSizeSmall: buttonSizeSmall ?? this.buttonSizeSmall,
      buttonSizeMedium: buttonSizeMedium ?? this.buttonSizeMedium,
      buttonSizeLarge: buttonSizeLarge ?? this.buttonSizeLarge,
      buttonSizeXLarge: buttonSizeXLarge ?? this.buttonSizeXLarge,
      socialSigninButtonSize: socialSigninButtonSize ?? this.socialSigninButtonSize,
      loadingWidgetSize: loadingWidgetSize ?? this.loadingWidgetSize,
      imageGalleryTileSize: imageGalleryTileSize ?? this.imageGalleryTileSize,
      imageGalleryImageSize: imageGalleryImageSize ?? this.imageGalleryImageSize,
      fontSizeXSmall: fontSizeXSmall ?? this.fontSizeXSmall,
      fontSizeSmall: fontSizeSmall ?? this.fontSizeSmall,
      fontSizeMedium: fontSizeMedium ?? this.fontSizeMedium,
      fontSizeMedium18: fontSizeMedium18 ?? this.fontSizeMedium18,
      fontSizeMediumLarge: fontSizeMediumLarge ?? this.fontSizeMediumLarge,
      fontSizeLarge: fontSizeLarge ?? this.fontSizeLarge,
      fontSizeXLarge: fontSizeXLarge ?? this.fontSizeXLarge,
      iconSizeXSmall: iconSizeXSmall ?? this.iconSizeXSmall,
      iconSizeSmall: iconSizeSmall ?? this.iconSizeSmall,
      iconSizeMedium: iconSizeMedium ?? this.iconSizeMedium,
      iconSizeLarge: iconSizeLarge ?? this.iconSizeLarge,
      iconSizeXLarge: iconSizeXLarge ?? this.iconSizeXLarge,
      iconSizeXxLarge: iconSizeXxLarge ?? this.iconSizeXxLarge,
      imageSizeSmall: imageSizeSmall ?? this.imageSizeSmall,
      imageSizeMedium: imageSizeMedium ?? this.imageSizeMedium,
      imageSizeLarge: imageSizeLarge ?? this.imageSizeLarge,
      imageSizeXLarge: imageSizeXLarge ?? this.imageSizeXLarge,
      zero: zero ?? this.zero,
      boxSizeSmall: boxSizeSmall ?? this.boxSizeSmall,
      boxSizeMedium: boxSizeMedium ?? this.boxSizeMedium,
      boxSizeMediumLarge: boxSizeMediumLarge ?? this.boxSizeMediumLarge,
      boxSizeLarge: boxSizeLarge ?? this.boxSizeLarge,
      boxSizeXLarge: boxSizeXLarge ?? this.boxSizeXLarge,
      boxSizeXxLarge: boxSizeXxLarge ?? this.boxSizeXxLarge,
      boxSizeXXLarge: boxSizeXXLarge ?? this.boxSizeXXLarge,
      recommandedCardHeightMin: recommandedCardHeightMin ?? this.recommandedCardHeightMin,
      welcomePreviewSlide: welcomePreviewSlide ?? this.welcomePreviewSlide,
      mobileUserCardWidth: mobileUserCardWidth ?? this.mobileUserCardWidth,
      messageMenuWidth: messageMenuWidth ?? this.messageMenuWidth,
      messageChannelMinWidth: messageChannelMinWidth ?? this.messageChannelMinWidth,
      messageChannelMaxWidth: messageChannelMaxWidth ?? this.messageChannelMaxWidth,
      countryCodeWidth: countryCodeWidth ?? this.countryCodeWidth,
      lineHeightX: lineHeightX ?? this.lineHeightX,
      boxWidth: boxWidth ?? this.boxWidth,
      boxHeight: boxHeight ?? this.boxHeight,
      imageRadius: imageRadius ?? this.imageRadius,
      popupYOffset: popupYOffset ?? this.popupYOffset,
      homeToolbarHeight: homeToolbarHeight ?? this.homeToolbarHeight,
      sheetRadius: sheetRadius ?? this.sheetRadius,
      textFieldRadius: textFieldRadius ?? this.textFieldRadius,
      vacationDialogImageHeight: vacationDialogImageHeight ?? this.vacationDialogImageHeight,
      vacationDialogContentHeight: vacationDialogContentHeight ?? this.vacationDialogContentHeight,
      vacationDialogContentWidth: vacationDialogContentWidth ?? this.vacationDialogContentWidth,
      welcomeCarouselIndicatorSize:
          welcomeCarouselIndicatorSize ?? this.welcomeCarouselIndicatorSize,
      radioButtonPadding: radioButtonPadding ?? this.radioButtonPadding,
      maxDesktopAppWidth: maxDesktopAppWidth ?? this.maxDesktopAppWidth,
      desktopAppBreakpoint: desktopAppBreakpoint ?? this.desktopAppBreakpoint,
      desktopAppBreakpointChat: desktopAppBreakpointChat ?? this.desktopAppBreakpointChat,
      desktopAppBreakpointInviteTab:
          desktopAppBreakpointInviteTab ?? this.desktopAppBreakpointInviteTab,
      desktopAppBreakpointTrainingDashboard:
          desktopAppBreakpointTrainingDashboard ?? this.desktopAppBreakpointTrainingDashboard,
      desktopOnboardingExtraScrollingSpace:
          desktopOnboardingExtraScrollingSpace ?? this.desktopOnboardingExtraScrollingSpace,
      imageAspectRatioThreshold: imageAspectRatioThreshold ?? this.imageAspectRatioThreshold,
      carousalImageAspectRatio: carousalImageAspectRatio ?? this.carousalImageAspectRatio,
      dialogWidthWeb: dialogWidthWeb ?? this.dialogWidthWeb,
      dialogSmallWidthWeb: dialogSmallWidthWeb ?? this.dialogSmallWidthWeb,
      desktopTrainingCardHeight: desktopTrainingCardHeight ?? this.desktopTrainingCardHeight,
      webUserCardSize: webUserCardSize ?? this.webUserCardSize,
      webArchiveMessageCardSize: webArchiveMessageCardSize ?? this.webArchiveMessageCardSize,
      webTrainingCardImageWidth: webTrainingCardImageWidth ?? this.webTrainingCardImageWidth,
      trainingSourceSize: trainingSourceSize ?? this.trainingSourceSize,
      nestedListViewIndent: nestedListViewIndent ?? this.nestedListViewIndent,
      footerHeight: footerHeight ?? this.footerHeight,
      inviteToConnectDialogHeight: inviteToConnectDialogHeight ?? this.inviteToConnectDialogHeight,
      tabIconHeight: tabIconHeight ?? this.tabIconHeight,
    );
  }

  @override
  ThemeExtension<Dimensions> lerp(covariant ThemeExtension<Dimensions>? other, double t) {
    if (other is! Dimensions) {
      return this;
    }
    return Dimensions(
      layoutBreakpointCompactWidthMax:
          lerpDouble(layoutBreakpointCompactWidthMax, other.layoutBreakpointCompactWidthMax, t)!,
      layoutBreakpointMediumWidthMin:
          lerpDouble(layoutBreakpointMediumWidthMin, other.layoutBreakpointMediumWidthMin, t)!,
      layoutBreakpointMediumWidthMax:
          lerpDouble(layoutBreakpointMediumWidthMax, other.layoutBreakpointMediumWidthMax, t)!,
      layoutBreakpointExpandedWidthMin:
          lerpDouble(layoutBreakpointExpandedWidthMin, other.layoutBreakpointExpandedWidthMin, t)!,
      layoutBreakpointCompactHeightMax:
          lerpDouble(layoutBreakpointCompactHeightMax, other.layoutBreakpointCompactHeightMax, t)!,
      layoutBreakpointMediumHeightMin:
          lerpDouble(layoutBreakpointMediumHeightMin, other.layoutBreakpointMediumHeightMin, t)!,
      layoutBreakpointMediumHeightMax:
          lerpDouble(layoutBreakpointMediumHeightMax, other.layoutBreakpointMediumHeightMax, t)!,
      layoutBreakpointExpandedHeightMin:
          lerpDouble(
            layoutBreakpointExpandedHeightMin,
            other.layoutBreakpointExpandedHeightMin,
            t,
          )!,
      appEdgePaddingCompact: lerpDouble(appEdgePaddingCompact, other.appEdgePaddingCompact, t)!,
      appEdgePaddingMedium: lerpDouble(appEdgePaddingMedium, other.appEdgePaddingMedium, t)!,
      appEdgePaddingExpanded: lerpDouble(appEdgePaddingExpanded, other.appEdgePaddingExpanded, t)!,
      paddingXxSmall: lerpDouble(paddingXxSmall, other.paddingXxSmall, t)!,
      paddingXSmall: lerpDouble(paddingXSmall, other.paddingXSmall, t)!,
      paddingSmall: lerpDouble(paddingSmall, other.paddingSmall, t)!,
      chipPadding: lerpDouble(chipPadding, other.chipPadding, t)!,
      paddingMedium: lerpDouble(paddingMedium, other.paddingMedium, t)!,
      paddingSmallMedium: lerpDouble(paddingSmallMedium, other.paddingSmallMedium, t)!,
      paddingLarge: lerpDouble(paddingLarge, other.paddingLarge, t)!,
      paddingXLarge: lerpDouble(paddingXLarge, other.paddingXLarge, t)!,
      padding15: lerpDouble(padding15, other.padding15, t)!,
      profilePhotoRadiusSmall:
          lerpDouble(profilePhotoRadiusSmall, other.profilePhotoRadiusSmall, t)!,
      profilePhotoRadiusMedium:
          lerpDouble(profilePhotoRadiusMedium, other.profilePhotoRadiusMedium, t)!,
      profilePhotoRadiusLarge:
          lerpDouble(profilePhotoRadiusLarge, other.profilePhotoRadiusLarge, t)!,
      roundedRectRadiusXSmall:
          lerpDouble(roundedRectRadiusXSmall, other.roundedRectRadiusXSmall, t)!,
      roundedRectRadiusSmall: lerpDouble(roundedRectRadiusSmall, other.roundedRectRadiusSmall, t)!,
      roundedRectRadiusMedium:
          lerpDouble(roundedRectRadiusMedium, other.roundedRectRadiusMedium, t)!,
      roundedRectRadiusLarge: lerpDouble(roundedRectRadiusLarge, other.roundedRectRadiusLarge, t)!,
      roundedRectRadiusXLarge:
          lerpDouble(roundedRectRadiusXLarge, other.roundedRectRadiusXLarge, t)!,
      roundedRectRadiusXxLarge:
          lerpDouble(roundedRectRadiusXxLarge, other.roundedRectRadiusXxLarge, t)!,
      elevationLevel0: lerpDouble(elevationLevel0, other.elevationLevel0, t)!,
      elevationLevel1: lerpDouble(elevationLevel1, other.elevationLevel1, t)!,
      elevationLevel2: lerpDouble(elevationLevel2, other.elevationLevel2, t)!,
      elevationLevel3: lerpDouble(elevationLevel3, other.elevationLevel3, t)!,
      elevationLevel4: lerpDouble(elevationLevel4, other.elevationLevel4, t)!,
      elevationLevel5: lerpDouble(elevationLevel5, other.elevationLevel5, t)!,
      borderWidthXSmall: lerpDouble(borderWidthXSmall, other.borderWidthXSmall, t)!,
      borderWidthSmall: lerpDouble(borderWidthSmall, other.borderWidthSmall, t)!,
      borderWidthRegular: lerpDouble(borderWidthRegular, other.borderWidthRegular, t)!,
      borderWidthMedium: lerpDouble(borderWidthMedium, other.borderWidthMedium, t)!,
      drawerHeaderHeight: lerpDouble(drawerHeaderHeight, other.drawerHeaderHeight, t)!,
      dropdownHeight: lerpDouble(dropdownHeight, other.dropdownHeight, t)!,
      highlightBorderWidth: lerpDouble(highlightBorderWidth, other.highlightBorderWidth, t)!,
      customToolbarHeight: lerpDouble(customToolbarHeight, other.customToolbarHeight, t)!,
      buttonSizeSmall: Size.lerp(buttonSizeSmall, other.buttonSizeSmall, t)!,
      buttonSizeMedium: Size.lerp(buttonSizeMedium, other.buttonSizeMedium, t)!,
      buttonSizeLarge: Size.lerp(buttonSizeLarge, other.buttonSizeLarge, t)!,
      buttonSizeXLarge: Size.lerp(buttonSizeXLarge, other.buttonSizeXLarge, t)!,
      socialSigninButtonSize: Size.lerp(socialSigninButtonSize, other.socialSigninButtonSize, t)!,
      loadingWidgetSize: Size.lerp(loadingWidgetSize, other.loadingWidgetSize, t)!,
      imageGalleryTileSize: Size.lerp(imageGalleryTileSize, other.imageGalleryTileSize, t)!,
      imageGalleryImageSize: Size.lerp(imageGalleryImageSize, other.imageGalleryImageSize, t)!,
      fontSizeXSmall: lerpDouble(fontSizeXSmall, other.fontSizeXSmall, t)!,
      fontSizeSmall: lerpDouble(fontSizeSmall, other.fontSizeSmall, t)!,
      fontSizeMedium: lerpDouble(fontSizeMedium, other.fontSizeMedium, t)!,
      fontSizeMedium18: lerpDouble(fontSizeMedium18, other.fontSizeMedium18, t)!,
      fontSizeMediumLarge: lerpDouble(fontSizeMediumLarge, other.fontSizeMediumLarge, t)!,
      fontSizeLarge: lerpDouble(fontSizeLarge, other.fontSizeLarge, t)!,
      fontSizeXLarge: lerpDouble(fontSizeXLarge, other.fontSizeXLarge, t)!,
      iconSizeXSmall: lerpDouble(iconSizeXSmall, other.iconSizeXSmall, t)!,
      iconSizeSmall: lerpDouble(iconSizeSmall, other.iconSizeSmall, t)!,
      iconSizeMedium: lerpDouble(iconSizeMedium, other.iconSizeMedium, t)!,
      iconSizeLarge: lerpDouble(iconSizeLarge, other.iconSizeLarge, t)!,
      iconSizeXLarge: lerpDouble(iconSizeXLarge, other.iconSizeXLarge, t)!,
      iconSizeXxLarge: lerpDouble(iconSizeXxLarge, other.iconSizeXxLarge, t)!,
      imageSizeSmall: lerpDouble(imageSizeSmall, other.imageSizeSmall, t)!,
      imageSizeMedium: lerpDouble(imageSizeMedium, other.imageSizeMedium, t)!,
      imageSizeLarge: lerpDouble(imageSizeLarge, other.imageSizeLarge, t)!,
      imageSizeXLarge: lerpDouble(imageSizeXLarge, other.imageSizeXLarge, t)!,
      zero: lerpDouble(zero, other.zero, t)!,
      boxSizeSmall: lerpDouble(boxSizeSmall, other.boxSizeSmall, t)!,
      boxSizeMedium: lerpDouble(boxSizeMedium, other.boxSizeMedium, t)!,
      boxSizeMediumLarge: lerpDouble(boxSizeMediumLarge, other.boxSizeMediumLarge, t)!,
      boxSizeLarge: lerpDouble(boxSizeLarge, other.boxSizeLarge, t)!,
      boxSizeXLarge: lerpDouble(boxSizeXLarge, other.boxSizeXLarge, t)!,
      boxSizeXXLarge: lerpDouble(boxSizeXXLarge, other.boxSizeXXLarge, t)!,
      boxSizeXxLarge: lerpDouble(boxSizeXxLarge, other.boxSizeXxLarge, t)!,
      recommandedCardHeightMin:
          lerpDouble(recommandedCardHeightMin, other.recommandedCardHeightMin, t)!,
      welcomePreviewSlide: lerpDouble(welcomePreviewSlide, other.welcomePreviewSlide, t)!,
      mobileUserCardWidth: lerpDouble(mobileUserCardWidth, other.mobileUserCardWidth, t)!,
      messageMenuWidth: lerpDouble(messageMenuWidth, other.messageMenuWidth, t)!,
      messageChannelMinWidth: lerpDouble(messageChannelMinWidth, other.messageChannelMinWidth, t)!,
      messageChannelMaxWidth: lerpDouble(messageChannelMaxWidth, other.messageChannelMaxWidth, t)!,
      countryCodeWidth: lerpDouble(countryCodeWidth, other.countryCodeWidth, t)!,
      lineHeightX: lerpDouble(lineHeightX, other.lineHeightX, t)!,
      boxWidth: lerpDouble(boxWidth, other.boxWidth, t)!,
      boxHeight: lerpDouble(boxHeight, other.boxHeight, t)!,
      imageRadius: lerpDouble(imageRadius, other.imageRadius, t)!,
      popupYOffset: lerpDouble(popupYOffset, other.popupYOffset, t)!,
      homeToolbarHeight: lerpDouble(homeToolbarHeight, other.homeToolbarHeight, t)!,
      sheetRadius: lerpDouble(sheetRadius, other.sheetRadius, t)!,
      textFieldRadius: lerpDouble(textFieldRadius, other.textFieldRadius, t)!,
      vacationDialogImageHeight:
          lerpDouble(vacationDialogImageHeight, other.vacationDialogImageHeight, t)!,
      vacationDialogContentHeight:
          lerpDouble(vacationDialogContentHeight, other.vacationDialogContentHeight, t)!,
      vacationDialogContentWidth:
          lerpDouble(vacationDialogContentWidth, other.vacationDialogContentWidth, t)!,
      radioButtonPadding: lerpDouble(radioButtonPadding, other.radioButtonPadding, t)!,
      welcomeCarouselIndicatorSize:
          Size.lerp(welcomeCarouselIndicatorSize, other.welcomeCarouselIndicatorSize, t)!,
      maxDesktopAppWidth: lerpDouble(maxDesktopAppWidth, other.maxDesktopAppWidth, t)!,
      desktopAppBreakpoint: lerpDouble(desktopAppBreakpoint, other.desktopAppBreakpoint, t)!,
      desktopAppBreakpointChat:
          lerpDouble(desktopAppBreakpointChat, other.desktopAppBreakpointChat, t)!,
      desktopAppBreakpointInviteTab:
          lerpDouble(desktopAppBreakpointInviteTab, other.desktopAppBreakpointInviteTab, t)!,
      desktopAppBreakpointTrainingDashboard:
          lerpDouble(
            desktopAppBreakpointTrainingDashboard,
            other.desktopAppBreakpointTrainingDashboard,
            t,
          )!,
      desktopOnboardingExtraScrollingSpace:
          lerpDouble(
            desktopOnboardingExtraScrollingSpace,
            other.desktopOnboardingExtraScrollingSpace,
            t,
          )!,
      imageAspectRatioThreshold:
          lerpDouble(imageAspectRatioThreshold, other.imageAspectRatioThreshold, t)!,
      carousalImageAspectRatio:
          lerpDouble(carousalImageAspectRatio, other.carousalImageAspectRatio, t)!,
      dialogWidthWeb: lerpDouble(dialogWidthWeb, other.dialogWidthWeb, t)!,
      dialogSmallWidthWeb: lerpDouble(dialogSmallWidthWeb, other.dialogSmallWidthWeb, t)!,
      desktopTrainingCardHeight:
          lerpDouble(desktopTrainingCardHeight, other.desktopTrainingCardHeight, t)!,
      webUserCardSize: Size.lerp(webUserCardSize, other.webUserCardSize, t)!,
      webArchiveMessageCardSize:
          Size.lerp(webArchiveMessageCardSize, other.webArchiveMessageCardSize, t)!,
      webTrainingCardImageWidth:
          lerpDouble(webTrainingCardImageWidth, other.webTrainingCardImageWidth, t)!,
      trainingSourceSize: Size.lerp(trainingSourceSize, other.trainingSourceSize, t)!,
      nestedListViewIndent: lerpDouble(nestedListViewIndent, other.nestedListViewIndent, t)!,
      footerHeight: lerpDouble(footerHeight, other.footerHeight, t)!,
      inviteToConnectDialogHeight:
          lerpDouble(inviteToConnectDialogHeight, other.inviteToConnectDialogHeight, t)!,
      tabIconHeight: lerpDouble(tabIconHeight, other.tabIconHeight, t)!,
    );
  }
}
