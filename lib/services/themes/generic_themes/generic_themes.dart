// In Dart, private members are accessible within the same library. With
// import you import a library and can access only its public members.
// With part/part of you can split one library into several files and private
// members are accessible for all code within these files.
library generic_themes;

import 'package:flutter/material.dart';

import '../extensions/app_features.dart';
import '../extensions/custom_styles.dart';
import '../extensions/dimensions.dart';
import '../extensions/theme_validator.dart';

part 'parts/color_schemes.dart';

class GenericThemes {
  GenericThemes._private();

  static ThemeData _baseThemeOverlay(ThemeData baseTheme, ColorScheme colorScheme) {
    return baseTheme.copyWith(
      colorScheme: colorScheme,
      typography: Typography.material2021(),
      appBarTheme: AppBarTheme(iconTheme: IconThemeData(color: colorScheme.primary)),
      extensions: [
        AppFeatures.defaultAppFeatures,
        CustomStyles.defaultCustomStyles,
        Dimensions.defaultDimensions,
        ThemeValidator.defaultThemeValidator,
      ],
    );
  }

  static ThemeData light() {
    return _baseThemeOverlay(ThemeData.light(), ColorSchemes.light);
  }

  static ThemeData dark() {
    return _baseThemeOverlay(ThemeData.dark(), ColorSchemes.dark);
  }
}
