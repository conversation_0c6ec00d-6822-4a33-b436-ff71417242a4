import 'package:flutter/material.dart';

import 'generic_themes/generic_themes.dart';
import 'micromentor_themes/micromentor_themes.dart';

class Themes {
  Themes._private();

  static ThemeData light(String name, Color primaryColor, Color secondaryColor) {
    if (name == 'Micromentor') {
      return MicromentorThemes.light(primaryColor, secondaryColor);
    }
    return GenericThemes.light();
  }

  static ThemeData dark(String name, Color primaryColor, Color secondaryColor) {
    if (name == 'Micromentor') {
      return MicromentorThemes.dark(primaryColor, secondaryColor);
    }
    return GenericThemes.dark();
  }
}
