// In Dart, private members are accessible within the same library. With
// import you import a library and can access only its public members.
// With part/part of you can split one library into several files and private
// members are accessible for all code within these files.
library micro_mentor_themes;

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../extensions/app_features.dart';
import '../extensions/custom_styles.dart';
import '../extensions/dimensions.dart';
import '../extensions/theme_validator.dart';

part 'parts/color_schemes.dart';

class MicromentorThemes {
  MicromentorThemes._private();

  static ThemeData _baseThemeOverlay(ThemeData baseTheme, ColorScheme colorScheme) {
    return baseTheme.copyWith(
      colorScheme: colorScheme,
      cardColor: colorScheme.surface,
      dialogTheme: DialogThemeData(backgroundColor: colorScheme.onPrimary),
      scaffoldBackgroundColor: colorScheme.surface,
      typography: Typography.material2021(),
      disabledColor: colorScheme.primaryContainer,
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        iconTheme: IconThemeData(color: colorScheme.onSurface),
        titleTextStyle: GoogleFonts.notoSans(
          textStyle: baseTheme.textTheme.titleLarge?.copyWith(
            color: colorScheme.surfaceBright,
            fontSize: Dimensions.defaultDimensions.fontSizeLarge,
            fontWeight: FontWeight.w700,
          ),
        ),
      ),
      textTheme: GoogleFonts.notoSansTextTheme(baseTheme.textTheme),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: Dimensions.defaultDimensions.elevationLevel2,
          backgroundColor: colorScheme.primary,
          minimumSize: Dimensions.defaultDimensions.buttonSizeSmall,
          foregroundColor: colorScheme.onPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              Dimensions.defaultDimensions.roundedRectRadiusXxLarge,
            ),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(minimumSize: Dimensions.defaultDimensions.buttonSizeSmall),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          minimumSize: Dimensions.defaultDimensions.buttonSizeSmall,
        ).copyWith(
          side: WidgetStateProperty.resolveWith<BorderSide?>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return BorderSide(color: colorScheme.outline);
            }
            return BorderSide(color: colorScheme.primary);
          }),
        ),
      ),
      chipTheme: ChipThemeData(backgroundColor: colorScheme.onSurface),
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary;
          }
          return colorScheme.outline;
        }),
      ),
      checkboxTheme: CheckboxThemeData(
        checkColor: WidgetStateProperty.resolveWith((_) {
          return colorScheme.primary;
        }),
        fillColor: WidgetStateProperty.resolveWith((Set states) {
          if (states.contains(WidgetState.selected)) {
            return Colors.transparent;
          }
          return colorScheme.onPrimary;
        }),
        side: WidgetStateBorderSide.resolveWith((Set<WidgetState> states) {
          if (states.contains(WidgetState.selected)) {
            return BorderSide(color: colorScheme.primary);
          }
          return BorderSide(color: colorScheme.outline);
        }),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(Dimensions.defaultDimensions.roundedRectRadiusXSmall),
        ),
      ),
      extensions: [
        AppFeatures.defaultAppFeatures,
        CustomStyles.defaultCustomStyles,
        Dimensions.defaultDimensions,
        ThemeValidator.defaultThemeValidator,
      ],
    );
  }

  static ThemeData light(Color primaryColor, Color secondaryColor) {
    return _baseThemeOverlay(
      ThemeData.light(),
      ColorSchemes.getLightColorScheme(primaryColor, secondaryColor),
    );
  }

  static ThemeData dark(Color primaryColor, Color secondaryColor) {
    return _baseThemeOverlay(
      ThemeData.dark(),
      ColorSchemes.getDarkColorScheme(primaryColor, secondaryColor),
    );
  }
}
