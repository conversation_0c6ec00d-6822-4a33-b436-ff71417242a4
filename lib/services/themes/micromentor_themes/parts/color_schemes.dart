part of '../micromentor_themes.dart';

//TODO: (pending colors) yet to add in colorScheme
//B0D0FF : (chat sent-link color)

//TODO: single colors's replacement
//ka<PERSON>'s suggestion: https://www.figma.com/file/dBTj8fIojYRh5Mj4EVHqFd?type=design&node-id=454-42935&mode=design#770381471

//F4F5F6 or 0xFFF4F5F6 : instead of this color we are using "0xFFCFD4DC" with opacity 0.3 or 0.4
//F2BBF4 or 0xFFF2BBF4 : instead of this color we are using "0xFFE26CE8" with opacity 0.5
//D0D5DD or 0xFFFD0D5DD: instead of this color we are using "0xFFCED1D6"
//A9AFB8 or 0xFF#A9AFB8: instead of this color we are using "0xFF878D97" with opacity 0.6

class ColorSchemes {
  const ColorSchemes._private();

  static getLightColorScheme(Color primaryColor, Color secondaryColor) {
    return ColorScheme(
      brightness: Brightness.light,
      primary: primaryColor,
      primaryFixedDim: const Color.fromRGBO(154, 195, 255, 1),
      onPrimary: const Color(0xFFFFFFFF),
      primaryContainer: const Color(0xFFF5F5F5),
      onPrimaryContainer: const Color(0xFF696E76),
      secondary: secondaryColor,
      onSecondary: const Color(0xFFFFFFFF),
      secondaryContainer: const Color(0xFFE6F0FF),
      onSecondaryContainer: const Color(0xFF0D394A),
      tertiary: const Color(0xFF971A9C),
      onTertiary: const Color(0xFFFFAA00), //TODO: tentitive `not sure
      tertiaryContainer: const Color(0xFFC121C8),
      tertiaryFixed: const Color(0xFF34A853),
      tertiaryFixedDim: const Color(0xFF12B76A),
      onTertiaryContainer: const Color(0xFFE8E8E8),
      error: const Color(0xFFFF2000),
      errorContainer: const Color(0xFFF9DEDC), //pending
      onError: const Color(0xFFFFFFFF), //pending
      onErrorContainer: const Color(0xFFEA4335),
      surface: const Color(0xFFFFFFFF),
      onSurface: const Color(0xFF51555B),
      surfaceBright: const Color(0xFF00388C),
      surfaceContainerHighest: const Color(0xFFDEE0E3),
      onSurfaceVariant: const Color(0xFF0048B5),
      outline: const Color(0xFFCED1D6),
      onInverseSurface: const Color(0xFF313033),
      inverseSurface: const Color(0xFF000000),
      inversePrimary: const Color(0xFFE26CE8),
      shadow: const Color.fromARGB(255, 7, 7, 8),
      surfaceTint: const Color(0xFFEEF6FF),
      outlineVariant: const Color(0xFFCFD4DC),
      scrim: const Color(0xFF878D97),
    );
  }

  static getDarkColorScheme(Color primaryColor, Color secondaryColor) {
    return const ColorScheme(
      brightness: Brightness.dark,
      primary: Color(0xFFD0BCFF),
      onPrimary: Color(0xFF381E72),
      primaryContainer: Color(0xFF4F378B),
      onPrimaryContainer: Color(0xFFEADDFF),
      secondary: Color(0xFFCCC2DC),
      onSecondary: Color(0xFF332D41),
      secondaryContainer: Color(0xFF4A4458),
      onSecondaryContainer: Color(0xFFE8DEF8),
      tertiary: Color(0xFFEFB8C8),
      onTertiary: Color(0xFF492532),
      tertiaryContainer: Color(0xFF633B48),
      onTertiaryContainer: Color(0xFFFFD8E4),
      error: Color(0xFFF2B8B5),
      errorContainer: Color(0xFF8C1D18),
      onError: Color(0xFF601410),
      onErrorContainer: Color(0xFFF9DEDC),
      surface: Color(0xFF1C1B1F),
      onSurface: Color(0xFFE6E1E5),
      surfaceContainerHighest: Color(0xFF49454F),
      onSurfaceVariant: Color(0xFFCAC4D0),
      outline: Color(0xFF938F99),
      onInverseSurface: Color(0xFF313033),
      inverseSurface: Color(0xFFE6E1E5),
      inversePrimary: Color(0xFF6750A4),
      shadow: Color(0xFF000000),
      surfaceTint: Color(0xFFD0BCFF),
      outlineVariant: Color(0xFF49454F),
      scrim: Color(0xFF000000),
    );
  }
}
