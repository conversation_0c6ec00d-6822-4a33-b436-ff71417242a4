import 'dart:async';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/models/local_data_model.dart';
import 'package:mm_flutter_app/utilities/debug_logger.dart';

// From the server:
//   export enum HttpHeaderNames {
//     acceptLanguage = 'Accept-Language',
//     adminUserId = 'x-authorization-admin-user',
//     authType = 'x-authorization-auth-type',
//     consumer = 'x-consumer',
//     consumerOs = 'x-consumer-os',
//     consumerVersion = 'x-consumer-version',
//     deviceUuid = 'x-device',
//     hmacSignature = 'x-authorization-content-sha256',
//     hmacTimestamp = 'x-authorization-timestamp',
//     locale = 'x-locale',
//     pushNotificationToken = 'x-pn-token',
//     timezone = 'x-timezone',
//     userId = 'x-user-id',
//   }

String? cachedPushNotificationToken;

typedef RequestTransformer = FutureOr<Request> Function(Request request);

typedef OnException = FutureOr<String> Function(HttpLinkServerException exception);

/// This is modeled after the AuthLink
/// [gql reference auth link]: https://github.com/gql-dart/gql/blob/1884596904a411363165bcf3c7cfa9dcc2a61c26/examples/gql_example_http_auth_link/lib/http_auth_link.dart
class GraphQlLink extends _AsyncReqTransformLink {
  final String acceptLanguageHeaderKey;
  final String userIdHeaderKey;
  final String adminUserIdHeaderKey;
  final String bearerHeaderKey;
  final String consumerHeaderKey;
  final String consumerOsHeaderKey;
  final String consumerVersionHeaderKey;
  final String deviceUuidHeaderKey;
  final String localeHeaderKey;
  final String pushNotificationTokenHeaderKey;
  final String timezoneHeaderKey;
  final LocalDataModel localData;

  GraphQlLink({
    this.acceptLanguageHeaderKey = 'Accept-Language',
    this.userIdHeaderKey = 'x-user-id',
    this.adminUserIdHeaderKey = 'x-admin-user-id',
    this.bearerHeaderKey = 'Authorization',
    this.consumerHeaderKey = 'x-consumer',
    this.consumerOsHeaderKey = 'x-consumer-os',
    this.consumerVersionHeaderKey = 'x-consumer-version',
    this.deviceUuidHeaderKey = 'x-device',
    this.localeHeaderKey = 'x-locale',
    this.pushNotificationTokenHeaderKey = 'x-pn-token',
    this.timezoneHeaderKey = 'x-timezone',
    required this.localData,
  }) : super(
         requestTransformer: transform(
           acceptLanguageHeaderKey,
           userIdHeaderKey,
           adminUserIdHeaderKey,
           bearerHeaderKey,
           consumerHeaderKey,
           consumerOsHeaderKey,
           consumerVersionHeaderKey,
           deviceUuidHeaderKey,
           localeHeaderKey,
           pushNotificationTokenHeaderKey,
           timezoneHeaderKey,
           localData,
         ),
       );

  static RequestTransformer transform(
    String acceptLanguageHeaderKey,
    String userIdHeaderKey,
    String adminUserIdHeaderKey,
    String bearerHeaderKey,
    String consumerHeaderKey,
    String consumerOsHeaderKey,
    String consumerVersionHeaderKey,
    String deviceUuidHeaderKey,
    String localeHeaderKey,
    String pushNotificationTokenHeaderKey,
    String timezoneHeaderKey,
    LocalDataModel localData,
  ) => (Request request) async {
    bool sendPushNotificationToken = false;
    if (localData.authToken.isNotEmpty == true &&
        localData.pushNotificationToken != cachedPushNotificationToken) {
      sendPushNotificationToken = true;
      cachedPushNotificationToken = localData.pushNotificationToken;
      DebugLogger.debug('Sending push notification token ${localData.pushNotificationToken}');
    }
    return request.updateContextEntry<HttpLinkHeaders>(
      (headers) => HttpLinkHeaders(
        headers: <String, String>{
          ...headers?.headers ?? <String, String>{},
          if (localData.acceptLanguage.isNotEmpty)
            acceptLanguageHeaderKey: localData.acceptLanguage,
          if (localData.userId.isNotEmpty && localData.authToken.isNotEmpty)
            userIdHeaderKey: localData.userId,
          if (localData.userId.isNotEmpty &&
              localData.authToken.isNotEmpty &&
              localData.adminUserId.isNotEmpty)
            adminUserIdHeaderKey: localData.adminUserId,
          bearerHeaderKey: 'Bearer ${localData.authToken.isEmpty ? 'none' : localData.authToken}',
          if (localData.consumer.isNotEmpty) consumerHeaderKey: localData.consumer,
          if (localData.consumerOs.isNotEmpty) consumerOsHeaderKey: localData.consumerOs,
          if (localData.consumerVersion.isNotEmpty)
            consumerVersionHeaderKey: localData.consumerVersion,
          if (localData.deviceUuid.isNotEmpty) deviceUuidHeaderKey: localData.deviceUuid,
          if (localData.locale.isNotEmpty) localeHeaderKey: localData.locale,
          if (sendPushNotificationToken)
            pushNotificationTokenHeaderKey: localData.pushNotificationToken,
          if (localData.timezone.isNotEmpty) timezoneHeaderKey: localData.timezone,
        },
      ),
    );
  };
}

/// Version of [TransformLink] that handles async transforms
class _AsyncReqTransformLink extends Link {
  final RequestTransformer requestTransformer;

  _AsyncReqTransformLink({required this.requestTransformer});

  @override
  Stream<Response> request(Request request, [NextLink? forward]) async* {
    final req = await requestTransformer(request);

    yield* forward!(req);
  }
}
