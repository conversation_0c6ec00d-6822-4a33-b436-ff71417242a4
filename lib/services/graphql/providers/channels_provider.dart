import 'dart:async';

import 'package:flutter/material.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/__generated/schema/operations_invitation.graphql.dart';
import 'package:mm_flutter_app/models/local_data_model.dart';
import 'package:mm_flutter_app/services/graphql/providers/messages_provider.dart';
import 'package:provider/provider.dart';

import '../../../__generated/schema/operations_channel.graphql.dart';
import '../../../__generated/schema/schema.graphql.dart';
import '../../../constants/constants.dart';
import '../../../utilities/errors/crash_handler.dart';
import 'base/base_provider.dart';
import 'base/operation_result.dart';

typedef Channel = Query$FindChannelById$findChannelById;
typedef ChannelChangedEvent = Subscription$ChannelChanged$channelChanged;
typedef ChannelForUser = Query$FindChannelsForUser$findChannelsForUser;
typedef ChannelForUserParticipant = Query$FindChannelsForUser$findChannelsForUser$participants;
typedef ChannelParticipant = Query$FindChannelById$findChannelById$participants;
typedef ChannelInvitation = Query$MyChannelInvitations$myChannelInvitations;

class ChannelsProvider extends BaseProvider with ChangeNotifier {
  final MessagesProvider messagesProvider;
  List<ChannelForUser> _channels = List.empty(growable: true);
  final LocalDataModel localData;
  AsyncState _asyncState = AsyncState.ready;
  bool isDataLoaded = false;

  // This indicates whether the provider has every pulled any data and might need to be
  // updated after an event.
  int _revision = 0;
  int get revision => _revision;

  bool endOfResults = false;
  bool isFetchingData = false;

  ChannelsProvider({required super.client, required BuildContext context, required this.localData})
    : messagesProvider = Provider.of<MessagesProvider>(context, listen: false);

  List<ChannelForUser> get channels => _channels;

  List<String> get channelIds => unarchivedChannels.map((c) => c.id).toList();

  AsyncState get asyncState => _asyncState;

  List<ChannelForUser> get archivedChannels =>
      channels.where((element) {
        return element.isArchivedForMe == true;
      }).toList();

  List<ChannelForUser> get unarchivedChannels =>
      channels.where((element) {
        return element.isArchivedForMe == false;
      }).toList();

  // -----------------------------------------------------------------------------------------------
  // Queries
  Future<LoadObjectResult<List<ChannelForUser>>> loadMyChannels({
    FetchPolicy fetchPolicy = FetchPolicy.networkOnly,
  }) async {
    if (isFetchingData) {
      return LoadObjectResult<List<ChannelForUser>>(object: _channels);
    }
    isFetchingData = true;
    if (localData.userId.isNotEmpty != true) {
      isFetchingData = false;
      return LoadObjectResult<List<ChannelForUser>>(
        errorCode: Enum$ErrorCode.systemError,
        diagnosticsMessage: 'no userId available',
      );
    }
    _asyncState = AsyncState.loading;
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindChannelsForUser,
        fetchPolicy: fetchPolicy,
        variables:
            Variables$Query$FindChannelsForUser(
              userId: localData.userId,
              mustBeAccepted: true,
              mustHaveMessages: true,
              options: Input$FindObjectsOptions(
                includeArchived: Enum$IncludeFilterOption.include,
                // TODO - temporary fix for see more channels
                //limit: _channels.isEmpty ? Limits.initialChannelsPageSize : Limits.channelsPageSize,
                //skip: _channels.length,
              ),
            ).toJson(),
      ),
    );

    if (queryResult.hasException) {
      _asyncState = AsyncState.error;
      isFetchingData = false;
      return LoadObjectResult<List<ChannelForUser>>(
        errorCode: Enum$ErrorCode.systemError,
        diagnosticsMessage: queryResult.exception.toString(),
      );
    }

    List<ChannelForUser> channelsList =
        queryResult.data == null
            ? []
            : Query$FindChannelsForUser.fromJson(queryResult.data!).findChannelsForUser;

    if (channelsList.isEmpty == true ||
        channelsList.length <
            (_channels.isEmpty ? Limits.initialChannelsPageSize : Limits.channelsPageSize)) {
      endOfResults = true;
    }

    if (queryResult.data?.isNotEmpty == true) {
      _channels.addAll(channelsList);
      final ids = _channels.map((c) => c.id).toList();
      ids.sort();
    } else {
      _channels = List.empty(growable: true);
    }

    _asyncState = AsyncState.ready;
    _revision++;

    if (hasListeners) {
      notifyListeners();
    }
    isFetchingData = false;
    return LoadObjectResult<List<ChannelForUser>>(object: _channels);
  }

  clearChannels() {
    _channels = List.empty(growable: true);
    endOfResults = false;
    isDataLoaded = false;
  }

  Future<OperationResult<Channel>> findChannelById({required String channelId}) async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindChannelById,
        fetchPolicy: FetchPolicy.networkOnly,
        variables: Variables$Query$FindChannelById(channelId: channelId).toJson(),
      ),
    );
    return OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindChannelById.fromJson(queryResult.data!).findChannelById
              : null,
    );
  }

  // returns either string or null, depending on whether the channel exists
  Future<OperationResult<Channel?>> findOneOnOneChannelForUser(String otherUserId) async {
    final nullResponse = OperationResult(
      gqlQueryResult: QueryResult(
        source: QueryResultSource.cache,
        options: QueryOptions(document: documentNodeQueryFindOneOnOneChannel),
      ),
      response: null,
    );
    if (localData.userId.isNotEmpty != true) {
      return nullResponse;
    }
    try {
      final QueryResult queryResult = await asyncQuery(
        queryOptions: QueryOptions(
          document: documentNodeQueryFindOneOnOneChannel,
          fetchPolicy: FetchPolicy.networkOnly,
          variables:
              Variables$Query$FindOneOnOneChannel(
                userIds: [localData.userId, otherUserId],
              ).toJson(),
        ),
      );
      return OperationResult(
        gqlQueryResult: queryResult,
        response:
            queryResult.data != null
                ? Query$FindOneOnOneChannel.fromJson(queryResult.data!).find1On1Channel as Channel
                : null,
      );
    } catch (_) {
      return nullResponse;
    }
  }

  // -----------------------------------------------------------------------------------------------
  // Mutations
  Future<OperationResult<String>> archiveChannelForMe({
    required String channelId,
    bool loadChannels = true,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationArchiveChannelForMe,
        fetchPolicy: FetchPolicy.noCache,
        variables: Variables$Mutation$ArchiveChannelForMe(channelId: channelId).toJson(),
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Mutation$ArchiveChannelForMe.fromJson(queryResult.data!).archiveChannelForMe
              : null,
    );

    // load channel after mutation until it is archived for me. only try 5 times (hard coded)
    // TODO: make this a subscription, not polling
    Channel archivedChannel = await findChannelById(
      channelId: channelId,
    ).then((value) => value.response!);
    int count = 0;
    while (!result.gqlQueryResult.hasException &&
        archivedChannel.isArchivedForMe != true &&
        count < 5) {
      archivedChannel = await findChannelById(
        channelId: channelId,
      ).then((value) => value.response!);
      await Future.delayed(const Duration(seconds: 1));
    }
    if (!result.gqlQueryResult.hasException && loadChannels) {
      clearChannels();
      isFetchingData = false;
      await loadMyChannels();
    }

    return result;
  }

  Future<OperationResult<String>> unarchiveChannelForMe({
    required String channelId,
    bool loadChannels = true,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationUnarchiveChannelForMe,
        fetchPolicy: FetchPolicy.noCache,
        variables: Variables$Mutation$UnarchiveChannelForMe(channelId: channelId).toJson(),
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Mutation$UnarchiveChannelForMe.fromJson(queryResult.data!).unarchiveChannelForMe
              : null,
    );

    if (!result.gqlQueryResult.hasException && loadChannels) {
      clearChannels();
      isFetchingData = false;
      await loadMyChannels();
    }

    return result;
  }

  // -----------------------------------------------------------------------------------------------
  // Subscriptions
  StreamSubscription<QueryResult<ChannelChangedEvent>> subscribeToChannel({
    required String channelId,
    required void Function(ChannelChangedEvent) onSubscriptionEvent,
  }) {
    final stream = client.subscribe<ChannelChangedEvent>(
      SubscriptionOptions(
        document: documentNodeSubscriptionChannelChanged,
        variables: Variables$Subscription$ChannelChanged(channelId: channelId).toJson(),
      ),
    );

    final subscription = stream.listen((QueryResult<ChannelChangedEvent> queryResult) async {
      if (queryResult.hasException) {
        CrashHandler.logCrashReport(
          'Subscription for Channel Id ($channelId)'
          'encountered an error: ${queryResult.exception}',
        );
        return;
      }
      if (queryResult.isLoading) {
        // Data is not ready, return and check again on the next cycle.
        return;
      }
      if (queryResult.data == null) {
        CrashHandler.logCrashReport('Received empty event for Channel $channelId)');
        return;
      }

      ChannelChangedEvent event =
          Subscription$ChannelChanged.fromJson(queryResult.data!).channelChanged;

      // Process new data:
      onSubscriptionEvent(event);
    });

    return subscription;
  }
}
