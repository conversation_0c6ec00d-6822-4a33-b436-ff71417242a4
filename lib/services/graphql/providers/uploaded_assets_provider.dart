import 'dart:async';
import 'dart:typed_data' as typed_data;
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:image/image.dart' as img;
import 'package:mm_flutter_app/__generated/schema/operations_uploaded_assets.graphql.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/models/local_data_model.dart';
import 'package:mm_flutter_app/services/graphql/providers/base/base_provider.dart';
import 'package:mm_flutter_app/services/graphql/providers/base/operation_result.dart';
import 'package:mm_flutter_app/services/graphql/providers/user_provider.dart';
import 'package:provider/provider.dart';

// Image compression configuration:
const format = CompressFormat.jpeg;
const minWidth = 800;
const minHeight = 800;
const quality = 80;

class UploadedAssetsProvider extends BaseProvider with ChangeNotifier {
  final UserProvider userProvider;
  final LocalDataModel localData;
  final String assetUploadUrl;

  UploadedAssetsProvider({
    required super.client,
    required BuildContext context,
    required this.localData,
    required this.assetUploadUrl,
  }) : userProvider = Provider.of<UserProvider>(context, listen: false);

  reset() {}

  Future<String?> uploadAssetFile({
    required Object file,
    required ui.Image imageBitmap,
    String? uploadedAssetId,
    String? ownerId,
    Enum$ModelType? ownerModelType,
    Enum$UploadedAssetType? assetType,
  }) async {
    typed_data.Uint8List? bytes;
    typed_data.Uint8List? compressedBytes;

    // We are trying to resize the image using the flutter_image_compress package and image package.
    // Depending on the platform, this may or may not succeed. We first try
    // 'compressWithList', then decode-encode methods from image package.

    //Here we are converting cropped image to byte array
    final ByteData? d = await imageBitmap.toByteData(format: ui.ImageByteFormat.png);
    bytes = d?.buffer.asUint8List();

    try {
      debugPrint('Calling FlutterImageCompress.compressWithList.');
      compressedBytes = await FlutterImageCompress.compressWithList(
        bytes!,
        format: format,
        minWidth: minWidth,
        minHeight: minHeight,
        quality: quality,
      );
      debugPrint(
        'FlutterImageCompress.compressWithList succeeded. compressed image size:  ${compressedBytes.length} bytes',
      );
    } catch (error) {
      debugPrint('FlutterImageCompress.compressWithList failed.');
      debugPrint('$error');
      compressedBytes = null;
    }

    if (compressedBytes?.isNotEmpty != true) {
      try {
        // Resize the image
        img.Image image = img.decodeImage(bytes!)!;
        img.Image resizedImage = img.copyResize(image, width: minWidth, height: minHeight);

        // Convert the resized image to a PNG byte array
        compressedBytes = img.encodeJpg(resizedImage, quality: quality);
        debugPrint(
          "Image compression succeeded by decodeImage-copyResize-encodePng from 'Image' package. compressed image size:  ${compressedBytes.length} bytes",
        );
      } catch (error) {
        debugPrint(
          "Image compression failed by decodeImage-copyResize-encodePng from 'Image' package",
        );
        debugPrint('$error');
        compressedBytes = null;
      }
    }

    Map<String, String> data = {
      'ownerId': ownerId ?? localData.userId,
      // If we succeeded in compressing the image, we tell the server to not
      // resize it again:
      'resizeFile': compressedBytes?.isNotEmpty == true ? 'false' : 'true',
      'ownerModelType': ownerModelType == null ? Enum$ModelType.User.name : ownerModelType.name,
    };

    if (uploadedAssetId != null) {
      data['uploadedAssetId'] = uploadedAssetId;
    }

    if (assetType != null) {
      data['assetType'] = assetType.name;
    }

    // From the server:
    //   export enum HttpHeaderNames {
    //     acceptLanguage = 'Accept-Language',
    //     adminUserId = 'x-authorization-admin-user',
    //     authType = 'x-authorization-auth-type',
    //     consumer = 'x-consumer',
    //     consumerOs = 'x-consumer-os',
    //     consumerVersion = 'x-consumer-version',
    //     deviceUuid = 'x-device',
    //     hmacSignature = 'x-authorization-content-sha256',
    //     hmacTimestamp = 'x-authorization-timestamp',
    //     locale = 'x-locale',
    //     pushNotificationToken = 'x-pn-token',
    //     timezone = 'x-timezone',
    //     userId = 'x-user-id',
    //   }

    String filename = 'profile-photo.png';

    if (compressedBytes?.isNotEmpty == true) {
      bytes = compressedBytes;
    }

    var multiPartFile = http.MultipartFile.fromBytes(
      'file',
      bytes as List<int>,
      filename: filename,
    );
    var uri = Uri.parse(assetUploadUrl);
    debugPrint('uploadAssetFile: uploading to $uri.');
    var request =
        http.MultipartRequest('POST', uri)
          ..fields.addAll(data)
          ..files.add(multiPartFile)
          ..headers['Authorization'] = 'Bearer ${localData.authToken}'
          ..headers['x-device'] = localData.deviceUuid
          ..headers['x-consumer'] = localData.consumer
          ..headers['x-consumer-os'] = localData.consumerOs
          ..headers['x-timezone'] = localData.timezone;

    try {
      http.StreamedResponse response = await request.send();

      if (response.statusCode > 199 && response.statusCode < 300) {
        debugPrint('uploadAssetFile: received success code: ${response.statusCode}.');
        return null;
      }
      debugPrint('uploadAssetFile: received failure code: ${response.statusCode}.');
      return response.reasonPhrase;
    } catch (error) {
      debugPrint('uploadAssetFile Exception happed while sending image to server: $error');

      return error.toString();
    }
  }

  // void fileUploadSuccessCallBack() {
  //   if (context.read<UploadFileProvider>().fileUploadSuccess.value) {
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       SnackBar(
  //         content: Text(
  //           'File uploaded successfully',
  //           style: TextStyle(fontSize: 22),
  //         ),
  //         backgroundColor: Colors.green,
  //       ),
  //     );
  //     context.read<UploadFileProvider>().clearSelectedFileData();
  //   }
  // }

  Future<OperationResult<Mutation$InitAssetUpload$initAssetUpload>> initAssetUpload({
    String? uploadedAssetId,
    String? ownerId,
    Enum$ModelType? ownerModelType,
    Enum$UploadedAssetType? assetType,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationInitAssetUpload,
        fetchPolicy: FetchPolicy.noCache,
        variables:
            Variables$Mutation$InitAssetUpload(
              input: Input$UploadedAssetInput(
                id: uploadedAssetId,
                ownerId: ownerId ?? localData.userId,
                ownerModelType: ownerModelType ?? Enum$ModelType.User,
                assetType: assetType,
              ),
            ).toJson(),
      ),
    );

    final OperationResult<Mutation$InitAssetUpload$initAssetUpload> result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Mutation$InitAssetUpload.fromJson(queryResult.data!).initAssetUpload
              : null,
    );

    return result;
  }
}
