import 'dart:async';

import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:retry/retry.dart';

import '../../../../utilities/debug_logger.dart';
import '../../../../utilities/errors/crash_handler.dart';
import '../../../../utilities/errors/exceptions.dart';

abstract class BaseProvider {
  static const int retryAttempts = 2;
  static const Duration retryDelayFactor = Duration(milliseconds: 500);
  final GraphQLClient client;

  BaseProvider({required this.client});

  Future<QueryResult<TParsed>> asyncQuery<TParsed>({
    required QueryOptions<TParsed> queryOptions,
    bool logFailures = true,
  }) async {
    const RetryOptions retryOptions = RetryOptions(
      maxAttempts: BaseProvider.retryAttempts,
      delayFactor: BaseProvider.retryDelayFactor,
    );
    QueryResult<TParsed> result = await client.query<TParsed>(queryOptions);
    if (result.hasException) {
      if (logFailures) {
        CrashHandler.logCrashReport(
          'Exception while executing Query:'
          '\n${result.exception.toString()}\n'
          'Retrying up to ${retryOptions.maxAttempts} additional times if exception is not unauthorized.',
        );
      }

      final linkException = result.exception?.linkException;
      if ((linkException is HttpLinkServerException) && linkException.statusCode == 401) {
        return result;
      }

      QueryResult<TParsed>? retryResult =
          await CrashHandler.retryOnException<QueryResult<TParsed>?>(
            () => _retryOperation(() => client.query(queryOptions)),
            onFailOperation: () {
              if (logFailures) {
                DebugLogger.warning(
                  'Failed to complete Query'
                  ' after ${retryOptions.maxAttempts + 1} attempts.',
                );
              }
              return null;
            },
            retryOptions: retryOptions,
            logFailures: logFailures,
          );
      if (retryResult != null) {
        result = retryResult;
      }
    }

    return result;
  }

  Future<QueryResult<TParsed>> asyncMutation<TParsed>({
    required MutationOptions<TParsed> mutationOptions,
    bool logFailures = true,
  }) async {
    const RetryOptions retryOptions = RetryOptions(
      maxAttempts: BaseProvider.retryAttempts,
      delayFactor: BaseProvider.retryDelayFactor,
    );
    QueryResult<TParsed> result = await client.mutate<TParsed>(mutationOptions);
    if (result.hasException) {
      if (logFailures) {
        CrashHandler.logCrashReport(
          'Exception while executing Mutation:'
          '\n${result.exception.toString()}\n'
          'Retrying up to ${retryOptions.maxAttempts} additional times if exception is not unauthorized.',
        );
      }

      final linkException = result.exception?.linkException;
      if ((linkException is HttpLinkServerException) && linkException.statusCode == 401) {
        return result;
      }

      QueryResult<TParsed>? retryResult =
          await CrashHandler.retryOnException<QueryResult<TParsed>?>(
            () => _retryOperation<TParsed>(() => client.mutate(mutationOptions)),
            onFailOperation: () {
              if (logFailures) {
                DebugLogger.warning(
                  'Failed to complete Mutation'
                  ' after ${retryOptions.maxAttempts + 1} attempts.',
                );
              }
              return null;
            },
            retryOptions: retryOptions,
            logFailures: logFailures,
          );
      if (retryResult != null) {
        result = retryResult;
      }
    }

    return result;
  }

  Future<QueryResult<TParsed>> _retryOperation<TParsed>(
    Future<QueryResult<TParsed>> Function() operation,
  ) async {
    QueryResult<TParsed> retryResult = await operation();
    if (retryResult.hasException) {
      throw RetryException(
        message: retryResult.exception.toString(),
        originalException: retryResult.exception,
      );
    }
    return retryResult;
  }
}
