import 'dart:async';
import 'package:flutter/material.dart';
import 'package:graphql_flutter/graphql_flutter.dart';

import '../../../__generated/schema/operations_multi_step_action.graphql.dart';
import '../../../__generated/schema/schema.graphql.dart';
import 'base/base_provider.dart';
import 'base/operation_result.dart';

class MultiStepActionProvider extends BaseProvider with ChangeNotifier {
  // StreamSubscription<QueryResult<Subscription$ObjectChanged>>? _subscription;
  int _attemptCount = 0;

  MultiStepActionProvider({required super.client});

  reset() {
    _attemptCount = 0;
  }

  Future<OperationResult<Mutation$StartResetPassword$startResetPassword>> startResetPassword({
    required String email,
  }) async {
    reset();

    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationStartResetPassword,
        fetchPolicy: FetchPolicy.noCache,
        variables:
            Variables$Mutation$StartResetPassword(
              input: Input$UserIdentInput(email: email),
            ).toJson(),
      ),
    );

    final OperationResult<Mutation$StartResetPassword$startResetPassword> result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Mutation$StartResetPassword.fromJson(queryResult.data!).startResetPassword
              : null,
    );

    return result;
  }

  Future<void> verifyToken({
    required String actionId,
    required String token,
    String? newPassword,
    required Function(String userId, String authToken) onSuccess,
    required Function() onTokenMismatch,
    required Function() onFailedToSendNotification,
    required Function() onUnknownErrorOccurred,
  }) async {
    var variables =
        Variables$Mutation$VerifyMultiStepActionToken(
          input: Input$VerifyMultiStepActionTokenInput(
            actionId: actionId,
            newPassword: newPassword,
            token: token,
          ),
        ).toJson();

    await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationVerifyMultiStepActionToken,
        fetchPolicy: FetchPolicy.noCache,
        variables: variables,
      ),
    );

    var watchQuery = WatchQueryOptions(
      document: documentNodeQueryGetMultiStepActionProgress,
      fetchPolicy: FetchPolicy.noCache,
      variables:
          Variables$Query$GetMultiStepActionProgress(
            actionId: actionId,
            confirmToken: token,
          ).toJson(),
      pollInterval: const Duration(seconds: 1),
      fetchResults: true,
    );
    var resultPoll = client.watchQuery(watchQuery);
    resultPoll.stream.listen((event) {
      var pollResponse = OperationResult(
        gqlQueryResult: event,
        response:
            event.data != null
                ? Query$GetMultiStepActionProgress.fromJson(event.data!).getMultiStepActionProgress
                : null,
      );

      if (pollResponse.response?.attemptCount != null &&
          pollResponse.response!.attemptCount <= _attemptCount) {
        return;
      }

      bool stopPolling = false;

      if (pollResponse.response?.actionStatus == Enum$MultiStepActionStatus.finished &&
          pollResponse.response?.result == Enum$MultiStepActionResult.ok &&
          pollResponse.response?.userId != null &&
          pollResponse.response?.authToken != null) {
        onSuccess(pollResponse.response!.userId, pollResponse.response!.authToken!);
        stopPolling = true;
      } else if (pollResponse.response?.actionStatus == Enum$MultiStepActionStatus.finished &&
          (pollResponse.response?.result != Enum$MultiStepActionResult.ok ||
              pollResponse.response?.userId == null ||
              pollResponse.response?.authToken == null)) {
        onUnknownErrorOccurred();
        stopPolling = true;
      } else if (pollResponse.response?.result == Enum$MultiStepActionResult.confirmTokenMismatch) {
        onTokenMismatch();
        stopPolling = true;
        _attemptCount = pollResponse.response!.attemptCount;
      } else if (pollResponse.response?.notificationResult ==
          Enum$MultiStepActionSendNotificationResult.failed) {
        onFailedToSendNotification();
        stopPolling = true;
        _attemptCount = pollResponse.response!.attemptCount;
      }

      if (stopPolling) {
        resultPoll.stopPolling();
        resultPoll.close();
      }
    });
  }

  // todo: This is currently not used, because the UI is using a polling logic. We want to
  //       change that to using a subscription logic.
  /*
  Future<OperationResult<Query$GetMultiStepActionProgress$getMultiStepActionProgress>>
      confirmResetPasswordToken({required String actionId, required String token}) async {
    var variables =
        Variables$Query$GetMultiStepActionProgress(actionId: actionId, confirmToken: token)
            .toJson();
    debugPrint('---- input: $variables');
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
          document: documentNodeQueryGetMultiStepActionProgress,
          fetchPolicy: FetchPolicy.noCache,
          variables: variables,
          pollInterval: const Duration(seconds: 1)),
    );

    // print("---- isLoading: ${queryResult.isLoading}");

    var result = OperationResult(
      gqlQueryResult: queryResult,
      response: queryResult.data != null
          ? Query$GetMultiStepActionProgress.fromJson(
              queryResult.data!,
            ).getMultiStepActionProgress
          : null,
    );

    debugPrint('---- response: ${result.response?.toJson()}');

    if (result.response?.actionId != null) {
      debugPrint('----- actionId: ${result.response?.actionId}');
      var watchQuery = WatchQueryOptions(
          document: documentNodeQueryGetMultiStepActionProgress,
          fetchPolicy: FetchPolicy.noCache,
          variables: Variables$Query$GetMultiStepActionProgress(actionId: actionId).toJson(),
          pollInterval: const Duration(seconds: 1),
          fetchResults: true);
      var resultPoll = client.watchQuery(watchQuery);
      resultPoll.stream.listen((event) {
        var tmpResult = OperationResult(
          gqlQueryResult: event,
          response: event.data != null
              ? Query$GetMultiStepActionProgress.fromJson(
                  event.data!,
                ).getMultiStepActionProgress
              : null,
        );

        debugPrint(
            '---- watch query stream result: ${tmpResult.response?.attemptCount} ${tmpResult.response?.actionStatus}');
      });

      var stream = client.subscribe<Subscription$ObjectChanged>(
        SubscriptionOptions(
          document: documentNodeSubscriptionObjectChanged,
          variables:
              Variables$Subscription$ObjectChanged(objectId: result.response!.actionId).toJson(),
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      _subscription = stream.listen((event) {
        var result = OperationResult(
          gqlQueryResult: event,
          response: event.data != null
              ? Subscription$ObjectChanged.fromJson(
                  event.data!,
                ).objectChanged
              : null,
        );

        debugPrint('---- subscriptionStream: ${result.response?.toJson()}');
      });
    }

    return result;
  } */
}
