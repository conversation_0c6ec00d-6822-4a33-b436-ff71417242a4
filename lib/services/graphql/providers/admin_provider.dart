import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/__generated/schema/operations_admin.graphql.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/models/local_data_model.dart';
import 'package:mm_flutter_app/utilities/debug_logger.dart';
import 'package:mm_flutter_app/utilities/errors/crash_handler.dart';

import 'base/base_provider.dart';
import 'base/operation_result.dart';

typedef AdminTask = Query$FindAdminTaskById$findAdminTaskById;
typedef AdminTaskDef = Query$FindAdminTaskDefs$findAdminTaskDefs;
typedef AdminTaskArgDef = Query$FindAdminTaskDefs$findAdminTaskDefs$args;
typedef CreatedAdminTask = Mutation$CreateAdminTask$createAdminTask;

class AdminProvider extends BaseProvider with ChangeNotifier {
  List<AdminTaskDef>? _adminTaskDefs;
  final LocalDataModel localData;

  AdminProvider({required super.client, required this.localData});

  List<AdminTaskDef>? get adminTaskDefs => _adminTaskDefs;

  void _setAdminTaskDefs(List<AdminTaskDef> adminTaskDefs) {
    _adminTaskDefs = adminTaskDefs;
  }

  Future<LoadObjectResult<AdminTask>> findAdminTaskById(String adminTaskId) async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindAdminTaskById,
        fetchPolicy: FetchPolicy.networkOnly,
        variables: Variables$Query$FindAdminTaskById(adminTaskId: adminTaskId).toJson(),
      ),
    );

    final result = OperationResult<AdminTask>(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindAdminTaskById.fromJson(queryResult.data!).findAdminTaskById
              : null,
    );

    if (result.gqlQueryResult.hasException) {
      final String e = result.gqlQueryResult.exception.toString();
      CrashHandler.logCrashReport('Could not load admin task: $e');
      return LoadObjectResult<AdminTask>(
        errorCode: Enum$ErrorCode.systemError,
        diagnosticsMessage: 'did not receive a valid admin task',
      );
    }

    if (result.response?.id.isNotEmpty != true) {
      DebugLogger.error('InboxProvider.findAdminTaskById: did not receive a valid task');
      return LoadObjectResult<AdminTask>(
        errorCode: Enum$ErrorCode.systemError,
        diagnosticsMessage: 'did not receive a valid task',
      );
    }
    return LoadObjectResult<AdminTask>(object: result.response);
  }

  Future<OperationResult<List<AdminTaskDef>>> findAdminTaskDefs() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindAdminTaskDefs,
        fetchPolicy: FetchPolicy.cacheFirst,
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindAdminTaskDefs.fromJson(queryResult.data!).findAdminTaskDefs
              : null,
    );
    if (result.response != null) _setAdminTaskDefs(result.response!);
    return result;
  }

  Future<OperationResult<CreatedAdminTask>> createAdminTask({
    required Enum$AdminTaskType adminTaskType,
    List<String>? args,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationCreateAdminTask,
        fetchPolicy: FetchPolicy.noCache,
        variables:
            Variables$Mutation$CreateAdminTask(
              adminTaskInput: Input$AdminTaskInput(adminTaskType: adminTaskType, args: args),
            ).toJson(),
      ),
    );
    final operationResult = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Mutation$CreateAdminTask.fromJson(queryResult.data!).createAdminTask
              : null,
    );

    if (operationResult.gqlQueryResult.hasException) {
      return operationResult;
    }

    return operationResult;
  }
}
