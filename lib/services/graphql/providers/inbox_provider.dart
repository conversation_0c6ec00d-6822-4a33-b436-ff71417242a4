import 'dart:async';
import 'dart:collection';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/__generated/schema/operations_system.graphql.dart';
import 'package:mm_flutter_app/__generated/schema/operations_user_inbox.graphql.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/models/local_data_model.dart';
import 'package:mm_flutter_app/services/graphql/providers/channels_provider.dart';
import 'package:mm_flutter_app/services/graphql/providers/invitations_provider.dart';
import 'package:mm_flutter_app/services/graphql/providers/messages_provider.dart';
import 'package:mm_flutter_app/services/graphql/providers/user_provider.dart';
import 'package:mm_flutter_app/utilities/debug_logger.dart';
import 'package:provider/provider.dart';

import '../../../utilities/errors/crash_handler.dart';
import 'base/base_provider.dart';
import 'base/operation_result.dart';

typedef UserInbox = Query$MyInbox$myInbox;
typedef ObjectChangedEvent = Subscription$ObjectChanged$objectChanged;
typedef InboxLatestMessage = Query$MyInbox$myInbox$channels$latestMessages;

/// The InboxBadge makes the number of new (unread) inbox items to various places in the app,
/// which uses it to display a badge (red circle with a number in it).
class InboxBadge {
  int channelInvitations = 0;
  int channelMessages = 0;
  int archivedChannelMessages = 0;
  HashMap<String, int> messagesByChannel = HashMap<String, int>();
  int get all => channelInvitations + channelMessages + archivedChannelMessages;

  int valueByType(BadgeType badgeType) {
    switch (badgeType) {
      case BadgeType.all:
        return channelInvitations + channelMessages + archivedChannelMessages;
      case BadgeType.messages:
        return channelMessages + archivedChannelMessages;
      case BadgeType.invitations:
        return channelInvitations;
    }
  }

  void clear() {
    channelInvitations = 0;
    channelMessages = 0;
    archivedChannelMessages = 0;
    messagesByChannel = HashMap<String, int>();
  }
}

String getInboxIdsFromInvitations(List<Query$MyInbox$myInbox$channels$invitations>? invitations) {
  if (invitations?.isNotEmpty != true) {
    return '';
  }
  final ids = invitations!.map((i) => i.id).toList();
  ids.sort();
  return ids.join('');
}

class InboxProvider extends BaseProvider with ChangeNotifier {
  // Providers
  final MessagesProvider messagesProvider;
  final ChannelsProvider channelsProvider;
  final InvitationsProvider invitationsProvider;
  final UserProvider userProvider;
  final LocalDataModel localData;
  StreamSubscription<QueryResult<Subscription$ObjectChanged>>? _subscription;
  AsyncState _asyncState = AsyncState.ready;

  // Actual data:
  String? _inboxId;
  UserInbox? _inbox;

  UserInbox? get inbox => _inbox;
  AsyncState get asyncState => _asyncState;

  // New items/badge counters:
  InboxBadge badge = InboxBadge();

  InboxProvider({required super.client, required BuildContext context, required this.localData})
    : channelsProvider = Provider.of<ChannelsProvider>(context, listen: false),
      messagesProvider = Provider.of<MessagesProvider>(context, listen: false),
      invitationsProvider = Provider.of<InvitationsProvider>(context, listen: false),
      userProvider = Provider.of<UserProvider>(context, listen: false) {
    // Delaying the loading of the inbox by 4 seconds to not impact the launching
    // of the app.
    Future.delayed(const Duration(milliseconds: 4000), () {
      if (userProvider.myUser?.id.isNotEmpty != true) {
        DebugLogger.debug('InboxProvider.loadInbox: no logged-in user; not loading inbox.');
        return;
      }
      loadInbox();
    });
  }

  void clear() {
    _inboxId = null;
    _inbox = null;
    badge = InboxBadge();

    if (_subscription != null) {
      _subscription!.cancel();
      _subscription = null;
    }
  }

  Future<void> start() async {
    clear();
    badge.clear();
    invitationsProvider.clear();
    await loadInbox();
  }

  // -----------------------------------------------------------------------------------------------
  // Queries
  Future<LoadObjectResult<UserInbox>> loadInbox({
    FetchPolicy fetchPolicy = FetchPolicy.networkOnly,
  }) async {
    _asyncState = AsyncState.loading;
    final QueryResult<UserInbox> queryResult = await asyncQuery<UserInbox>(
      queryOptions: QueryOptions(document: documentNodeQueryMyInbox, fetchPolicy: fetchPolicy),
    );
    final result = OperationResult<UserInbox>(
      gqlQueryResult: queryResult,
      response: queryResult.data != null ? Query$MyInbox.fromJson(queryResult.data!).myInbox : null,
    );

    if (result.gqlQueryResult.hasException) {
      _asyncState = AsyncState.error;
      final String e = result.gqlQueryResult.exception.toString();
      CrashHandler.logCrashReport('Could not load inbox: $e');
      return LoadObjectResult<UserInbox>(
        errorCode: Enum$ErrorCode.systemError,
        diagnosticsMessage: 'did not receive a valid inbox',
      );
    }

    if (result.response?.id.isNotEmpty != true) {
      _asyncState = AsyncState.error;
      DebugLogger.error('InboxProvider.loadInbox: did not receive a valid inbox');
      return LoadObjectResult<UserInbox>(
        errorCode: Enum$ErrorCode.systemError,
        diagnosticsMessage: 'did not receive a valid inbox',
      );
    }

    DebugLogger.debug('InboxProvider.loadInbox: received inbox; id=${result.response?.id}');
    _inbox = result.response;
    _inboxId = _inbox!.id;

    // Badge values (number of new/unread items, such as chat messages, invitations):
    badge.clear();
    badge.archivedChannelMessages = _inbox?.channels?.unseenArchivedMessages?.length ?? 0;
    badge.channelMessages = _inbox?.channels?.unseenMessages?.length ?? 0;

    if (_inbox?.channels?.unseenMessages?.isNotEmpty == true) {
      for (Query$MyInbox$myInbox$channels$unseenMessages msg
          in (_inbox?.channels?.unseenMessages ?? [])) {
        badge.messagesByChannel[msg.channelId] = (badge.messagesByChannel[msg.channelId] ?? 0) + 1;
      }
    }

    final myUserId = localData.userId;

    String receivedIds = '';
    String sentIds = '';
    String unseenInvitationIds = '';

    if (myUserId.isNotEmpty == true && _inbox?.channels?.invitations?.isNotEmpty == true) {
      receivedIds = getInboxIdsFromInvitations(
        _inbox?.channels?.invitations?.where((inv) => inv.createdBy != myUserId).toList(),
      );
      sentIds = getInboxIdsFromInvitations(
        _inbox?.channels?.invitations?.where((inv) => inv.createdBy == myUserId).toList(),
      );
      badge.channelInvitations =
          _inbox!.channels!.invitations!
              .where((inv) => inv.createdBy != myUserId && inv.readByRecipientAt == null)
              .length;
    }

    // If the invitation provider has already pulled some data from the server
    // (revision > 0) we check whether that data needs updating:
    // if (invitationsProvider.revision > 0) {
    if (invitationsProvider.receivedInvitationIds.isEmpty ||
        receivedIds != invitationsProvider.receivedInvitationIds) {
      await invitationsProvider.loadMyInvitations(sent: false);
    }

    if (invitationsProvider.sentInvitationIds.isEmpty ||
        sentIds != invitationsProvider.sentInvitationIds) {
      await invitationsProvider.loadMyInvitations(sent: true);
    }

    if (invitationsProvider.unseenInvitationIds.isEmpty ||
        unseenInvitationIds != invitationsProvider.unseenInvitationIds) {
      await invitationsProvider.loadUnseenInvitations();
    }
    // }

    // If the channel provider has already pulled some data from the server
    // (revision > 0) we check whether that data needs updating:
    if (channelsProvider.revision > 0) {
      final List<String>? channelIds =
          _inbox?.channels?.latestMessages?.map((msg) => msg.channelId).toList();
      Function deepEq = const DeepCollectionEquality.unordered().equals;
      if (!deepEq(channelIds, channelsProvider.channelIds)) {
        channelsProvider.clearChannels();
        await channelsProvider.loadMyChannels();
      }
    }

    if (_subscription == null) {
      _subscribeToInbox();
    }

    _asyncState = AsyncState.ready;

    if (hasListeners) {
      notifyListeners();
    }

    return LoadObjectResult<UserInbox>(object: _inbox);
  }

  // -----------------------------------------------------------------------------------------------
  // Subscriptions
  void _subscribeToInbox() {
    if (_inboxId?.isNotEmpty != true) {
      DebugLogger.error('InboxProvider.subscribeToInbox called without inbox ID.');
      return;
    }

    var stream = client.subscribe<Subscription$ObjectChanged>(
      SubscriptionOptions(
        document: documentNodeSubscriptionObjectChanged,
        variables:
            Variables$Subscription$ObjectChanged(
              modelType: Enum$ModelType.UserInbox,
              objectId: _inboxId!,
            ).toJson(),
        fetchPolicy: FetchPolicy.noCache,
      ),
    );

    _subscription?.cancel();

    _subscription = stream.listen((QueryResult<Subscription$ObjectChanged> queryResult) async {
      if (queryResult.hasException) {
        CrashHandler.logCrashReport(
          'Subscription for Inbox Id ($_inboxId!)'
          'encountered an error: ${queryResult.exception}',
        );
        return;
      }

      if (queryResult.isLoading) {
        // Data is not ready, return and check again on the next cycle.
        return;
      }

      ObjectChangedEvent? event =
          queryResult.data != null
              ? Subscription$ObjectChanged.fromJson(queryResult.data!).objectChanged
              : null;

      if (event == null) {
        CrashHandler.logCrashReport('Received empty event for Inbox $_inboxId!)');
        return;
      }

      if (event.modelType != Enum$ModelType.UserInbox) {
        CrashHandler.logCrashReport('Received event for wrong model type: ${event.modelType}');
        return;
      }

      DebugLogger.debug('InboxProvider: objectChanged received; will pull new inbox.');

      loadInbox();
    });
  }

  (String?, bool) getChannelIdForUser(String userId) {
    final channel = _inbox?.channels?.latestMessages?.firstWhereOrNull(
      (latestMessage) => latestMessage.userIds?.contains(userId) == true,
    );
    return (channel?.channelId, channel?.isArchived ?? false);
  }

  (String?, bool) getArchivedChannelIdForUser(String userId) {
    final channel = inbox?.channels?.latestArchivedMessages?.firstWhereOrNull(
      (latestArchivedMessage) => latestArchivedMessage.userIds?.contains(userId) == true,
    );
    return (channel?.channelId, true);
  }

  Future<(String?, bool)> getOneOnOneChannelIdForUser(String userId) async {
    (String?, bool) channelData = getChannelIdForUser(userId);
    if (channelData.$1 != null) {
      return channelData;
    }
    channelData = getArchivedChannelIdForUser(userId);
    if (channelData.$1 != null) {
      return channelData;
    }

    // If all channels have been loaded, return null
    if (_inbox?.channels?.channelsExceedMaxCount == false) return (null, false);

    // If no channelId is found, use the call for 1:1 channels
    OperationResult<Channel?> result = await channelsProvider.findOneOnOneChannelForUser(userId);

    return (result.response?.id, result.response?.isArchivedForMe ?? false);
  }

  String? getSentInvitationIdForUser(String userId) {
    return _inbox?.channels?.pendingInvitations
        ?.firstWhereOrNull(
          (invitation) =>
              invitation.createdBy == userProvider.myUser?.id && invitation.recipientId == userId,
        )
        ?.id;
  }

  String? getReceivedInvitationIdForUser(String userId) {
    return _inbox?.channels?.invitations
        ?.firstWhereOrNull(
          (invitation) =>
              invitation.createdBy == userId &&
              invitation.recipientId == userProvider.myUser?.id &&
              invitation.status == Enum$ChannelInvitationStatus.created,
        )
        ?.id;
  }
}
