import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/__generated/schema/operations_user.graphql.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/flavor_config.dart';
import 'package:mm_flutter_app/services/graphql/providers/user_provider.dart';
import 'package:mm_flutter_app/utilities/errors/error_handler.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';
import '../../../__generated/schema/operations_vts.graphql.dart';
import '../../../__generated/schema/schema.graphql.dart';
import 'base/base_provider.dart';
import 'base/operation_result.dart';

typedef Training = Query$FindTrainingsForMe$findTrainingsForMe;
typedef IndividualTraining = Query$FindTrainingById$findTrainingById;
typedef TrainingContentPage = Query$FindTrainingById$findTrainingById$trainingContentPages;
typedef TrainingSession = Query$FindTrainingSessionsForMe$findTrainingSessionsForMe;

class VtsProvider extends BaseProvider with ChangeNotifier {
  VtsProvider({required super.client});

  List<Training> _myTrainings = [];

  List<Training> get myTrainings => _myTrainings;

  // -----------------------------------------------------------------------------------------------
  // Queries

  Future<OperationResult<IndividualTraining>> findTrainingById({required String trainingId}) async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindTrainingById,
        fetchPolicy: FetchPolicy.networkOnly,
        variables: Variables$Query$FindTrainingById(trainingId: trainingId).toJson(),
      ),
    );
    return OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindTrainingById.fromJson(queryResult.data!).findTrainingById
              : null,
    );
  }

  Future<OperationResult<List<TrainingSession>>> findTrainingSessionsForMe({
    required String trainingId,
  }) async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindTrainingSessionsForMe,
        fetchPolicy: FetchPolicy.networkOnly,
        variables:
            Variables$Query$FindTrainingSessionsForMe(
              trainingId: trainingId,
              options: Input$FindObjectsOptions(),
            ).toJson(),
      ),
    );
    return OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindTrainingSessionsForMe.fromJson(
                queryResult.data!,
              ).findTrainingSessionsForMe
              : null,
    );
  }

  Future<List<Training>> findTrainingsForMe() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindTrainingsForMe,
        fetchPolicy: FetchPolicy.networkOnly,
        variables: Variables$Query$FindTrainingsForMe(options: Input$FindObjectsOptions()).toJson(),
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindTrainingsForMe.fromJson(queryResult.data!).findTrainingsForMe
              : null,
    );
    _myTrainings = result.response ?? [];
    notifyListeners();
    return result.response ?? [];
  }

  // UI Logic //
  int _currentIndexInProgress = 0;
  int _currentIndexRecommended = 0;
  int _currentIndexCompleted = 0;
  List<int> tagsIndexList = [];

  int getCurrentIndex(TrainingStatus type, {int index = 0}) {
    switch (type) {
      case TrainingStatus.inProgress:
        return _currentIndexInProgress;
      case TrainingStatus.notStarted:
        return _currentIndexRecommended;
      case TrainingStatus.passed || TrainingStatus.failed:
        return _currentIndexCompleted;
      case TrainingStatus.tags:
        return tagsIndexList[index];
    }
  }

  resetCurrentInitialIndex() {
    _currentIndexInProgress = 0;
    _currentIndexRecommended = 0;
    _currentIndexCompleted = 0;
  }

  void updateIndex(TrainingStatus type, int cardCount, bool isNext, {int index = 0}) {
    switch (type) {
      case TrainingStatus.inProgress:
        _currentIndexInProgress =
            isNext ? _currentIndexInProgress + cardCount : _currentIndexInProgress - cardCount;
        break;
      case TrainingStatus.notStarted:
        _currentIndexRecommended =
            isNext ? _currentIndexRecommended + cardCount : _currentIndexRecommended - cardCount;
        break;
      case TrainingStatus.passed || TrainingStatus.failed:
        _currentIndexCompleted =
            isNext ? _currentIndexCompleted + cardCount : _currentIndexCompleted - cardCount;
        break;
      case TrainingStatus.tags:
        tagsIndexList[index] =
            isNext ? tagsIndexList[index] + cardCount : tagsIndexList[index] - cardCount;
        break;
    }
    notifyListeners();
  }

  Map<String, List<Training>> getResourceTags() {
    // TODO - improve code, performance
    final Map<String, List<Training>> groupedTrainings = {};
    for (final training in myTrainings) {
      if (training.tags != null) {
        for (final tag in training.tags!) {
          if (tag.startsWith('resourceGroup:')) {
            if (!groupedTrainings.containsKey(tag)) {
              groupedTrainings[tag] = [];
            }
            groupedTrainings[tag]!.add(training);
          }
        }
      }
    }
    return groupedTrainings;
  }
  ////////////////

  void sortStriveResourceGroups(List<String> resourceTags) {
    const priorities = {'Literasi': 1, 'Siber': 2, 'Digital': 3};

    int getPriority(String tag) {
      for (final entry in priorities.entries) {
        if (tag.contains(entry.key)) return entry.value;
      }
      return 999; //Assign default priority values (lower = higher priority)
    }

    resourceTags.sort((a, b) {
      final priorityA = getPriority(a);
      final priorityB = getPriority(b);

      if (priorityA != priorityB) {
        return priorityA - priorityB;
      }
      return a.compareTo(b);
    });
  }

  Future<String?> getOtpUrl(BuildContext context, String redirectUrlPath) async {
    debugPrint('Getting OTP URL');
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    OperationResult<Mutation$CreateOneTimeAuthTokenForMe> otpResponse =
        await userProvider.createOneTimeAuthTokenForMe();
    if (otpResponse.gqlQueryResult.hasException && context.mounted) {
      AppErrorHandler(context: context, exception: otpResponse.gqlQueryResult.exception);
    }
    String? otp = otpResponse.response?.createOneTimeAuthTokenForMe ?? '';
    if (otp.isEmpty) {
      debugPrint('Error: OTP is null or empty');
      return null;
    }

    String url = Identifiers.mm2TestVtsOtpUrl
        .replaceFirst('MM2_DOMAIN', FlavorConfig.instance.getMM2Domain())
        .replaceFirst('REDIRECT_URL_PATH', redirectUrlPath)
        // auth fields
        .replaceFirst('REPLACE_OTP', otp)
        .replaceFirst('REPLACE_USER_IDENT', userProvider.myUser?.id ?? '');

    debugPrint('OTP URL: $url');

    return url;
  }

  startTraining(
    BuildContext context,
    String trainingTitle,
    String? trainingMm2Id,
    String? backupTrainingPath, {
    bool? isTrainingPassedForMe,
    bool openBrowser = true,
    bool useWebview = true,
  }) async {
    if (trainingMm2Id == null || trainingMm2Id.isEmpty) {
      if (backupTrainingPath != null && backupTrainingPath.isEmpty) return;
    }

    String vtsViewDestination =
        isTrainingPassedForMe == true
            ? Identifiers.vtsReviewTrainingPath
            : Identifiers.vtsBeginTrainingPath;

    String path =
        trainingMm2Id != null && trainingMm2Id.isNotEmpty
            ? '$vtsViewDestination$trainingMm2Id'
            : backupTrainingPath ?? '';
    String? url = await getOtpUrl(context, path);

    if (!context.mounted) return;
    if (url == null) {
      AppErrorHandler(context: context, message: 'Error: OTP URL is null or empty');
      return;
    }

    if (openBrowser) {
      if (useWebview) {
        GoRouter.of(context).pushNamed(
          AppRoutes.webView.name,
          pathParameters: {RouteParams.targetUrl: url, RouteParams.webviewTitle: trainingTitle},
        );
      } else {
        await AppUtility.openLink(url);
      }
    }
  }

  downloadTrainingCertificate(
    BuildContext context,
    String? trainingMm2Id,
    String? trainingName,
    bool isWeb,
  ) async {
    debugPrint('Download certificate provider');
    if (trainingMm2Id == null || trainingMm2Id.isEmpty) {
      AppErrorHandler(context: context, message: 'Error: Training ID is null or empty');
      return;
    }

    String trainingCertificatePath = '${Identifiers.vtsDownloadCertificatePath}$trainingMm2Id';
    String? certificateDownloadUrl = await getOtpUrl(context, trainingCertificatePath);

    debugPrint('Certificate download url: $certificateDownloadUrl');

    if (certificateDownloadUrl == null) {
      if (!context.mounted) return;
      AppErrorHandler(context: context, message: 'Error: OTP URL is null or empty');
      return;
    }

    if (isWeb) {
      await AppUtility.openLink(certificateDownloadUrl);
    } else {
      String certName = Identifiers.certificateName.replaceAll(
        'REPLACE_TRAINING_NAME',
        trainingName != null && trainingName.isNotEmpty ? trainingName : trainingMm2Id,
      );
      if (context.mounted) {
        await AppUtility.downloadFileMobile(context, certificateDownloadUrl, certName, 'downloads');
      }
    }
  }

  List<MyLessonPlanModel> convertLessonsToMap(List<dynamic>? items) {
    if (items == null || items.isEmpty) return [];
    List<MyLessonPlanModel> result = [];
    for (var element in items) {
      final lessonPlan = MyLessonPlanModel(
        title: element.title,
        children: convertLessonsToMap(element.children),
      );
      result.add(lessonPlan);
    }
    return result;
  }

  static TrainingStatus getTrainingStatus(Training? training) {
    if (training == null || training.myLatestTrainingSession == null) {
      return TrainingStatus.notStarted;
    } else if (training.isTrainingPassedForMe == false ||
        training.myLatestTrainingSession?.isInProgress == true) {
      return TrainingStatus.inProgress;
    } else if (training.isTrainingCompletedForMe == true) {
      return training.isTrainingPassedForMe == true ? TrainingStatus.passed : TrainingStatus.failed;
    }
    return TrainingStatus.notStarted;
  }

  static TrainingStatus getIndividualTrainingStatus(IndividualTraining? training) {
    if (training == null || training.myLatestTrainingSession == null) {
      return TrainingStatus.notStarted;
    } else if (training.isTrainingPassedForMe == false ||
        training.myLatestTrainingSession?.isInProgress == true) {
      return TrainingStatus.inProgress;
    } else if (training.isTrainingCompletedForMe == true) {
      return training.isTrainingPassedForMe == true ? TrainingStatus.passed : TrainingStatus.failed;
    }
    return TrainingStatus.notStarted;
  }
}

class MyLessonPlanModel {
  String title;
  dynamic children;

  MyLessonPlanModel({required this.title, required this.children});
}
