import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:graphql_flutter/graphql_flutter.dart';

import '../../../__generated/schema/operations_group.graphql.dart';
import 'base/base_provider.dart';

typedef Group = Query$FindGroupByIdent$findGroupByIdent;

class GroupProvider extends BaseProvider with ChangeNotifier {
  final List<Group> _groups = [];

  List<Group> get groups => (_groups);

  GroupProvider({required super.client}) {
    debugPrint('GroupProvider initialized');
  }

  Future<Group?> getGroup(String ident) {
    final group = _groups.firstWhereOrNull((group) => group.ident == ident);

    if (group != null) {
      return Future.value(group);
    }

    return _fetchGroupByIdent(groupIdent: ident);
  }

  Future<Group?> _fetchGroupByIdent({
    required String groupIdent,
    FetchPolicy fetchPolicy = FetchPolicy.cacheFirst,
  }) async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindGroupByIdent,
        fetchPolicy: fetchPolicy,
        variables: Variables$Query$FindGroupByIdent(groupIdent: groupIdent).toJson(),
      ),
    );

    if (queryResult.data == null) {
      // Handle the case where the query failed
      if (queryResult.hasException) {
        debugPrint(
          'Error fetching group by ident: $groupIdent, ${queryResult.exception.toString()}',
        );
      } else {
        debugPrint('No data returned for group ident: $groupIdent');
      }
      return null;
    }

    final group = Query$FindGroupByIdent.fromJson(queryResult.data!).findGroupByIdent;

    if (group == null) {
      debugPrint('No group found for ident: $groupIdent');
      return null;
    }

    if (!_groups.any((group) => group.ident == groupIdent)) {
      _groups.add(group);
    } else {
      // replace the existing group with the new one
      final index = _groups.indexWhere((g) => g.ident == groupIdent);
      if (index != -1) {
        _groups[index] = group;
      }
    }

    return group;
  }

  clear() {
    _groups.clear();
  }
}
