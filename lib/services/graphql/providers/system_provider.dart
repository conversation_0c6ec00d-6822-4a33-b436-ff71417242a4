import 'dart:async';

import 'package:collection/collection.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/__generated/schema/operations_system.graphql.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/models/local_data_model.dart';

import '../../../constants/constants.dart';
import '../../../utilities/debug_logger.dart';
import 'base/base_provider.dart';
import 'base/operation_result.dart';

abstract mixin class SystemProviderListener {
  String get systemProviderListenerName;
  void onFinishedServiceRequest(final ServiceRequest serviceRequest) {}
}

class SystemProvider extends BaseProvider {
  final LocalDataModel localData;
  final List<SystemProviderListener> _providerListeners = [];

  SystemProvider({required super.client, required this.localData});

  void addSystemProviderListener(SystemProviderListener listener) {
    final existingListener = _providerListeners.firstWhereOrNull(
      (l) => l.systemProviderListenerName == listener.systemProviderListenerName,
    );
    if (existingListener != null) {
      // This listener has already been registered
      return;
    }
    _providerListeners.add(listener);
  }

  void removeSystemProviderListener(String name) {
    final existingListener = _providerListeners.firstWhereOrNull(
      (l) => l.systemProviderListenerName == name,
    );
    if (existingListener != null) {
      _providerListeners.remove(existingListener);
    }
  }

  Future<LoadObjectResult<ServiceRequest>> loadServiceRequest({
    required String serviceRequestId,
    LoadObjectOptionsForServiceRequest? options,
  }) {
    DebugLogger.debug('SystemProvider.loadServiceRequest called.');
    final completer = options?.completer ?? Completer<LoadObjectResult<ServiceRequest>>();
    final FetchPolicy fetchPolicy = options?.fetchPolicy ?? FetchPolicy.networkOnly;

    if (serviceRequestId.isNotEmpty != true) {
      DebugLogger.error('SystemProvider.loadServiceRequest no serviceRequestId given.');
      completer.complete(
        LoadObjectResult<ServiceRequest>(
          errorCode: Enum$ErrorCode.systemError,
          diagnosticsMessage: 'serviceRequestId not given',
        ),
      );
      return completer.future;
    }

    int delayMs = 0;
    if (options?.pollingConfig != null) {
      delayMs =
          options?.pollingConfig?.curPollIndex == 0
              ? options!.pollingConfig!.initialDelayMs
              : options!.pollingConfig!.initialDelayMs;
    }
    Future.delayed(Duration(milliseconds: delayMs), () {
      asyncQuery<Query$FindServiceRequestById>(
            queryOptions: QueryOptions<Query$FindServiceRequestById>(
              document: documentNodeQueryFindServiceRequestById,
              fetchPolicy: fetchPolicy,
              variables:
                  Variables$Query$FindServiceRequestById(
                    serviceRequestId: serviceRequestId,
                  ).toJson(),
            ),
          )
          .then((QueryResult<Query$FindServiceRequestById> queryResult) {
            final operationResult = OperationResult<Query$FindServiceRequestById>(
              gqlQueryResult: queryResult,
              response:
                  queryResult.data != null
                      ? Query$FindServiceRequestById.fromJson(queryResult.data!)
                      : null,
            );
            final serviceRequest = operationResult.response?.findServiceRequestById;

            if (operationResult.gqlQueryResult.hasException || serviceRequest == null) {
              DebugLogger.error('SystemProvider.loadServiceRequest: error.');
              completer.complete(
                LoadObjectResult<ServiceRequest>(
                  object: operationResult.response?.findServiceRequestById,
                  errorCode: Enum$ErrorCode.systemError,
                  operationException: operationResult.gqlQueryResult.exception,
                  diagnosticsMessage: operationResult.gqlQueryResult.toString(),
                ),
              );
              return;
            }

            if (
            // No options or polling config given, so we don't poll:
            options == null ||
                options.pollingConfig == null ||
                (
                // Max polling count reached:
                options.pollingConfig != null &&
                    options.pollingConfig!.maxPollCount > 0 &&
                    options.pollingConfig!.curPollIndex >= options.pollingConfig!.maxPollCount) ||
                (
                // The retrieved service request is marked as finished:
                options.waitForFinished == true &&
                    (serviceRequest.result == Enum$ServiceRequestResult.ok ||
                        serviceRequest.result == Enum$ServiceRequestResult.error)) ||
                (
                // The retrieved service request is the expected state:
                options.pollingConfig?.isUpdatedFunc != null &&
                    options.pollingConfig!.isUpdatedFunc!.call(serviceRequest))) {
              if (serviceRequest.result == Enum$ServiceRequestResult.ok ||
                  serviceRequest.result == Enum$ServiceRequestResult.error) {
                if (_providerListeners.isNotEmpty) {
                  for (SystemProviderListener listener in _providerListeners) {
                    listener.onFinishedServiceRequest(serviceRequest);
                  }
                }
              }
              DebugLogger.debug('SystemProvider.loadServiceRequest: done.');
              completer.complete(LoadObjectResult<ServiceRequest>(object: serviceRequest));
              return;
            }

            // Next poll:
            DebugLogger.debug('SystemProvider.loadServiceRequest: will poll again.');
            options.pollingConfig!.curPollIndex++;
            options.completer = completer;
            loadServiceRequest(serviceRequestId: serviceRequestId, options: options);
          })
          .catchError((error) {
            completer.complete(
              LoadObjectResult<ServiceRequest>(
                errorCode: Enum$ErrorCode.systemError,
                diagnosticsMessage: error.toString(),
              ),
            );
          });
    });

    return completer.future;
  }
}
