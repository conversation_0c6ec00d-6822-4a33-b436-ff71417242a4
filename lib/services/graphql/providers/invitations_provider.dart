import 'package:flutter/cupertino.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/services/graphql/providers/channels_provider.dart';
import 'package:provider/provider.dart';

import '../../../__generated/schema/operations_invitation.graphql.dart';
import '../../../__generated/schema/schema.graphql.dart';
import 'base/base_provider.dart';
import 'base/operation_result.dart';

typedef ChannelInvitationById = Query$FindChannelInvitationById$findChannelInvitationById;
typedef CreatedChannelInvitation = Mutation$CreateChannelInvitation$createChannelInvitation;
typedef MyChannelInvitation = Query$MyChannelInvitations$myChannelInvitations;

String getIdsFromInvitations(List<MyChannelInvitation>? invitations) {
  if (invitations?.isNotEmpty != true) {
    return '';
  }
  final ids = invitations!.map((i) => i.id).toList();
  ids.sort();
  return ids.join();
}

class InvitationsProvider extends BaseProvider with ChangeNotifier {
  ChannelsProvider channelsProvider;

  List<MyChannelInvitation> _receivedInvitations = List.empty(growable: true);
  List<MyChannelInvitation> _sentInvitations = List.empty(growable: true);
  List<MyChannelInvitation> _unseenInvitations = List.empty(growable: true);
  String _receivedInvitationIds = '';
  String _sentInvitationIds = '';
  String _unseenInvitationIds = '';
  AsyncState _asyncState = AsyncState.ready;
  AsyncState get asyncState => _asyncState;
  AsyncState _asyncStateForUnseenInvites = AsyncState.ready;
  AsyncState get asyncStateForUnseenInvites => _asyncStateForUnseenInvites;
  bool endOfResultsSent = false;
  bool endOfResultsReceived = false;
  final int maxRevisionLimit = 10;

  // This indicates whether the provider has every pulled any data and might need to be
  // updated after an event.
  int _revision = 0;
  int get revision => _revision;

  List<MyChannelInvitation> get receivedInvitations => _receivedInvitations;
  List<MyChannelInvitation> get sentInvitations => _sentInvitations;
  List<MyChannelInvitation> get unseenInvitations => _unseenInvitations;
  String get receivedInvitationIds => _receivedInvitationIds;
  String get sentInvitationIds => _sentInvitationIds;
  String get unseenInvitationIds => _unseenInvitationIds;

  InvitationsProvider({required super.client, required BuildContext context})
    : channelsProvider = Provider.of<ChannelsProvider>(context, listen: false);

  // -----------------------------------------------------------------------------------------------
  // Queries
  Future<LoadObjectResult<List<MyChannelInvitation>>> loadMyInvitations({
    required bool sent,
    FetchPolicy fetchPolicy = FetchPolicy.networkOnly,
  }) async {
    if (_asyncState == AsyncState.loading) {
      return LoadObjectResult<List<MyChannelInvitation>>(
        object: sent ? _sentInvitations : _receivedInvitations,
      );
    }
    _asyncState = AsyncState.loading;

    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryMyChannelInvitations,
        fetchPolicy: fetchPolicy,
        variables:
            Variables$Query$MyChannelInvitations(
              direction:
                  sent
                      ? Enum$ChannelInvitationDirection.sent
                      : Enum$ChannelInvitationDirection.received,
              onlyPending: true,
              options: Input$FindObjectsOptions(
                includeArchived: Enum$IncludeFilterOption.include,
                limit: Limits.invitationsPageSize,
                skip: sent ? _sentInvitations.length : _receivedInvitations.length,
              ),
            ).toJson(),
      ),
    );

    if (queryResult.hasException) {
      _asyncState = AsyncState.error;
      if (hasListeners) {
        notifyListeners();
      }

      return LoadObjectResult<List<MyChannelInvitation>>(
        errorCode: Enum$ErrorCode.systemError,
        diagnosticsMessage: queryResult.exception.toString(),
      );
    }

    final List<MyChannelInvitation> invitations =
        queryResult.data?.isNotEmpty == true
            ? Query$MyChannelInvitations.fromJson(queryResult.data!).myChannelInvitations
            : [];

    if (invitations.isEmpty || invitations.length < Limits.invitationsPageSize) {
      sent ? endOfResultsSent = true : endOfResultsReceived = true;
    }

    if (sent) {
      _sentInvitations.addAll(invitations);
      _sentInvitationIds = getIdsFromInvitations(_sentInvitations);
    } else {
      _receivedInvitations.addAll(invitations);
      _receivedInvitationIds = getIdsFromInvitations(_receivedInvitations);
    }

    _asyncState = AsyncState.ready;
    _revision++;

    if (hasListeners) {
      notifyListeners();
    }

    return LoadObjectResult<List<MyChannelInvitation>>(object: invitations);
  }

  loadUnseenInvitations({FetchPolicy fetchPolicy = FetchPolicy.networkOnly}) async {
    _asyncStateForUnseenInvites = AsyncState.loading;

    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryMyChannelInvitations,
        fetchPolicy: fetchPolicy,
        variables:
            Variables$Query$MyChannelInvitations(
              direction: Enum$ChannelInvitationDirection.received,
              onlyPending: true,
              onlyUnseen: true,
              options: Input$FindObjectsOptions(includeArchived: Enum$IncludeFilterOption.include),
            ).toJson(),
      ),
    );

    if (queryResult.hasException) {
      _asyncStateForUnseenInvites = AsyncState.error;
      if (hasListeners) {
        notifyListeners();
      }
    }

    final List<MyChannelInvitation> invitations =
        queryResult.data?.isNotEmpty == true
            ? Query$MyChannelInvitations.fromJson(queryResult.data!).myChannelInvitations
            : [];

    _unseenInvitations = invitations;
    _unseenInvitationIds = getIdsFromInvitations(_unseenInvitations);

    _asyncStateForUnseenInvites = AsyncState.ready;
    _revision++;

    if (hasListeners) {
      notifyListeners();
    }
  }

  Future<OperationResult<ChannelInvitationById>> findChannelInvitationById({
    required String channelInvitationId,
  }) async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindChannelInvitationById,
        fetchPolicy: FetchPolicy.networkOnly,
        variables:
            Variables$Query$FindChannelInvitationById(
              channelInvitationId: channelInvitationId,
            ).toJson(),
      ),
    );

    if (hasListeners) {
      notifyListeners();
    }

    return OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindChannelInvitationById.fromJson(
                queryResult.data!,
              ).findChannelInvitationById
              : null,
    );
  }

  // -----------------------------------------------------------------------------------------------
  // Mutations
  Future<OperationResult<String>> acceptChannelInvitation({
    required String channelInvitationId,
    required String senderUserId,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationAcceptChannelInvitation,
        fetchPolicy: FetchPolicy.noCache,
        variables:
            Variables$Mutation$AcceptChannelInvitation(
              channelInvitationId: channelInvitationId,
            ).toJson(),
      ),
    );
    final operationResult = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Mutation$AcceptChannelInvitation.fromJson(queryResult.data!).acceptChannelInvitation
              : null,
    );

    if (operationResult.gqlQueryResult.hasException) {
      return operationResult;
    }

    clearReceivedInvitation();
    await loadMyInvitations(sent: false);

    bool hasChannelLoaded = false;
    int counter = 0;
    while (!hasChannelLoaded && counter < maxRevisionLimit) {
      final response = await channelsProvider.loadMyChannels();
      if (response.hasError) {
        break;
      }
      final ChannelForUser? newChannel =
          channelsProvider.channels
              .where((e) => e.participants.any((p) => p.user.id == senderUserId))
              .firstOrNull;
      hasChannelLoaded = newChannel != null;
      counter++;
    }
    return operationResult;
  }

  Future<OperationResult<CreatedChannelInvitation>> createChannelInvitation({
    required String createdBy,
    required String recipientId,
    required String messageText,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationCreateChannelInvitation,
        fetchPolicy: FetchPolicy.noCache,
        variables:
            Variables$Mutation$CreateChannelInvitation(
              channelInvitationInput: Input$ChannelInvitationInput(
                createdBy: createdBy,
                recipientId: recipientId,
                messageText: messageText,
              ),
            ).toJson(),
      ),
    );
    final operationResult = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Mutation$CreateChannelInvitation.fromJson(queryResult.data!).createChannelInvitation
              : null,
    );

    if (operationResult.gqlQueryResult.hasException) {
      return operationResult;
    }
    clearSentInvitation();
    await loadMyInvitations(sent: true);

    return operationResult;
  }

  Future<OperationResult<String>> declineChannelInvitation({
    required String channelInvitationId,
    required Enum$DeclineChannelInvitationReasonTextId reasonTextId,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationDeclineChannelInvitation,
        fetchPolicy: FetchPolicy.noCache,
        variables:
            Variables$Mutation$DeclineChannelInvitation(
              channelInvitationId: channelInvitationId,
              reasonTextId: reasonTextId,
            ).toJson(),
      ),
    );
    final operationResult = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Mutation$DeclineChannelInvitation.fromJson(
                queryResult.data!,
              ).declineChannelInvitation
              : null,
    );

    if (operationResult.gqlQueryResult.hasException) {
      return operationResult;
    }

    clearReceivedInvitation();
    clearSentInvitation();
    await loadMyInvitations(sent: true);
    await loadMyInvitations(sent: false);

    return operationResult;
  }

  Future<OperationResult<String>> withdrawChannelInvitation({
    required String channelInvitationId,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationDeleteChannelInvitation,
        fetchPolicy: FetchPolicy.noCache,
        variables:
            Variables$Mutation$DeleteChannelInvitation(
              channelInvitationId: channelInvitationId,
              deletePhysically: true,
            ).toJson(),
      ),
    );
    final operationResult = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Mutation$DeleteChannelInvitation.fromJson(queryResult.data!).deleteChannelInvitation
              : null,
    );

    if (operationResult.gqlQueryResult.hasException) {
      return operationResult;
    }

    clearSentInvitation();
    await loadMyInvitations(sent: true);

    return operationResult;
  }

  Future<OperationResult<String>> markChannelInvitationAsSeenByMe({
    required String channelInvitationId,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationMarkChannelInvitationAsSeenByMe,
        fetchPolicy: FetchPolicy.noCache,
        variables:
            Variables$Mutation$MarkChannelInvitationAsSeenByMe(
              channelInvitationInput: Input$ChannelInvitationInput(
                id: channelInvitationId,
                readByRecipientAt: DateTime.now().toUtc(),
              ),
            ).toJson(),
      ),
    );
    final operationResult = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Mutation$MarkChannelInvitationAsSeenByMe.fromJson(
                queryResult.data!,
              ).updateChannelInvitation
              : null,
    );

    if (operationResult.gqlQueryResult.hasException) {
      return operationResult;
    }

    clearReceivedInvitation();
    clearUnseenInvitation();
    await loadMyInvitations(sent: false);
    await loadUnseenInvitations();

    return operationResult;
  }

  clear() {
    clearReceivedInvitation();
    clearSentInvitation();
    clearUnseenInvitation();
  }

  clearReceivedInvitation() {
    _receivedInvitations = List.empty(growable: true);
    _receivedInvitationIds = '';
    endOfResultsReceived = false;
  }

  clearSentInvitation() {
    _sentInvitations = List.empty(growable: true);
    _sentInvitationIds = '';
    endOfResultsSent = false;
  }

  clearUnseenInvitation() {
    _unseenInvitations = List.empty(growable: true);
    _unseenInvitationIds = '';
  }
}
