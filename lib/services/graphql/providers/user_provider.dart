import 'dart:async';

import 'package:collection/collection.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/__generated/schema/operations_user_inbox.graphql.dart';
import 'package:mm_flutter_app/constants/parts/micromentor_icons.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/models/explore_card_filters_model.dart';
import 'package:mm_flutter_app/models/local_data_model.dart';
import 'package:mm_flutter_app/models/locale_model.dart';
import 'package:mm_flutter_app/models/onboarding_model.dart';
import 'package:mm_flutter_app/services/graphql/providers/providers.dart';
import 'package:provider/provider.dart';

import '../../../__generated/schema/operations_user.graphql.dart';
import '../../../__generated/schema/operations_user_device.graphql.dart';
import '../../../__generated/schema/schema.graphql.dart';
import '../../../constants/constants.dart';
import '../../../utilities/debug_logger.dart';
import '../../firebase/firebase_auth_service.dart';
import '../../firebase/firebase_service.dart';
import 'base/base_provider.dart';
import 'base/operation_result.dart';

abstract mixin class UserProviderListener {
  String get userProviderListenerName;
  void onReceivedMyUser(final User myUser) {}
  void onSignedIn(final User myUser) {}
}

class UserProvider extends BaseProvider with ChangeNotifier, FirebaseAuthListener {
  final FirebaseService firebaseService;
  final LocalDataModel localData;
  final SystemProvider systemProvider;
  User? _myUser;
  UserCms? _userCms;
  bool _isPollingUser = false;
  final List<UserProviderListener> _providerListeners = [];

  UserProvider({
    required super.client,
    required this.firebaseService,
    required this.localData,
    required this.systemProvider,
  });

  void addUserProviderListener(UserProviderListener listener) {
    final existingListener = _providerListeners.firstWhereOrNull(
      (l) => l.userProviderListenerName == listener.userProviderListenerName,
    );
    if (existingListener != null) {
      // This listener has already been registered
      return;
    }
    _providerListeners.add(listener);
  }

  void removeUserProviderListener(String name) {
    final existingListener = _providerListeners.firstWhereOrNull(
      (l) => l.userProviderListenerName == name,
    );
    if (existingListener != null) {
      _providerListeners.remove(existingListener);
    }
  }

  Future<void> _clear(BuildContext? context, {String? pathName, bool navigateToPath = true}) async {
    _myUser = null;
    await client.resetStore(); // clears GraphQL API cache
    localData.clear();
    await firebaseService.firebaseAuthService.signOut();
    if (context != null && context.mounted) {
      Provider.of<ExploreCardFiltersModel>(context, listen: false).clearData();
      Provider.of<InboxProvider>(context, listen: false).clear();
      Provider.of<LocaleModel>(context, listen: false).clear();
      Provider.of<ChannelsProvider>(context, listen: false).clearChannels();
      Provider.of<InvitationsProvider>(context, listen: false).clear();
      Provider.of<ContentProvider>(context, listen: false).clear();
      Provider.of<OnboardingModel>(context, listen: false).setGroupIdent(GroupIdent.mentees);

      if (!navigateToPath) return;
      if ((pathName ?? '').trim().isNotEmpty) {
        GoRouter.of(context).go(pathName!);
      } else {
        GoRouter.of(context).goNamed(AppRoutes.welcome.name);
      }
    }
    notifyListeners();
  }

  User? get myUser => _myUser;

  bool get isAdmin {
    return _myUser?.roles.contains(Enum$UserRole.admin) ?? false;
  }

  bool get isImpersonating {
    return localData.adminUserId.isNotEmpty;
  }

  List<IdentityProviderConfig> getIdentityProviderConfigs({
    required bool includeOwn,
    required bool excludeInactive,
  }) {
    List<IdentityProviderConfig> list = [
      IdentityProviderConfig(
        provider: Enum$IdentityProvider.apple,
        label: AppLocale.current.identityProviderApple,
        iconData: MicromentorIcons.apple,
        isActive: true,
      ),
      IdentityProviderConfig(
        provider: Enum$IdentityProvider.facebook,
        label: AppLocale.current.identityProviderFacebook,
        iconAsset: Assets.identityProviderFacebookIcon,
        isActive: StaticAppFeatures.socialLogins,
      ),
      IdentityProviderConfig(
        provider: Enum$IdentityProvider.google,
        label: AppLocale.current.identityProviderGoogle,
        iconAsset: Assets.identityProviderGoogleIcon,
        isActive: StaticAppFeatures.socialLogins,
      ),
      IdentityProviderConfig(
        provider: Enum$IdentityProvider.linkedIn,
        label: AppLocale.current.identityProviderLinkedIn,
        iconAsset: Assets.identityProviderLinkedInIcon,
        isActive: false,
      ),
      IdentityProviderConfig(
        provider: Enum$IdentityProvider.telegram,
        label: AppLocale.current.identityProviderTelegram,
        iconAsset: Assets.identityProviderTelegramIcon,
        isActive: false,
      ),
      IdentityProviderConfig(
        provider: Enum$IdentityProvider.whatsApp,
        label: AppLocale.current.ssoWhatsapp,
        iconAsset: Assets.ssoWhatsAppIcon,
        isActive: false,
      ),
    ];

    if (excludeInactive) {
      list = list.where((ip) => ip.isActive).toList();
    }

    if (includeOwn) {
      list.add(
        IdentityProviderConfig(
          provider: Enum$IdentityProvider.own,
          label: AppLocale.current.identityProviderOwn,
          iconData: MicromentorIcons.emailOutline,
          isActive: true,
        ),
      );
    }
    return list;
  }

  Future<void> impersonateAnotherUser(String userId) async {
    if (_myUser == null) {
      return;
    }
    localData.adminUserId = _myUser!.id;
    localData.userId = userId;
    await loadUser(options: LoadObjectOptions<User>(fetchPolicy: FetchPolicy.networkOnly));
  }

  Future<void> clearImpersonation() async {
    if (_myUser == null || !isImpersonating) {
      return;
    }
    localData.userId = localData.adminUserId;
    localData.adminUserId = '';
    await loadUser(options: LoadObjectOptions<User>(fetchPolicy: FetchPolicy.networkOnly));
  }

  Future<OperationResult<Mutation$BlockUserForMe>> blockUser({
    required String userId,
    String? notes,
    String? reasonTextId,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationBlockUserForMe,
        fetchPolicy: FetchPolicy.networkOnly,
        variables:
            Variables$Mutation$BlockUserForMe(
              userId: userId,
              notes: notes,
              reasonTextId: reasonTextId,
            ).toJson(),
      ),
    );

    if (queryResult.data != null) {
      final blockUser = Mutation$BlockUserForMe.fromJson(queryResult.data!);
      return OperationResult(gqlQueryResult: queryResult, response: blockUser);
    } else {
      // Handling the case where queryResult.data is null
      return OperationResult(gqlQueryResult: queryResult, response: null);
    }
  }

  Future<bool> isUserIdentAvailable({
    required String text,
    required Enum$UserIdentType identType,
  }) async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryIsUserIdentAvailable,
        fetchPolicy: FetchPolicy.networkOnly,
        variables: Variables$Query$IsUserIdentAvailable(identType: identType, ident: text).toJson(),
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null ? Query$IsUserIdentAvailable.fromJson(queryResult.data!) : null,
    );

    return result.response?.isUserIdentAvailable == true;
  }

  // Unblock User
  Future<OperationResult<Mutation$UnblockUserForMe>> unblockUser({required String userId}) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationUnblockUserForMe,
        fetchPolicy: FetchPolicy.networkOnly,
        variables: Variables$Mutation$UnblockUserForMe(userId: userId).toJson(),
      ),
    );

    if (queryResult.data != null) {
      final unblockUser = Mutation$UnblockUserForMe.fromJson(queryResult.data!);
      return OperationResult(gqlQueryResult: queryResult, response: unblockUser);
    } else {
      // Handling the case where queryResult.data is null
      return OperationResult(gqlQueryResult: queryResult, response: null);
    }
  }

  Future<LoadObjectResult<User>> loadUser({String? userId, LoadObjectOptions<User>? options}) {
    DebugLogger.debug('UserProvider.loadUser called. userId=$userId');
    final completer = options?.completer ?? Completer<LoadObjectResult<User>>();
    final FetchPolicy fetchPolicy = options?.fetchPolicy ?? FetchPolicy.networkOnly;

    if ((userId ?? '').isEmpty == true) {
      userId = localData.userId;
      if (userId.isEmpty) {
        final r = LoadObjectResult<User>(
          errorCode: Enum$ErrorCode.systemError,
          diagnosticsMessage: 'userId not given',
        );
        completer.complete(r);
        return completer.future;
      }
    }

    int delayMs = 0;
    if (options?.pollingConfig != null) {
      delayMs =
          options?.pollingConfig?.curPollIndex == 0
              ? options!.pollingConfig!.initialDelayMs
              : options!.pollingConfig!.initialDelayMs;
    }
    // DebugLogger.debug('UserProvider.loadUser: delaying $delayMs ms.');
    Future.delayed(Duration(milliseconds: delayMs), () {
      findUserById(userId: userId, fetchPolicy: fetchPolicy)
          .then((operationResult) {
            if (operationResult.gqlQueryResult.hasException) {
              completer.complete(
                LoadObjectResult<User>(
                  errorCode: Enum$ErrorCode.systemError,
                  diagnosticsMessage: operationResult.gqlQueryResult.toString(),
                  operationException: operationResult.gqlQueryResult.exception,
                ),
              );
              if (_isPollingUser &&
                  options?.pollingConfig != null &&
                  options!.pollingConfig!.curPollIndex > 0) {
                _isPollingUser = false;
              }
              return;
            }

            if (
            // UserProvider only allows 1 polling at a time:
            (_isPollingUser && options?.pollingConfig?.curPollIndex == 0) ||
                // No options or polling config given, so we don't poll:
                options == null ||
                options.pollingConfig == null ||
                (
                // Max polling count reached:
                options.pollingConfig != null &&
                    options.pollingConfig!.maxPollCount > 0 &&
                    options.pollingConfig!.curPollIndex >= options.pollingConfig!.maxPollCount) ||
                ((
                    // The retrieved user's updatedAt has been updated:
                    options.pollingConfig != null &&
                        options.pollingConfig!.isUpdatedFunc == null &&
                        operationResult.response != null &&
                        options.pollingConfig!.prevUpdatedAt!.isBefore(
                          operationResult.response!.updatedAt!,
                        )) ||
                    (
                    // The retrieved user is the expected state:
                    operationResult.response != null &&
                        options.pollingConfig?.isUpdatedFunc != null &&
                        options.pollingConfig!.isUpdatedFunc!.call(operationResult.response!)))) {
              DebugLogger.debug('UserProvider.loadUser: done.');
              _isPollingUser = false;
              completer.complete(LoadObjectResult<User>(object: operationResult.response));
              return;
            }

            // Next poll:
            DebugLogger.debug('UserProvider.loadUser: will poll again.');
            _isPollingUser = true;
            options.pollingConfig!.curPollIndex++;
            options.completer = completer;
            loadUser(userId: userId, options: options);
          })
          .catchError((error) {
            completer.complete(
              LoadObjectResult<User>(
                errorCode: Enum$ErrorCode.systemError,
                diagnosticsMessage: error.toString(),
              ),
            );
          });
    });

    return completer.future;
  }

  Future<OperationResult<User>> findUserById({
    String? userId,
    bool logFailures = true,
    FetchPolicy fetchPolicy = FetchPolicy.networkOnly,
  }) async {
    final bool isMyUser = userId?.isNotEmpty != true || userId == localData.userId;
    if (userId?.isNotEmpty != true) {
      userId = localData.userId;
    }

    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindUserById,
        fetchPolicy: fetchPolicy,
        variables: Variables$Query$FindUserById(userId: userId!).toJson(),
      ),
      logFailures: logFailures,
    );
    final operationResult = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindUserById.fromJson(queryResult.data!).findUserById
              : null,
    );

    if (operationResult.response?.avatarUrl == '') {
      operationResult.response = operationResult.response!.copyWith(avatarUrl: null);
    }

    if (isMyUser) {
      if (operationResult.response != null) {
        _myUser = operationResult.response!;

        if (_myUser != null && _providerListeners.isNotEmpty) {
          for (UserProviderListener listener in _providerListeners) {
            listener.onReceivedMyUser(_myUser!);
          }
        }
      } else {
        _myUser = null;
      }
    }
    notifyListeners();
    return operationResult;
  }

  Future<OperationResult<Query$FindUserSearch$findUserSearchById>> getUserSearch({
    required String userSearchId,
    bool fetchFromNetworkOnly = false,
  }) async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindUserSearch,
        fetchPolicy: fetchFromNetworkOnly ? FetchPolicy.networkOnly : FetchPolicy.cacheFirst,
        variables: Variables$Query$FindUserSearch(userSearchId: userSearchId).toJson(),
      ),
    );
    return OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindUserSearch.fromJson(queryResult.data!).findUserSearchById
              : null,
    );
  }

  Future<OperationResult<Query$FindUsers>> findUsers({
    required Input$UserListFilter filter,
    FetchPolicy fetchPolicy = FetchPolicy.networkOnly,
  }) async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindUsers,
        fetchPolicy: fetchPolicy,
        variables: Variables$Query$FindUsers(filter: filter).toJson(),
      ),
    );

    return OperationResult(
      gqlQueryResult: queryResult,
      response: queryResult.data != null ? Query$FindUsers.fromJson(queryResult.data!) : null,
    );
  }

  Future<List<BlockedUser>> findMyBlockedUsers() async {
    final QueryResult<Query$GetMyBlockedUsers> queryResult =
        await asyncQuery<Query$GetMyBlockedUsers>(
          queryOptions: QueryOptions(
            document: documentNodeQueryGetMyBlockedUsers,
            fetchPolicy: FetchPolicy.networkOnly,
          ),
        );

    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null ? Query$GetMyBlockedUsers.fromJson(queryResult.data!) : null,
    );

    return result.response?.getMyBlockedUsers ?? [];
  }

  Future<OperationResult<List<Query$FindUserSearchResults$findUserSearchResults>>>
  findUserSearchResults({
    required String userSearchId,
    required Input$FindObjectsOptions optionsInput,
    bool fetchFromNetworkOnly = false,
  }) async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindUserSearchResults,
        fetchPolicy: fetchFromNetworkOnly ? FetchPolicy.networkOnly : FetchPolicy.cacheFirst,
        variables:
            Variables$Query$FindUserSearchResults(
              userSearchId: userSearchId,
              options: optionsInput,
            ).toJson(),
      ),
    );
    return OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindUserSearchResults.fromJson(queryResult.data!).findUserSearchResults
              : null,
    );
  }

  // Mutations
  Future<OperationResult<void>> endMySession() async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationEndMySession,
        fetchPolicy: FetchPolicy.noCache,
        variables: Variables$Mutation$EndMySession(deviceUuid: localData.deviceUuid).toJson(),
      ),
    );
    notifyListeners();
    return OperationResult(gqlQueryResult: queryResult, response: null);
  }

  Future<OperationResult<Mutation$CreateUserSearch$createUserSearch>> createUserSearch({
    required Input$UserSearchInput searchInput,
    bool fetchFromNetworkOnly = false,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationCreateUserSearch,
        fetchPolicy:
            fetchFromNetworkOnly
                // use cache to prevent executing the same searches multiple times
                ? FetchPolicy.networkOnly
                : FetchPolicy.cacheFirst,
        variables: Variables$Mutation$CreateUserSearch(input: searchInput).toJson(),
      ),
    );
    return OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Mutation$CreateUserSearch.fromJson(queryResult.data!).createUserSearch
              : null,
    );
  }

  //Refresh Notifier: Just get notified to visible consumers
  refreshState() {
    notifyListeners();
  }

  //Report User Mutation
  Future<OperationResult<Mutation$ReportUser>> reportUser({
    required String userId,
    required Enum$ReportUserReasonTextId reasonTextId,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationReportUser,
        fetchPolicy: FetchPolicy.networkOnly,
        variables:
            Variables$Mutation$ReportUser(
              input: Input$ReportUserInput(userId: userId, reasonTextId: reasonTextId),
            ).toJson(),
      ),
    );

    if (queryResult.data == null) {
      debugPrint('Error: GraphQL mutation reportUser did not respond with valid data.');
      // todo: Handling the case where queryResult.data is null
      return OperationResult(gqlQueryResult: queryResult, response: null);
    }

    final reportUser = Mutation$ReportUser.fromJson(queryResult.data!);
    return OperationResult(gqlQueryResult: queryResult, response: reportUser);
  }

  Future<OperationResult<Mutation$SignInUser$signInUser>> signInUser({
    required String email,
    required String password,
  }) async {
    localData.identityProvider = Enum$IdentityProvider.own;

    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationSignInUser,
        fetchPolicy: FetchPolicy.noCache,
        variables:
            Variables$Mutation$SignInUser(
              input: Input$SignInUserInput(
                ident: email,
                identType: Enum$UserIdentType.email,
                password: password,
                pushNotificationToken: localData.pushNotificationToken,
              ),
            ).toJson(),
      ),
    );

    final OperationResult<Mutation$SignInUser$signInUser> result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Mutation$SignInUser.fromJson(queryResult.data!).signInUser
              : null,
    );

    if (result.response != null) {
      localData.userId = result.response!.userId;
      localData.authToken = result.response!.authToken!;

      debugPrint('userId: ${result.response!.userId}');
      debugPrint('authToken: ${result.response!.authToken}');
    }

    return result;
  }

  Future<void> signOutUser(
    BuildContext context, {
    String? pathName,
    bool navigateToPath = true,
  }) async {
    await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationSignOutUser,
        fetchPolicy: FetchPolicy.noCache,
      ),
    );

    await _clear(
      context.mounted ? context : null,
      pathName: pathName,
      navigateToPath: navigateToPath,
    );
  }

  Future<void> clearData(BuildContext context) async {
    _myUser = null;
    await client.resetStore();
    localData.clear();
    await firebaseService.firebaseAuthService.signOut();
  }

  Future<void> deleteUserAccount(BuildContext context) async {
    if (_myUser?.id.isNotEmpty != true) {
      return;
    }
    await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationDeleteUser,
        fetchPolicy: FetchPolicy.noCache,
        variables:
            Variables$Mutation$DeleteUser(deletePhysically: true, userId: _myUser!.id).toJson(),
      ),
    );

    await _clear(context.mounted ? context : null);
  }

  Future<OperationResult<Mutation$SignUpUser$signUpUser>> signUpUser({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required bool optIntoNewsletter,
  }) async {
    localData.identityProvider = Enum$IdentityProvider.own;

    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationSignUpUser,
        fetchPolicy: FetchPolicy.noCache,
        variables:
            Variables$Mutation$SignUpUser(
              input: Input$SignUpUserInput(
                firstName: firstName,
                lastName: lastName,
                email: email,
                password: password,
                optIntoNewsletter: optIntoNewsletter,
                pushNotificationToken: localData.pushNotificationToken,
                trackId: localData.trackId,
              ),
            ).toJson(),
      ),
    );

    final OperationResult<Mutation$SignUpUser$signUpUser> result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Mutation$SignUpUser.fromJson(queryResult.data!).signUpUser
              : null,
    );

    if (result.response != null && !result.gqlQueryResult.hasException) {
      localData.userId = result.response!.userId;
      localData.authToken = result.response!.authToken!;
      // After a successful sign-up, we clear the trackId. This allows another user
      // to be tracked on this device.
      localData.clearTrackId();
    }

    return result;
  }

  Future<OperationResult<void>> startMySession({
    required String deviceUuid,
    String pushNotificationToken = '',
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationStartMySession,
        fetchPolicy: FetchPolicy.noCache,
        variables:
            Variables$Mutation$StartMySession(
              deviceUuid: deviceUuid,
              pushNotificationToken: pushNotificationToken,
            ).toJson(),
      ),
    );
    notifyListeners();
    return OperationResult(gqlQueryResult: queryResult, response: null);
  }

  Future<OperationResult<void>> updateAcademicExperience({
    required Input$AcademicExperienceInput input,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationUpdateAcademicExperience,
        fetchPolicy: FetchPolicy.noCache,
        variables: Variables$Mutation$UpdateAcademicExperience(input: input).toJson(),
      ),
    );
    notifyListeners();
    return OperationResult(gqlQueryResult: queryResult, response: null);
  }

  Future<OperationResult<void>> updateBusinessExperience({
    required Input$BusinessExperienceInput input,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationUpdateBusinessExperience,
        fetchPolicy: FetchPolicy.noCache,
        variables: Variables$Mutation$UpdateBusinessExperience(input: input).toJson(),
      ),
    );
    notifyListeners();
    return OperationResult(gqlQueryResult: queryResult, response: null);
  }

  Future<OperationResult<void>> updateCompany({required Input$CompanyInput input}) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationUpdateCompany,
        fetchPolicy: FetchPolicy.noCache,
        variables: Variables$Mutation$UpdateCompany(input: input).toJson(),
      ),
    );
    notifyListeners();
    return OperationResult(gqlQueryResult: queryResult, response: null);
  }

  Future<OperationResult<void>> updateIqlaaGroupMembership({
    required Input$IqlaaGroupMembershipInput input,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationUpdateIqlaaGroupMembership,
        fetchPolicy: FetchPolicy.noCache,
        variables: Variables$Mutation$UpdateIqlaaGroupMembership(input: input).toJson(),
      ),
    );
    notifyListeners();
    return OperationResult(gqlQueryResult: queryResult, response: null);
  }

  Future<OperationResult<void>> updateMenteesGroupMembership({
    required Input$MenteesGroupMembershipInput input,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationUpdateMenteesGroupMembership,
        fetchPolicy: FetchPolicy.noCache,
        variables: Variables$Mutation$UpdateMenteesGroupMembership(input: input).toJson(),
      ),
    );
    notifyListeners();
    return OperationResult(gqlQueryResult: queryResult, response: null);
  }

  Future<OperationResult<void>> updateMentorsGroupMembership({
    required Input$MentorsGroupMembershipInput input,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationUpdateMentorsGroupMembership,
        fetchPolicy: FetchPolicy.noCache,
        variables: Variables$Mutation$UpdateMentorsGroupMembership(input: input).toJson(),
      ),
    );
    notifyListeners();
    return OperationResult(gqlQueryResult: queryResult, response: null);
  }

  Future<OperationResult<void>> updateMastercardGroupMembership({
    required Input$MastercardGroupMembershipInput input,
  }) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationUpdateMastercardGroupMembership,
        fetchPolicy: FetchPolicy.noCache,
        variables: Variables$Mutation$UpdateMastercardGroupMembership(input: input).toJson(),
      ),
    );
    notifyListeners();
    return OperationResult(gqlQueryResult: queryResult, response: null);
  }

  Future<LoadObjectResult<User>> addUserToGroup({
    required String userId,
    GroupIdent? groupIdent,
    String? groupIdentName,
  }) async {
    Completer<LoadObjectResult<User>> completer = Completer<LoadObjectResult<User>>();

    if (userId.isEmpty) {
      completer.complete(
        LoadObjectResult<User>(
          errorCode: Enum$ErrorCode.systemError,
          diagnosticsMessage: 'no valid userId given',
        ),
      );
      return completer.future;
    }

    await asyncMutation<Mutation$AddUserToGroup>(
          mutationOptions: MutationOptions(
            document: documentNodeMutationAddUserToGroup,
            fetchPolicy: FetchPolicy.noCache,
            variables:
                Variables$Mutation$AddUserToGroup(
                  addUserToGroupUserId: userId,
                  groupIdent: groupIdentName ?? groupIdent?.name,
                  roles: [],
                ).toJson(),
          ),
        )
        .then((QueryResult<Mutation$AddUserToGroup> queryResult) {
          final operationResult = OperationResult<Mutation$AddUserToGroup>(
            gqlQueryResult: queryResult,
            response:
                queryResult.data != null
                    ? Mutation$AddUserToGroup.fromJson(queryResult.data!)
                    : null,
          );

          if (operationResult.gqlQueryResult.hasException) {
            DebugLogger.error('UserProvider.addUserToGroup: error');
            completer.complete(
              LoadObjectResult<User>(
                errorCode: Enum$ErrorCode.systemError,
                diagnosticsMessage: operationResult.gqlQueryResult.toString(),
                operationException: operationResult.gqlQueryResult.exception,
              ),
            );
            return;
          }
          final Mutation$AddUserToGroup$addUserToGroup? serviceRequest =
              operationResult.response?.addUserToGroup;
          final String? serviceRequestId = serviceRequest?.id;
          completer = loadUserWithServiceRequest(
            serviceRequestId: serviceRequestId,
            userId: localData.userId,
          );
        })
        .catchError((error) {
          completer.complete(
            LoadObjectResult<User>(
              errorCode: Enum$ErrorCode.systemError,
              diagnosticsMessage: error.toString(),
            ),
          );
        });
    return completer.future;
  }

  Future<OperationResult<void>> removeUserFromGroup({
    required GroupIdent groupIdent,
    required String userId,
  }) async {
    final QueryResult queryResult = await asyncMutation<Mutation$AddUserToGroup>(
      mutationOptions: MutationOptions(
        document: documentNodeMutationRemoveUserFromGroup,
        fetchPolicy: FetchPolicy.noCache,
        variables:
            Variables$Mutation$RemoveUserFromGroup(
              force: true,
              userId: userId,
              groupIdent: groupIdent.toString(),
            ).toJson(),
      ),
    );
    return OperationResult(gqlQueryResult: queryResult, response: null);
  }

  Future<LoadObjectResult<User>> updateUser({
    required Input$UserInput input,
    bool reloadUser = true,
    UpdateObjectOptions<User>? options,
  }) async {
    Completer<LoadObjectResult<User>> completer = Completer<LoadObjectResult<User>>();
    final String userId = input.id ?? localData.userId;

    if (userId.isEmpty) {
      completer.complete(
        LoadObjectResult<User>(
          errorCode: Enum$ErrorCode.systemError,
          diagnosticsMessage: 'no valid userId given',
        ),
      );
      return completer.future;
    }

    if (input.id?.isNotEmpty != true) {
      input = input.copyWith(id: userId);
    }

    await asyncMutation<Mutation$UpdateUser>(
          mutationOptions: MutationOptions(
            document: documentNodeMutationUpdateUser,
            fetchPolicy: FetchPolicy.noCache,
            variables: Variables$Mutation$UpdateUser(input: input).toJson(),
          ),
        )
        .then((QueryResult<Mutation$UpdateUser> queryResult) {
          final operationResult = OperationResult<Mutation$UpdateUser>(
            gqlQueryResult: queryResult,
            response:
                queryResult.data != null ? Mutation$UpdateUser.fromJson(queryResult.data!) : null,
          );

          if (operationResult.gqlQueryResult.hasException) {
            DebugLogger.error('UserProvider.updateUser: error');
            completer.complete(
              LoadObjectResult<User>(
                errorCode: Enum$ErrorCode.systemError,
                diagnosticsMessage: operationResult.gqlQueryResult.toString(),
                operationException: operationResult.gqlQueryResult.exception,
              ),
            );
            return;
          }

          if (!reloadUser) {
            DebugLogger.debug('UserProvider.updateUser: not reloading user.');
            completer.complete(LoadObjectResult<User>(object: null));
            notifyListeners();
            return;
          }

          completer = loadUserWithServiceRequest();
        })
        .catchError((error) {
          completer.complete(
            LoadObjectResult<User>(
              errorCode: Enum$ErrorCode.systemError,
              diagnosticsMessage: error.toString(),
            ),
          );
        });

    return completer.future;
  }

  loadUserWithServiceRequest({
    String? userId,
    String? serviceRequestId,
    UpdateObjectOptions<User>? options,
  }) {
    final completer = Completer<LoadObjectResult<User>>();

    if (options?.useServiceRequest == true || options?.loadServiceRequestOptions != null) {
      // Tracking using the service request.
      if (serviceRequestId?.isNotEmpty == true) {
        final o = options?.loadServiceRequestOptions ?? LoadObjectOptionsForServiceRequest();
        o.pollingConfig ??= PollingConfig<ServiceRequest>();
        systemProvider
            .loadServiceRequest(serviceRequestId: serviceRequestId!, options: o)
            .then((LoadObjectResult<ServiceRequest> loadServiceRequestResult) {
              // The service request is marked as finished. We can now load the user again.
              // If the service request contains an error, we are setting the result object's
              // error to that.
              final ServiceRequest? serviceRequest = loadServiceRequestResult.object;
              Enum$ErrorCode? errorCodeFromServiceRequest = serviceRequest?.errorCode;
              String? diagnosticsMessageFromServiceRequest = serviceRequest?.message;
              loadUser(userId: userId)
                  .then((LoadObjectResult<User> loadUserResult) {
                    completer.complete(
                      LoadObjectResult<User>(
                        object: loadUserResult.object,
                        errorCode:
                            errorCodeFromServiceRequest ??
                            loadServiceRequestResult.errorCode ??
                            loadUserResult.errorCode,
                        diagnosticsMessage:
                            diagnosticsMessageFromServiceRequest ??
                            loadServiceRequestResult.diagnosticsMessage ??
                            loadUserResult.diagnosticsMessage,
                        operationException:
                            loadServiceRequestResult.operationException ??
                            loadUserResult.operationException,
                        serviceRequest: loadServiceRequestResult.object,
                      ),
                    );
                    notifyListeners();
                  })
                  .catchError((error) {
                    completer.complete(
                      LoadObjectResult<User>(
                        errorCode: Enum$ErrorCode.systemError,
                        diagnosticsMessage: error.toString(),
                      ),
                    );
                  });
            })
            .catchError((error) {
              completer.complete(
                LoadObjectResult<User>(
                  errorCode: Enum$ErrorCode.systemError,
                  diagnosticsMessage: error.toString(),
                ),
              );
            });
      }
      return;
    }

    // Polling the user, not using the service request:
    loadUser(userId: userId, options: options?.loadObjectOptions)
        .then((LoadObjectResult<User> loadUserResult) {
          completer.complete(
            LoadObjectResult<User>(
              object: loadUserResult.object,
              errorCode: loadUserResult.errorCode,
              diagnosticsMessage: loadUserResult.diagnosticsMessage,
              operationException: loadUserResult.operationException,
            ),
          );
          notifyListeners();
        })
        .catchError((error) {
          completer.complete(
            LoadObjectResult<User>(
              errorCode: Enum$ErrorCode.systemError,
              diagnosticsMessage: error.toString(),
            ),
          );
        });
    return completer;
  }

  Future<OperationResult<void>> updateUserDevice({required Input$UserDeviceInput input}) async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationUpdateUserDevice,
        fetchPolicy: FetchPolicy.noCache,
        variables: Variables$Mutation$UpdateUserDevice(input: input).toJson(),
      ),
    );
    notifyListeners();
    return OperationResult(gqlQueryResult: queryResult, response: null);
  }

  Future<OperationResult<Mutation$SignInOauthUser$signInOauthUser>> signInOauthUser({
    String? firstName,
    String? lastName,
    String? displayName,
    String? email,
    bool? emailVerified,
    String? phoneNumber,
    bool? phoneNumberVerified,
    required Enum$IdentityProvider identityProvider,
    required Enum$FederatedIdentityProvider oauthFederatedProvider,
    required String oauthToken,
    String? oauthTokenId,
    String? oauthRefreshToken,
    String? oauthUserId,
    String? oauthDelegateUserId,
    String? profileUrl,
  }) async {
    localData.identityProvider = identityProvider;
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationSignInOauthUser,
        fetchPolicy: FetchPolicy.noCache,
        variables:
            Variables$Mutation$SignInOauthUser(
              input: Input$SignInOauthUserInput(
                firstName: firstName,
                lastName: lastName,
                displayName: displayName,
                email: email,
                emailVerified: emailVerified,
                phoneNumber: phoneNumber,
                phoneNumberVerified: phoneNumberVerified,
                oauthToken: oauthToken,
                oauthTokenId: oauthTokenId,
                oauthRefreshToken: oauthRefreshToken,
                oauthUserId: oauthUserId,
                oauthDelegateUserId: oauthDelegateUserId,
                oauthProfileUrl: profileUrl,
                identityProvider: identityProvider,
                oauthFederatedProvider: oauthFederatedProvider,
                pushNotificationToken: localData.pushNotificationToken,
                trackId: localData.trackId,
              ),
            ).toJson(),
      ),
    );

    final OperationResult<Mutation$SignInOauthUser$signInOauthUser> result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Mutation$SignInOauthUser.fromJson(queryResult.data!).signInOauthUser
              : null,
    );

    if (result.response != null &&
        result.response!.userId.isNotEmpty &&
        result.response!.authToken != null) {
      final userId = result.response!.userId;
      localData.userId = userId;
      localData.authToken = result.response!.authToken!;

      await findUserById(userId: userId);
    }

    return result;
  }

  Future<bool> startSignIn(Enum$IdentityProvider provider, BuildContext context) async {
    if (provider == Enum$IdentityProvider.own) {
      context.push(AppRoutes.signupCreateAccount.path);
      return true;
    }

    return signInWithOauth(provider, context);
  }

  Future<OperationResult<Mutation$CreateOneTimeAuthTokenForMe>>
  createOneTimeAuthTokenForMe() async {
    final QueryResult queryResult = await asyncMutation(
      mutationOptions: MutationOptions(
        document: documentNodeMutationCreateOneTimeAuthTokenForMe,
        fetchPolicy: FetchPolicy.noCache,
      ),
    );
    notifyListeners();
    return OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Mutation$CreateOneTimeAuthTokenForMe.fromJson(queryResult.data!)
              : null,
    );
  }

  // ----------------------------------------------------------------------
  // Firebase Auth:
  Future<bool> signInWithOauth(Enum$IdentityProvider identityProvider, BuildContext context) async {
    localData.identityProvider = identityProvider;
    firebaseService.firebaseAuthService.addListener(this);
    bool isSuccess = await firebaseService.firebaseAuthService.signIn(identityProvider, context);
    return isSuccess;
  }

  @override
  String firebaseAuthListenerName() {
    return 'UserProvider';
  }

  @override
  Future<bool> onUserSignedIntoFirebase(
    firebase_auth.User user,
    Enum$IdentityProvider identityProvider,
  ) async {
    var authTokenId = await user.getIdToken();

    final response = await signInOauthUser(
      displayName: user.displayName,
      email: user.email,
      emailVerified: user.emailVerified,
      phoneNumber: user.phoneNumber,
      // phoneNumberVerified: user.phoneNumberVerified,
      identityProvider: identityProvider,
      oauthFederatedProvider: Enum$FederatedIdentityProvider.firebase,
      oauthToken: authTokenId ?? '',
      oauthTokenId: authTokenId ?? '',
      oauthRefreshToken: user.refreshToken,
      oauthUserId: user.providerData[0].uid,
      oauthDelegateUserId: user.uid,
      profileUrl: user.photoURL,
    );

    if (response.response == null || response.gqlQueryResult.hasException) {
      DebugLogger.error('UserProvider.signInOauthUser failed.');
      return false;
    }

    // At this point, the user has been signed in and _user has been loaded.
    if (_myUser == null) {
      DebugLogger.error('UserProvider.onUserSignedIntoFirebase: _user not set.');
      return false;
    }

    if (_myUser != null && _providerListeners.isNotEmpty) {
      for (UserProviderListener listener in _providerListeners) {
        listener.onSignedIn(_myUser!);
      }
    }

    return true;
  }

  isOnboardingCompleted(OnboardingModel onboardingModel) {
    final stage =
        myUser?.onboardingStage?.isNotEmpty == true
            ? OnboardingStage.values.byName(myUser!.onboardingStage!)
            : onboardingModel.firstStage();
    return onboardingModel.isCompleted(stage);
  }

  Future<bool> willReceiveWelcomeMessage() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryUserWillReceiveWelcomeMessage,
        fetchPolicy: FetchPolicy.networkOnly,
        variables: Variables$Query$UserWillReceiveWelcomeMessage(userId: localData.userId).toJson(),
      ),
    );

    if (queryResult.data != null) {
      return Query$UserWillReceiveWelcomeMessage.fromJson(
        queryResult.data!,
      ).userWillReceiveWelcomeMessage;
    }

    return false;
  }

  // polls to check if this user now has at least 1 chat
  Future<bool> hasReceivedWelcomeMessage() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryMyInbox,
        fetchPolicy: FetchPolicy.networkOnly,
      ),
    );

    if (queryResult.data != null) {
      final inbox = Query$MyInbox.fromJson(queryResult.data!);
      return inbox.myInbox.channels?.latestMessages?.isNotEmpty == true;
    }
    return false;
  }

  /// Polls the welcome message status with configurable parameters
  ///
  /// @param maxAttempts Maximum number of polling attempts
  /// @param intervalSeconds Time between polling attempts in seconds
  /// @param onUpdate Optional callback that receives the current status on each poll
  /// @return Future<bool> that resolves to true if welcome message is found
  Future<bool> pollForWelcomeMessage({
    int maxAttempts = 5,
    int intervalMilliseconds = 500,
    void Function(bool hasMessage, int attempt)? onUpdate,
  }) async {
    bool hasWelcomeMessage = false;
    int attempts = 0;

    if (!await willReceiveWelcomeMessage()) {
      return false; // No need to poll if the user won't receive a welcome message
    }

    while (!hasWelcomeMessage && attempts < maxAttempts) {
      try {
        hasWelcomeMessage = await hasReceivedWelcomeMessage();

        // Call the update callback if provided
        if (onUpdate != null) {
          onUpdate(hasWelcomeMessage, attempts);
        }

        if (hasWelcomeMessage) {
          break; // Exit the loop if we got a welcome message
        }

        // Wait before trying again, but don't wait after the last attempt
        if (attempts < maxAttempts - 1) {
          await Future.delayed(Duration(milliseconds: intervalMilliseconds));
          intervalMilliseconds *= 2; // Exponential backoff
          // 1/2 second, 1 second, 2 seconds, 4 seconds, 8 seconds
        }
      } catch (e) {
        // Log the error but continue polling
        DebugLogger.debug('Error polling for welcome message: $e');
      }
      attempts++;
    }

    return hasWelcomeMessage;
  }

  Future<UserCms?> getUserCms() async {
    if (_myUser == null) return null;
    if (_userCms == null) {
      await _fetchUserCms();
    }

    return _userCms;
  }

  Future<UserCms?> _fetchUserCms({FetchPolicy fetchPolicy = FetchPolicy.cacheFirst}) async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindUserCmsByUserId,
        fetchPolicy: fetchPolicy,
        variables: Variables$Query$FindUserCmsByUserId(userId: _myUser!.id).toJson(),
      ),
    );

    if (queryResult.data == null) {
      // Handle the case where the query failed
      if (queryResult.hasException) {
        debugPrint(
          'Error fetching user cms by usesId: ${_myUser!.id}, ${queryResult.exception.toString()}',
        );
      } else {
        debugPrint('No data returned for userId: ${_myUser!.id}');
      }
      return null;
    }

    _userCms = Query$FindUserCmsByUserId.fromJson(queryResult.data!).findUserCmsByUserId;

    return _userCms;
  }
}
