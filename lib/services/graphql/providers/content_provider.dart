import 'package:flutter/material.dart';
import 'package:graphql_flutter/graphql_flutter.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../__generated/schema/operations_content.graphql.dart';
import '../../../models/models.dart';
import 'base/base_provider.dart';
import 'base/operation_result.dart';

typedef AllOptionsByType = Query$FindAllOptionsByType;
typedef CompanyStage = Query$FindCompanyStages$findCompanyStages;
typedef CompanyType = Query$FindCompanyTypes$findCompanyTypes;
typedef Country = Query$FindCountries$findCountries;
typedef EducationLevel = Query$FindEducationLevels$findEducationLevels;
typedef ErrorCodeOption = Query$FindErrorCodes$findErrorCodes;
typedef Expertise = Query$FindExpertises$findExpertises;
typedef Gender = Query$FindGenders$findGenders;
typedef Industry = Query$FindIndustries$findIndustries;
typedef Language = Query$FindLanguages$findLanguages;
typedef Pronoun = Query$FindPronouns$findPronouns;
typedef ReportUserReasons = Query$FindReportUserReasons$findReportUserReasons;
typedef DeclineChannelInvitationReasons =
    Query$FindDeclineChannelInvitationReasons$findDeclineChannelInvitationReasons;
typedef JordanianGovernorate = Query$FindIqlaaJordanianGovernorates$findIqlaaJordanianGovernorates;
typedef JordanianDistrict = Query$FindIqlaaJordanianDistricts$findIqlaaJordanianDistricts;
typedef IndonesianCity = Query$FindIndonesianCities$findIndonesianCities;
typedef IndonesianProvince = Query$FindIndonesianProvinces$findIndonesianProvinces;
typedef MastercardBank = Query$FindMastercardBanks$findMastercardBanks;

class ContentProvider extends BaseProvider with ChangeNotifier {
  List<CompanyStage>? _companyStages;
  List<CompanyType>? _companyTypes;
  List<Country>? _countries;
  List<EducationLevel>? _educationLevels;
  List<ErrorCodeOption>? _errorCodeOptions;
  List<Expertise>? _expertises;
  List<Gender>? _genders;
  List<Industry>? _industries;
  List<Language>? _languages;
  List<Pronoun>? _pronouns;
  List<ReportUserReasons>? _reportUserReasons;
  List<DeclineChannelInvitationReasons>? _declineChannelInvitationReasons;
  List<JordanianDistrict>? _jordanianDistricts;
  List<JordanianGovernorate>? _jordanianGovernorates;
  List<IndonesianProvince>? _indonesianProvinces;
  List<IndonesianCity>? _indonesianCities;
  List<MastercardBank>? _mastercardBanks;
  bool isFetchingData = false;

  List<String> get countryTextIds => (_countries ?? []).map((c) => c.textId).toList();
  List<String> get languageTextIds => (_languages ?? []).map((l) => l.textId).toList();
  List<String> get expertiseTextIds => (_expertises ?? []).map((e) => e.textId).toList();
  List<String> get industryTextIds => (_industries ?? []).map((i) => i.textId).toList();
  List<String> get companyStageTextIds => (_companyStages ?? []).map((i) => i.textId).toList();
  List<String> get jordanianDistrictsTextIds =>
      (_jordanianDistricts ?? []).map((e) => e.textId).toList();
  List<String> get jordanianGovernoratesTextIds =>
      (_jordanianGovernorates ?? []).map((e) => e.textId).toList();

  String? translateCountry(String id) =>
      (_countries ?? []).where((c) => c.textId == id).firstOrNull?.translatedValue;
  String? translateLanguages(String id) =>
      (_languages ?? []).where((l) => l.textId == id).firstOrNull?.translatedValue;
  String? translateExpertise(String id) =>
      (_expertises ?? []).where((e) => e.textId == id).firstOrNull?.translatedValue;
  String? translateIndustry(String id) =>
      (_industries ?? []).where((i) => i.textId == id).firstOrNull?.translatedValue;
  String? translateReportUserReasons(String id) =>
      (_reportUserReasons ?? []).where((i) => i.textId == id).firstOrNull?.translatedValue;
  String? translateDeclineChannelInvitationReasons(String id) =>
      (_declineChannelInvitationReasons ?? [])
          .where((i) => i.textId == id)
          .firstOrNull
          ?.translatedValue;
  String? translateJordanianDistrict(String id) =>
      (_jordanianDistricts ?? []).where((e) => e.textId == id).firstOrNull?.translatedValue;
  String? translateJordanianGovernorate(String id) =>
      (_jordanianGovernorates ?? []).where((e) => e.textId == id).firstOrNull?.translatedValue;

  String? translateIndonesianProvince(String id) =>
      (_indonesianProvinces ?? []).where((e) => e.textId == id).firstOrNull?.translatedValue;
  String? translateIndonesianCities(String id) =>
      (_indonesianCities ?? []).where((e) => e.textId == id).firstOrNull?.translatedValue;

  String? translateMastercardBanks(String id) =>
      (_mastercardBanks ?? []).where((e) => e.textId == id).firstOrNull?.displayName;

  ContentProvider({required super.client}) {
    debugPrint('ContentProvider initialized');
  }

  // one setter for all content options
  void _setAllContentOptions({
    List<CompanyStage>? companyStages,
    List<CompanyType>? companyTypes,
    List<Country>? countries,
    List<EducationLevel>? educationLevels,
    List<ErrorCodeOption>? errorCodeOptions,
    List<Expertise>? expertises,
    List<Gender>? genders,
    List<Industry>? industries,
    List<Language>? languages,
    List<Pronoun>? pronouns,
    List<ReportUserReasons>? reportUserReasons,
    List<DeclineChannelInvitationReasons>? declineChannelInvitationReasons,
    List<JordanianDistrict>? jordanianDistricts,
    List<JordanianGovernorate>? jordanianGovernorates,
    List<IndonesianProvince>? indonesianProvinces,
    List<IndonesianCity>? indonesianCities,
    List<MastercardBank>? mastercardBanks,
  }) {
    if (companyStages != null) {
      _companyStages = companyStages;
    }
    if (companyTypes != null) {
      _companyTypes = companyTypes;
    }
    if (countries != null) {
      _countries = countries;
    }
    if (educationLevels != null) {
      _educationLevels = educationLevels;
    }
    if (errorCodeOptions != null) {
      _errorCodeOptions = errorCodeOptions;
    }
    if (expertises != null) {
      _expertises = expertises;
    }
    if (genders != null) {
      //TODO: Temperarily Removing "non-binary and other" gender option for MVP
      genders.removeWhere((element) => element.textId == 'x');
      genders.sort((a, b) => a.textId.compareTo(b.textId));
      _genders = genders;
    }
    if (industries != null) {
      _industries = industries;
    }
    if (languages != null) {
      _languages = languages;
    }
    if (pronouns != null) {
      _pronouns = pronouns;
    }
    if (reportUserReasons != null) {
      _reportUserReasons = reportUserReasons;
    }
    if (declineChannelInvitationReasons != null) {
      _declineChannelInvitationReasons = declineChannelInvitationReasons;
    }
    if (jordanianDistricts != null) {
      _jordanianDistricts = jordanianDistricts;
    }
    if (jordanianGovernorates != null) {
      _jordanianGovernorates = jordanianGovernorates;
    }
    if (indonesianProvinces != null) {
      _indonesianProvinces = indonesianProvinces;
    }
    if (indonesianCities != null) {
      _indonesianCities = indonesianCities;
    }
    if (mastercardBanks != null) {
      _mastercardBanks = mastercardBanks;
    }
    debugPrint('Updated content provider values: ${toString()}');
  }

  // separate setters for each content option

  void _setCompanyStageOptions(List<CompanyStage> companyStages) {
    _companyStages = companyStages;
    debugPrint('Updated content provider company stage values: ${toString()}');
  }

  void _setCompanyTypeOptions(List<CompanyType> companyTypes) {
    _companyTypes = companyTypes;
    debugPrint('Updated content provider company type values: ${toString()}');
  }

  void _setCountryOptions(List<Country> countries) {
    _countries = countries;
    debugPrint('Updated content provider country values: ${toString()}');
  }

  void _setEducationLevelOptions(List<EducationLevel> educationLevels) {
    _educationLevels = educationLevels;
    debugPrint('Updated content provider education level values: ${toString()}');
  }

  void _setErrorCodeOptions(List<ErrorCodeOption> errorCodeOptions) {
    _errorCodeOptions = errorCodeOptions;
    debugPrint('Updated content provider error code option values: ${toString()}');
  }

  void _setExpertiseOptions(List<Expertise> expertises) {
    _expertises = expertises;
    debugPrint('Updated content provider expertise values: ${toString()}');
  }

  void _setGenderOptions(List<Gender> genders) {
    _genders = genders;
    debugPrint('Updated content provider preset gender values: ${toString()}');
  }

  void _setIndustryOptions(List<Industry> industries) {
    _industries = industries;
    debugPrint('Updated content provider industry values: ${toString()}');
  }

  void _setLanguageOptions(List<Language> languages) {
    _languages = languages;
    debugPrint('Updated content provider language values: ${toString()}');
  }

  void _setPronounOptions(List<Pronoun> pronoun) {
    _pronouns = pronoun;
    debugPrint('Updated content provider preset pronoun values: ${toString()}');
  }

  void _setReportUserReasonsOptions(List<ReportUserReasons> reportUserReasons) {
    _reportUserReasons = reportUserReasons;
    debugPrint('Updated content provider report user reasons values: ${toString()}');
  }

  void _setDeclineChannelInvitationReasonsOptions(
    List<DeclineChannelInvitationReasons> declineChannelInvitationReasons,
  ) {
    _declineChannelInvitationReasons = declineChannelInvitationReasons;
    debugPrint('Updated content provider decline channel invitation reasons values: ${toString()}');
  }

  void _setJordanianDistrictsOptions(List<JordanianDistrict> districts) {
    _jordanianDistricts = districts;
    debugPrint('Updated content provider expertise values: ${toString()}');
  }

  void _setJordanianGovernoratesOptions(List<JordanianGovernorate> governorates) {
    _jordanianGovernorates = governorates;
    debugPrint('Updated content provider expertise values: ${toString()}');
  }

  void _setIndonesianProvincesOptions(List<IndonesianProvince> provinces) {
    _indonesianProvinces = provinces;
    debugPrint('Updated content provider expertise values: ${toString()}');
  }

  void _setIndonesianCitiesOptions(List<IndonesianCity> cities) {
    _indonesianCities = cities;
    debugPrint('Updated content provider expertise values: ${toString()}');
  }

  void _setMastercardBanksOptions(List<MastercardBank> banks) {
    _mastercardBanks = banks;
    debugPrint('Updated content provider expertise values: ${toString()}');
  }

  // separate getters for each content option
  List<CompanyStage>? get companyStageOptions {
    return _companyStages;
  }

  List<CompanyType>? get companyTypeOptions {
    return _companyTypes;
  }

  List<Country>? get countryOptions {
    return _countries?.where((c) => c.phoneCode != 'x').toList();
  }

  List<EducationLevel>? get educationLevelOptions {
    return _educationLevels;
  }

  List<ErrorCodeOption>? get errorCodeOptions {
    return _errorCodeOptions;
  }

  List<Expertise>? get expertiseOptions {
    return _expertises;
  }

  List<Gender>? get genderOptions {
    return _genders;
  }

  List<Industry>? get industryOptions {
    return _industries;
  }

  List<Language>? get languageOptions {
    return _languages;
  }

  List<Language>? get uiLanguages {
    // TODO: this is not currently used anywhere, the stubbed in hardCodedUiLanguages method is used instead
    return (_languages ?? []).where((l) => l.isUiLanguage == true).toList();
  }

  List<Pronoun>? get pronounOptions {
    return _pronouns;
  }

  List<ReportUserReasons>? get reportUserReasonOptions {
    return _reportUserReasons;
  }

  List<DeclineChannelInvitationReasons>? get declineChannelInvitationReasons {
    return _declineChannelInvitationReasons;
  }

  List<JordanianDistrict>? get jordanianDistrictOptions {
    return _jordanianDistricts;
  }

  List<JordanianGovernorate>? get jordanianGovernorateOptions {
    return _jordanianGovernorates;
  }

  List<IndonesianProvince>? get indonesianProvincesOptions {
    return _indonesianProvinces;
  }

  List<IndonesianCity>? get indonesianCitiesOptions {
    return _indonesianCities;
  }

  List<MastercardBank>? get mastercardBanks {
    return _mastercardBanks;
  }

  // stubbed in uiLanguages method
  // TODO: Remove this once all languages are translated
  List<Language>? hardCodedUiLanguages(LocaleModel localeModel) {
    if (_languages == null) return null;
    var backendUiLanguages = (_languages ?? []).where((l) => l.isUiLanguage == true).toList();
    // filter, only allow values returned from locale_provider's getSupportedLocales
    if (backendUiLanguages.isEmpty) return null;
    return localeModel
        .getSupportedLocales()
        .map((l) => backendUiLanguages.firstWhere((b) => b.textId == l.languageCode))
        .toList();
  }

  // Queries
  Future<OperationResult<AllOptionsByType>> findAllOptionsByType({
    FetchPolicy fetchPolicy = FetchPolicy.cacheFirst,
    Enum$UiLanguage? fallbackUiLanguage,
  }) async {
    isFetchingData = true;
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindAllOptionsByType,
        fetchPolicy: fetchPolicy,
        variables: Variables$Query$FindCountries(fallbackUiLanguage: fallbackUiLanguage).toJson(),
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null ? Query$FindAllOptionsByType.fromJson(queryResult.data!) : null,
    );

    if (result.response != null) {
      _setAllContentOptions(
        companyStages:
            result.response?.findCompanyStages
                .map((e) => Query$FindCompanyStages$findCompanyStages.fromJson(e.toJson()))
                .toList(),
        companyTypes:
            result.response?.findCompanyTypes
                .map((e) => Query$FindCompanyTypes$findCompanyTypes.fromJson(e.toJson()))
                .toList(),
        countries:
            result.response?.findCountries
                .map((e) => Query$FindCountries$findCountries.fromJson(e.toJson()))
                .toList(),
        expertises:
            result.response?.findExpertises
                .map((e) => Query$FindExpertises$findExpertises.fromJson(e.toJson()))
                .toList(),
        educationLevels:
            result.response?.findEducationLevels
                .map((e) => Query$FindEducationLevels$findEducationLevels.fromJson(e.toJson()))
                .toList(),
        errorCodeOptions:
            result.response?.findErrorCodes
                .map((e) => Query$FindErrorCodes$findErrorCodes.fromJson(e.toJson()))
                .toList(),
        genders:
            result.response?.findGenders
                .map((e) => Query$FindGenders$findGenders.fromJson(e.toJson()))
                .toList(),
        industries:
            result.response?.findIndustries
                .map((e) => Query$FindIndustries$findIndustries.fromJson(e.toJson()))
                .toList(),
        languages:
            result.response?.findLanguages
                .map((e) => Query$FindLanguages$findLanguages.fromJson(e.toJson()))
                .toList(),
        pronouns:
            result.response?.findPronouns
                .map((e) => Query$FindPronouns$findPronouns.fromJson(e.toJson()))
                .toList(),
        reportUserReasons:
            result.response?.findReportUserReasons
                .map((e) => Query$FindReportUserReasons$findReportUserReasons.fromJson(e.toJson()))
                .toList(),
        declineChannelInvitationReasons:
            result.response?.findDeclineChannelInvitationReasons
                .map(
                  (e) =>
                      Query$FindDeclineChannelInvitationReasons$findDeclineChannelInvitationReasons.fromJson(
                        e.toJson(),
                      ),
                )
                .toList(),
      );
    }
    isFetchingData = false;
    notifyListeners();
    return result;
  }

  Future<OperationResult<List<CompanyType>>> findCompanyTypes() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindCompanyTypes,
        fetchPolicy: FetchPolicy.cacheFirst,
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindCompanyTypes.fromJson(queryResult.data!).findCompanyTypes
              : null,
    );
    if (result.response != null) _setCompanyTypeOptions(result.response!);
    return result;
  }

  Future<OperationResult<List<CompanyStage>>> findCompanyStages() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindCompanyStages,
        fetchPolicy: FetchPolicy.cacheFirst,
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindCompanyStages.fromJson(queryResult.data!).findCompanyStages
              : null,
    );
    if (result.response != null) _setCompanyStageOptions(result.response!);
    return result;
  }

  Future<OperationResult<List<Country>>> findCountries() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindCountries,
        fetchPolicy: FetchPolicy.cacheFirst,
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindCountries.fromJson(queryResult.data!).findCountries
              : null,
    );
    if (result.response != null) _setCountryOptions(result.response!);
    return result;
  }

  Future<OperationResult<List<EducationLevel>>> findEducationLevels() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindEducationLevels,
        fetchPolicy: FetchPolicy.cacheFirst,
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindEducationLevels.fromJson(queryResult.data!).findEducationLevels
              : null,
    );
    if (result.response != null) _setEducationLevelOptions(result.response!);
    return result;
  }

  Future<OperationResult<List<ErrorCodeOption>>> findErrorCodeOptions() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindErrorCodes,
        fetchPolicy: FetchPolicy.cacheFirst,
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindErrorCodes.fromJson(queryResult.data!).findErrorCodes
              : null,
    );
    if (result.response != null) _setErrorCodeOptions(result.response!);
    return result;
  }

  Future<OperationResult<List<Expertise>>> findExpertises() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindExpertises,
        fetchPolicy: FetchPolicy.cacheFirst,
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindExpertises.fromJson(queryResult.data!).findExpertises
              : null,
    );
    if (result.response != null) _setExpertiseOptions(result.response!);
    return result;
  }

  Future<OperationResult<List<Gender>>> findGenders() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindGenders,
        fetchPolicy: FetchPolicy.cacheFirst,
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindGenders.fromJson(queryResult.data!).findGenders
              : null,
    );
    if (result.response != null) {
      _setGenderOptions(result.response!);
    }
    return result;
  }

  Future<OperationResult<List<Industry>>> findIndustries() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindIndustries,
        fetchPolicy: FetchPolicy.cacheFirst,
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindIndustries.fromJson(queryResult.data!).findIndustries
              : null,
    );
    if (result.response != null) _setIndustryOptions(result.response!);
    return result;
  }

  Future<OperationResult<List<Language>>> findLanguages() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindLanguages,
        fetchPolicy: FetchPolicy.cacheFirst,
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindLanguages.fromJson(queryResult.data!).findLanguages
              : null,
    );
    if (result.response != null) _setLanguageOptions(result.response!);
    return result;
  }

  Future<OperationResult<List<Pronoun>>> findPronouns() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindPronouns,
        fetchPolicy: FetchPolicy.cacheFirst,
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindPronouns.fromJson(queryResult.data!).findPronouns
              : null,
    );
    if (result.response != null) _setPronounOptions(result.response!);
    return result;
  }

  Future<OperationResult<List<ReportUserReasons>>> findReportUserReasons() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindReportUserReasons,
        fetchPolicy: FetchPolicy.cacheFirst,
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindReportUserReasons.fromJson(queryResult.data!).findReportUserReasons
              : null,
    );
    if (result.response != null) _setReportUserReasonsOptions(result.response!);
    return result;
  }

  Future<OperationResult<List<DeclineChannelInvitationReasons>>>
  findDeclineChannelInvitationReasons() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindDeclineChannelInvitationReasons,
        fetchPolicy: FetchPolicy.cacheFirst,
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindDeclineChannelInvitationReasons.fromJson(
                queryResult.data!,
              ).findDeclineChannelInvitationReasons
              : null,
    );
    if (result.response != null) _setDeclineChannelInvitationReasonsOptions(result.response!);
    return result;
  }

  Future<OperationResult<List<JordanianDistrict>>> findIqlaaJordanianDistricts() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindIqlaaJordanianDistricts,
        fetchPolicy: FetchPolicy.cacheFirst,
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindIqlaaJordanianDistricts.fromJson(
                queryResult.data!,
              ).findIqlaaJordanianDistricts
              : null,
    );
    if (result.response != null) _setJordanianDistrictsOptions(result.response!);
    return result;
  }

  Future<OperationResult<List<JordanianGovernorate>>> findIqlaaJordanianGovernorates() async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindIqlaaJordanianGovernorates,
        fetchPolicy: FetchPolicy.cacheFirst,
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindIqlaaJordanianGovernorates.fromJson(
                queryResult.data!,
              ).findIqlaaJordanianGovernorates
              : null,
    );
    if (result.response != null) _setJordanianGovernoratesOptions(result.response!);
    return result;
  }

  Future<OperationResult<List<IndonesianProvince>>> findIndonesianProvinces(
    Enum$UiLanguage? fallbackUiLanguage,
  ) async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindIndonesianProvinces,
        fetchPolicy: FetchPolicy.cacheFirst,
        variables: Variables$Query$FindCountries(fallbackUiLanguage: fallbackUiLanguage).toJson(),
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindIndonesianProvinces.fromJson(queryResult.data!).findIndonesianProvinces
              : null,
    );
    if (result.response != null) _setIndonesianProvincesOptions(result.response!);
    return result;
  }

  Future<OperationResult<List<IndonesianCity>>> findIndonesianCities(
    Enum$UiLanguage? fallbackUiLanguage,
  ) async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindIndonesianCities,
        fetchPolicy: FetchPolicy.cacheFirst,
        variables: Variables$Query$FindCountries(fallbackUiLanguage: fallbackUiLanguage).toJson(),
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindIndonesianCities.fromJson(queryResult.data!).findIndonesianCities
              : null,
    );
    if (result.response != null) _setIndonesianCitiesOptions(result.response!);
    return result;
  }

  Future<OperationResult<List<MastercardBank>>> findMastercardBanks(
    Enum$UiLanguage? fallbackUiLanguage,
  ) async {
    final QueryResult queryResult = await asyncQuery(
      queryOptions: QueryOptions(
        document: documentNodeQueryFindMastercardBanks,
        fetchPolicy: FetchPolicy.cacheFirst,
        variables: Variables$Query$FindCountries(fallbackUiLanguage: fallbackUiLanguage).toJson(),
      ),
    );
    final result = OperationResult(
      gqlQueryResult: queryResult,
      response:
          queryResult.data != null
              ? Query$FindMastercardBanks.fromJson(queryResult.data!).findMastercardBanks
              : null,
    );
    if (result.response != null) _setMastercardBanksOptions(result.response!);
    return result;
  }

  String getNativeLanguageTitle(Enum$UiLanguage languageEnum) {
    switch (languageEnum) {
      case Enum$UiLanguage.ar:
        return 'العربية'; //(Arabic)
      case Enum$UiLanguage.en:
        return 'English'; //(English)
      case Enum$UiLanguage.es:
        return 'Español'; //(Spanish)
      case Enum$UiLanguage.id:
        return 'Bahasa Indonesia'; //(Indonesian)
      case Enum$UiLanguage.ru:
        return 'Русский'; //Русский
      case Enum$UiLanguage.so:
        return 'Soomaali'; //Somali
      case Enum$UiLanguage.$unknown:
        return 'English'; //English //default
    }
  }

  clear() {
    _companyStages?.clear();
    _companyTypes?.clear();
    _countries?.clear();
    _educationLevels?.clear();
    _errorCodeOptions?.clear();
    _expertises?.clear();
    _genders?.clear();
    _industries?.clear();
    _languages?.clear();
    _pronouns?.clear();
    _reportUserReasons?.clear();
    _declineChannelInvitationReasons?.clear();
    _jordanianDistricts?.clear();
    _jordanianGovernorates?.clear();
    _languages?.clear();
  }
}
