import 'package:flutter/foundation.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/models/local_data_model.dart';

import './graphql_link.dart';

class GraphqlService {
  final String serverUrl;
  final String subscriptionUrl;
  final LocalDataModel localData;

  ValueNotifier<GraphQLClient>? client;

  GraphqlService({required this.serverUrl, required this.subscriptionUrl, required this.localData});

  void connect() {
    final HttpLink httpLink = HttpLink(serverUrl);

    final GraphQlLink graphQlLink = GraphQlLink(localData: localData);

    final ErrorLink errorLink = ErrorLink(
      onException: (Request request, forward, exception) {
        final errorMessage = exception.originalException.toString();

        //Clear local storage if bearer token is expired i.e user is Unauthorized
        if ((((exception is HttpLinkParserException) &&
                    (exception.response.body.contains('Unauthorized'))) ||
                errorMessage.contains('Unauthorized')) &&
            localData.authToken.isNotEmpty) {
          debugPrint('Exception:  Unauthorized');
          localData.clear();
        }

        return forward(request);
      },
    );

    Link link = graphQlLink.concat(errorLink).concat(httpLink);

    final WebSocketLink wsLink = WebSocketLink(
      subscriptionUrl,
      subProtocol: GraphQLProtocol.graphqlTransportWs,
      config: SocketClientConfig(
        autoReconnect: true,
        inactivityTimeout: null,
        delayBetweenReconnectionAttempts: const Duration(seconds: 1),
        initialPayload: () async {
          return {'Authorization': 'Bearer ${localData.authToken}'};
        },
      ),
    );

    /// Subscriptions must be split otherwise `HttpLink` will. swallow them
    link = Link.split((request) => request.isSubscription, wsLink, link);

    client = ValueNotifier(GraphQLClient(link: link, cache: GraphQLCache(store: InMemoryStore())));
  }
}
