import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/themes/extensions/dimensions.dart';
import 'package:mm_flutter_app/services/themes/extensions/theme_validator.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'themes/extensions/app_features.dart';
import 'themes/extensions/custom_styles.dart';

extension BuildContextExtensions on BuildContext {
  ThemeData get theme => Theme.of(this);

  TextTheme get textTheme => theme.textTheme;

  ColorScheme get colorScheme => theme.colorScheme;

  DefaultTextStyle get defaultTextStyle => DefaultTextStyle.of(this);

  MediaQueryData get mediaQuery => MediaQuery.of(this);

  Size get mediaQuerySize => MediaQuery.sizeOf(this);

  NavigatorState get navigator => Navigator.of(this);

  FocusScopeNode get focusScope => FocusScope.of(this);

  ScaffoldState get scaffold => Scaffold.of(this);

  ScaffoldMessengerState get scaffoldMessenger => ScaffoldMessenger.of(this);
}

extension WidgetStateHelpers on Iterable<WidgetState> {
  bool get isHovered => contains(WidgetState.hovered);
  bool get isFocused => contains(WidgetState.focused);
  bool get isPressed => contains(WidgetState.pressed);
  bool get isDragged => contains(WidgetState.dragged);
  bool get isSelected => contains(WidgetState.selected);
  bool get isScrolledUnder => contains(WidgetState.scrolledUnder);
  bool get isDisabled => contains(WidgetState.disabled);
  bool get isError => contains(WidgetState.error);
}

extension IterableExtensions on Iterable {
  bool containsAny(Iterable<Object?> other) => other.any((e) => contains(e));
}

extension ThemeDataExtensions on ThemeData {
  AppFeatures get appFeatures => extension<AppFeatures>() ?? AppFeatures.defaultAppFeatures;
  Dimensions get d => extension<Dimensions>() ?? Dimensions.defaultDimensions;

  ThemeValidator get validator =>
      extension<ThemeValidator>() ?? ThemeValidator.defaultThemeValidator;

  CustomStyles get buttonStyle => extension<CustomStyles>() ?? CustomStyles.defaultCustomStyles;
}

extension StringExtension on String? {
  String? capitalize() {
    if (this == null || this!.trim().isEmpty) return '';
    return '${this?[0].toUpperCase()}${this?.substring(1).toLowerCase()}';
  }
}

extension NumberExtension on num? {
  bool isInteger() => this is int || this == this?.roundToDouble();
}

extension MastercardCardTypeExtension on Enum$MastercardCardType {
  String get title {
    switch (this) {
      case Enum$MastercardCardType.credit:
        return AppLocale.current.mastercardTypeCredit;

      case Enum$MastercardCardType.debit:
        return AppLocale.current.mastercardTypeDebit;

      case Enum$MastercardCardType.prepaid:
        return AppLocale.current.mastercardTypePrepaid;

      case Enum$MastercardCardType.none:
        return AppLocale.current.mastercardTypeNone;

      case Enum$MastercardCardType.notProvided:
        return AppLocale.current.mastercardTypeNotProvided;

      default:
        return name.toString();
    }
  }
}
