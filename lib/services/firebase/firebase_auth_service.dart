import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:mm_flutter_app/models/local_data_model.dart';
import 'package:mm_flutter_app/utilities/debug_logger.dart';
import 'package:mm_flutter_app/utilities/loading/loading_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';

import '../../__generated/schema/schema.graphql.dart';
import '.env.dart';

const googleScopes = ['email', 'https://www.googleapis.com/auth/userinfo.profile'];

abstract mixin class FirebaseAuthListener {
  String firebaseAuthListenerName();
  Future<void> onUserSignedOutOfFirebase() async {}
  Future<bool> onUserSignedIntoFirebase(User user, Enum$IdentityProvider identityProvider) async {
    return true;
  }
}

// What Firebase/Google sends back for GetAccountInfoResponse:
// {
//   "kind": "identitytoolkit#GetAccountInfoResponse",
//   "users": [
//     {
//       "localId": "crx5kt8bpWdiC8RqEC41vC6Ch3u1",
//       "email": "<EMAIL>",
//       "displayName": "Holger Selover-Stephan",
//       "photoUrl": "https://lh3.googleusercontent.com/a/ACg8ocIjTdUcuafaA7VbpoxPuy1nZujxR_NVPiDxAYyS5cPR=s96-c",
//       "emailVerified": true,
//       "providerUserInfo": [
//         {
//           "providerId": "google.com",
//           "displayName": "Holger Selover-Stephan",
//           "photoUrl": "https://lh3.googleusercontent.com/a/ACg8ocIjTdUcuafaA7VbpoxPuy1nZujxR_NVPiDxAYyS5cPR=s96-c",
//           "federatedId": "112332467959390042775",
//           "email": "<EMAIL>",
//           "rawId": "112332467959390042775"
//         }
//       ],
//       "validSince": "**********",
//       "lastLoginAt": "*************",
//       "createdAt": "**********080",
//       "lastRefreshAt": "2023-12-30T01:25:10.748Z"
//     }
//   ]
// }
class FirebaseAuthService {
  FirebaseAuth? _firebaseAuth;
  GoogleSignIn? googleSignIn;
  final List<FirebaseAuthListener> _listeners = [];

  FirebaseAuth? get firebaseAuth => _firebaseAuth;

  addListener(FirebaseAuthListener listener) {
    if (!_listeners.any(
      (l) => l.firebaseAuthListenerName() == listener.firebaseAuthListenerName(),
    )) {
      _listeners.add(listener);
    }
  }

  Future<void> init() async {
    _firebaseAuth = FirebaseAuth.instance;
    if (kIsWeb) await _firebaseAuth?.setPersistence(Persistence.NONE);

    _firebaseAuth!.authStateChanges().listen((User? user) {
      if (user == null) {
        debugPrint('FirebaseAuthService.authStateChanges: User is currently signed out!');
        if (_listeners.isNotEmpty) {
          for (FirebaseAuthListener listener in _listeners) {
            listener.onUserSignedOutOfFirebase();
          }
        }
        // We probably don't need to call the listeners here, as we call
        // listener.onUserSignedIntoFirebase() during the actual (user initiated)
        // sign in process.
        // } else {
        //   debugPrint('FirebaseAuthService.authStateChanges: User is signed in!');
        //   if (_listeners.isNotEmpty) {
        //     for (FirebaseAuthListener listener in _listeners) {
        //       listener.onUserSignedIntoFirebase(user);
        //     }
        //   }
      }
    });

    // Probably not needed:
    // FirebaseAuth.instance.idTokenChanges().listen((User? user) {
    //   if (user == null) {
    //     print('FirebaseAuthService.idTokenChanges: User is currently signed out!');
    //   } else {
    //     print('FirebaseAuthService.idTokenChanges: User is signed in!');
    //   }
    // });
  }

  Future<bool> signIn(Enum$IdentityProvider provider, BuildContext context) async {
    if (_firebaseAuth == null) {
      return false;
    }

    try {
      if (!context.mounted) {
        debugPrint('GoogleAuthProvider.signIn: context not mounted.');
        return false;
      }
      UserCredential userCredential;
      Loader.show(context);

      if (provider == Enum$IdentityProvider.apple) {
        userCredential = await _signInWithApple(context);
      } else if (provider == Enum$IdentityProvider.facebook) {
        userCredential = await _signInWithFacebook(context);
      } else if (provider == Enum$IdentityProvider.google) {
        userCredential = await _signInWithGoogle(context);
      } else if (provider == Enum$IdentityProvider.twitter) {
        userCredential = await _signInWithTwitter(context);
      } else {
        debugPrint('FirebaseAuthProvider.signIn: unknown provider $provider.');
        if (context.mounted) Loader.hide(context);
        return false;
      }

      if (context.mounted) {
        Loader.hide(context);
      }
      User? user = userCredential.user;
      if (user != null) {
        if (!context.mounted) {
          debugPrint('FirebaseAuthProvider.signIn: context not mounted.');
          return false;
        }
        if (_listeners.isNotEmpty) {
          for (FirebaseAuthListener listener in _listeners) {
            final isSuccess = await listener.onUserSignedIntoFirebase(user, provider);
            if (!isSuccess) return false;
            break;
          }
        }
      }
      return true;
    } on FirebaseAuthException catch (e) {
      debugPrint('Firebase Auth error: $e');
      if (context.mounted) {
        Loader.hide(context);
      }
      return false;
    } catch (e) {
      debugPrint('Firebase Auth failed: $e');
      if (context.mounted) {
        Loader.hide(context);
      }
      return false;
    }
  }

  // see: https://firebase.google.com/docs/auth/flutter/federated-auth#facebook
  Future<UserCredential> _signInWithApple(BuildContext context) async {
    // todo: what about API key/secret?
    final appleProvider = AppleAuthProvider();
    final localData = Provider.of<LocalDataModel>(context, listen: false);
    appleProvider.addScope('email');
    appleProvider.addScope('name');

    if (kIsWeb) {
      appleProvider.addScope('email');
      // if locale != '', send it to apple
      if (localData.locale.isNotEmpty) {
        appleProvider.setCustomParameters({'locale': localData.locale});
      }
      return _firebaseAuth!.signInWithPopup(appleProvider);
    }

    return _firebaseAuth!.signInWithProvider(appleProvider);
  }

  // see: https://firebase.google.com/docs/auth/flutter/federated-auth#facebook
  Future<UserCredential> _signInWithFacebook(BuildContext context) async {
    FacebookAuthProvider facebookProvider = FacebookAuthProvider();
    if (kIsWeb) {
      facebookProvider.addScope('email');
      facebookProvider.setCustomParameters({'display': 'popup'});
      // facebookProvider.setCustomParameters(customOAuthParameters)

      return _firebaseAuth!.signInWithPopup(facebookProvider);
    }

    await FacebookAuth.instance.logOut();

    late LoginResult loginResult;
    late OAuthCredential facebookAuthCredential;
    final trackingPermission = await Permission.appTrackingTransparency.status;
    try {
      String tokenString = '';
      String? nonce;
      if (trackingPermission == PermissionStatus.granted) {
        loginResult = await FacebookAuth.instance.login(
          permissions: ['public_profile', 'email'],
          loginTracking: LoginTracking.enabled,
        );
      } else {
        loginResult = await FacebookAuth.instance.login(permissions: ['public_profile', 'email']);
      }

      if (loginResult.status == LoginStatus.success) {
        final AccessToken? accessToken = loginResult.accessToken;
        tokenString = accessToken!.tokenString;
        if (accessToken is LimitedToken) {
          nonce = accessToken.nonce;
          facebookAuthCredential = OAuthCredential(
            providerId: 'facebook.com',
            signInMethod: 'oauth',
            idToken: tokenString,
            rawNonce: nonce,
          );
        } else {
          facebookAuthCredential = FacebookAuthProvider.credential(tokenString);
        }
      }
    } catch (e) {
      DebugLogger.error(
        'FirebaseAuthService.signInWithFacebook: Error when logging in with Facebook service: $e',
      );
    }

    return await FirebaseAuth.instance.signInWithCredential(facebookAuthCredential);
  }

  // see: https://firebase.google.com/docs/auth/flutter/federated-auth#google
  Future<UserCredential> _signInWithGoogle(BuildContext context) async {
    if (kIsWeb) {
      GoogleAuthProvider authProvider = GoogleAuthProvider();
      for (String scope in googleScopes) {
        authProvider.addScope(scope);
      }
      authProvider.setCustomParameters({'prompt': 'select_account'});

      return _firebaseAuth!.signInWithPopup(authProvider);
    }

    googleSignIn = GoogleSignIn(scopes: googleScopes, clientId: getGoogleApiKey(context));

    final GoogleSignInAccount? googleSignInAccount = await googleSignIn?.signIn();

    if (googleSignInAccount == null) {
      throw 'googleSignInAccount missing';
    }

    final GoogleSignInAuthentication googleSignInAuthentication =
        await googleSignInAccount.authentication;

    final AuthCredential credential = GoogleAuthProvider.credential(
      accessToken: googleSignInAuthentication.accessToken,
      idToken: googleSignInAuthentication.idToken,
    );

    return _firebaseAuth!.signInWithCredential(credential);
  }

  // see: https://firebase.google.com/docs/auth/flutter/federated-auth#twitter
  Future<UserCredential> _signInWithTwitter(BuildContext context) async {
    // todo: what about API key/secret?
    TwitterAuthProvider twitterProvider = TwitterAuthProvider();

    if (kIsWeb) {
      return _firebaseAuth!.signInWithPopup(twitterProvider);
    }

    return _firebaseAuth!.signInWithProvider(twitterProvider);
  }

  Future<void> signOut() async {
    try {
      await _firebaseAuth!.signOut();
      if (googleSignIn != null) {
        await googleSignIn?.signOut();
        await googleSignIn?.disconnect();
      }
      await FacebookAuth.instance.logOut();
    } catch (e) {
      debugPrint('Firebase Auth: signOut failed: $e');
    }
  }

  String? getGoogleApiKey(BuildContext context) {
    if (Theme.of(context).platform == TargetPlatform.iOS) {
      return GOOGLE_IOS_CLIENT_ID;
    }
    return GOOGLE_WEB_CLIENT_ID;
  }
}
