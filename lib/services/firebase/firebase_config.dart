import '.env.dart';
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart' show defaultTargetPlatform, kIsWeb, TargetPlatform;

class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return FIREBASE_OPTIONS_WEB;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return FIREBASE_OPTIONS_ANDROID;
      case TargetPlatform.iOS:
        return FIREBASE_OPTIONS_IOS;
      case TargetPlatform.macOS:
        return FIREBASE_OPTIONS_MACOS;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError('DefaultFirebaseOptions are not supported for this platform.');
    }
  }
}
