import 'package:firebase_messaging/firebase_messaging.dart';

class FirebaseNotifications {
  final _fbInstance = FirebaseMessaging.instance;

  void handleMessage(RemoteMessage? message) {
    // TODO - refresh data here
  }

  Future<void> initialize() async {
    await _fbInstance.requestPermission();

    // IOS configs for initializing push notifications
    await _fbInstance.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    // Handling push notifications from a terminated state
    _fbInstance.getInitialMessage().then(handleMessage);

    // Handling push notifications
    // When the app is opened from a background state
    FirebaseMessaging.onMessageOpenedApp.listen(handleMessage);
  }
}
