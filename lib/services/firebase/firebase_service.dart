import 'dart:async';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart' as foundation;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mm_flutter_app/models/local_data_model.dart';

import '../../utilities/errors/crash_handler.dart';
import 'firebase_auth_service.dart';
import 'firebase_notifications.dart';
import 'firebase_config.dart';

mixin FirebaseServiceListener {
  String firebaseServiceListenerName();
  void onNewFirebaseTokenReceived(final String firebaseToken);
}

// see: https://firebase.google.com/docs/cloud-messaging/flutter/receive
class FirebaseService {
  StreamSubscription<String>? _tokenSubscriptionStream;
  FirebaseApp? _firebaseApp;
  final FirebaseAuthService _firebaseAuthService = FirebaseAuthService();
  String? _firebaseToken;
  final List<FirebaseServiceListener> _listeners = [];
  late final LocalDataModel _localData;

  get firebaseToken => _firebaseToken;
  FirebaseAuthService get firebaseAuthService => _firebaseAuthService;

  Future<void> init(LocalDataModel localData) async {
    _localData = localData;

    try {
      _firebaseApp = await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
    } catch (e) {
      debugPrint('Firebase initialization failed: $e');
    }
  }

  setup(LocalDataModel localData) async {
    if (_firebaseApp != null) {
      _firebaseAuthService.init();
    }

    //TODO(m-rosario): Make crash data collection opt-in.
    final CrashHandler crashHandler = CrashHandler(localData, !kDebugMode, true);
    FlutterError.onError = crashHandler.handleUncaughtFlutterError;
    PlatformDispatcher.instance.onError = crashHandler.handleUncaughtPlatformError;
    ErrorWidget.builder = crashHandler.handleBuildError;

    _setTokenSubscriptionStream();

    if (!foundation.kIsWeb) {
      _firebaseToken = await FirebaseMessaging.instance.getToken();
      localData.pushNotificationToken = _firebaseToken ?? '';
      await FirebaseNotifications().initialize();
    }
  }

  addListener(FirebaseServiceListener listener) {
    if (!_listeners.any(
      (l) => l.firebaseServiceListenerName() == listener.firebaseServiceListenerName(),
    )) {
      _listeners.add(listener);
    }
  }

  void shutDownService() {
    if (_tokenSubscriptionStream != null) {
      _tokenSubscriptionStream!.cancel();
    }
  }

  // This callback is fired at each app startup and whenever a new
  // token is generated.
  void _setTokenSubscriptionStream() {
    _tokenSubscriptionStream = FirebaseMessaging.instance.onTokenRefresh.listen((
      firebaseToken,
    ) async {
      if (firebaseToken != _firebaseToken) {
        debugPrint('Received new firebase token: $firebaseToken');

        _firebaseToken = firebaseToken;
        _localData.pushNotificationToken = _firebaseToken ?? '';

        if (_listeners.isNotEmpty) {
          for (FirebaseServiceListener listener in _listeners) {
            listener.onNewFirebaseTokenReceived(firebaseToken);
          }
        }
      }
    });
  }
}
