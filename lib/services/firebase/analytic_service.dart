import 'package:firebase_analytics/firebase_analytics.dart';

class AnalyticService {
  static final _analyticsInstance = FirebaseAnalytics.instance;

  static FirebaseAnalyticsObserver observer() {
    return FirebaseAnalyticsObserver(analytics: _analyticsInstance);
  }

  static void userLogIn(String loginMethod) async {
    return _analyticsInstance.logLogin(loginMethod: loginMethod);
  }

  static void setAnalyticsCollection(bool value) {
    _analyticsInstance.setAnalyticsCollectionEnabled(value);
  }

  static void pageTracking(String pageName) async {
    return _analyticsInstance.logEvent(name: 'pages_tracked', parameters: {'page_name': pageName});
  }

  static void vacationTracking(bool mode) async {
    if (mode) {
      return _analyticsInstance.logEvent(name: 'vacation_mode', parameters: {'mode': 'On'});
    } else {
      return _analyticsInstance.logEvent(name: 'vacation_mode', parameters: {'mode': 'Off'});
    }
  }

  static void invitationSent() async {
    return _analyticsInstance.logEvent(name: 'invitation_sent');
  }

  static void invitationWithdraw() async {
    return _analyticsInstance.logEvent(name: 'invitation_withdraw');
  }

  static void invitationAccepted() async {
    return _analyticsInstance.logEvent(name: 'invitation_accepted');
  }

  static void invitationDeclined() async {
    return _analyticsInstance.logEvent(name: 'invitation_declined');
  }

  static void sentMessages() async {
    return _analyticsInstance.logEvent(name: 'sent_messages');
  }

  static void onBoardingCompleted() async {
    return _analyticsInstance.logEvent(name: 'on_boarding_completed');
  }

  static void userSignUp(String provider) async {
    return _analyticsInstance.logEvent(name: 'user_signup', parameters: {'provider': provider});
  }
}
