import 'dart:io';

import 'package:flutter/foundation.dart';

/*
* This class is a custom HTTP client that can handle cookies and redirects.
* It is used to make HTTP requests and handle responses.
* 
* For the MM2 VTS, we need to follow a redirect with cookies in order to download
* training certificates. MM2 uses cookie authentication for verifying the user.
*/
class CustomHTTPClient {
  final HttpClient _client = HttpClient();
  final Map<String, String> _cookies = {};

  CustomHTTPClient() {
    _client.connectionTimeout = const Duration(seconds: 10);
  }

  Future<HttpClientResponse> get(String url, {int maxRedirect = 3}) async {
    final Uri parsedUrl = Uri.parse(url);
    try {
      final HttpClientRequest request = await _client.getUrl(parsedUrl);
      request.followRedirects = false;
      _beforeRequest(request);

      final HttpClientResponse response = await request.close();
      _afterResponse(response);

      // Handle redirection
      if (response.isRedirect && maxRedirect > 0) {
        final String? location =
            response.headers.value('location') ?? response.headers.value('Location');
        if (location == null || location.isEmpty) {
          throw Exception('Redirect location not found');
        }

        // Drain the current response and recursively follow the redirect
        await response.drain();
        final String resolvedUrl = parsedUrl.resolve(location).toString();
        return get(resolvedUrl, maxRedirect: maxRedirect - 1);
      }

      return response;
    } catch (error, stackTrace) {
      // Handle errors (log or rethrow as needed)
      debugPrint('Error occurred: $error');
      debugPrint('StackTrace: $stackTrace');
      rethrow;
    }
  }

  void _beforeRequest(HttpClientRequest request) {
    request.headers.set(HttpHeaders.acceptEncodingHeader, 'gzip, deflate, br');

    // Set cookie
    final String rawCookies = _cookies.keys
        .map((String name) => '$name=${_cookies[name]}')
        .join('; ');
    if (rawCookies.isNotEmpty) request.headers.set(HttpHeaders.cookieHeader, rawCookies);
  }

  void _afterResponse(HttpClientResponse response) {
    response.headers.forEach((String name, List<String> values) {
      if (name == 'set-cookie') {
        // Get cookies for next request
        for (var rawCookie in values) {
          try {
            Cookie cookie = Cookie.fromSetCookieValue(rawCookie);
            _cookies[cookie.name] = cookie.value;
          } catch (e) {
            final List<String> cookie = rawCookie.split(';')[0].split('=');
            _cookies[cookie[0]] = cookie[1];
          }
        }
        return;
      }
    });
  }
}
