import 'package:flutter/material.dart';

class Loader {
  static int showLoadingCount = 0;
  static show(BuildContext context) {
    try {
      if (showLoadingCount == 0) {
        showDialog(
          barrierDismissible: false,
          barrierColor: Colors.transparent,
          context: context,
          useRootNavigator: true,
          builder: (BuildContext context) {
            return const Center(child: CircularProgressIndicator());
          },
        );
      }
      showLoadingCount = showLoadingCount + 1;
    } catch (e) {
      debugPrint('showLoading failed: $e');
    }
  }

  static hide(BuildContext context) {
    try {
      showLoadingCount = showLoadingCount - 1;
      if (showLoadingCount == 0 &&
          (Navigator.of(context).canPop() || Navigator.of(context, rootNavigator: true).canPop())) {
        Navigator.of(context, rootNavigator: true).pop();
      }
    } catch (_) {
      showLoadingCount = 0;
    }
    if (showLoadingCount < 0) showLoadingCount = 0;
  }
}
