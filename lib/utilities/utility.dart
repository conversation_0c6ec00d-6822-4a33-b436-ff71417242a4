import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:collection/collection.dart';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/main/app.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/messages_provider.dart';
import 'package:mm_flutter_app/services/graphql/providers/user_provider.dart';
import 'package:mm_flutter_app/utilities/http_redirect_helpers.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:uuid/uuid.dart';

import '../../constants/constants.dart';
import '../constants/parts/micromentor_icons.dart';
import '../models/models.dart';
import '../widgets/widgets.dart';
import 'file_helpers.dart';
import 'loading/loading_provider.dart';

class AppUtility {
  AppUtility._private();
  static String generateUuid() {
    return const Uuid().v1();
  }

  static Widget widgetForAsyncState({
    bool isAppLaunch = false,
    required AsyncState state,
    required Widget Function() onReady,
    Widget Function()? onLoading,
    Widget Function()? onError,
  }) {
    switch (state) {
      case AsyncState.loading:
        return onLoading != null
            ? onLoading()
            : Center(child: isAppLaunch ? const Splash() : const Loading());
      case AsyncState.error:
        return onError != null ? onError() : const Center(child: CustomErrorWidget());
      default:
        return onReady();
    }
  }

  static Widget widgetForAsyncSnapshot({
    bool isAppLaunch = false,
    required AsyncSnapshot snapshot,
    required Widget Function() onReady,
    Widget Function()? onLoading,
    Widget Function()? onError,
  }) {
    final AsyncState state;
    if (snapshot.hasError) {
      state = AsyncState.error;
    } else if (snapshot.connectionState != ConnectionState.done) {
      state = AsyncState.loading;
    } else if (!snapshot.hasData) {
      state = AsyncState.loading;
    } else {
      state = AsyncState.ready;
    }
    return widgetForAsyncState(
      isAppLaunch: isAppLaunch,
      state: state,
      onReady: onReady,
      onLoading: onLoading,
      onError: onError,
    );
  }

  static String formatPhoneNumber(String? countryCode, String? phoneNumber) {
    if (countryCode?.trim().isEmpty == true || phoneNumber?.trim().isEmpty == true) {
      return '';
    }
    return '+$countryCode$phoneNumber';
  }

  static String getUserFullName(String? firstName, String? lastName) {
    if (firstName?.isNotEmpty == true && lastName?.isNotEmpty == true) {
      return '$firstName $lastName';
    }
    if (firstName?.isNotEmpty == true) {
      return firstName!;
    }
    if (lastName?.isNotEmpty == true) {
      return lastName!;
    }
    return '';
  }

  static String getUserInitials(String fullName) {
    return fullName.trim().split(RegExp(' +')).map((name) => name[0]).take(3).join();
  }

  static String addSchemeToUrl(String uri) {
    if (uri.contains('https://') || uri.contains('http://')) {
      return uri;
    }
    return 'https://$uri';
  }

  static String hideSchemeToUrl(String uri) {
    return uri.replaceAll('https://', '');
  }

  static String sanitizeUri(String uri) {
    uri = uri.trim();
    if (uri.startsWith('http://') == true) {
      return 'https://${uri.substring(7)}';
    }
    if (!uri.startsWith('https://')) {
      return 'https://$uri';
    }
    return uri;
  }

  static String timestampDateFormat(
    BuildContext context,
    DateTime date, {
    required String locale,
    bool capitalize = true,
  }) {
    DateTime now = DateTime.now();
    if (now.day == date.day && now.month == date.month && now.year == date.year) {
      final String todayText =
          capitalize
              ? AppLocale.current.dateToday[0].toUpperCase() +
                  AppLocale.current.dateToday.substring(1)
              : AppLocale.current.dateToday;
      return '$todayText ${DateFormat('', locale).add_jm().format(date)}';
    } else {
      return DateFormat('MMM d', locale).add_jm().format(date);
    }
  }

  static String simplePastDateFormat(
    BuildContext context,
    DateTime date, {
    required String locale,
    bool capitalize = true,
  }) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final pastDate = DateTime(date.year, date.month, date.day);
    final String result;

    if (pastDate == today) {
      result = AppLocale.current.dateToday;
    } else if (pastDate == yesterday) {
      result = AppLocale.current.dateYesterday;
    } else if ((today.difference(pastDate).inDays / 7).floor() == 0) {
      // Within a week
      result = DateFormat('EEEE', locale).format(date); // Day of week
    } else if ((today.difference(pastDate).inDays / 7).ceil() <= 5) {
      // Within 5 weeks
      final int weeksAgo = (today.difference(pastDate).inDays / 7).ceil();
      result =
          weeksAgo == 1
              ? AppLocale.current.dateLastWeek
              : AppLocale.current.datePastWeeks(weeksAgo);
    } else if (today.difference(pastDate).inDays <= 365 && today.month != pastDate.month) {
      // Within a year
      int monthsAgo = today.month - pastDate.month;
      if (monthsAgo.isNegative) {
        monthsAgo += 12;
      }
      result =
          monthsAgo == 1
              ? AppLocale.current.dateLastMonth
              : AppLocale.current.datePastMonths(monthsAgo);
    } else {
      // More than a year
      final int yearsAgo = today.year - pastDate.year;
      result =
          yearsAgo == 1
              ? AppLocale.current.dateLastYear
              : AppLocale.current.datePastYears(yearsAgo);
    }
    return capitalize ? result[0].toUpperCase() + result.substring(1) : result;
  }

  static bool isUserBlocked(User? myUser, String? userId) {
    return myUser != null &&
        myUser.userBlocks?.isNotEmpty == true &&
        myUser.userBlocks!.any((u) => u.userId == userId);
  }

  static bool containsUnseenChannelMessages(List<ChannelMessage> messages, String? myUserId) {
    if (messages.isNotEmpty != true || myUserId?.isNotEmpty != true) {
      return false;
    }
    return messages.any((msg) => !isChannelMessageMarkedAsSeen(msg, myUserId));
  }

  static bool isChannelMessageMarkedAsSeen(ChannelMessage message, String? myUserId) {
    if (message.statuses?.isNotEmpty != true || myUserId?.isNotEmpty != true) {
      return false;
    }
    return message.statuses?.firstWhereOrNull((s) => s.userId == myUserId)?.seenAt != null;
  }

  static Future<bool> isRealDevice() async {
    if (kIsWeb) return false;
    if (Platform.isIOS) {
      var deviceInfo = await DeviceInfoPlugin().iosInfo;
      return deviceInfo.isPhysicalDevice;
    } else if (Platform.isAndroid) {
      var deviceInfo = await DeviceInfoPlugin().androidInfo;
      return deviceInfo.isPhysicalDevice;
    }
    return false;
  }

  static onboardingExitDialog(BuildContext context) {
    return showDialog(
      context: context,
      builder: (context) {
        return DialogTemplate(
          title: AppLocale.current.exitSignUpTitle,
          description: AppLocale.current.exitSignUpMessage,
          actionButtonTitle: AppLocale.current.exit,
          cancelButtonTitle: AppLocale.current.exitContinueSignUp,
          onAction: () async {
            Provider.of<UserProvider>(context, listen: false).localData.clear();
            if (!context.mounted) return;
            bool pushToWelcomeScreen = false;
            (context.canPop()) ? context.pop() : pushToWelcomeScreen = true;
            (Navigator.canPop(context)) ? Navigator.pop(context) : pushToWelcomeScreen = true;
            if (pushToWelcomeScreen) {
              context.go(AppRoutes.welcome.path);
            }
          },
        );
      },
    );
  }

  // Show a confirmation dialog to the user, asking if they really want to exit the app or if it was accidental.
  static Future<bool> showAppExitConfirmationDialog(BuildContext context) async {
    bool shouldExit = false;
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return DialogTemplate(
          title: AppLocale.current.exitAppDialogTitle,
          description: AppLocale.current.exitAppDialogMessage,
          actionButtonTitle: AppLocale.current.exitAppDialogExitLabel,
          cancelButtonTitle: AppLocale.current.exitAppDialogContinueUsingLabel,
          onAction: () {
            shouldExit = false;
            Navigator.pop(context);
          },
          onCancel: () {
            shouldExit = true;
            Navigator.pop(context);
          },
        );
      },
    );

    return Future<bool>.value(shouldExit);
  }

  static Future<void> openLink(String link) async {
    try {
      if (await canLaunchUrl(Uri.parse(link))) {
        await launchUrl(Uri.parse(link));
      } else {
        throw 'Could not launch $link';
      }
    } catch (error) {
      debugPrint('$error');
    }
  }

  static Future<void> downloadFileMobile(
    BuildContext context,
    String url,
    String fileName,
    String dir,
  ) async {
    if (Platform.isAndroid) {
      bool permitted = await _checkAndRequestStoragePermission(context);
      if (!permitted) return;
    }
    if (context.mounted) Loader.show(context);
    debugPrint('Downloading file from $url on mobile');
    CustomHTTPClient httpClient = CustomHTTPClient();
    File file;
    String filePath = '';

    try {
      HttpClientResponse response = await httpClient.get(url);
      if (response.statusCode == 200) {
        Uint8List bytes = await consolidateHttpClientResponseBytes(response);
        filePath = '${await FileHelpers.fileDirectoryPath()}/$fileName';
        file = File(filePath);
        debugPrint('Writing file to $filePath');
        await file.writeAsBytes(bytes);
      } else {
        debugPrint('Error downloading file: ${response.statusCode}');
        filePath = 'Error code: ${response.statusCode.toString()}';
      }
    } catch (ex) {
      debugPrint('Error downloading file: $ex');
      // TODO:  handle error
      filePath = 'Can not fetch url';
    }
    debugPrint('downloaded file on mobile');
    if (context.mounted) Loader.hide(context);

    return;
  }

  static String generateNonce(bool hashed) {
    List<int> randomBytes = List<int>.generate(32, (i) => Random.secure().nextInt(256));
    String nonce = base64Url.encode(randomBytes);

    if (hashed) {
      List<int> base64Bytes = utf8.encode(nonce);
      Digest digest = sha256.convert(base64Bytes);
      nonce = digest.toString();
    }
    return nonce;
  }

  static bool isMobilePlatform() {
    try {
      // First check if it's a mobile browser
      if (isMobileBrowser()) return true;

      // Then check native mobile platforms
      if (defaultTargetPlatform == TargetPlatform.iOS ||
          defaultTargetPlatform == TargetPlatform.android) {
        return true;
      }

      // For non-web platforms, use Platform checks as fallback
      if (!kIsWeb) {
        return Platform.isIOS || Platform.isAndroid;
      }

      return false;
    } catch (e) {
      if (kIsWeb) {
        return false; // Handle web case
      }
      rethrow; // Rethrow the error for non-web platforms
    }
  }

  static bool isMobileBrowser() {
    try {
      return kIsWeb &&
          (defaultTargetPlatform == TargetPlatform.iOS ||
              defaultTargetPlatform == TargetPlatform.android);
    } catch (e) {
      return false;
    }
  }

  static bool isApplePlatform() {
    return defaultTargetPlatform == TargetPlatform.iOS ||
        defaultTargetPlatform == TargetPlatform.macOS;
  }

  // TODO: If we ever implement a tablet/small screen UI, this will need to be changed
  // Blocked for now by lack of UI/UX team and engineering time.
  static bool isDesktopPlatform(BuildContext context) {
    return !isMobilePlatform();
  }

  static bool exceedsDesktopWidth(BuildContext context, {bool isChatScreen = false}) {
    return context.mediaQuerySize.width >
        (isChatScreen
            ? context.theme.d.desktopAppBreakpointChat
            : context.theme.d.desktopAppBreakpoint);
  }

  static bool displayDesktopUI(BuildContext context, {bool isChatScreen = false}) {
    return exceedsDesktopWidth(context, isChatScreen: isChatScreen) && isDesktopPlatform(context);
  }

  static seeMoreButton(BuildContext context, {Function? onTap}) {
    return TextButton(
      onPressed: () => onTap?.call(),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingXxSmall),
        child: Column(
          children: [
            Text(
              AppLocale.current.exploreSearchSeeMore,
              style: context.theme.textTheme.labelLarge?.copyWith(
                color: context.theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.bold,
              ),
            ),
            Icon(MicromentorIcons.arrowDropdown, color: context.theme.colorScheme.onSurfaceVariant),
          ],
        ),
      ),
    );
  }

  static setNavigationIconSelection(BuildContext context, Tabs tab) {
    final userProvider = context.read<UserProvider>();
    int index;

    bool isDesktop =
        (navigatorKey.currentContext != null)
            ? displayDesktopUI(navigatorKey.currentContext!)
            : displayDesktopUI(context);

    if (isDesktop) {
      index = (desktopNavigationTabs(userProvider.myUser?.hasTrainings ?? false)).indexOf(tab);
    } else {
      index = (mobileNavigationTabs(userProvider.myUser?.hasTrainings ?? false)).indexOf(tab);
    }

    final ScaffoldModel scaffoldModel = context.read<ScaffoldModel>();

    //Index can be negative in case of profile tabs, when we resize website window
    if ((scaffoldModel.selectedTabIndex != (index == -1 ? 0 : index))) {
      scaffoldModel.setParams(index: index);
    }
  }

  static List<Tabs> mobileNavigationTabs(bool hasTraining) {
    return Tabs.values.where((tab) {
      if (StaticAppFeatures.vts && hasTraining) {
        return tab != Tabs.profile;
      } else {
        return tab != Tabs.trainings;
      }
    }).toList();
  }

  static List<Tabs> desktopNavigationTabs(bool hasTraining) {
    if ((StaticAppFeatures.vts) && hasTraining) {
      return Tabs.values;
    }

    return Tabs.values.where((tab) {
      return tab != Tabs.trainings;
    }).toList();
  }

  static switchWithTitle({
    required BuildContext context,
    required String title,
    bool showOnOffTitle = false,
    bool initialValue = false,
    required Function(bool isActive) onChanged,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: context.theme.d.chipPadding,
        vertical: context.theme.d.chipPadding,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(child: Text(title, style: context.theme.textTheme.bodyMedium!)),
          SizedBox(height: context.theme.d.chipPadding),
          CustomSwitch(onChanged: onChanged, initialValue: initialValue, showOnOff: showOnOffTitle),
        ],
      ),
    );
  }

  static Duration snackBarDuration = const Duration(seconds: 3);

  static topDivider(BuildContext context) {
    return displayDesktopUI(context)
        ? Border(top: BorderSide(color: context.colorScheme.primaryContainer))
        : null;
  }

  static checkForNonMigratedGroups(User? user) {
    if (user == null) return;
    if (user.groups.isNotEmpty == true) {
      final nonMigratedGroups =
          user.groups
              .where((g) => g.isMigratedToMm3 == false && g.mm2RedirectUrl?.isNotEmpty == true)
              .toList();

      // redirect the user if any of these groups exist
      if (nonMigratedGroups.isNotEmpty == true &&
          (nonMigratedGroups[0].mm2RedirectUrl ?? '').isNotEmpty) {
        openLink(nonMigratedGroups[0].mm2RedirectUrl ?? '');
      }
    }
  }

  static setLocaleValues(BuildContext context, User? myUser) {
    var localeModel = Provider.of<LocaleModel>(context, listen: false);

    // Set locale saved in account settings, fallback locale, or use device locale
    if (myUser?.selectedUiLanguageTextId != null) {
      localeModel.setLanguage(myUser?.selectedUiLanguageTextId);
    } else if (myUser?.fallbackUiLanguageTextId != null) {
      localeModel.setLanguage(myUser?.fallbackUiLanguageTextId);
    } else {
      var deviceLocale = localeModel.getDeviceLanguage();
      localeModel.set(deviceLocale);
    }
  }

  static setTrackId(BuildContext context, GoRouterState state) {
    if (state.uri.queryParameters.isNotEmpty) {
      final String? trackId = state.uri.queryParameters[RouteParams.trackId];
      if (trackId != null) {
        Provider.of<LocalDataModel>(context, listen: false).trackId = trackId;
      }
    }
  }

  static Widget noPreviewAvaliable(BuildContext context) {
    return Container(
      height: context.theme.d.boxSizeXXLarge,
      width: double.maxFinite,
      alignment: Alignment.center,
      child: Text(AppLocale.current.noPreviewAvaliable),
    );
  }
}

Future<bool> _checkAndRequestStoragePermission(BuildContext context) async {
  if (await isAndroid13OrAbove()) return true;
  PermissionStatus status = await Permission.storage.status;

  if (status.isGranted) {
    return true;
  } else if (status.isPermanentlyDenied) {
    if (!context.mounted) return false;
    debugPrint('Storage permission permanetly denied');
    await showDialog(
      context: context,
      builder:
          (context) => DialogTemplate(
            description: AppLocale.current.trainingDownloadCertificatesPermissionDeniedMsg,
            title: AppLocale.current.trainingDownloadCertificatesPermissionLabel,
            actionButtonTitle: AppLocale.current.trainingDownloadCertificatesOpenSettingsLabel,
            actionButtonIcon: Image.asset(Assets.openInNew, height: context.theme.d.iconSizeMedium),
            onAction: () {
              Navigator.of(context).pop();
              openAppSettings();
            },
          ),
    );
    return false;
  } else {
    if (!context.mounted) return false;
    debugPrint('Storage permission is not granted!');
    await showDialog(
      context: context,
      builder:
          (dialogContext) => DialogTemplate(
            title: AppLocale.current.trainingDownloadCertificatesPermissionLabel,
            description: AppLocale.current.trainingDownloadCertificatesStoragePermissionRequest,
            onAction: () async {
              Navigator.of(dialogContext).pop();
              await Permission.storage.request();
            },
          ),
    );
    return false;
  }
}

Future<bool> isAndroid13OrAbove() async {
  if (Platform.isAndroid) {
    final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;

    if (androidInfo.version.sdkInt >= 33) {
      return true;
    } else {
      return false;
    }
  }
  return false;
}

String getLocalizedUrl({required LegalDocumentType urlType, required LocaleModel localeModel}) {
  bool isEnglish = localeModel.isEnglish();
  String? languageCode;
  if (!isEnglish) {
    languageCode = localeModel.getCurrentLanguageCode();
  }

  switch (urlType) {
    case LegalDocumentType.codeOfConduct:
      return isEnglish
          ? Identifiers.codeOfConductUrl
          : '${Identifiers.codeOfConductUrl}-$languageCode';

    case LegalDocumentType.termsOfUse:
      return isEnglish ? Identifiers.termsOfUseUrl : '${Identifiers.termsOfUseUrl}-$languageCode';

    case LegalDocumentType.privacyPolicy:
      return isEnglish
          ? Identifiers.privacyPolicyUrl
          : '${Identifiers.privacyPolicyUrl}-$languageCode';
  }
}
