import 'dart:async';
import 'dart:io';

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart' show TargetPlatform, defaultTargetPlatform, kIsWeb;
import 'package:flutter/material.dart';
import 'package:mm_flutter_app/utilities/errors/error_handler.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:retry/retry.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../models/models.dart';
import '../../widgets/shared/error_widget.dart';
import '../debug_logger.dart';
import 'errors.dart';
import 'exceptions.dart';

class CrashHandler {
  bool _isReleaseMode = false;
  bool _isCollectionEnabled = false;
  static bool _isCrashlyticsSupported = false;
  late final LocalDataModel _localData;

  CrashHandler(LocalDataModel localDataModel, bool isReleaseMode, bool isCollectionEnabled) {
    _isReleaseMode = isReleaseMode;
    _isCollectionEnabled = isCollectionEnabled;
    _localData = localDataModel;
    if (kIsWeb) {
      _isCrashlyticsSupported = false;
    } else {
      switch (defaultTargetPlatform) {
        case TargetPlatform.android:
        case TargetPlatform.iOS:
          _isCrashlyticsSupported = true;
        default:
          {
            _isCrashlyticsSupported = false;
            break;
          }
      }
      _initializeCrashlytics();
    }

    _initializeSentry();
    _configureCrashHandlerUserScope();
  }

  static Future<void> updateUserId(String userId) async {
    if (kIsWeb) return;
    bool userDeniedTrackingConsent = false;
    if (Platform.isIOS) {
      final status = await Permission.appTrackingTransparency.status;
      userDeniedTrackingConsent = status.isDenied || status.isPermanentlyDenied;
    }

    if (!userDeniedTrackingConsent) {
      if (_isCrashlyticsSupported) {
        FirebaseCrashlytics.instance.setUserIdentifier(userId);
      }
      Sentry.configureScope((scope) {
        // https://develop.sentry.dev/sdk/data-model/event-payloads/user/
        // not including email for privacy reasons
        scope.setUser(SentryUser(id: userId, ipAddress: '{{auto}}'));
      });
    }
  }

  void _initializeCrashlytics() {
    if (_isCrashlyticsSupported) {
      FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(
        _isReleaseMode && _isCollectionEnabled,
      );
    }
  }

  void _initializeSentry() {
    Sentry.configureScope((scope) {
      scope.setTag('environment', _isReleaseMode ? 'production' : 'development');
    });
  }

  static FutureOr<T> retryOnException<T>(
    FutureOr<T> Function() operation, {
    FutureOr<T> Function()? onFailOperation,
    RetryOptions retryOptions = const RetryOptions(maxAttempts: 4),
    bool logFailures = true,
  }) async {
    try {
      return await retryOptions.retry<T>(
        operation,
        retryIf: (e) {
          return (e is RetryException && !e.message.contains(AppErrorHandler.tooManyRequestsError));
        },
        onRetry: (e) {
          if (logFailures) {
            logCrashReport('Retrying after exception: ${(e as RetryException).message}.');
          }
        },
      );
    } catch (e) {
      if (e is RetryException && onFailOperation != null) {
        if (logFailures) {
          logCrashReport(
            'Maximum number of retry attempts reached: ${e.message}'
            '\nExecuting onFailOperation callback.',
          );
          sendCrashReport(exception: e);
        }
        return onFailOperation();
      }
      rethrow;
    }
  }

  void handleUncaughtFlutterError(FlutterErrorDetails details) async {
    logCrashReport('Application error/exception caught by FlutterError handler.');
    if (!_isReleaseMode) {
      FlutterError.presentError(details);
    }

    final Object e = details.exception;
    if (e is Exception) {
      _handleException(e);
    } else if (e is Error) {
      _handleError(e);
    } else {
      logCrashReport('Unknown error/exception: ${e.toString()}');
    }

    sendCrashReport(details: details, isFatal: e is Error);
  }

  bool handleUncaughtPlatformError(Object error, StackTrace trace) {
    logCrashReport('Error caught by the PlatformDispatcher handler.');
    if (error is RetryException) {
      // RetryExceptions are handled here only when thrown from retry function,
      // meaning that the operation exhausted the max number of attempts.
      logCrashReport('Maximum number of retry attempts reached: ${error.message}');
      return true;
    }
    sendCrashReport(exception: error, trace: trace, isFatal: error is Error);
    // Return false to allow error propagation to the next handler.
    return false;
  }

  Widget handleBuildError(FlutterErrorDetails details) {
    logCrashReport('Error occurred during widget build: ${details.summary}');
    return const CustomErrorWidget();
  }

  static void logCrashReport(String message) {
    try {
      DebugLogger.error(message);
      if (_isCrashlyticsSupported) {
        FirebaseCrashlytics.instance.log(message);
      }
      Sentry.captureMessage(message);
    } catch (_) {
      // Do nothing if error logging fails, as we do not want the logger itself
      // to crash the app.
    }
  }

  static void sendCrashReport({
    dynamic exception,
    StackTrace? trace,
    FlutterErrorDetails? details,
    bool isFatal = false,
  }) {
    if (!_isCrashlyticsSupported) {
      return;
    }

    if (details != null) {
      if (_isCrashlyticsSupported) {
        FirebaseCrashlytics.instance.recordFlutterError(details, fatal: isFatal);
      }
      Sentry.captureException(details.exception, stackTrace: details.stack);
    } else if (exception != null) {
      if (_isCrashlyticsSupported) {
        FirebaseCrashlytics.instance.recordError(exception, trace, fatal: isFatal);
      }
      Sentry.captureException(exception, stackTrace: trace);
    }
  }

  void _handleException(Exception e) {
    if (e is AppException) {
      logCrashReport('App Exception ID ${e.exceptionId}: ${e.message}');
    } else {
      logCrashReport('Exception: ${e.toString()}');
    }
  }

  void _handleError(Error e) {
    if (e is AppError) {
      logCrashReport('App Error ID ${e.errorId}: ${e.message}');
    } else {
      logCrashReport('Error: ${e.toString()}');
    }
  }

  // docs: https://firebase.flutter.dev/docs/crashlytics/usage/#set-user-identifiers
  // https://docs.sentry.io/platforms/flutter/enriching-events/scopes/

  Future<void> _configureCrashHandlerUserScope() async {
    bool userDeniedTrackingConsent = false;
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.iOS) {
      final status = await Permission.appTrackingTransparency.status;
      userDeniedTrackingConsent = status.isDenied || status.isPermanentlyDenied;
    }

    if (!userDeniedTrackingConsent) {
      // it's ok to use empty string for userId
      String userId = _localData.userId;
      String deviceUuid = _localData.deviceUuid;

      if (deviceUuid.isEmpty) {
        Sentry.captureMessage('Device UUID is empty.');
        return;
      }

      // TODO: only do this if we have permission to track the user (i.e. after they agree to our terms before signup).
      // Apple users can deny permission to track, but I'm not sure how we store that.'
      if (userId.isEmpty) {
        userId = 'anonymous';
      }
      if (_isCrashlyticsSupported) {
        FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
        FirebaseCrashlytics.instance.setUserIdentifier(userId);
        FirebaseCrashlytics.instance.setCustomKey('deviceUuid', deviceUuid);
      }
      Sentry.configureScope((scope) {
        // https://develop.sentry.dev/sdk/data-model/event-payloads/user/
        // not including email for privacy reasons
        scope.setUser(SentryUser(id: userId, ipAddress: '{{auto}}'));
        scope.setTag('deviceUuid', deviceUuid);
      });
    }
  }
}
