import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:collection/collection.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/router/app_router.dart';
import 'package:mm_flutter_app/services/graphql/providers/content_provider.dart';
import 'package:mm_flutter_app/services/graphql/providers/user_provider.dart';
import 'package:another_flushbar/flushbar.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

class AppErrorHandler {
  BuildContext context;
  OperationException? exception;
  String? message;
  Enum$ErrorCode? errorCode;
  // note: this doesnt exactly match the error message. We check if
  // the error message contians this string, rather than checking
  // for equality.
  static String tooManyRequestsError = 'Too many requests';

  AppErrorHandler({required this.context, this.exception, this.message, this.errorCode}) {
    final ContentProvider contentProvider = Provider.of<ContentProvider>(context, listen: false);

    String? errorMessage;
    if (exception?.linkException != null) {
      if (exception?.linkException is ServerException) {
        // If it is ServerException it can be anything like NoInternet, Timeout etc
        // but getting "statusCode" and "parsedResponse" both null so not able to differentiate between different network related errors here
        ServerException serverException = exception?.linkException as ServerException;
        errorMessage = serverException.parsedResponse?.errors?.firstOrNull?.message;
        serverException.statusCode;
        debugPrint('ServerException : $errorMessage - ${serverException.statusCode}');
        ///////////////////
      }
      final error = contentProvider.errorCodeOptions?.firstWhere(
        (o) => o.textId == Enum$ErrorCode.failedToConnect.name,
      );
      errorMessage = error?.translatedValue ?? AppLocale.current.errorMessageFailedToConnect;
    } else if (exception != null) {
      final code = exception?.graphqlErrors.firstOrNull?.message;
      final error = contentProvider.errorCodeOptions?.firstWhereOrNull((o) => o.textId == code);

      // Handled unauthorized error
      if (error?.textId == Enum$ErrorCode.unauthorized.name &&
          AppRouter.getLastRoutePath(context) != AppRoutes.signin.path) {
        Provider.of<UserProvider>(context, listen: false).clearData(context);
        context.go(AppRoutes.signin.path);
        return;
      }
      errorMessage = error?.translatedValue ?? AppLocale.current.errorMessageUnknownError;
    } else {
      final error =
          contentProvider.errorCodeOptions?.where((o) => o.textId == errorCode?.name).firstOrNull;
      errorMessage =
          error?.translatedValue ?? message ?? AppLocale.current.errorMessageUnknownError;
    }

    Future.delayed(const Duration(milliseconds: 500), () {
      if (!context.mounted) return;
      Flushbar(message: errorMessage, duration: AppUtility.snackBarDuration).show(context);
    });
  }
}
