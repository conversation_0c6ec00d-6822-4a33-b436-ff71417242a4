import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import '../../constants/constants.dart';

class UserHelpers {
  static bool isUserAMentee(User user) {
    if (user.seeksHelp != true) {
      return false;
    }
    final MenteesGroupMembership? menteesGroupMembership =
        user.groupMemberships
                .where((element) => element.groupIdent == GroupIdent.mentees.name)
                .firstOrNull
            as MenteesGroupMembership?;

    return menteesGroupMembership != null;
  }

  static bool isUserAMentor(User user) {
    if (user.offersHelp != true) {
      return false;
    }
    final MentorsGroupMembership? mentorsGroupMembership =
        user.groupMemberships
                .where((element) => element.groupIdent == GroupIdent.mentors.name)
                .firstOrNull
            as MentorsGroupMembership?;

    return mentorsGroupMembership != null;
  }

  static String getLocationText(String? country, String? city, String? region) {
    String locationText = '';
    if (city != null && city.isNotEmpty) {
      locationText = city;
    }
    if (region != null && region.isNotEmpty) {
      locationText = locationText.isNotEmpty ? '$locationText, $region' : region;
    }
    if (country != null && country.isNotEmpty) {
      locationText = locationText.isNotEmpty ? '$locationText, $country' : country;
    }

    return locationText.isNotEmpty
        ? '$locationText, $country'
        : AppLocale.current.defaultValueLocation;
  }
}
