import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../models/scaffold_model.dart';

mixin NavigationMixin<T extends StatefulWidget> on State<T> implements RouteAware {
  late final ScaffoldModel _scaffoldModel;
  late GoRouter _router;
  late final RouteObserver<PageRoute> _routeObserver;
  PageRoute? _pageRoute;

  GoRouter get router => _router;

  @override
  void initState() {
    super.initState();
    _scaffoldModel = Provider.of<ScaffoldModel>(context, listen: false);
    _routeObserver = Provider.of<RouteObserver<PageRoute>>(context, listen: false);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _router = GoRouter.of(context);
    if (_pageRoute != null) {
      _routeObserver.unsubscribe(this);
    }
    if (ModalRoute.of(context) is PageRoute) {
      _pageRoute = ModalRoute.of(context)! as PageRoute;
      _routeObserver.subscribe(this, _pageRoute!);
    }
  }

  @override
  void dispose() {
    _routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPop() {
    /// instead of manually clearing scaffold added by this mixin
    /// we used this approach to get clear scaffold automatically after screen get popped, i.e to clear it here in didPop.
    /// took reference from official doc: https://api.flutter.dev/flutter/widgets/RouteAware-class.html
    _resetAppBar();
  }

  @override
  void didPopNext() {}

  @override
  void didPush() {}

  @override
  void didPushNext() {
    _resetAppBar();
  }

  void buildPageRouteScaffold(void Function(ScaffoldModel scaffoldModel) build) {
    if (_pageRoute == null) return;
    if (_pageRoute?.isCurrent ?? false) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        build(_scaffoldModel);
      });
    }
  }

  isCurrentPageRoute() {
    if (mounted && ModalRoute.of(context) is DialogRoute) return true;
    return _pageRoute?.isCurrent ?? false;
  }

  void _resetAppBar() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scaffoldModel.clear();
    });
  }
}
