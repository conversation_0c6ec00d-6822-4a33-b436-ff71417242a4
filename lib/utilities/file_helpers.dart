import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

class FileInfo {
  String filePath;
  String fileName;

  FileInfo({required this.filePath, required this.fileName});
}

class FileHelpers {
  FileHelpers._private();
  static String generateUuid() {
    return const Uuid().v1();
  }

  static Future<String> compressImage({
    required String imagePath,
    CompressFormat format = CompressFormat.png,
    int minWidth = 800,
    int minHeight = 800,
    int quality = 90,
  }) async {
    final tmpDir = (await getTemporaryDirectory()).path;
    final targetPath = '$tmpDir/${DateTime.now().millisecondsSinceEpoch}.png';

    debugPrint('Calling FlutterImageCompress.compressAndGetFile...');
    final result = await FlutterImageCompress.compressAndGetFile(
      imagePath,
      targetPath,
      format: format,
      quality: quality,
    );

    if (result == null) {
      return '';
    }

    return targetPath;
  }

  static Future<List<int>> getFileBinaryData(Object? file) async {
    if (file is XFile) {
      return file.readAsBytes();
    }

    if (file is PlatformFile) {
      if (kIsWeb) {
        return file.bytes!;
      }
      String? filePath = file.path;
      File fileObj = File(filePath!);
      return fileObj.readAsBytes();
    }

    throw Exception('Invalid file type');
  }

  static FileInfo getFileInfo(Object? file) {
    String fileName;
    String filePath;

    if (file is XFile) {
      fileName = file.name;
      filePath = file.path;
    } else if (file is PlatformFile) {
      fileName = file.name;
      // see: https://github.com/miguelpruivo/flutter_file_picker/wiki/FAQ
      filePath = '';
    } else {
      throw Exception('Invalid file type');
    }

    return FileInfo(filePath: filePath, fileName: fileName);
  }

  static List<String> getSupportedImageFileExtensions() {
    List<String> acceptableFileType = ['jpeg', 'jpg', 'png'];

    if (AppUtility.isMobilePlatform()) {
      acceptableFileType.addAll(['heic', 'heif']);
    }

    return acceptableFileType;
  }

  static Future<String> fileDirectoryPath() async {
    final androidVersion = int.parse(Platform.version.split('.')[0]);

    final Directory? directory;

    if (Platform.isAndroid) {
      if (androidVersion >= 30) {
        // For Android 11 and above, use app-specific storage
        directory = await getExternalStorageDirectory();
      } else {
        // For Android 10 and below, save directly to Downloads folder
        directory = Directory('/storage/emulated/0/Download');
      }
    } else {
      directory = await getApplicationDocumentsDirectory();
    }

    if (directory == null) {
      throw Exception('Unable to get external storage directory');
    }
    return directory.path;
  }
}
