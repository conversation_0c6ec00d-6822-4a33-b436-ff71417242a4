import 'dart:async'; // Required for runZoneGuarded
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
//import 'package:flutter/semantics.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/flavor_config.dart';
import 'package:mm_flutter_app/main/my_app.dart';
import 'package:mm_flutter_app/services/graphql/providers/admin_provider.dart';
import 'package:mm_flutter_app/services/graphql/providers/uploaded_assets_provider.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:flutter_web_plugins/url_strategy.dart';

import '../models/models.dart';
import '../services/firebase/firebase_service.dart';
import '../services/graphql/graphql.dart';
import '../services/graphql/providers/providers.dart';

final navigatorKey = GlobalKey<NavigatorState>();
final FirebaseService firebaseService = FirebaseService();

init() async {
  // runZonedGuarded is used to catch all exceptions and send them to Sentry. https://github.com/getsentry/sentry-dart/issues/263
  runZonedGuarded(
    () async {
      initApp();
    },
    (exception, stackTrace) async {
      await Sentry.captureException(exception, stackTrace: stackTrace);
    },
  );
}

initApp() async {
  if (kIsWeb) {
    usePathUrlStrategy();
    GoRouter.optionURLReflectsImperativeAPIs = true;
  }
  WidgetsFlutterBinding.ensureInitialized();

  bool isRealDevice = await AppUtility.isRealDevice();
  const String appFlavor = String.fromEnvironment('FLUTTER_APP_FLAVOR');
  FlavorConfig(appFlavor: appFlavor, isRealDevice: isRealDevice);

  // Initialize Sentry
  await _initSentryFlutter();
  await _setPortraitModeOnly();

  final serverUrl = FlavorConfig.instance.graphUrl();
  final assetUploadUrl = FlavorConfig.instance.assetUploadUrl();
  final subscriptionUrl = FlavorConfig.instance.subscriptionUrl();

  final localData = LocalDataModel();
  await localData.init();
  await firebaseService.init(localData);
  firebaseService.setup(localData);

  debugPrint('Server: $serverUrl');
  if (serverUrl.isEmpty || subscriptionUrl.isEmpty) {
    debugPrint('Set your server and websockets URLs in FlavorConfig.');
    return;
  }

  final graphql = GraphqlService(
    serverUrl: serverUrl,
    subscriptionUrl: subscriptionUrl,
    localData: localData,
  );
  graphql.connect();

  runApp(
    GraphQLProvider(
      client: graphql.client,
      child: GraphQLConsumer(
        builder: (client) {
          final systemProvider = SystemProvider(client: client, localData: localData);

          return MultiProvider(
            providers: [
              // System Providers:
              ChangeNotifierProvider(create: (context) => ScaffoldModel(context: context)),
              ChangeNotifierProvider(create: (context) => ThemeProvider()),
              ChangeNotifierProvider(create: (context) => LocaleModel()),
              Provider<RouteObserver<PageRoute>>.value(value: RouteObserver<PageRoute>()),
              Provider<LocalDataModel>.value(value: localData),
              Provider<SystemProvider>.value(value: systemProvider),
              ChangeNotifierProvider(create: (context) => MultiStepActionProvider(client: client)),
              ChangeNotifierProvider(create: (context) => ContentProvider(client: client)),
              ChangeNotifierProvider(create: (context) => ExploreCardFiltersModel()),
              ChangeNotifierProvider(
                create:
                    (context) => UserProvider(
                      client: client,
                      firebaseService: firebaseService,
                      localData: localData,
                      systemProvider: systemProvider,
                    ),
              ),
              ChangeNotifierProvider(create: (context) => MessagesProvider(client: client)),
              ChangeNotifierProvider(
                create:
                    (context) =>
                        ChannelsProvider(client: client, context: context, localData: localData),
              ),
              ChangeNotifierProvider(
                create: (context) => InvitationsProvider(client: client, context: context),
              ),
              ChangeNotifierProvider(
                create:
                    (context) =>
                        InboxProvider(client: client, context: context, localData: localData),
              ),
              ChangeNotifierProvider(
                create:
                    (context) => UploadedAssetsProvider(
                      client: client,
                      context: context,
                      localData: localData,
                      assetUploadUrl: assetUploadUrl,
                    ),
              ),
              ChangeNotifierProvider(
                create: (context) => AdminProvider(client: client, localData: localData),
              ),
              ChangeNotifierProvider(create: (context) => VtsProvider(client: client)),
              Provider<OnboardingModel>(create: (context) => OnboardingModel()),
              ChangeNotifierProvider(create: (context) => GroupProvider(client: client)),
            ],
            child: const MyApp(),
          );
        },
      ),
    ),
  );
  //SemanticsBinding.instance.ensureSemantics();
}

_setPortraitModeOnly() async {
  if (AppUtility.isMobilePlatform()) {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }
}

_initSentryFlutter() async {
  final sentryDsn = FlavorConfig.sentryDsn();

  await SentryFlutter.init((options) {
    options.dsn = sentryDsn;
    options.attachScreenshot = true;
    options.environment = FlavorConfig.instance.flavor.toString();
    // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing.
    // We recommend adjusting this value in production.
    options.tracesSampleRate = 0.1;
    // The sampling rate for profiling is relative to tracesSampleRate
    // Setting to 1.0 will profile 100% of sampled transactions:
    options.profilesSampleRate = 0.1;
  });
}
