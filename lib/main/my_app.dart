import 'dart:ui';

import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/debug_logger.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../__generated/schema/schema.graphql.dart';
import '../constants/constants.dart';
import '../models/models.dart';
import '../router/app_router.dart';
import '../services/firebase/firebase_auth_service.dart';
import '../services/firebase/firebase_service.dart';
import '../services/graphql/providers/providers.dart';
import '../services/themes/themes.dart';
import 'app.dart';

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp>
    with FirebaseServiceListener, FirebaseAuthListener, UserProviderListener {
  late final AppLifecycleListener _appLifeCycleListener;
  late final UserProvider _userProvider;
  late final LocalDataModel _localData;
  late final LocaleModel _localeModel;
  late GoRouter router;
  bool _didReportSessionStarted = false;
  final navigatorKey = GlobalKey<NavigatorState>();

  @override
  void initState() {
    super.initState();
    _appLifeCycleListener = AppLifecycleListener(onStateChange: _onStateChanged);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _localeModel = Provider.of<LocaleModel>(context, listen: false);
    firebaseService.addListener(this);
    _userProvider.addUserProviderListener(this);
  }

  @override
  void dispose() {
    _appLifeCycleListener.dispose();
    firebaseService.shutDownService();
    _userProvider.removeUserProviderListener('_StartScreenState');
    super.dispose();
  }

  void _onStateChanged(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.detached:
        debugPrint('App detached');
      case AppLifecycleState.resumed:
        debugPrint('App resumed');
        _reportSessionStarted();
      case AppLifecycleState.inactive:
        debugPrint('App inactive');
        _reportSessionEnded();
      case AppLifecycleState.hidden:
        debugPrint('App hidden');
      case AppLifecycleState.paused:
        debugPrint('App paused');
    }
  }

  void _reportSessionStarted() {
    _didReportSessionStarted = true;
    if (!kIsWeb && firebaseService.firebaseToken == null) {
      debugPrint('_reportSessionStarted called without having firebaseToken.');
      return;
    }
    if (_userProvider.myUser == null) return;
    _userProvider.startMySession(
      deviceUuid: _localData.deviceUuid,
      pushNotificationToken: firebaseService.firebaseToken ?? '',
    );
  }

  void _reportSessionEnded() {
    _didReportSessionStarted = true;
    if (_userProvider.myUser == null) return;
    Provider.of<UserProvider>(context, listen: false).endMySession();
  }

  @override
  void didChangeDependencies() async {
    super.didChangeDependencies();
  }

  @override
  String firebaseServiceListenerName() {
    return '_StartScreenState';
  }

  @override
  void onNewFirebaseTokenReceived(String firebaseToken) {
    // If we haven't yet reported the starting of the session, we'll send
    // the Firebase token together with that, instead of here.
    if (_didReportSessionStarted) {
      if (_localData.deviceUuid.isNotEmpty) {
        Provider.of<UserProvider>(context, listen: false).updateUserDevice(
          input: Input$UserDeviceInput(
            deviceUuid: _localData.deviceUuid,
            pushNotificationToken: firebaseToken,
          ),
        );
      }
    }
  }

  @override
  String firebaseAuthListenerName() {
    return '_StartScreenState';
  }

  @override
  void onSignedIn(final User myUser) {
    var onboardingModel = Provider.of<OnboardingModel>(context, listen: false);

    if (myUser.onboardingStage != null &&
        myUser.onboardingStage != '' &&
        OnboardingModel.isValidStage(myUser.onboardingStage)) {
      try {
        final stage = OnboardingStage.values.byName(myUser.onboardingStage ?? '');
        if (!onboardingModel.isCompleted(stage)) {
          router.push(onboardingModel.getRouteFromStage(stage).path);
          return;
        }
      } catch (error) {
        DebugLogger.error(
          'UserProvider.getMyUser: user has invalid onboarding stage: ${myUser.onboardingStage}',
        );
      }
    }

    AppUtility.checkForNonMigratedGroups(myUser);

    router.pushNamed(
      AppRoutes.root.name,
      queryParameters: {RouteParams.nextRouteName: AppRoutes.home.name},
    );
  }

  @override
  Future<bool> onUserSignedIntoFirebase(
    firebase_auth.User user,
    Enum$IdentityProvider identityProvider,
  ) async {
    return true;
  }

  @override
  Future<void> onUserSignedOutOfFirebase() async {
    await Provider.of<UserProvider>(context, listen: false).signOutUser(context);
  }

  @override
  String get userProviderListenerName => '_StartScreenState';

  @override
  Widget build(BuildContext context) {
    router = AppRouter.createRouter(context);
    return Consumer2<LocaleModel, ThemeProvider>(
      builder: (context, filters, themeProvider, _) {
        return MaterialApp.router(
          routerDelegate: router.routerDelegate,
          routeInformationParser: router.routeInformationParser,
          routeInformationProvider: router.routeInformationProvider,
          title: Identifiers.appName,
          debugShowCheckedModeBanner: false,
          theme: Themes.light(
            'Micromentor',
            themeProvider.selectedPrimaryColor,
            themeProvider.selectedSecondaryColor,
          ),
          darkTheme:
              (context.theme.appFeatures.darkTheme)
                  ? Themes.dark(
                    'Micromentor',
                    themeProvider.selectedPrimaryColor,
                    themeProvider.selectedSecondaryColor,
                  )
                  : null,
          locale: _localeModel.locale ?? _localeModel.getDeviceLanguage(),
          localizationsDelegates: const [
            AppLocale.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: _localeModel.getSupportedLocales(),
          scrollBehavior: kIsWeb ? WebScrollBehavior() : null,
          backButtonDispatcher: CustomBackButtonDispatcher(router: router),
        );
      },
    );
  }
}

class WebScrollBehavior extends MaterialScrollBehavior {
  // Override behavior methods and getters like dragDevices
  @override
  Set<PointerDeviceKind> get dragDevices => {
    PointerDeviceKind.touch,
    PointerDeviceKind.mouse,
    PointerDeviceKind.trackpad,
  };
}

// Override the Android back button behavior on top-level screens to prevent accidental exits.
class CustomBackButtonDispatcher extends RootBackButtonDispatcher {
  final GoRouter router;

  CustomBackButtonDispatcher({required this.router});

  @override
  Future<bool> invokeCallback(Future<bool> defaultValue) async {
    if (router.routerDelegate.canPop()) {
      return await super.invokeCallback(defaultValue);
    } else {
      super.invokeCallback(Future<bool>.value(false));
      bool shouldExit = await AppUtility.showAppExitConfirmationDialog(
        router.routerDelegate.navigatorKey.currentContext!,
      );

      return shouldExit ? Future<bool>.value(true) : Future<bool>.value(false);
    }
  }
}
