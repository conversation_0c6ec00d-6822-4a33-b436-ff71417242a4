import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:provider/provider.dart';

import '../constants/constants.dart';
import '../models/models.dart';
import '../services/graphql/providers/base/operation_result.dart';
import '../services/graphql/providers/providers.dart';
import '../utilities/errors/crash_handler.dart';
import '../utilities/utility.dart';
import '../widgets/widgets.dart';

class SplashScreen extends StatefulWidget {
  final String nextRouteName;
  final Map<String, String> pathParameters;
  const SplashScreen({super.key, required this.nextRouteName, this.pathParameters = const {}});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  late final Future<OperationResult<AllOptionsByType>> _findAllOptionsByTypeOperationResult;
  Future<LoadObjectResult>? _loadObjectResult;
  late final MultiStepActionProvider _multiStepActionProvider;
  late final ContentProvider _contentProvider;
  late final UserProvider _userProvider;
  late final ExploreCardFiltersModel _filtersModel;
  late final OnboardingModel _onboardingModel;
  late final ScaffoldModel _scaffoldModel;
  String? groupIdent;

  @override
  void initState() {
    super.initState();

    _multiStepActionProvider = Provider.of<MultiStepActionProvider>(context, listen: false);
    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _filtersModel = Provider.of<ExploreCardFiltersModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _scaffoldModel = Provider.of<ScaffoldModel>(context, listen: false);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _findAllOptionsByTypeOperationResult = _contentProvider.findAllOptionsByType(
      fetchPolicy: FetchPolicy.networkOnly,
    );

    if (_userProvider.myUser != null) {
      _loadObjectResult = Future.delayed(
        Duration.zero,
        () => LoadObjectResult(object: _userProvider.myUser),
      ).then((value) {
        if (mounted) AppUtility.setLocaleValues(context, _userProvider.myUser);
        return value;
      });
    }

    _loadObjectResult = _userProvider.loadUser().then((value) {
      if (mounted) AppUtility.setLocaleValues(context, _userProvider.myUser);
      return value;
    });
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<OperationResult<AllOptionsByType>>(
      future: _findAllOptionsByTypeOperationResult,
      builder: (
        BuildContext context,
        AsyncSnapshot<OperationResult<AllOptionsByType>> optionsSnapshot,
      ) {
        return AppUtility.widgetForAsyncSnapshot(
          snapshot: optionsSnapshot,
          isAppLaunch: true,
          onReady:
              () => FutureBuilder<LoadObjectResult>(
                future: _loadObjectResult,
                builder: (BuildContext context, AsyncSnapshot<LoadObjectResult?> userSnapshot) {
                  return AppUtility.widgetForAsyncSnapshot(
                    snapshot: userSnapshot,
                    isAppLaunch: true,
                    onError: () => const WelcomeScreen(),
                    onReady: () {
                      if (userSnapshot.data?.errorCode != null) {
                        // todo: show error
                      }
                      final user = userSnapshot.data?.object;
                      if (user == null) {
                        return widget.nextRouteName == AppRoutes.home.name
                            ? const WelcomeScreen()
                            : SignInScreen(nextRouteName: widget.nextRouteName);
                      }

                      WidgetsBinding.instance.addPostFrameCallback((_) async {
                        _filtersModel.setFilters();
                        _filtersModel.setAdvancedFilters();
                        _multiStepActionProvider.reset();

                        if (!context.mounted) return;

                        if (_userProvider.myUser != null) {
                          CrashHandler.updateUserId(_userProvider.myUser!.id);
                        }

                        // Is the user done with the onboarding flow?
                        try {
                          bool showCreateAccountScreen =
                              (((user as User).firstName ?? '').isEmpty == true ||
                                      (user.lastName ?? '').isEmpty == true)
                                  ? true
                                  : false;

                          final stage =
                              user.onboardingStage?.isNotEmpty == true
                                  ? OnboardingStage.values.byName(user.onboardingStage!)
                                  : showCreateAccountScreen
                                  ? _onboardingModel.createAccountStage()
                                  : _onboardingModel.firstStage();

                          _setGroups(user);
                          if (!_onboardingModel.isCompleted(stage)) {
                            await _fetchGroupsData();
                            if (context.mounted) {
                              context.pushReplacement(
                                _onboardingModel.getRouteFromStage(stage).path,
                              );
                            }

                            return;
                          }
                        } catch (_) {}

                        int selectedTabIndex = Tabs.home.index;
                        String nextRouteName = widget.nextRouteName;
                        if (widget.nextRouteName.contains(AppRoutes.inboxChats.name)) {
                          selectedTabIndex = Tabs.inbox.index;
                        } else if (widget.nextRouteName.contains(AppRoutes.trainings.name)) {
                          selectedTabIndex = Tabs.trainings.index;
                        }
                        // Show training tab for striveIndonesia users
                        if (groupIdent?.toLowerCase() ==
                            GroupIdent.striveIndonesia.name.toLowerCase()) {
                          selectedTabIndex = Tabs.trainings.index;
                          nextRouteName = AppRoutes.trainings.name;
                        }

                        _scaffoldModel.setParams(index: selectedTabIndex);
                        Provider.of<LocalDataModel>(context, listen: false).lastSelectedTab =
                            selectedTabIndex;

                        context.goNamed(nextRouteName, pathParameters: widget.pathParameters);
                      });
                      return const LoadingScreen();
                    },
                  );
                },
              ),
        );
      },
    );
  }

  _setGroups(User user) {
    groupIdent = ProfileUtility.fromFindId(user).groupIdent;
    if (groupIdent?.isNotEmpty == true) {
      _onboardingModel.groupIdent =
          GroupIdent.values
              .where((e) => e.name.toLowerCase() == groupIdent?.toLowerCase())
              .firstOrNull ??
          (ProfileUtility.fromFindId(user).isMentee ? GroupIdent.mentees : GroupIdent.mentors);
      groupIdent = _onboardingModel.groupIdent.name;
    }

    if ((_onboardingModel.belongsToEBRDGroup)) {
      final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
      themeProvider.setEBRDColorScheme();
    }
  }

  _fetchGroupsData() async {
    if (_onboardingModel.isRegularUser) return;

    final groupProvider = Provider.of<GroupProvider>(context, listen: false);
    final localeModel = Provider.of<LocaleModel>(context, listen: false);
    final group = await groupProvider.getGroup(groupIdent ?? _onboardingModel.groupIdent.name);
    _onboardingModel.userCms = await _userProvider.getUserCms();

    if (group?.languageTextId != null) {
      localeModel.set(Locale(group?.languageTextId ?? ''));
      if (context.mounted) {
        _contentProvider.clear();
        await _contentProvider.findAllOptionsByType(
          fetchPolicy: FetchPolicy.networkOnly,
          fallbackUiLanguage: Enum$UiLanguage.values.byName(localeModel.getCurrentLanguageCode()),
        );
      }
    }
  }
}
