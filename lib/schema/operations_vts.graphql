# ----------------------------------------------------------------------------------------------------------------
# Queries

query FindTrainingsForMe($options: FindObjectsOptions) {
  findTrainingsForMe(options: $options) {
    id
    mm2Id
    title
    about
    introduction
    imageUrls
    relativeUrlPath
    isTrainingCompletedForMe
    isTrainingPassedForMe
    tags
    myLatestTrainingSession {
      isInProgress
      percentCompleted
      completionInfo {
        completedAt
        questionsAnsweredCorrectly
        numberOfQuestions
        numCorrectAnswersToPass
        isPassingScore
        preTestQuestionsAnsweredCorrectly
        numberOfPreTestQuestions
      }
    }
  }
}

query FindTrainingById($trainingId: String!) {
  findTrainingById(id: $trainingId) {
    id
    mm2Id
    title
    about
    introduction
    imageUrls
    relativeUrlPath
    isTrainingCompletedForMe
    isTrainingPassedForMe
    tags
    myLatestTrainingSession {
      isInProgress
      percentCompleted
      completionInfo {
        completedAt
        questionsAnsweredCorrectly
        numberOfQuestions
        numCorrectAnswersToPass
        isPassingScore
        preTestQuestionsAnsweredCorrectly
        numberOfPreTestQuestions
      }
    }

    # Lesson plan relies on the TrainingContentPages stored in the children, and their children. This is a tree structure.
    # Use pre-order traversal of the tree to display the data.
    # (i.e. display the first child's title of the top level, then the title of that child's fist child, and so on.)
    # 4 levels seems like it should be enough, but 7 are requested just in case.
    trainingContentPages {
      title
      children {
        title
        children {
          title
          children {
            title
            children {
              title
              children {
                title
                children {
                  title
                }
              }
            }
          }
        }
      }
    }
  }
}

query FindTrainingSessionsForMe($options: FindObjectsOptions, $trainingId: String!) {
  findTrainingSessionsForMe(options: $options, trainingId: $trainingId) {
    id
    trainingId
    isInProgress
    percentCompleted
  }
}