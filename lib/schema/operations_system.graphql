# ----------------------------------------------------------------------------------------------------------------
# Queries

query FindServiceRequestById($serviceRequestId: String!) {
  findServiceRequestById(serviceRequestId: $serviceRequestId) {
    id
    serviceRequestType
    userId
    userRoles
    objectIds
    modelTypes
    result
    messageIds
    message
    errorCode
    events {
      time
      modelEventType
      message
    }
    deviceUuid
    source
    createdAt
    createdBy
    updatedAt
    updatedBy
    deletedAt
    deletedBy
    finishedAt
    expiresAt
  }
}


# ----------------------------------------------------------------------------------------------------------------
# Subscriptions

subscription ObjectChanged($modelType: ModelType, $objectId: ID!) {
  objectChanged(modelType: $modelType, objectId: $objectId) {
    objectId
    modelType
    ownerUserId
    messageType
    requestId
    serviceRequest {
      id
      serviceRequestType
      userId
      userRoles
      objectIds
      modelTypes
      result
      messageIds
      message
      errorCode
      events {
        time
        modelEventType
        message
      }
      deviceUuid
      source
      createdAt
      createdBy
      updatedAt
      updatedBy
      deletedAt
      deletedBy
      finishedAt
      expiresAt
    }
  }
}
