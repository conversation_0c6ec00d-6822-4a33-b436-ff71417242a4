query FindChannelMessageById($channelMessageId: String!) {
    findChannelMessageById(id: $channelMessageId) {
        id
        createdBy
        channelId
        messageText
        createdAt
        replyToMessageId
        deletedBy
        updatedAt
        deletedAt
        editedAt
        statuses {
            seenAt
            userId
        }
    }
}

query FindChannelMessages ($filter: ChannelMessageListFilter, $options: FindObjectsOptions) {
    findChannelMessages(filter: $filter, options: $options) {
        id
        createdBy
        channelId
        messageText
        createdAt
        replyToMessageId
        deletedBy
        updatedAt
        deletedAt
        editedAt
        statuses {
            seenAt
            userId
        }
    }
}

mutation CreateChannelMessage ($input: ChannelMessageInput!) {
    createChannelMessage(input: $input) {
        id
        createdBy
        channelId
        messageText
        createdAt
        replyToMessageId
        deletedBy
        updatedAt
        deletedAt
        editedAt
        statuses {
            seenAt
            userId
        }
    }
}

mutation DeleteChannelMessage ($deletePhysically: Boolean!, $channelMessageId: String!) {
    deleteChannelMessage(deletePhysically: $deletePhysically, channelMessageId: $channelMessageId)
}

mutation MarkChannelMessagesAsSeenByMe ($channelId: String!) {
    markChannelMessagesAsSeenByMe(channelId: $channelId)
}

mutation UpdateChannelMessage ($input: ChannelMessageInput!) {
    updateChannelMessage(input: $input)
}
