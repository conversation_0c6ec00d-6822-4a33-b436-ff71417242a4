query FindAllOptionsByType($fallbackUiLanguage: UiLanguage) {
  findCompanyStages(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
  findCompanyTypes(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
  findCountries(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
    alpha2Key
    phoneCode
  }
  findEducationLevels(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
  findErrorCodes(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
  findExpertises(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
  findGenders(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
  findIndustries(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
  findLanguages(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
    isUiLanguage
  }
  findPronouns(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
  findReportUserReasons(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
  findDeclineChannelInvitationReasons(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
}

query FindCompanyStages($fallbackUiLanguage: UiLanguage) {
  findCompanyStages(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
}

query FindCompanyTypes($fallbackUiLanguage: UiLanguage) {
  findCompanyTypes(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
}

query FindCountries($fallbackUiLanguage: UiLanguage) {
  findCountries(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
    alpha2Key
    phoneCode
  }
}

query FindDeclineChannelInvitationReasons($fallbackUiLanguage: UiLanguage) {
  findDeclineChannelInvitationReasons(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
}

query FindEducationLevels($fallbackUiLanguage: UiLanguage) {
  findEducationLevels(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
}

query FindErrorCodes($fallbackUiLanguage: UiLanguage) {
  findErrorCodes(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
}

query FindExpertises($fallbackUiLanguage: UiLanguage) {
  findExpertises(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
}

query FindGenders($fallbackUiLanguage: UiLanguage) {
  findGenders(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
}

query FindIqlaaJordanianDistricts($fallbackUiLanguage: UiLanguage) {
  findIqlaaJordanianDistricts(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    parentTextId
    translatedValue
  }
}

query FindIqlaaJordanianGovernorates($fallbackUiLanguage: UiLanguage) {
  findIqlaaJordanianGovernorates(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    parentTextId
    translatedValue
  }
}

query FindIndonesianCities($fallbackUiLanguage: UiLanguage) {
  findIndonesianCities(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    parentTextId
    translatedValue
  }
}

query FindIndonesianProvinces($fallbackUiLanguage: UiLanguage) {
  findIndonesianProvinces(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    parentTextId
    translatedValue
  }
}

query FindIndustries($fallbackUiLanguage: UiLanguage) {
  findIndustries(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
}

query FindLanguages($fallbackUiLanguage: UiLanguage) {
  findLanguages(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
    shortLangCode
    longLangCode
    language
    isUiLanguage
  }
}

query FindPronouns($fallbackUiLanguage: UiLanguage) {
  findPronouns(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
}

query FindReportUserReasons($fallbackUiLanguage: UiLanguage) {
  findReportUserReasons(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    translatedValue
  }
}

## Custom types only used if a user is in a specific group

query FindMastercardBanks($fallbackUiLanguage: UiLanguage) {
  findMastercardBanks(fallbackUiLanguage: $fallbackUiLanguage) {
    textId
    countryTextId
    displayName
    name
  }
}