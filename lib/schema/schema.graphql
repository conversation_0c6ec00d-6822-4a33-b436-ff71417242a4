"""
Indicates exactly one field must be supplied and this field must not be `null`.
"""
directive @oneOf on INPUT_OBJECT

type UserMetadata implements BaseModelMetadata {
  updatedAt: DateTimeISO
  totalTimeOnPlatform: Int!
  channelsMetadata: ChannelsUserMetadata!
  groupsMetadata: GroupsUserMetadata!
}

interface BaseModelMetadata {
  updatedAt: DateTimeISO
}

"""
A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.This scalar is serialized to a string in ISO 8601 format and parsed from a string in ISO 8601 format.
"""
scalar DateTimeISO

type ChannelsUserMetadata {
  mentoringSessionCount: Int!
}

type GroupsUserMetadata {
  groupCount: Int!
  updatedAt: DateTimeISO
}

type GroupMembership implements IGroupMembership {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  groupId: ID!
  groupIdent: String!
  userId: ID!
  roles: [GroupMembershipRole!]!
  expertises: [Expertise!]!
  industries: [Industry!]!
  industry: Industry
  soughtExpertises: [Expertise!]!
}

interface IGroupMembership {
  id: ID!
  groupId: ID!
  groupIdent: String!
  userId: ID!
  roles: [GroupMembershipRole!]!
}

enum GroupMembershipRole {
  admin
  coordinator
  moderator
  owner
}

type ModelEvent {
  time: DateTimeISO!
  modelEventType: ModelEventType!
  message: String!
}

enum ModelEventType {
  error
  warning
  info
}

type Expertise {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
  childExpertises: [Expertise!]
  parentExpertise: Expertise
}

enum OptionType {
  errorCode
  companyStage
  companyType
  educationLevel
  ethnicity
  expertise
  gender
  pronoun
  userRelationshipType
  declineChannelInvitationReason
  country
  industry
  language
  unset
  contentTagType
  indonesianCity
  indonesianProvince
  iqlaaJordanianDistrict
  iqlaaJordanianGovernorate
  mm2Expertise
  mm2Industry
  blockUserReason
  notificationTypeOption
  reportUserReason
}

enum UiLanguage {
  ar
  en
  es
  id
  ru
  so
}

type Option {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
}

type Industry {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
}

"""
This object contains the profile information describing a mentee/entrepreneur/business owner.

Users with User.seeksHelp are members of the "mentees" group, which comes with extra
profile attributes. This is one of the 'embedded' group memberships that are available
through User.groupMemberships.
"""
type MenteesGroupMembership implements IGroupMembership {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  groupId: ID!
  groupIdent: String!
  userId: ID!
  roles: [GroupMembershipRole!]!
  expertises: [Expertise!]!
  industries: [Industry!]!
  industry: Industry
  soughtExpertises: [Expertise!]!

  """Must match expertise textIds."""
  soughtExpertisesTextIds: [String!]

  """Must match expertise textIds."""
  additionalSoughtExpertisesTextIds: [String!]

  """Must match industry textIds."""
  industryTextId: String

  """Must match mm2 expertise textIds -- only used by synchronizer"""
  mm2SoughtExpertisesTextIds: [String!]

  """Must match mm2 industry textIds -- only used by synchronizer"""
  mm2IndustryTextId: String

  """From MM2, not used in MM3 (yet)"""
  actionsTaken: String

  """From MM2, not used in MM3 (yet)"""
  currentChallenges: String

  """From MM2, not used in MM3 (yet)"""
  futureGoals: String

  """From MM2, not used in MM3 (yet)"""
  motivationsForMentorship: String
  reasonsForStartingBusiness: String
  howCanMentorSupportMe: String
}

type MentorsGroupMembership implements IGroupMembership {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  groupId: ID!
  groupIdent: String!
  userId: ID!
  roles: [GroupMembershipRole!]!
  expertises: [Expertise!]!
  industries: [Industry!]!
  industry: Industry
  soughtExpertises: [Expertise!]!

  """Must match expertise textIds."""
  expertisesTextIds: [String!]

  """Must match expertise textIds."""
  additionalExpertisesTextIds: [String!]

  """Must match industry textIds."""
  industriesTextIds: [String!]

  """Must match mm2 expertise textIds -- only used by synchronizer"""
  mm2ExpertisesTextIds: [String!]

  """Must match mm2 industry textIds -- only used by synchronizer"""
  mm2IndustriesTextIds: [String!]
  helpICanOffer: String
  expectationsForMentees: String
  menteePreparationInstructions: String
  endorsements: Int
  reasonsForMentoring: String
  howICanHelpMentees: String
}

"""
This object contains the profile information describing an MASTERCARD user.

MASTERCARD users are members of the "mastercard" group, which comes with extra
profile attributes. These fields are used to store the user's
MASTERCARD-specific information.
"""
type MastercardGroupMembership implements IGroupMembership {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  groupId: ID!
  groupIdent: String!
  userId: ID!
  roles: [GroupMembershipRole!]!
  expertises: [Expertise!]!
  industries: [Industry!]!
  industry: Industry
  soughtExpertises: [Expertise!]!

  """Names of banks for reports"""
  bankNames: [String!]

  """Text IDs of banks for reports"""
  bankTextIds: [String!]

  """Small business card types, e.g. credit, debit, etc"""
  smallBusinessCardTypes: [MastercardCardType!]

  """Personal card types"""
  personalCardTypes: [MastercardCardType!]
}

enum MastercardCardType {
  credit
  debit
  prepaid
  none
  notProvided
}

"""
This object contains the profile information describing an IQLAA user.

IQLAA users are members of the "iqlaa" group, which comes with extra
profile attributes. These fields are used to store the user's
IQLAA-specific information.
"""
type IqlaaGroupMembership implements IGroupMembership {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  groupId: ID!
  groupIdent: String!
  userId: ID!
  roles: [GroupMembershipRole!]!
  expertises: [Expertise!]!
  industries: [Industry!]!
  industry: Industry
  soughtExpertises: [Expertise!]!

  """Fathers name"""
  fatherName: String

  """Date of birth"""
  birthDate: DateTimeISO

  """
  "Is your business a home-based business?"
  """
  isBusinessHomeBased: Boolean

  """
  "Is the Business/ Project registered in the Companies Control Department -Ministry of industries and trading?"
  """
  isBusinessRegisteredWithCCD: Boolean

  """(Optional) Business registration number"""
  businessRegistrationNumber: String

  """Is the user a Jordan national?"""
  isJordanNational: Boolean
}

"""
This object contains the profile information describing an a Strive Indonesia user.
"""
type StriveIndonesiaGroupMembership implements IGroupMembership {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  groupId: ID!
  groupIdent: String!
  userId: ID!
  roles: [GroupMembershipRole!]!
  expertises: [Expertise!]!
  industries: [Industry!]!
  industry: Industry
  soughtExpertises: [Expertise!]!

  """Venture Start Date"""
  ventureStartDate: DateTimeISO

  """Number of employees"""
  numberOfEmployees: Int
}

type Query {
  doesUserExist(identType: UserIdentType!, ident: String!): Boolean!
  findUserById(options: FindObjectsOptions, id: String!): User!
  findUserByIdent(options: FindUserByIdentOptions, identType: UserIdentType, ident: String!): User!
  isUserIdentAvailable(identType: UserIdentType, ident: String!): Boolean!
  findUsers(options: FindObjectsOptions, match: UserInput, filter: UserListFilter): [UserListItem!]!
  findUserDeviceById(options: FindObjectsOptions, id: String!): UserDeviceWithoutAuth!
  findUserDevices(options: FindObjectsOptions, match: UserDeviceInput, filter: UserDeviceListFilter): [UserDeviceWithoutAuth!]!
  findMyUserDevices(options: FindObjectsOptions): [UserDeviceWithoutAuth!]!
  findCompanyStages(fallbackUiLanguage: UiLanguage): [CompanyStage!]!
  findCompanyTypes(fallbackUiLanguage: UiLanguage): [CompanyType!]!
  findEducationLevels(fallbackUiLanguage: UiLanguage): [EducationLevel!]!
  findExpertises(fallbackUiLanguage: UiLanguage, isParent: Boolean, parentTextId: String): [Expertise!]!
  findGenders(fallbackUiLanguage: UiLanguage): [Gender!]!
  findPronouns(fallbackUiLanguage: UiLanguage): [Pronoun!]!
  findUserCmsByUserId(userId: String!): UserCms
  myInbox(refresh: Boolean): UserInbox!
  findAdminTaskById(adminTaskId: String!): AdminTask!
  findAdminTaskDefs: [AdminTaskDef!]!
  findAnalyticsServiceRecord: AnalyticsServiceRecord!
  findUploadedAssetById(options: FindObjectsOptions, id: String!): UploadedAsset!
  findUploadedAssets(options: FindObjectsOptions, match: UploadedAssetInput, filter: UploadedAssetListFilter): [UploadedAsset!]!
  findUploadedAssetsForUser(options: FindObjectsOptions, userId: String!): [UploadedAsset!]!
  findChannelInvitationById(id: String!): ChannelInvitation!
  findChannelInvitationsBetweenUsers(options: FindObjectsOptions, onlyUnseen: Boolean, onlyPending: Boolean, userIds: [String!]!): [ChannelInvitation!]!
  findChannelInvitationsForUser(options: FindObjectsOptions, onlyUnseen: Boolean, onlyPending: Boolean, direction: ChannelInvitationDirection, userId: String!): [ChannelInvitation!]!
  myChannelInvitations(options: FindObjectsOptions, onlyUnseen: Boolean, onlyPending: Boolean, direction: ChannelInvitationDirection): [ChannelInvitation!]!
  findPendingChannelInvitationsForUser(options: FindObjectsOptions, userId: String!): [ChannelInvitation!]!
  findChannelById(id: String!): Channel!
  findChannels(options: FindObjectsOptions, match: ChannelInput, filter: ChannelListFilter): [Channel!]!
  findChannelsForUser(options: FindObjectsOptions, mustBeAccepted: Boolean, mustHaveMessages: Boolean, userId: String!): [Channel!]!
  find1On1Channel(userIds: [String!]!): Channel
  findMyChannels(options: FindObjectsOptions): [Channel!]!
  findChannelMessageById(id: String!): ChannelMessage!
  findChannelMessages(options: FindObjectsOptions, match: ChannelMessageInput, filter: ChannelMessageListFilter): [ChannelMessage!]!
  findChannelParticipantById(id: String!): ChannelParticipant!
  findDeclineChannelInvitationReasons(fallbackUiLanguage: UiLanguage): [DeclineChannelInvitationReason!]!
  findOptions(fallbackUiLanguage: UiLanguage, isParent: Boolean, parentTextId: String, optionType: OptionType!): [Option!]!
  findCountries(fallbackUiLanguage: UiLanguage): [Country!]!
  findErrorCodes(fallbackUiLanguage: UiLanguage): [ErrorCodeOption!]!
  findIndustries(fallbackUiLanguage: UiLanguage): [Industry!]!
  findLanguages(fallbackUiLanguage: UiLanguage): [Language!]!
  apiVersion: String!
  findGroupCmsByGroupIdent(groupIdent: String!): GroupCms
  findGroupCmsByGroupId(groupId: String!): GroupCms
  findGroupCmsById(id: String!): GroupCms
  findGroupMembershipById(id: String!): GroupMembership!
  myGroupMemberships: [IGroupMembership!]!
  findGroupMemberships(options: FindObjectsOptions, match: GroupMembershipInput, filter: GroupMembershipListFilter): [IGroupMembership!]!
  findGroupById(id: String!): Group
  findGroupByIdent(groupIdent: String!): Group
  findGroups(options: FindObjectsOptions, match: GroupInput, filter: GroupListFilter): [Group!]!
  findIndonesianCities(fallbackUiLanguage: UiLanguage): [IndonesianCity!]!
  findIndonesianProvinces(fallbackUiLanguage: UiLanguage): [IndonesianProvince!]!
  findIqlaaJordanianDistricts(fallbackUiLanguage: UiLanguage): [IqlaaJordanianDistrict!]!
  findIqlaaJordanianGovernorates(fallbackUiLanguage: UiLanguage): [IqlaaJordanianGovernorate!]!
  findMastercardBanks(fallbackUiLanguage: UiLanguage): [MastercardBank!]!
  userWillReceiveWelcomeMessage(userId: String!): Boolean!
  findUserSearchById(options: FindObjectsOptions, userSearchId: String!): UserSearch!
  findUserSearches(options: FindObjectsOptions, match: UserSearchInput, filter: UserSearchListFilter): [UserSearch!]!
  findUserSearchResults(options: FindObjectsOptions, runIndex: Int, userSearchId: String!): [UserWithScore!]!
  myUserSearches: [UserSearch!]!
  getMm2Integration: Mm2Integration!
  findServiceRequestById(serviceRequestId: String!): ServiceRequest!
  findMyActiveMultiStepAction: [SidMultiStepAction!]!
  getMultiStepActionProgress(
    """
    The result will only contain the authToken, if you specify a valid confirmToken
    """
    confirmToken: String
    actionId: String!
  ): SidMultiStepActionProgress!
  findAvailableUserHandle(startValue: String!): String!
  getMyUser: User! @deprecated(reason: "Use findMyUser")
  findMyUser: MyUser!
  getMyBlockedUsers: [User!]! @deprecated(reason: "Use findMyBlockedUsers")
  findMyBlockedUsers: [User!]!
  findReportUserReasons(fallbackUiLanguage: UiLanguage): [ReportUserReason!]!
  findTrainingsForMe(displayInTrainingsList: Boolean, options: FindObjectsOptions): [Training!]!
  findTrainingsForUser(displayInTrainingsList: Boolean, options: FindObjectsOptions, userId: String!): [Training!]!
  findTrainingById(selectedLanguage: UiLanguage, id: String!): Training!
  findTrainingSessionById(id: String!): TrainingSession!
  findTrainingSessionsForMe(options: FindObjectsOptions, trainingId: String!): [TrainingSession!]!

  """
  Find training sessions by training  id. By default, finds the requestor's sessions.
  """
  findTrainingSessionsByTrainingId(userId: String, options: FindObjectsOptions, trainingId: String!): [TrainingSession!]!
  findLatestTrainingSessionForMe(options: FindObjectsOptions, trainingId: String!): TrainingSession
}

enum UserIdentType {
  any
  email
  id
  oauthProfileUrl
  oauthUserId
  phoneNumber
  userHandle
}

type User {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: UserMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  firstName: String
  lastName: String
  userHandle: String
  phoneNumber: String
  phoneNumberUpdatedAt: DateTimeISO
  isPhoneNumberVerified: Boolean!
  email: String

  """The source of the email address, e.g. "google", "facebook", etc."""
  emailSource: String
  emailUpdatedAt: DateTimeISO
  isEmailVerified: Boolean!
  genderTextId: String
  cityOfResidence: String
  regionOfResidence: String
  countryOfResidenceTextId: String
  postalCode: String
  avatarUrl: String
  websites: [LabeledStringValue!]
  authType: AuthType
  tfaBackupCodes: String
  passwordUpdatedAt: DateTimeISO
  preferredLanguageTextId: String
  spokenLanguagesTextIds: [String!]!
  selectedUiLanguageTextId: UiLanguage
  fallbackUiLanguageTextId: UiLanguage

  """
  If discoverable is not true, the user will not be included in search results or recommended to other users. The system will set discoverable to null for various reasons, i.e. for a bad actor. The user can set it to false intentionally.
  """
  discoverable: Boolean
  roles: [UserRole!]!
  appFeatures: [AppFeature!]
  source: String
  timezone: String
  preferences: UserPreferences
  trustLevel: Int!
  signedInAt: DateTimeISO
  signedOutAt: DateTimeISO
  latestActivityAt: DateTimeISO
  userDevices: [UserDeviceWithoutAuth!]!
  userBlocks: [UserBlock!]
  relationships: [UserRelationship!]
  inactivatedAt: DateTimeISO
  inactivatedBy: ID
  termsAndConditionsAcceptedAt: DateTimeISO
  optIntoNewsletter: Boolean
  onboardingStage: String
  suspendedAt: DateTimeISO
  suspendedBy: ID
  anonymizedAt: DateTimeISO
  syncedToAnalyticsAt: DateTimeISO
  addedToBgVaultAt: DateTimeISO
  companyIds: [ID!]
  companies: [Company!]
  groupIds: [ID!]!
  parentGroupIds: [ID!]!
  externalGroupIds: [ID!]!
  pronounsTextIds: [String!]
  groupMemberships: [IGroupMembership!]!
  seeksHelp: Boolean
  offersHelp: Boolean
  birthYear: Int
  ethnicity: String
  educationLevelTextId: String
  personalBio: String
  yearsManagementExperience: Int
  yearsOwnershipExperience: Int
  academicExperienceIds: [ID!]
  academicExperiences: [AcademicExperience!]

  """This attribute is only used by the MM2 synchronizer."""
  genderSelfDescribed: String
  businessExperienceIds: [ID!]
  businessExperiences: [BusinessExperience!]
  cityOfOrigin: String
  regionOfOrigin: String
  countryOfOriginTextId: String
  isOnVacation: Boolean
  avatarAsset: UploadedAsset
  profileRoleHistory: [UserProfileRoleHistoryItem!]
  ssoIdp: String

  """Records whether a user was originally created in MM2."""
  originatedInMm2: Boolean

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This is the MM2 password hash."""
  mm2PasswordHash: String

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String

  """This attribute is only used by the MM2 synchronizer."""
  mm2PhotoOriginal: String

  """For MM2 users, this means a profile is completed."""
  mm2BasicAccountCompleted: Boolean

  """Records whether a user has logged into MM3."""
  hasSignedInToMm3: Boolean

  """Records whether a user has logged into MM2."""
  hasSignedInToMm2: Boolean

  """This attribute is a copy of the mentor group membership."""
  mentor: MentorsGroupMembership

  """This attribute is a copy of the mentee group membership."""
  mentee: MenteesGroupMembership
  countryOfResidence: Country
  gender: Gender
  latestUserDevice: UserDeviceWithoutAuth!
  spokenLanguages: [Language!]!
  preferredLanguage: Language
  fallbackUiLanguage: Language!
  preferredUiLanguage: Language
  unreadInAppMessages: [Notification!]!
  channels(
    """Set options on channels, e.g. to include archived channels."""
    options: FindObjectsOptions

    """
    Set to True to return only channels which have been accepted. Default is False, which also returns channels with multiple messages that have not been accepted. Channels with only unaccepted invitations have no messages (except for channels created via the synchronizer).
    """
    mustBeAccepted: Boolean

    """
    Set to True to return only channels with messages. Default is False, which also returns channels without messages. Channels with only unaccepted invitations have no messages (except for channels created via the synchronizer).
    """
    mustHaveMessages: Boolean
  ): [Channel!]!
  channelInvitations: [ChannelInvitation!]!
  channelParticipants: [ChannelParticipant!]!
  countryOfOrigin: Country
  educationLevel: EducationLevel
  endorsements: [EndorsementWithTypes!]
  groupMembers: [IGroupMembership!]!
  groups: [Group!]!
  hasTrainings: Boolean!
  profileCompletionPercentage: Int!
  profileRole: UserProfileRole!
  pronouns: [Pronoun!]!
  pronounsDisplay: String!
  uploadedAssets: [UploadedAsset!]!
}

type LabeledStringValue {
  label: String
  value: String!
  tags: [String!]
}

enum AuthType {
  none
  oauth
  token
  hmac
  saml
}

enum UserRole {
  admin
  support
  staff
  qa
  test
}

enum AppFeature {
  testFeatures1
  testFeatures2
}

type UserPreferences {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  shareEmail: Boolean
  sharePhoneNumber: Boolean
  showWelcomeMessage: Boolean
  notificationOptions: [NotificationOptions!]
}

type NotificationOptions {
  notificationType: NotificationType!
  enableEmail: Boolean
  enableInAppMessage: Boolean
  enablePushNotification: Boolean
  enableSms: Boolean
  frequency: String
}

enum NotificationType {
  accountDeletedConfirmation
  channelInvitationAccepted
  channelInvitationDeclined
  channelInvitationReceived
  channelMessageReceived
  completeProfile
  completeSignUp
  matchesRecommendations
  newPrivacyRules
  newsletter
  resetPasswordConfirmation
  resetPasswordConfirmToken
  sendFirstInvitation
  unset
  welcome
}

type UserDeviceWithoutAuth {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  userId: ID!
  deviceUuid: String!
  deviceType: String
  trusted: Boolean!
  phoneNumber: String
  phoneNumberUpdatedAt: DateTimeISO
  isPhoneNumberVerified: Boolean!
  brand: String
  model: String
  isTablet: Boolean!
  screenWidth: Int!
  screenHeight: Int!
  os: String
  osVersion: String
  timezone: String
  ipAddress: String
  consumer: String
  consumerVersion: String
  acceptedLanguage: String
  locale: String
  countryCode: String
  appVersion: String
  signedInAt: DateTimeISO
  signedOutAt: DateTimeISO
  sessionStartedAt: DateTimeISO
  sessionEndedAt: DateTimeISO
  identityProvider: IdentityProvider
  oauthProfileUrl: String
  trustedAt: DateTimeISO
}

enum IdentityProvider {
  apple
  facebook
  google
  instagram
  linkedIn
  microsoft
  own
  sso
  telegram
  twitter
  whatsApp
}

type UserBlock {
  userId: ID!
  reasonTextId: String!
  notes: String
  adminNotes: String
  syncedToAnalyticsAt: DateTimeISO
  createdAt: DateTimeISO!

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
}

type UserRelationship {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  userId: ID!
  typeTextIds: [String!]!
  blockedAt: DateTimeISO
  blockReason: String
  notes: String
}

type Company {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  userIds: [String!]
  name: String!
  description: String
  location: String
  companyTypeTextId: String
  companyStageTextId: String
  websites: [LabeledStringValue!]
  industries: [String!]
  isOperational: Boolean
  isFundraising: Boolean
  annualRevenue: Int
  employeeCount: Int
  foundedAt: DateTimeISO

  """
  If a Company was created from the imported attributes of an MM2 Profile, mm2UserId references the MM2 user ID. This attribute is only used by the MM2 synchronizer.
  """
  mm2UserId: String

  """
  If a Company was created from the imported from MM2, mm2CompanyRole is either "mentor" or "mentee". This attribute is only used by the MM2 synchronizer.
  """
  mm2CompanyRole: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO
  companyStage: CompanyStage
  companyType: CompanyType
}

type CompanyStage {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
}

type CompanyType {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
}

type AcademicExperience {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  institutionName: String!

  """
  E.g. "Bachelor of Science"
  """
  degreeType: String

  """
  E.g. "Computer Science"
  """
  fieldOfStudy: String

  """If no start date is provided, startDate is null."""
  startDate: DateTimeISO

  """If the experience is ongoing, endDate is null."""
  endDate: DateTimeISO
  userId: ID!
}

type BusinessExperience {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  businessName: String!
  jobTitle: String
  city: String
  state: String
  country: String

  """If no start date is provided, startDate is null."""
  startDate: DateTimeISO

  """If the experience is ongoing, endDate is null."""
  endDate: DateTimeISO
  userId: ID!
}

type UploadedAsset {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  ownerId: ID!
  ownerModelType: ModelType!
  assetType: UploadedAssetType!
  hostingService: AssetHostingService!
  url: String
  path: String
  s3Bucket: String
  s3Key: String
  mimeType: String
  uploadUrl: String
  uploadUrlExpiresAt: DateTimeISO
  uploadedAt: DateTimeISO
  expiresAt: DateTimeISO
}

enum ModelType {
  AcademicExperience
  BusinessExperience
  Company
  DataDeletion
  MentorBoard
  MentoringSession
  UserInbox
  UserMetadata
  UserPreferences
  AdminTask
  AnalyticsServiceRecord
  AnalyticsSynchronization
  UploadedAsset
  Channel
  ChannelInbox
  ChannelInvitation
  ChannelMessage
  ChannelParticipant
  ContentStatus
  Option
  ContentTag
  AppliedGroupRule
  Group
  GroupCms
  GroupMembership
  GroupRule
  GroupRuleConfig
  MastercardBank
  SupportChannelConfig
  Match
  MatchProfile
  MatchingEngine
  UserSearch
  Notification
  NotificationTemplate
  Mm2Integration
  Mm2Synchronization
  Mm2SynchronizationResultItem
  ModerationConcern
  ApiAuthInfo
  MultiStepAction
  MyUser
  ServiceRequest
  User
  UserDevice
  UserRelationship
  ServiceRecord
  TrackingEvent
  UserTracking
  Training
  TrainingContentPage
  TrainingSession
  unset
}

enum UploadedAssetType {
  unset
  avatar
  profileHeroImage
}

enum AssetHostingService {
  s3
  unset
}

type UserProfileRoleHistoryItem {
  newRole: UserProfileRole!
  createdAt: DateTimeISO!
}

enum UserProfileRole {
  mentee
  mentor
  both
  none
}

type Country {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
  alpha2Key: String!
  alpha3Key: String!
  phoneCode: String!
}

type Gender {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
}

type Language {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]

  """ISO 639-1, 2 letter language code. e.g. "en" for English."""
  shortLangCode: String

  """ISO 639-2, 3 letter language code. e.g. "eng" for English."""
  longLangCode: String
  isUiLanguage: Boolean!

  """Right to left text flow."""
  isRtl: Boolean
}

type Notification {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  notificationType: NotificationType!
  templateId: ID!
  recipientId: ID!
  multiStepActionId: ID!
  initiatorId: ID!
  replyingToId: ID!
  title: String!
  messageText: String!
  shortMessageText: String!
  htmlMessage: String!
  language: UiLanguage
  isTranslated: Boolean
  appLink: String!
  action0: AppAction
  action1: AppAction
  action2: AppAction
  actionTaken: AppAction
  sendEmail: Boolean!
  sendInAppMessage: Boolean!
  sendPushNotification: Boolean!
  sendSms: Boolean!
  emailSentAt: DateTimeISO
  inAppMessageSentAt: DateTimeISO
  inAppMessageReceivedAt: DateTimeISO
  pushNotificationSentAt: DateTimeISO
  smsSentAt: DateTimeISO
  emailSendReport: String!
  pushNotificationSendReport: String!
  smsSendReport: String!
  sentMessagesCount: Int!
  context: NotificationContext
}

enum AppAction {
  editProfile
  updateApp
  unset
}

type NotificationContext {
  title: String
  senderId: String
  senderFirstName: String
  senderLastName: String
  senderFullName: String
  senderEmail: String
  senderPhoneNumber: String
  recipientId: String
  recipientFirstName: String
  recipientLastName: String
  recipientFullName: String
  recipientEmail: String
  recipientPhoneNumber: String

  """The user id of the user who appears in a notification"""
  displayedUserId: String

  """The first name of the user who appears in a notification"""
  displayedUserFirstName: String
  displayedUserLastName: String
  displayedUserFullName: String
  displayedUserEmail: String
  displayedUserPhoneNumber: String
  displayedUserCountry: String
  displayedUserBusinessOrJobTitle: String
  appLink: String
  locale: String
  textDirection: String
  confirmToken: String
}

type Channel {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: ChannelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  name: String
  topic: String
  description: String
  tags: [String!]
  channelType: ChannelType!
  statuses: [BgChannelStatus!]
  userIds: [ID!]

  """
  For 1:1 channels, the ID of the other user. The first user is createdBy.
  """
  otherUserId: ID
  pausedAt: DateTimeISO
  pausedBy: ID
  suspendedAt: DateTimeISO
  suspendedBy: ID
  lockedAt: DateTimeISO
  lockedBy: ID
  archivedAt: DateTimeISO
  archivedBy: ID
  assumedMentorId: ID

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO
  creator: User!
  status: BgChannelStatus
  invitations: [ChannelInvitation!]!
  isArchivedForMe: Boolean!
  latestMessage: ChannelMessage
  messages: [ChannelMessage!]!
  participants: [ChannelParticipant!]!
  pendingInvitations: [ChannelInvitation!]!
}

type ChannelMetadata {
  updatedAt: DateTimeISO
  unseenMessageInfo: [BgLatestUnseenChannelMessageInfo!]
  channelInvitationAccepted: Boolean!
  messagesSentByCreatorCount: Int!
  messagesSentByFirstParticipantCount: Int!
}

type BgLatestUnseenChannelMessageInfo {
  userId: ID!
  createdAt: DateTimeISO!
}

enum ChannelType {
  unset
  mentoring
  support
  welcome
}

type BgChannelStatus {
  userId: ID!
  archivedAt: DateTimeISO
}

type ChannelInvitation {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  channelId: ID
  recipientId: ID!
  channelName: String
  channelTopic: String
  messageText: String

  """
  An authorized sender (i.e. role: ["support"]) can skip the acceptance step.
  """
  autoAccept: Boolean
  declineReasonTextId: String
  dismissedFromInboxBySenderAt: DateTimeISO
  dismissedFromInboxByRecipientAt: DateTimeISO
  readByRecipientAt: DateTimeISO
  status: ChannelInvitationStatus!
  suspendedAt: DateTimeISO
  suspendedBy: ID
  userSearchId: ID
  searchRank: Int

  """This attribute is only used by the MM2 synchronizer."""
  mm2ConversationId: String

  """This attribute is only used by the MM2 synchronizer. Mm2 message ID."""
  mm2Id: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO
  channel: Channel!
  declineReason: DeclineChannelInvitationReason
  recipient: User
  sender: User
}

enum ChannelInvitationStatus {
  created
  accepted
  declined
  unset
}

type DeclineChannelInvitationReason {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
}

type ChannelMessage {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: ChannelMessageMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  channelId: ID!
  replyToMessageId: ID
  channelMessageType: ChannelMessageType
  messageText: String
  statuses: [ChannelMessageStatus!]
  editedAt: DateTimeISO
  suspendedAt: DateTimeISO
  suspendedBy: ID

  """This attribute is only used by the MM2 synchronizer."""
  mm2ConversationId: String

  """This attribute is only used by the MM2 synchronizer. Mm2 message ID."""
  mm2Id: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO
  channel: Channel!
  sender: User!
}

type ChannelMessageMetadata {
  updatedAt: DateTimeISO
  senderUserHandle: String
  senderFirstName: String
  senderLastName: String
  senderAvatarUrl: String
}

enum ChannelMessageType {
  unset
  invitation
  support
  welcome
}

type ChannelMessageStatus {
  userId: ID!
  receivedAt: DateTimeISO
  seenAt: DateTimeISO
}

type ChannelParticipant {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  channelId: ID!
  userId: ID!
  invitedBy: ID
  channelName: String
  role: ChannelParticipantRole
  suspendedAt: DateTimeISO
  suspendedBy: ID
  channel: Channel!
  user: UserListItem!
}

enum ChannelParticipantRole {
  admin
  moderator
  owner
  unset
}

type UserListItem {
  id: ID!
  createdAt: DateTimeISO!
  updatedAt: DateTimeISO
  deletedAt: DateTimeISO
  userHandle: String
  firstName: String
  lastName: String
  avatarUrl: String
  timezone: String

  """
  If discoverable is not true, the user will not be included in search results or recommended to other users. The system will set discoverable to null for various reasons, i.e. for a bad actor. The user can set it to false intentionally.
  """
  discoverable: Boolean
  trustLevel: Int!
  latestActivityAt: DateTimeISO
  userBlocks: [UserBlock!]
  inactivatedAt: DateTimeISO
  suspendedAt: DateTimeISO
  cityOfResidence: String
  regionOfResidence: String
  websites: [LabeledStringValue!]
  seeksHelp: Boolean
  offersHelp: Boolean
  yearsManagementExperience: Int
  yearsOwnershipExperience: Int
  academicExperiences: [AcademicExperience!]
  businessExperiences: [BusinessExperience!]
  isOnVacation: Boolean

  """This attribute is a copy of the mentor group membership."""
  mentor: MentorsGroupMembership

  """This attribute is a copy of the mentee group membership."""
  mentee: MenteesGroupMembership
  groupMemberships: [IGroupMembership!]!
  companies: [Company!]!
  countryOfOrigin: Country
  countryOfResidence: Country
  educationLevel: EducationLevel
  endorsements: [EndorsementWithTypes!]
  groups: [Group!]!
  profileCompletionPercentage: Int!
  profileRole: UserProfileRole!
  pronouns: [Pronoun!]!
  pronounsDisplay: String!
}

type EducationLevel {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
}

type EndorsementWithTypes {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  objectId: ID!
  contentModelType: ModelType!

  """
  ID of the user that created/owns the content referred to in this ContentTag. The ID of the user that created this ContentTag is stored in the createdBy field for ContentTags that were created by a user.
  """
  userId: ID
  contentTagTypeTextId: String!
  childContentTagTypeTextId: String
  messageText: String
  moderationConcern: ModerationConcern
  allModerationConcerns: [ModerationConcern!]
  approvedByRecipientAt: DateTimeISO

  """ID of the admin user that verified the tag."""
  verifiedBy: ID
  verifiedAt: DateTimeISO

  """ID of the admin user that dimsissed the tag."""
  dismissedBy: ID
  dismissedAt: DateTimeISO
  childContentTagType: ContentTagType
  contentTagType: ContentTagType
}

type ModerationConcern {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  moderationConcernType: ModerationConcernType!
  name: String
  description: String
  value: String!
  languageTextId: String

  """
  must match with capitalization; ignored if isRegex = true; default = true
  """
  isCaseSensitive: Boolean

  """only matches full words; ignored if isRegex = true; default = false"""
  isWord: Boolean

  """value is a regex expression without flags; default = false"""
  isRegex: Boolean

  """default = false"""
  isNameOfBadActor: Boolean

  """default = false"""
  isCompanyNameOfBadActor: Boolean

  """default = false"""
  isEmailOfBadActor: Boolean

  """default = false"""
  isPhoneNumberOfBadActor: Boolean

  """default = false"""
  isWebsiteOfBadActor: Boolean

  """delete the content, if a match is found; default = false"""
  deleteContent: Boolean

  """number of points to reduce a users trustLevel, if found"""
  trustLevelImpact: Int
  version: String
}

enum ModerationConcernType {
  phrase
  unknown
}

type ContentTagType {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
  addToTrustLevel: Int
}

type Group {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  parentGroupId: ID
  matchingEngineId: ID
  name: String!
  shortName: String!
  ident: String!
  slug: String
  domain: String
  badgeName: String
  description: String
  planType: String
  embedded: Boolean!
  appliedGroupRules: [AppliedGroupRule!]

  """
  The language of this group. The app will be set to this language, should a new user arrive at this groups landing page.
  """
  languageTextId: String

  """Deprecated, use GroupCmsOnboarding instead."""
  allowProfileRoleOnSignUp: UserProfileRole @deprecated(reason: "Use GroupCmsOnboarding instead.")

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String

  """This attribute is only used by the MM2 synchronizer."""
  isMm2Organization: Boolean

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This is false for groups which are expected to continue using MM2."""
  isMigratedToMm3: Boolean

  """The URL which can be used to redirect a group to MM2."""
  mm2RedirectUrl: String

  """
  The domain name used by the MM3 deep links, if different from the default groups.
  """
  mm3DeepLinksUrl: String
  groupCms: GroupCms
}

type AppliedGroupRule {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  groupRuleId: ID!
  groupId: ID!
  subscribedToEvents: [GroupRuleEventType!]!
  config: GroupRuleBaseConfig
}

enum GroupRuleEventType {
  enterGroup
  exitGroup
  signUp
  sendMessage
  userSearch
  updateUser
  unknown
}

type GroupRuleBaseConfig {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  value: String!
}

type GroupCms {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  groupId: ID!
  onboarding: GroupCmsOnboarding
}

type GroupCmsOnboarding {
  """
  This defines which profile roles (mentor/mentee) are available to a new user when signing up to this group.
  """
  allowProfileRoleOnSignUp: UserProfileRole
  showDataConsentPage: Boolean
  showPreferredLanguagePage: Boolean
  showLocationPage: Boolean
  showPhoneNumberPage: Boolean
  showGenderPage: Boolean
  showBirthYearPage: Boolean
  showProfileRolePage: Boolean
  showExpertisesPage: Boolean
  showIndustryPage: Boolean
  showVentureNamePage: Boolean
  showVentureStartDatePage: Boolean
  showVentureStagePage: Boolean
  showReasonToJoinPage: Boolean
  showMentorRolePage: Boolean
  showAcceptTermsPage: Boolean
  nextRoute: String
}

type Pronoun {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
}

input FindObjectsOptions {
  limit: Int
  sort: [SortItem!]
  skip: Int
  timeout: Boolean
  tailable: Boolean
  awaitData: Boolean
  batchSize: Int
  returnKey: Boolean
  maxTimeMS: Int
  maxAwaitTimeMS: Int
  noCursorTimeout: Boolean
  singleBatch: Boolean
  allowPartialResults: Boolean
  showRecordId: Boolean
  includeArchived: IncludeFilterOption
  includeBlocked: IncludeFilterOption
  includeDeleted: IncludeFilterOption
  includeSuspended: IncludeFilterOption
}

input SortItem {
  field: String! = ""
  direction: SortDirection = asc
}

enum SortDirection {
  asc
  desc
}

enum IncludeFilterOption {
  include
  exclude
  only
}

input FindUserByIdentOptions {
  includeDeleted: Boolean
  includeGroupProfiles: [String!]
}

input UserInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  firstName: String
  lastName: String
  fullName: String
  userHandle: String
  phoneNumber: String
  phoneNumberUpdatedAt: DateTimeISO
  isPhoneNumberVerified: Boolean
  email: String
  emailUpdatedAt: DateTimeISO
  isEmailVerified: Boolean

  """The source of the email address, e.g. "google", "facebook", etc."""
  emailSource: String
  genderTextId: String
  cityOfResidence: String
  regionOfResidence: String
  countryOfResidenceTextId: String
  postalCode: String
  avatarUrl: String
  websites: [LabeledStringValueInput!]
  authType: AuthType
  currentPassword: String
  newPassword: String
  preferredLanguageTextId: String
  spokenLanguagesTextIds: [String!]
  selectedUiLanguageTextId: UiLanguage
  fallbackUiLanguageTextId: UiLanguage
  discoverable: Boolean
  roles: [UserRole!]
  appFeatures: [AppFeature!]
  source: String
  timezone: String
  preferences: UserPreferencesInput
  trustLevel: Int
  signedInAt: DateTimeISO
  signedOutAt: DateTimeISO
  latestActivityAt: DateTimeISO
  inactivatedAt: DateTimeISO
  inactivatedBy: ID
  termsAndConditionsAcceptedAt: DateTimeISO
  optIntoNewsletter: Boolean
  onboardingStage: String
  suspendedAt: DateTimeISO
  suspendedBy: ID
  syncedToAnalyticsAt: DateTimeISO
  companyIds: [ID!]

  """Used internally, will not work in GraphQL queries."""
  companies: [CompanyInput!]
  groupIds: [ID!]
  parentGroupIds: [ID!]
  externalGroupIds: [ID!]
  pronounsTextIds: [String!]
  groupMemberships: [GroupMembershipInput!]
  addToGroupIds: [String!]
  removeFromGroupIds: [String!]
  seeksHelp: Boolean
  offersHelp: Boolean
  birthYear: Int
  ethnicity: String
  educationLevelTextId: String
  personalBio: String
  yearsManagementExperience: Int
  yearsOwnershipExperience: Int
  academicExperienceIds: [ID!]

  """Specify a company you want to create and add the user to."""
  company: CompanyInput

  """
  Specify a list of academic experiences you want to create for the user.
  """
  academicExperiences: [AcademicExperienceInput!]
  businessExperienceIds: [ID!]

  """
  Specify a list of business experiences you want to create for the user.
  """
  businessExperiences: [BusinessExperienceInput!]
  cityOfOrigin: String
  regionOfOrigin: String

  """Users Country of origin. Use a Country Options textId."""
  countryOfOriginTextId: String
  isOnVacation: Boolean
  profileRoleHistory: [UserProfileRoleHistoryItemInput!]
  ssoIdp: String
}

input ModelEventInput {
  time: DateTimeISO! = "2025-05-22T14:56:40.017Z"
  modelEventType: ModelEventType! = info
  message: String! = ""
}

input BaseModelMetadataInput {
  updatedAt: DateTimeISO
}

input LabeledStringValueInput {
  label: String
  value: String
  tags: [String!]
}

input UserPreferencesInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  shareEmail: Boolean
  sharePhoneNumber: Boolean
  showWelcomeMessage: Boolean
  notificationOptionsInput: [NotificationOptionsInput!]
}

input NotificationOptionsInput {
  notificationType: NotificationType
  enableEmail: Boolean
  enableInAppMessage: Boolean
  enablePushNotification: Boolean
  enableSms: Boolean
  frequency: String
}

input CompanyInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  name: String
  description: String
  location: String
  companyTypeTextId: String
  companyStageTextId: String
  websites: [LabeledStringValueInput!]
  industries: [String!]
  isOperational: Boolean
  isFundraising: Boolean
  annualRevenue: Int
  employeeCount: Int
  foundedAt: DateTimeISO
  addUserIds: [String!]
}

input GroupMembershipInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  groupId: ID
  groupIdent: String
  userId: ID
  roles: [GroupMembershipRole!]
}

input AcademicExperienceInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  institutionName: String

  """
  E.g. "Bachelor of Science"
  """
  degreeType: String

  """
  E.g. "Computer Science"
  """
  fieldOfStudy: String
  startDate: DateTimeISO

  """If the experience is ongoing, endDate is null."""
  endDate: DateTimeISO
  userId: ID
}

input BusinessExperienceInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  businessName: String
  jobTitle: String
  city: String
  state: String
  country: String
  startDate: DateTimeISO

  """If the experience is ongoing, endDate is null."""
  endDate: DateTimeISO
  userId: ID
}

input UserProfileRoleHistoryItemInput {
  newRole: UserProfileRole! = none
  createdAt: DateTimeISO! = "2025-05-22T14:56:40.017Z"
}

input UserListFilter {
  ids: [String!]
  excludeIds: [ID!]
  searchText: String
  caseSensitive: Boolean
  textSearchFields: [String!]
  createdAtFrom: DateTimeISO
  createdAtUntil: DateTimeISO
  updatedAtFrom: DateTimeISO
  updatedAtUntil: DateTimeISO
  rolesIn: [UserRole!]
  emailIn: [String!]
  createdAtGreaterThan: DateTimeISO
  latestActivityAtGreaterThan: DateTimeISO
  companyId: ID
  syncedWithMm2: Boolean
  isMm2User: Boolean
}

input UserDeviceInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  userId: ID
  deviceUuid: String
  deviceType: String
  trusted: Boolean
  phoneNumber: String
  phoneNumberUpdatedAt: DateTimeISO
  isPhoneNumberVerified: Boolean
  brand: String
  model: String
  isTablet: Boolean
  screenWidth: Int
  screenHeight: Int
  os: String
  osVersion: String
  timezone: String
  ipAddress: String
  consumer: String
  consumerVersion: String
  acceptedLanguage: String
  locale: String
  countryCode: String
  appVersion: String
  signedInAt: DateTimeISO
  signedOutAt: DateTimeISO
  sessionStartedAt: DateTimeISO
  sessionEndedAt: DateTimeISO
  authType: AuthType
  identityProvider: IdentityProvider
  oauthFederatedProvider: FederatedIdentityProvider
  oauthUserId: String
  oauthDelegateUserId: String
  oauthProfileUrl: String
  oauthToken: String
  oauthTokenCreatedAt: DateTimeISO
  oauthTokenExpiresAt: DateTimeISO
  oauthRefreshToken: String
  oauthRefreshTokenCreatedAt: DateTimeISO
  oauthRefreshTokenExpiresAt: DateTimeISO
  pushNotificationToken: String
  trustedAt: DateTimeISO
}

enum FederatedIdentityProvider {
  none
  firebase
}

input UserDeviceListFilter {
  ids: [String!]
  excludeIds: [ID!]
  searchText: String
  caseSensitive: Boolean
  textSearchFields: [String!]
  createdAtFrom: DateTimeISO
  createdAtUntil: DateTimeISO
  updatedAtFrom: DateTimeISO
  updatedAtUntil: DateTimeISO
}

type UserCms {
  userId: ID!
  groupCms: GroupCms
}

type UserInbox {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  userId: ID!
  channels: ChannelInbox
}

type ChannelInbox {
  userId: ID!
  unseenMessages: [ChannelInboxItemMessage!]
  unseenArchivedMessages: [ChannelInboxItemMessage!]
  latestMessages: [ChannelInboxItemMessage!]
  latestArchivedMessages: [ChannelInboxItemMessage!]
  pendingInvitations: [ChannelInboxItemInvitation!]
  invitations: [ChannelInboxItemInvitation!]
  updatedAt: DateTimeISO
  updatedBy: ID
  channelsExceedMaxCount: Boolean
  invitationsExceedMaxCount: Boolean
}

type ChannelInboxItemMessage {
  id: ID!
  channelId: ID!
  replyToMessageId: ID
  channelMessageType: ChannelMessageType
  messageText: String
  senderUserHandle: String
  senderFirstName: String
  senderLastName: String
  senderAvatarUrl: String
  seenAt: DateTimeISO
  isArchived: Boolean
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  userIds: [ID!]
}

type ChannelInboxItemInvitation {
  id: ID!
  channelId: ID
  messageText: String
  readByRecipientAt: DateTimeISO
  status: ChannelInvitationStatus!
  createdAt: DateTimeISO!
  createdBy: ID
  recipientId: ID
}

type AdminTask {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  adminTaskType: AdminTaskType!
  result: AdminTaskResult
  resultMessage: String

  """Any error messages that happened during the run"""
  error: String

  """arguments"""
  args: [String!]

  """number of milliseconds before timeout"""
  timeout: Int

  """run ask after creation"""
  autoRun: Boolean

  """should the task run synchronously, or in the background?"""
  synchronous: Boolean

  """Date/time this task started to run"""
  startedAt: DateTimeISO

  """Date/time this task will be removed from the db"""
  expiresAt: DateTimeISO

  """Date/time this task completed its run"""
  finishedAt: DateTimeISO
}

enum AdminTaskType {
  addAppFeature
  deleteUser
  formatPhoneNumbers
  mergeUsers
  refreshAllEmbeddedCompanies
  refreshAllUserInboxes
  removeAppFeature
  resetUserPassword
  setUserPassword
  suspendUser
  verifyUserPassword
  decryptString
  unset
  createAnalyticsSynchronization
  pauseAnalyticsSynchronization
  runAnalyticsSynchronization
  updateChannelMetadata
  updateChannelOtherUserId
  runDataGenerator
  loadDbCache
  queryDbVersion
  recreateDbIndexes
  updateGroupIdentsInAllGroupMemberships
  addLanguageText
  recreateDefaultMatchingEngine
  refreshAllMatchProfiles
  clearBusMessages
  removeBusMessage
  sendPushNotification
  compareMm2Object
  compareMm2ObjectIdsOfModel
  fixAllSyncedChannelInvitationInitialMessages
  mergeAllDuplicateMm3ChatObjects
  mergeAllDuplicateMm3Users
  pauseMm2Synchronization
  runMm2Synchronization
  syncAllUsersWhoSignedUpInMm3
  syncDocsWithoutMm2Ids
  syncUsersWithLanguageMismatch
  triggerRandomSynchronizations
  addOrRemoveAppFeature
  createApiAuthInfo
  removeAllInvalidUserBlocks
  doDataMaintenance
  sendPendingTrackingEvents
  setTrainingTags
}

enum AdminTaskResult {
  ok
  error
}

type AdminTaskDef {
  adminTaskType: AdminTaskType!
  label: String!
  description: String

  """arguments"""
  args: [AdminTaskArgDef!]

  """is this admin task available?"""
  available: Boolean!

  """number of milliseconds before timeout"""
  timeout: Int
}

type AdminTaskArgDef {
  name: String!
  label: String
  dataType: String!
  choices: [String!]
  optional: Boolean
  description: String
}

type AnalyticsServiceRecord {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  serviceName: ServiceName!
  fullSyncStartedAt: DateTimeISO
  fullSyncFinishedAt: DateTimeISO
  queueBatchStartedAt: DateTimeISO
  queueBatchFinishedAt: DateTimeISO
}

enum ServiceName {
  accounts
  admin
  adminJs
  analytics
  appEvents
  assets
  aws
  bgChannels
  bullBoard
  channels
  content
  contentTags
  dataGenerator
  db
  firebase
  graphqlApi
  groups
  http
  i18N
  logger
  matching
  messageBus
  messaging
  mm2
  models
  moderation
  nlp
  redis
  restApi
  secureId
  slack
  system
  tracking
  unset
  vts
}

input UploadedAssetInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  ownerId: ID
  ownerModelType: ModelType
  assetType: UploadedAssetType
  hostingService: AssetHostingService
  url: String
  path: String
  s3Bucket: String
  s3Key: String
  mimeType: String
  uploadUrl: String
  uploadUrlExpiresAt: DateTimeISO
  uploadedAt: DateTimeISO
  expiresAt: DateTimeISO
}

input UploadedAssetListFilter {
  ids: [String!]
  excludeIds: [ID!]
  searchText: String
  caseSensitive: Boolean
  textSearchFields: [String!]
  createdAtFrom: DateTimeISO
  createdAtUntil: DateTimeISO
  updatedAtFrom: DateTimeISO
  updatedAtUntil: DateTimeISO
}

enum ChannelInvitationDirection {
  sent
  received
}

input ChannelInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  name: String
  topic: String
  description: String
  tags: [String!]
  channelType: ChannelType
  statuses: [BgChannelStatusInput!]
  userIds: [ID!]

  """
  For 1:1 channels, the ID of the other user. The first user is createdBy.
  """
  otherUserId: ID
  inviteUserIds: [ID!]
  pausedAt: DateTimeISO
  pausedBy: ID
  suspendedAt: DateTimeISO
  suspendedBy: ID
  archivedAt: DateTimeISO
  archivedBy: ID
  assumedMentorId: ID

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO
}

input BgChannelStatusInput {
  userId: ID
  archivedAt: DateTimeISO
}

input ChannelListFilter {
  ids: [String!]
  excludeIds: [ID!]
  searchText: String
  caseSensitive: Boolean
  textSearchFields: [String!]
  createdAtFrom: DateTimeISO
  createdAtUntil: DateTimeISO
  updatedAtFrom: DateTimeISO
  updatedAtUntil: DateTimeISO
  userId: ID
  userIds: [ID!]
}

input ChannelMessageInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  channelId: ID
  replyToMessageId: ID
  channelMessageType: ChannelMessageType
  messageText: String
  statuses: [ChannelMessageStatusInput!]
  editedAt: DateTimeISO
  suspendedAt: DateTimeISO
  suspendedBy: ID

  """This attribute is only used by the MM2 synchronizer."""
  mm2ConversationId: String

  """This attribute is only used by the MM2 synchronizer. Mm2 message ID."""
  mm2Id: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO
}

input ChannelMessageStatusInput {
  userId: ID
  receivedAt: DateTimeISO
  seenAt: DateTimeISO
}

input ChannelMessageListFilter {
  ids: [String!]
  excludeIds: [ID!]
  searchText: String
  caseSensitive: Boolean
  textSearchFields: [String!]
  createdAtFrom: DateTimeISO
  createdAtUntil: DateTimeISO
  updatedAtFrom: DateTimeISO
  updatedAtUntil: DateTimeISO
  channelId: ID
  userIds: [ID!]
  receiverUserId: ID
  replyToMessageId: ID
  includeChannelMessageType: [ChannelMessageType!]
  received: Boolean
  seen: Boolean
}

type ErrorCodeOption {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
}

input GroupMembershipListFilter {
  ids: [String!]
  excludeIds: [ID!]
  searchText: String
  caseSensitive: Boolean
  textSearchFields: [String!]
  createdAtFrom: DateTimeISO
  createdAtUntil: DateTimeISO
  updatedAtFrom: DateTimeISO
  updatedAtUntil: DateTimeISO
  userId: ID
  embedded: Boolean
  roles: [GroupMembershipRole!]
}

input GroupInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  parentGroupId: ID
  matchingEngineId: ID
  name: String
  shortName: String
  ident: String
  slug: String
  domain: String
  badgeName: String
  description: String
  planType: String
  embedded: Boolean! = false
  appliedGroupRules: [AppliedGroupRuleInput!]

  """
  The language of this group. The app will be set to this language, should a new user arrive at this groups landing page.
  """
  languageTextId: String

  """
  This defines which profile roles (mentor/mentee) are available to a new user when signing up to this group.
  """
  allowProfileRoleOnSignUp: UserProfileRole
}

input AppliedGroupRuleInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  groupRuleId: ID! = ""
  groupId: ID! = ""
  subscribedToEvents: [GroupRuleEventType!]! = []
  config: GroupRuleBaseConfigInput
}

input GroupRuleBaseConfigInput {
  value: String! = ""
}

input GroupListFilter {
  ids: [String!]
  excludeIds: [ID!]
  searchText: String
  caseSensitive: Boolean
  textSearchFields: [String!]
  createdAtFrom: DateTimeISO
  createdAtUntil: DateTimeISO
  updatedAtFrom: DateTimeISO
  updatedAtUntil: DateTimeISO
  embedded: Boolean
  syncedWithMm2: Boolean
}

type IndonesianCity {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
}

type IndonesianProvince {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
}

type IqlaaJordanianDistrict {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
}

type IqlaaJordanianGovernorate {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
}

type MastercardBank {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  countryTextId: String!

  """The name of the bank"""
  name: String!

  """(<Country name in English>) <Bank name>"""
  enDisplayName: String!

  """(<Country name in Spanish>) <Bank name>"""
  esDisplayName: String!

  """Not stored, just used for graphQL API"""
  displayName: String
}

type UserSearch {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID

  """The ID of the user that is searching and owns this object"""
  userId: ID!
  matchingEngineId: ID
  userSearchType: UserSearchType!
  name: String
  filter: UserSearchFilter

  """
  A list of user IDs of users that should not be included into the search results, i.e. blocked users.
  """
  excludeUserIds: [ID!]
  maxResultCount: Int!
  subscription: UserSearchSubscriptionType
  expiresAt: DateTimeISO
  resultExpiresAt: DateTimeISO
  runInfos: [UserSearchRunInfo!]
  topFoundUsers: [UserListItem!]!
}

enum UserSearchType {
  search
  mentorRecommendation
  menteeRecommendation
}

type UserSearchFilter {
  searchText: String
  seeksHelp: UserSearchFieldOption
  offersHelp: UserSearchFieldOption
  languagesTextIds: [String!]
  expertisesTextIds: [String!]
  industriesTextIds: [String!]
  countryTextIds: [String!]
  companyStagesTextIds: [String!]
  latestActivityAfter: DateTimeISO
}

enum UserSearchFieldOption {
  isTrue
  isFalse
  any
  match
}

enum UserSearchSubscriptionType {
  none
  daily
  weekly
  monthly
}

type UserSearchRunInfo {
  topUserIds: [ID!]
  userCount: Int!
  matchCount: Int!
  batchSize: Int!
  batchCount: Int
  startedAt: DateTimeISO
  finishedAt: DateTimeISO
  durationInSecs: Int
  durationHuman: String
  totalDurationSearchInDb: Int
  totalDurationReadFromDb: Int
  totalDurationWriteToDb: Int
  totalDurationMatching: Int
}

input UserSearchInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID

  """The ID of the user that is searching and owns this object"""
  userId: ID
  matchingEngineId: ID
  userSearchType: UserSearchType
  name: String
  filter: UserSearchFilterInput

  """
  A list of user IDs of users that should not be included into the search results, i.e. blocked users.
  """
  excludeUserIds: [ID!]
  maxResultCount: Int
  subscription: UserSearchSubscriptionType
  expiresAt: DateTimeISO
  resultExpiresAt: DateTimeISO
  startSearch: Boolean! = true
}

input UserSearchFilterInput {
  searchText: String
  seeksHelp: UserSearchFieldOption
  offersHelp: UserSearchFieldOption
  languagesTextIds: [String!]! = []
  expertisesTextIds: [String!]! = []
  industriesTextIds: [String!]! = []
  countryTextIds: [String!]! = []
  companyStagesTextIds: [String!]! = []
  latestActivityAfter: DateTimeISO
}

input UserSearchListFilter {
  ids: [String!]
  excludeIds: [ID!]
  searchText: String
  caseSensitive: Boolean
  textSearchFields: [String!]
  createdAtFrom: DateTimeISO
  createdAtUntil: DateTimeISO
  updatedAtFrom: DateTimeISO
  updatedAtUntil: DateTimeISO
}

type UserWithScore {
  id: ID!
  createdAt: DateTimeISO!
  updatedAt: DateTimeISO
  deletedAt: DateTimeISO
  userHandle: String
  firstName: String
  lastName: String
  avatarUrl: String
  timezone: String

  """
  If discoverable is not true, the user will not be included in search results or recommended to other users. The system will set discoverable to null for various reasons, i.e. for a bad actor. The user can set it to false intentionally.
  """
  discoverable: Boolean
  trustLevel: Int!
  latestActivityAt: DateTimeISO
  userBlocks: [UserBlock!]
  inactivatedAt: DateTimeISO
  suspendedAt: DateTimeISO
  cityOfResidence: String
  regionOfResidence: String
  websites: [LabeledStringValue!]
  seeksHelp: Boolean
  offersHelp: Boolean
  yearsManagementExperience: Int
  yearsOwnershipExperience: Int
  academicExperiences: [AcademicExperience!]
  businessExperiences: [BusinessExperience!]
  isOnVacation: Boolean

  """This attribute is a copy of the mentor group membership."""
  mentor: MentorsGroupMembership

  """This attribute is a copy of the mentee group membership."""
  mentee: MenteesGroupMembership
  groupMemberships: [IGroupMembership!]!
  companies: [Company!]!
  countryOfOrigin: Country
  countryOfResidence: Country
  educationLevel: EducationLevel
  endorsements: [EndorsementWithTypes!]
  groups: [Group!]!
  profileCompletionPercentage: Int!
  profileRole: UserProfileRole!
  pronouns: [Pronoun!]!
  pronounsDisplay: String!

  """The score value that the matching engine assigned to this user."""
  score: Float
}

type Mm2Integration {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  fullSyncAt: DateTimeISO
}

type ServiceRequest {
  id: ID!
  serviceRequestType: ServiceRequestType!
  userId: ID
  userRoles: [UserRole!]
  objectIds: [ID!]
  modelTypes: [ModelType!]
  result: ServiceRequestResult!
  messageIds: [ServiceRequestMessageId!]
  message: String
  errorCode: ErrorCode
  events: [ModelEvent!]
  deviceUuid: String
  source: ServiceRequestSource
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  finishedAt: DateTimeISO
  expiresAt: DateTimeISO
}

enum ServiceRequestType {
  graphQlMutationCreateAcademicExperience
  graphQlMutationDeleteAcademicExperience
  graphQlMutationUpdateAcademicExperience
  graphQlMutationCreateBusinessExperience
  graphQlMutationDeleteBusinessExperience
  graphQlMutationUpdateBusinessExperience
  graphQlMutationCreateCompany
  graphQlMutationDeleteCompany
  graphQlMutationUpdateCompany
  graphQlQueryFindAndUpdateAllMm2Users
  graphQlQueryUserInboxUser
  graphQlMutationCreateAdminTask
  graphQlMutationFindAdminTaskById
  graphQlMutationDeleteAdminTask
  graphQlMutationUpdateAdminTask
  graphQlQueryFindAdminTask
  graphQlQueryAdminTaskDefinitions
  graphQlMutationClearAllAnalyticsSyncInfo
  graphQlMutationCreateAnalyticsSynchronization
  graphQlMutationDeleteAnalyticsSynchronization
  graphQlMutationPauseAnalyticsSynchronization
  graphQlMutationRunAnalyticsSynchronization
  graphQlQueryFindAnalyticsServiceRecord
  graphQlQueryFindAnalyticsSynchronizationById
  graphQlMutationCreateUploadedAsset
  graphQlMutationDeleteUploadedAsset
  graphQlMutationFindUploadedAssetById
  graphQlMutationFindUploadedAssetForUser
  graphQlMutationInitAssetUpload
  graphQlMutationUpdateUploadedAsset
  graphQlQueryFindUploadedAssetById
  graphQlQueryFindUploadedAssets
  graphQlQueryFindUploadedAssetsForUser
  graphQlMutationAddChannelMessageEvent
  graphQlMutationArchiveChannelForUserByMe
  graphQlMutationCreateChannel
  graphQlMutationCreateChannelInvitation
  graphQlMutationCreateChannelMessage
  graphQlMutationCreateChannelParticipant
  graphQlMutationDeleteChannel
  graphQlMutationDeleteChannelInvitation
  graphQlMutationDeleteChannelMessage
  graphQlMutationDeleteChannelParticipant
  graphQlMutationDeleteGroup
  graphQlMutationDeleteGroupMembership
  graphQlMutationMarkChannelMessagesAsSeenByMe
  graphQlMutationUpdateChannel
  graphQlMutationUpdateChannelInvitation
  graphQlMutationUpdateChannelMessage
  graphQlMutationUpdateChannelParticipant
  graphQlQueryChannelInvitations
  graphQlQueryChannelMessageChannel
  graphQlQueryChannelParticipants
  graphQlQueryFindChannelById
  graphQlQueryFindChannelInvitationById
  graphQlQueryFindChannelInvitationsBetweenUsers
  graphQlQueryFindChannelInvitationsForUser
  graphQlQueryFindChannelMessageById
  graphQlQueryFindChannelMessages
  graphQlQueryFindChannelParticipantById
  graphQlQueryFindChannels
  graphQlQueryFindChannelsForUser
  graphQlQueryFindMyChannels
  graphQlQueryFindPendingChannelInvitationsForUser
  graphQlQueryMyInbox
  graphQlQueryUserChannels
  graphQlQueryUserCompanies
  graphQlQueryUserGroupMembers
  graphQlQueryUserGroups
  graphQlQueryFindCountries
  graphQlQueryFindExpertises
  graphQlQueryFindIndustries
  graphQlQueryFindOptions
  unset
  graphQlQueryContentTag
  graphQlMutationCreateContentTag
  graphQlMutationDeleteContentTag
  graphQlMutationUpdateContentTag
  graphQlMutationRunDataGenerator
  graphQlQueryNotificationTemplate
  graphQlQueryAvailableUserHandle
  graphQlQueryUser
  graphQlMutationAddUserToGroup
  graphQlMutationCreateGroup
  graphQlMutationCreateGroupMembership
  graphQlMutationCreateSupportChannelConfig
  graphQlMutationDeleteGroupCms
  graphQlMutationDeleteSupportChannelConfig
  graphQlMutationRemoveUserFromAllGroups
  graphQlMutationRemoveUserFromGroup
  graphQlMutationUpdateGroup
  graphQlMutationUpdateGroupMembership
  graphQlMutationUpdateSupportChannelConfig
  graphQlQueryFindGroupById
  graphQlQueryFindGroupByIdent
  graphQlQueryFindGroupCmsByGroupId
  graphQlQueryFindGroupCmsByGroupIdent
  graphQlQueryFindGroupCmsById
  graphQlQueryFindGroupMembershipByIdField
  graphQlQueryFindGroupMemberships
  graphQlQueryFindGroupsField
  graphQlQueryMyGroupMemberships
  graphQlMutationCreateUserSearch
  graphQlMutationDeleteUserSearch
  graphQlMutationUpdateUserSearch
  graphQlQueryFindUserSearchById
  graphQlQueryFindUserSearchResults
  graphQlQueryUserSearchFoundUsers
  graphQlMutationCreateNotification
  graphQlMutationCreateNotificationTemplate
  graphQlMutationDeleteNotification
  graphQlMutationDeleteNotificationTemplate
  graphQlMutationMarkInAppMessageReceived
  graphQlMutationSendMultiStepActionNotification
  graphQlMutationUpdateNotification
  graphQlMutationUpdateNotificationTemplate
  graphQlMutationClearAllSyncInfo
  graphQlMutationCreateMm2Synchronization
  graphQlMutationDeleteAllMm2DataInMm3
  graphQlMutationDeleteMm2Synchronization
  graphQlMutationRunMm2Synchronization
  graphQlQueryFindMm2SynchronizationById
  graphQlQueryGetMm2Integration
  graphQlMutationNlpLabelMessage
  graphQlMutationUpdateNlpConversation
  graphQlMutationUpdateNlpMessage
  graphQlQueryFindNlpConversation
  graphQlMutationAddFeatureToUser
  graphQlMutationBlockUser
  graphQlMutationCreateMultiStepAction
  graphQlMutationCreateUserDevice
  graphQlMutationCreateUserRelationship
  graphQlMutationDeleteMyUser
  graphQlMutationDeleteUser
  graphQlMutationEndMySession
  graphQlMutationRemoveFeatureFromUser
  graphQlMutationReportUser
  graphQlMutationSignInUser
  graphQlMutationSignMeOut
  graphQlMutationSignUpOauthUser
  graphQlMutationSignUpUser
  graphQlMutationStartMySession
  graphQlMutationUnblockUser
  graphQlMutationUpdateMyUser
  graphQlMutationUpdateUser
  graphQlMutationUpdateUserDevice
  graphQlMutationUpdateUserRelationship
  graphQlMutationUpsertBackgroundTask
  graphQlMutationVerifyMultiStepActionToken
  graphQlQueryBackgroundTask
  graphQlQueryFindAvailableUserHandle
  graphQlQueryFindMyBlockedUsers
  graphQlQueryFindMyUser
  graphQlQueryFindMyUserDevices
  graphQlQueryFindUserById
  graphQlQueryFindUserByIdent
  graphQlQueryFindUserDeviceById
  graphQlQueryFindUserDevices
  graphQlQueryFindUserRelationshipById
  graphQlQueryFindUsers
  graphQlQueryGetMultiStepActionProgress
  graphQlQueryLatestUserDevice
  graphQlQueryUnreadInAppMessages
  graphQlQueryUserUserRelationships
  graphQlMutationCreateUserTracking
  graphQlMutationUpdateUserTracking
  graphQlQueryFindTrainingById
  graphQlQueryFindTrainingsForMe
  graphQlQueryFindTrainingsForUser
  graphQlQueryFindTrainingSessionById
  graphQlQueryFindTrainingSessionsByTrainingId
  graphQlQueryFindTrainingSessionsForMe
}

enum ServiceRequestResult {
  ok
  error
  unset
}

enum ServiceRequestMessageId {
  systemError
  invalidInput
  groupRuleFailed
  unknown
}

enum ErrorCode {
  academicExperienceNameMissing
  academicExperienceUserIdMissing
  businessExperienceNameMissing
  businessExperienceUserIdMissing
  companyNameMissing
  companyNameTaken
  contentTagAlreadyExist
  contentTagModelTypeMissing
  contentTagObjectIdMissing
  contentTagTypeMissing
  failedToConnect
  failedToUpdate
  unknown
  alreadyGroupMember
  groupLevelTooDeep
  groupNameMissing
  groupNameTaken
  groupNotActive
  groupRuleFailed
  groupSlugMissing
  groupSlugTaken
  notAGroupMember
  parentGroupNotFound
  matchingEngineNameMissing
  matchingEngineNameTaken
  expertiseBidirectionalMappingError
  expertiseTextIdDNE
  alreadyExists
  alreadyInitialized
  dataValidationFailed
  exceedsLimit
  invalidInput
  noLiveWebsocketConnectionAvailable
  noNotificationMethodAvailable
  notAllowed
  notFound
  notImplemented
  notInitialized
  notSupported
  serviceNotAvailable
  systemError
  timeout
  tooManyRequests
  authTokenNoMatch
  currentPasswordIncorrect
  currentPasswordMissing
  deviceUuidMissing
  emailInvalid
  emailMissing
  failedToCreateAccount
  failedToSignin
  invalidPushNotificationToken
  passwordMissing
  passwordNoMatch
  phoneNumberInvalid
  phoneNumberMissing
  phoneNumberNotSupported
  unauthorized
  userAlreadyExists
  userAnonymized
  userDeviceNotFound
  userNotActive
  userNotFound
  trackingInvalidTrackId
  trainingCannotUpdateFields
  trainingContentPageCannotUpdateFields
  trainingContentPageMm2IdMissing
  trainingContentPageMm2IdTaken
  trainingMm2IdMissing
  trainingMm2IdTaken
  trainingSessionCannotUpdateFields
  trainingSessionMm2IdMissing
  trainingSessionMm2IdTaken
  trainingSessionProgressInvalid
  trainingSessionTrainingMissing
  trainingSessionUserIdMissing
}

enum ServiceRequestSource {
  analytics
  graphqlApi
  restApi
  httpRoute
  system
  admin
  vts
  mm2
}

type SidMultiStepAction {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  userId: ID!
  userIdent: String
  userHandle: String
  email: String
  phoneNumber: String
  deviceUuid: String
  actionType: MultiStepActionType!
  actionStatus: MultiStepActionStatus!
  notificationMethod: NotificationMethod!
  result: MultiStepActionResult!
  confirmToken: String
  attemptCount: Int!
  notificationSentAt: DateTimeISO
  notificationResult: MultiStepActionSendNotificationResult
  notificationId: String
  textData: String
  report: String
  emailPassed: Boolean
  emailUpdatedAt: DateTimeISO
  emailVerifiedAt: DateTimeISO
  errors: [MultiStepActionError!]
  password: String
  passwordPassed: Boolean
  passwordResettedAt: DateTimeISO
  passwordUpdatedAt: DateTimeISO
  phoneNumberPassed: Boolean
  phoneNumberUpdatedAt: DateTimeISO
  phoneNumberVerifiedAt: DateTimeISO
  signedInAt: DateTimeISO
  tfaBackupCodes: String
  expiresAt: DateTimeISO
}

enum MultiStepActionType {
  resetPassword
  tokenSignIn
  unset
  updateEmail
  updatePassword
  updatePhoneNumber
  verifyEmail
  verifyPhoneNumber
  verifyPhoneSignupOnSignup
}

enum MultiStepActionStatus {
  created
  started
  finished
}

enum NotificationMethod {
  off
  auto
  email
  sms
  pushNotification
  inAppNotification
}

enum MultiStepActionResult {
  confirmTokenMismatch
  dataValidationFailed
  deviceNotFound
  emailMismatch
  emailNotVerified
  error
  expired
  invalidEmail
  phoneNumberInvalid
  missingEmail
  missingPhoneNumber
  notFound
  ok
  passed
  passwordMismatch
  passwordUpdated
  phoneNumberMismatch
  phoneNumberNotVerified
  systemError
  unset
  userFailedValidation
  userNotFound
  userNotSignedIn
}

enum MultiStepActionSendNotificationResult {
  ok
  failed
  phoneNumberInvalid
}

type MultiStepActionError {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  key: String!
  messageId: String!
  message: String!
}

type SidMultiStepActionProgress {
  actionId: ID!
  userId: ID!
  actionType: MultiStepActionType!
  actionStatus: MultiStepActionStatus
  notificationMethod: NotificationMethod
  result: MultiStepActionResult!
  attemptCount: Int!
  notificationSentAt: DateTimeISO
  notificationResult: MultiStepActionSendNotificationResult
  notificationId: String
  textData: String
  report: String
  emailPassed: Boolean
  emailUpdatedAt: DateTimeISO
  emailVerifiedAt: DateTimeISO
  errors: [MultiStepActionError!]
  authToken: String
  authTokenExpiresAt: DateTimeISO
  passwordPassed: Boolean
  passwordResettedAt: DateTimeISO
  passwordUpdatedAt: DateTimeISO
  phoneNumberPassed: Boolean
  phoneNumberUpdatedAt: DateTimeISO
  phoneNumberVerifiedAt: DateTimeISO
  signedInAt: DateTimeISO
  expiresAt: DateTimeISO
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
}

type MyUser {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: UserMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  firstName: String
  lastName: String
  userHandle: String
  phoneNumber: String
  phoneNumberUpdatedAt: DateTimeISO
  isPhoneNumberVerified: Boolean!
  email: String

  """The source of the email address, e.g. "google", "facebook", etc."""
  emailSource: String
  emailUpdatedAt: DateTimeISO
  isEmailVerified: Boolean!
  genderTextId: String
  cityOfResidence: String
  regionOfResidence: String
  countryOfResidenceTextId: String
  postalCode: String
  avatarUrl: String
  websites: [LabeledStringValue!]
  authType: AuthType
  tfaBackupCodes: String
  passwordUpdatedAt: DateTimeISO
  preferredLanguageTextId: String
  spokenLanguagesTextIds: [String!]!
  selectedUiLanguageTextId: UiLanguage
  fallbackUiLanguageTextId: UiLanguage

  """
  If discoverable is not true, the user will not be included in search results or recommended to other users. The system will set discoverable to null for various reasons, i.e. for a bad actor. The user can set it to false intentionally.
  """
  discoverable: Boolean
  roles: [UserRole!]!
  appFeatures: [AppFeature!]
  source: String
  timezone: String
  preferences: UserPreferences
  trustLevel: Int!
  signedInAt: DateTimeISO
  signedOutAt: DateTimeISO
  latestActivityAt: DateTimeISO
  userDevices: [UserDeviceWithoutAuth!]!
  userBlocks: [UserBlock!]
  relationships: [UserRelationship!]
  inactivatedAt: DateTimeISO
  inactivatedBy: ID
  termsAndConditionsAcceptedAt: DateTimeISO
  optIntoNewsletter: Boolean
  onboardingStage: String
  suspendedAt: DateTimeISO
  suspendedBy: ID
  anonymizedAt: DateTimeISO
  syncedToAnalyticsAt: DateTimeISO
  addedToBgVaultAt: DateTimeISO
  companyIds: [ID!]
  companies: [Company!]
  groupIds: [ID!]!
  parentGroupIds: [ID!]!
  externalGroupIds: [ID!]!
  pronounsTextIds: [String!]
  groupMemberships: [IGroupMembership!]!
  seeksHelp: Boolean
  offersHelp: Boolean
  birthYear: Int
  ethnicity: String
  educationLevelTextId: String
  personalBio: String
  yearsManagementExperience: Int
  yearsOwnershipExperience: Int
  academicExperienceIds: [ID!]
  academicExperiences: [AcademicExperience!]

  """This attribute is only used by the MM2 synchronizer."""
  genderSelfDescribed: String
  businessExperienceIds: [ID!]
  businessExperiences: [BusinessExperience!]
  cityOfOrigin: String
  regionOfOrigin: String
  countryOfOriginTextId: String
  isOnVacation: Boolean
  avatarAsset: UploadedAsset
  profileRoleHistory: [UserProfileRoleHistoryItem!]
  ssoIdp: String

  """Records whether a user was originally created in MM2."""
  originatedInMm2: Boolean

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This is the MM2 password hash."""
  mm2PasswordHash: String

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String

  """This attribute is only used by the MM2 synchronizer."""
  mm2PhotoOriginal: String

  """For MM2 users, this means a profile is completed."""
  mm2BasicAccountCompleted: Boolean

  """Records whether a user has logged into MM3."""
  hasSignedInToMm3: Boolean

  """Records whether a user has logged into MM2."""
  hasSignedInToMm2: Boolean

  """This attribute is a copy of the mentor group membership."""
  mentor: MentorsGroupMembership

  """This attribute is a copy of the mentee group membership."""
  mentee: MenteesGroupMembership
  countryOfResidence: Country
  gender: Gender
  latestUserDevice: UserDeviceWithoutAuth!
  spokenLanguages: [Language!]!
  preferredLanguage: Language
  fallbackUiLanguage: Language!
  preferredUiLanguage: Language
  unreadInAppMessages: [Notification!]!
  channels(
    """Set options on channels, e.g. to include archived channels."""
    options: FindObjectsOptions

    """
    Set to True to return only channels which have been accepted. Default is False, which also returns channels with multiple messages that have not been accepted. Channels with only unaccepted invitations have no messages (except for channels created via the synchronizer).
    """
    mustBeAccepted: Boolean

    """
    Set to True to return only channels with messages. Default is False, which also returns channels without messages. Channels with only unaccepted invitations have no messages (except for channels created via the synchronizer).
    """
    mustHaveMessages: Boolean
  ): [Channel!]!
  channelInvitations: [ChannelInvitation!]!
  channelParticipants: [ChannelParticipant!]!
  countryOfOrigin: Country
  educationLevel: EducationLevel
  endorsements: [EndorsementWithTypes!]
  groupMembers: [IGroupMembership!]!
  groups: [Group!]!
  hasTrainings: Boolean!
  profileCompletionPercentage: Int!
  profileRole: UserProfileRole!
  pronouns: [Pronoun!]!
  pronounsDisplay: String!
  uploadedAssets: [UploadedAsset!]!
  inbox: UserInbox! @deprecated(reason: "Use findMyInbox")
}

type ReportUserReason {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  textId: String!
  parentTextId: String
  isParent: Boolean
  optionType: OptionType!
  value: String!
  translatedValue: String
  supportedLanguages: [UiLanguage!]

  """
  Material icon name. Intended to be used by the Flutter app for the expertises and industries icons.
  """
  materialIconName: String
  description: String
  translatedDescription: String
  language: UiLanguage

  """This attribute is only used by the MM2 synchronizer."""
  mm2Id: String
  mm2Value: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """This attribute is only used by the MM2 synchronizer."""
  mm2TextId: String

  """This attribute is only used by the MM2 synchronizer."""
  mm3TextId: String
  childOptions: [Option!]
  parentOption: [Option!]
}

type Training {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  title: String
  titleAr: String
  titleEn: String
  titleEs: String
  titleInd: String
  titleRu: String
  titleSo: String
  slug: String
  slugAr: String
  slugEn: String
  slugEs: String
  slugInd: String
  slugRu: String
  slugSo: String
  urlPath: String
  urlPathAr: String
  urlPathEn: String
  urlPathEs: String
  urlPathInd: String
  urlPathRu: String
  urlPathSo: String
  relativeUrlPath: String
  relativeUrlPathAr: String
  relativeUrlPathEn: String
  relativeUrlPathEs: String
  relativeUrlPathInd: String
  relativeUrlPathRu: String
  relativeUrlPathSo: String
  live: Boolean!
  locked: Boolean!
  expired: Boolean!

  """
  This attribute is only used by the MM2/VTS synchronizer. MM2 Wagtail page PK/ MM2 training page ID.
  """
  mm2Id: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO
  numCorrectAnswersToPass: Int!
  about: String
  aboutAr: String
  aboutEn: String
  aboutEs: String
  aboutInd: String
  aboutRu: String
  aboutSo: String
  aboutMm2: String
  aboutArMm2: String
  aboutEnMm2: String
  aboutEsMm2: String
  aboutIndMm2: String
  aboutRuMm2: String
  aboutSoMm2: String
  introduction: String
  introductionAr: String
  introductionEn: String
  introductionEs: String
  introductionInd: String
  introductionRu: String
  introductionSo: String
  introductionMm2: String
  introductionArMm2: String
  introductionEnMm2: String
  introductionEsMm2: String
  introductionIndMm2: String
  introductionRuMm2: String
  introductionSoMm2: String
  lessonPlanLevels: Int!
  certificateTemplateId: String
  code: String
  tags: [String!]
  restricted: Boolean

  """Maps to wagtailcore_page.show_in_menus from MM2"""
  showInMenus: Boolean!

  """Maps to TrainingPageCountry from MM2"""
  countriesTextIds: [String!]

  """Maps to TrainingPageCommunity from MM2"""
  groupIds: [String!]

  """Maps to TrainingPageLanguage from MM2"""
  languagesTextIds: [String!]

  """Restrict the training to mentees. Maps to MM2 TrainingPage.role."""
  seeksHelp: Boolean!

  """Restrict the training to mentors. Maps to MM2 TrainingPage.role."""
  offersHelp: Boolean!
  imageUrls: [String!]

  """Markdown field which contains the trainings lesson plan"""
  lessonPlan: String

  """Markdown field which contains the trainings lesson plan in Arabic"""
  lessonPlanAr: String

  """Markdown field which contains the trainings lesson plan in English"""
  lessonPlanEn: String

  """Markdown field which contains the trainings lesson plan in Spanish"""
  lessonPlanEs: String

  """
  Markdown field which contains the trainings lesson plan in Bahasa Indonesian
  """
  lessonPlanInd: String

  """Markdown field which contains the trainings lesson plan in Russian"""
  lessonPlanRu: String

  """Markdown field which contains the trainings lesson plan in Somali"""
  lessonPlanSo: String

  """Training content pages used for the lesson plan"""
  trainingContentPages: [TrainingContentPage!]
  myTrainingSessions: [TrainingSession!]!
  myLatestTrainingSession: TrainingSession
  isTrainingCompletedForMe: Boolean
  isTrainingPassedForMe: Boolean
}

type TrainingContentPage {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  title: String
  titleAr: String
  titleEn: String
  titleEs: String
  titleInd: String
  titleRu: String
  titleSo: String
  slug: String
  slugAr: String
  slugEn: String
  slugEs: String
  slugInd: String
  slugRu: String
  slugSo: String
  urlPath: String
  urlPathAr: String
  urlPathEn: String
  urlPathEs: String
  urlPathInd: String
  urlPathRu: String
  urlPathSo: String
  relativeUrlPath: String
  relativeUrlPathAr: String
  relativeUrlPathEn: String
  relativeUrlPathEs: String
  relativeUrlPathInd: String
  relativeUrlPathRu: String
  relativeUrlPathSo: String
  live: Boolean!
  locked: Boolean!
  expired: Boolean!

  """
  This attribute is only used by the MM2/VTS synchronizer. MM2 Wagtail page PK/ MM2 trainingContentPage page ID.
  """
  mm2Id: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO

  """
  TrainingContentPages can have children TrainingContentPages. This is used to represent the tree structure of the training content.
  """
  children: [TrainingContentPage!]

  """FK to Training model"""
  trainingId: String!
}

type TrainingSession {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  mm2Id: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO
  startedAt: DateTimeISO!

  """The ID of the training in MM3."""
  trainingId: String!
  userId: String!
  isInProgress: Boolean!
  percentCompleted: Float!
  completionInfo: TrainingSessionCompletionInfo
}

type TrainingSessionCompletionInfo {
  updatedAt: DateTimeISO
  mm2Id: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO
  completedAt: DateTimeISO!
  questionsAnsweredCorrectly: Int!
  numberOfQuestions: Int!
  numCorrectAnswersToPass: Int!
  isPassingScore: Boolean!
  preTestQuestionsAnsweredCorrectly: Int!
  numberOfPreTestQuestions: Int!
}

type Mutation {
  createOneTimeAuthTokenForMe: String!
  signInUser(input: SignInUserInput!): UserAuthResponse!
  signInOauthUser(input: SignInOauthUserInput!): UserAuthResponse!
  signMeOut: String!
  signUpUser(input: SignUpUserInput!): UserAuthResponse!
  verifyOneTimeAuthToken(input: VerifyOneTimeAuthTokenInput!): Boolean!
  addAppFeatureToUser(options: FindObjectsOptions, match: UserInput, filter: UserListFilter, appFeature: AppFeature!): String!
  deleteUser(anonymizePersonalData: Boolean, deletePhysically: Boolean!, description: String, cause: String, userId: String!): String!
  removeAppFeatureFromUser(options: FindObjectsOptions, match: UserInput, filter: UserListFilter, appFeature: AppFeature!): String!
  reportUser(input: ReportUserInput!): String!
  updateUser(input: UserInput!): String!
  createUserDevice(input: UserDeviceInput!): UserDeviceWithoutAuth!
  updateUserDevice(input: UserDeviceInput!): String!
  createAcademicExperience(input: AcademicExperienceInput!): AcademicExperience!
  deleteAcademicExperience(deletePhysically: Boolean!, academicExperienceId: String!): ServiceRequest!
  updateAcademicExperience(input: AcademicExperienceInput!): ServiceRequest!
  createBusinessExperience(input: BusinessExperienceInput!): BusinessExperience!
  deleteBusinessExperience(deletePhysically: Boolean!, businessExperienceId: String!): ServiceRequest!
  updateBusinessExperience(input: BusinessExperienceInput!): ServiceRequest!
  createCompany(input: CompanyInput!): Company!
  deleteCompany(anonymizePersonalData: Boolean!, deletePhysically: Boolean!, companyId: String!): ServiceRequest!
  updateCompany(input: CompanyInput!): ServiceRequest!
  findAndUpdateAllMm2Users: Boolean!
  createAdminTask(adminTaskInput: AdminTaskInput!): AdminTask!
  deleteAdminTask(adminTaskId: String!): ServiceRequest!
  runAdminTask(adminTaskId: String!): ServiceRequest!
  updateAdminTask(adminTaskInput: AdminTaskInput!): AdminTask!
  clearAllSyncInfo(includeMm3: Boolean!, includeMm2: Boolean!): String!
  createAnalyticsSynchronization(input: AnalyticsSynchronizationInput!): AnalyticsSynchronization
  deleteAnalyticsSynchronization(analyticsSynchronizationId: String!): AnalyticsSynchronization
  findAnalyticsSynchronizationById(analyticsSynchronizationId: String!): AnalyticsSynchronization
  pauseAnalyticsSynchronizationById(analyticsSynchronizationId: String!): AnalyticsSynchronization
  runAnalyticsSynchronization(runAgain: Boolean, id: String!): AnalyticsSynchronization
  createUploadedAsset(input: UploadedAssetInput!): UploadedAsset!
  deleteUploadedAsset(deletePhysically: Boolean!, id: String!): UploadedAsset!
  initAssetUpload(input: UploadedAssetInput!): UploadedAsset!
  updateUploadedAsset(input: UploadedAssetInput!): String!
  acceptChannelInvitation(channelInvitationId: String!): String!
  createChannelInvitation(input: ChannelInvitationInput!): ChannelInvitation!
  declineChannelInvitation(reasonTextId: DeclineChannelInvitationReasonTextId!, channelInvitationId: String!): String!
  deleteChannelInvitation(deletePhysically: Boolean!, channelInvitationId: String!): String!
  dismissChannelInvitationFromInbox(channelInvitationId: String!): String!
  updateChannelInvitation(input: ChannelInvitationInput!): String!
  archiveChannelForMe(channelId: String!): String!
  createChannel(input: ChannelInput!): Channel!
  deleteChannel(anonymizePersonalData: Boolean!, deletePhysically: Boolean!, channelId: String!): String!
  markChannelMessagesAsSeenByMe(channelId: String!): String!
  updateChannel(input: ChannelInput!): String!
  unarchiveChannelForMe(channelId: String!): String!
  addChannelMessageEvent(input: BgAddChannelMessageEventInput!): String!
  createChannelMessage(input: ChannelMessageInput!): ChannelMessage!
  deleteChannelMessage(deletePhysically: Boolean!, channelMessageId: String!): String!
  updateChannelMessage(input: ChannelMessageInput!): String!
  createChannelParticipant(input: ChannelParticipantInput!): ChannelParticipant!
  deleteChannelParticipant(deletePhysically: Boolean!, channelParticipantId: String!): String!
  updateChannelParticipant(input: ChannelParticipantInput!): String!
  createContentTag(input: ContentTagInput!): ContentTag!
  deleteContentTag(deletePhysically: Boolean!, contentTagId: String!): ServiceRequest!
  updateContentTag(input: ContentTagInput!): ServiceRequest!
  runDataGenerator(input: GenRequest!): Boolean!
  createGroupMembership(input: GroupMembershipInput!): ServiceRequest!
  createMenteesGroupMembership(input: MenteesGroupMembershipInput!): ServiceRequest!
  createMentorsGroupMembership(input: MentorsGroupMembershipInput!): ServiceRequest!
  deleteGroupMembership(deletePhysically: Boolean!, groupMembershipId: String!): String!
  updateGroupMembership(input: GroupMembershipInput!): ServiceRequest!
  updateIqlaaGroupMembership(input: IqlaaGroupMembershipInput!): ServiceRequest!
  updateMastercardGroupMembership(input: MastercardGroupMembershipInput!): ServiceRequest!
  updateMenteesGroupMembership(input: MenteesGroupMembershipInput!): ServiceRequest!
  updateMentorsGroupMembership(input: MentorsGroupMembershipInput!): ServiceRequest!
  addUserToGroup(roles: [GroupMembershipRole!]!, groupIdent: String, groupId: String, userId: String!): ServiceRequest!
  createGroup(input: GroupInput!): Group!
  deleteGroup(deletePhysically: Boolean!, groupId: String!): ServiceRequest!
  removeUserFromGroup(force: Boolean!, groupIdent: String, groupId: String, userId: String!): String!
  updateGroup(input: GroupInput!): ServiceRequest!
  createSupportChannelConfig(input: SupportChannelConfigInput!): SupportChannelConfig!
  deleteSupportChannelConfig(deletePhysically: Boolean!, supportChannelConfigId: String!): ServiceRequest!
  updateSupportChannelConfig(input: SupportChannelConfigInput!): ServiceRequest!
  createUserSearch(input: UserSearchInput!): UserSearch!
  deleteUserSearch(deletePhysically: Boolean!, userSearchId: String!): ServiceRequest!
  updateUserSearch(input: UserSearchInput!): ServiceRequest!
  createNotification(notificationInput: NotificationInput!): Notification!
  deleteNotification(deletePhysically: Boolean!, notificationId: String!): String!
  markInAppMessageReceived(actionTaken: AppAction!, notificationId: String!): String!
  sendMultiStepActionNotification(input: SendMultiStepActionNotificationInput!): String!
  updateNotification(notificationInput: NotificationInput!): String!
  createNotificationTemplate(notificationTemplateInput: NotificationTemplateInput!): NotificationTemplate!
  deleteNotificationTemplate(deletePhysically: Boolean!, notificationTemplateId: String!): String!
  updateNotificationTemplate(notificationTemplateInput: NotificationTemplateInput!): String!

  """
  Deletes all data that was imported from MM2. This field is only available in non-production environments.
  """
  deleteAllMm2DataInMm3: String!
  createMm2Synchronization(input: Mm2SynchronizationInput!): Mm2Synchronization!
  deleteMm2Synchronization(mm2SynchronizationId: String!): Mm2Synchronization!
  findMm2SynchronizationById(mm2SynchronizationId: String!): Mm2Synchronization!
  runMm2Synchronization(runAgain: Boolean, id: String!): Mm2Synchronization!
  createMultiStepAction(input: SidMultiStepActionInput!): SidMultiStepActionProgress!
  startResetPassword(input: UserIdentInput!): SidMultiStepActionProgress!
  startVerifyEmail(email: String!): SidMultiStepActionProgress!
  startVerifyPhoneNumber(phoneNumber: String): SidMultiStepActionProgress!
  verifyMultiStepActionToken(input: VerifyMultiStepActionTokenInput!): SidMultiStepActionProgress!
  blockUserForMe(notes: String, reasonTextId: String, userId: String!): String! @deprecated(reason: "Use blockUserForMeV2")
  blockUserForMeV2(notes: String, reasonTextId: String, userId: String!): ServiceRequest!
  deleteMyUser(anonymizePersonalData: Boolean, deletePhysically: Boolean!, description: String, cause: String): String!
  endMySession(deviceUuid: String!): String! @deprecated(reason: "Use endMySessionV2")
  endMySessionV2: String!
  startMySession(pushNotificationToken: String, deviceUuid: String!): String! @deprecated(reason: "Use startMySessionV2")
  startMySessionV2(returnContentStatus: Boolean, pushNotificationToken: String): ContentStatus!
  unblockUserForMe(userId: String!): String! @deprecated(reason: "Use unblockUserForMeV2")
  unblockUserForMeV2(userId: String!): ServiceRequest!
  updateMyUser(input: MyUserInput!): String!
  createUserTracking(input: UserTrackingInput!): String!
}

"""API response to signInUser/signUpUser/signInOauthUser"""
type UserAuthResponse {
  userId: String!
  firstName: String!
  lastName: String!
  onboardingStage: String!
  foundUser: Boolean!
  authType: AuthType!
  authToken: String
  authTokenExpiresAt: DateTimeISO
}

"""User sign up input data"""
input SignInUserInput {
  authType: AuthType! = token
  ident: String
  identType: UserIdentType
  password: String
  pushNotificationToken: String
  cookieConsentChoice: CookieChoiceTextId
  allowToTrack: Boolean
}

enum CookieChoiceTextId {
  acceptAll
  rejectAll
  acceptEssentials
}

"""User sign up input data"""
input SignInOauthUserInput {
  ident: String
  identType: UserIdentType
  firstName: String
  lastName: String
  displayName: String
  userHandle: String
  email: String
  emailVerified: Boolean
  phoneNumber: String
  phoneNumberVerified: Boolean
  identityProvider: IdentityProvider! = own
  oauthFederatedProvider: FederatedIdentityProvider
  oauthUserId: String
  oauthDelegateUserId: String
  oauthProfileUrl: String
  oauthToken: String
  oauthTokenId: String
  oauthIdToken: String
  oauthTokenCreatedAt: DateTimeISO
  oauthTokenExpiresAt: DateTimeISO
  oauthRefreshToken: String
  oauthRefreshTokenCreatedAt: DateTimeISO
  oauthRefreshTokenExpiresAt: DateTimeISO
  pushNotificationToken: String
  source: String
  trackId: String
  cookieConsentChoice: CookieChoiceTextId
  allowToTrack: Boolean
  checkAvailable: Boolean! = true
}

"""User sign up input data"""
input SignUpUserInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  firstName: String
  lastName: String
  userHandle: String
  email: String
  emailVerifiedAt: DateTimeISO
  emailSource: String
  phoneNumber: String
  authType: AuthType! = token
  password: String
  avatarUrl: String
  source: String
  timezone: String
  optIntoNewsletter: Boolean
  pushNotificationToken: String
  trackId: String
  cookieConsentChoice: CookieChoiceTextId
  allowToTrack: Boolean
  checkAvailable: Boolean! = true
  isTestUser: Boolean
  offersHelp: Boolean
  seeksHelp: Boolean
}

input VerifyOneTimeAuthTokenInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  userIdent: String! = ""
  userIdentType: UserIdentType
  deviceUuid: String! = ""
  token: String! = ""
}

input ReportUserInput {
  userId: ID
  reasonTextId: ReportUserReasonTextId
  messageText: String
  createdBy: ID
}

enum ReportUserReasonTextId {
  notSet
  badActor
  fakePerson
  harasses
  impersonator
  inappropriate
  objectionableLanguage
  promotesHate
  sharesObjectionableContent
  spammer
  usesObjectionableLanguage
  violatesRules
}

input AdminTaskInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  adminTaskType: AdminTaskType
  result: AdminTaskResult
  resultMessage: String

  """Any error messages that happened during the run"""
  error: String

  """arguments"""
  args: [String!]

  """number of milliseconds before timeout"""
  timeout: Int

  """run ask after creation"""
  autoRun: Boolean

  """should the task run synchronously, or in the background?"""
  synchronous: Boolean

  """Date/time this task started to run"""
  startedAt: DateTimeISO

  """Date/time this task will be removed from the db"""
  expiresAt: DateTimeISO

  """Date/time this task completed its run"""
  finishedAt: DateTimeISO
}

type AnalyticsSynchronization {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID

  """If synchronizing a single object, set this to its ID."""
  objectId: String
  modelType: ModelType
  syncMode: AnalyticsSynchronizationMode!
  skip: Int
  batchSize: Int

  """Number of objects to synchronize."""
  limit: Int

  """Set to true to run the synchronization right after creation."""
  autorun: Boolean!
  duration: Int
  itemCount: Int
  pace: Int

  """Will only include objects with a newer then specified updatedAt date."""
  usersSinceUpdatedAt: String
  previousSyncAt: DateTimeISO
  startedAt: DateTimeISO
  finishedAt: DateTimeISO

  """
  Time this object will be deleted from the DB. Default = 10 days after creation.
  """
  expiresAt: DateTimeISO
  pausedAt: DateTimeISO
}

enum AnalyticsSynchronizationMode {
  full
  updated
  new
}

input AnalyticsSynchronizationInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID

  """If synchronizing a single object, set this to its ID."""
  objectId: String

  """If synchronizing a single object, set this to its model type."""
  modelType: ModelType
  syncMode: AnalyticsSynchronizationMode! = full
  skip: Int
  batchSize: Int

  """Number of objects to synchronize."""
  limit: Int

  """Set to true to run the synchronization right after creation."""
  autorun: Boolean! = true
  usersSinceUpdatedAt: String

  """
  Time this object will be deleted from the DB. Default = 10 days after creation.
  """
  expiresAt: DateTimeISO
}

input ChannelInvitationInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  channelId: ID
  recipientId: ID
  channelName: String
  channelTopic: String
  messageText: String

  """
  An authorized sender (i.e. role: ["support"]) can skip the acceptance step.
  """
  autoAccept: Boolean
  declineReasonTextId: String
  dismissedFromInboxBySenderAt: DateTimeISO
  dismissedFromInboxByRecipientAt: DateTimeISO
  readByRecipientAt: DateTimeISO
  status: ChannelInvitationStatus
  suspendedAt: DateTimeISO
  suspendedBy: ID
  userSearchId: ID
  searchRank: Int

  """This attribute is only used by the MM2 synchronizer."""
  mm2ConversationId: String

  """This attribute is only used by the MM2 synchronizer. Mm2 message ID."""
  mm2Id: String

  """This attribute is only used by the MM2 synchronizer."""
  syncedWithMm2At: DateTimeISO
}

enum DeclineChannelInvitationReasonTextId {
  notGoodFit
  tooBusy
  noReason
  fakeProfile
  inappropriate
}

input BgAddChannelMessageEventInput {
  channelId: ID! = ""
  messageIds: [ID!]! = []
  recipientId: ID! = ""
  event: ChannelMessageEvent! = unset
}

enum ChannelMessageEvent {
  received
  seen
  unset
}

input ChannelParticipantInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  channelId: ID
  userId: ID
  invitedBy: ID
  channelName: String
  role: ChannelParticipantRole
  suspendedAt: DateTimeISO
  suspendedBy: ID
}

type ContentTag {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  objectId: ID!
  contentModelType: ModelType!

  """
  ID of the user that created/owns the content referred to in this ContentTag. The ID of the user that created this ContentTag is stored in the createdBy field for ContentTags that were created by a user.
  """
  userId: ID
  contentTagTypeTextId: String!
  childContentTagTypeTextId: String
  messageText: String
  moderationConcern: ModerationConcern
  allModerationConcerns: [ModerationConcern!]
  approvedByRecipientAt: DateTimeISO

  """ID of the admin user that verified the tag."""
  verifiedBy: ID
  verifiedAt: DateTimeISO

  """ID of the admin user that dimsissed the tag."""
  dismissedBy: ID
  dismissedAt: DateTimeISO
  childContentTagType: ContentTagType
  contentTagType: ContentTagType
}

input ContentTagInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  objectId: ID
  contentModelType: ModelType

  """
  ID of the user that created/owns the content referred to in this ContentTag. The ID of the user that created this ContentTag is stored in the createdBy field for ContentTags that were created by a user.
  """
  userId: ID
  contentTagTypeTextId: String
  childContentTagTypeTextId: String
  messageText: String
  moderationConcern: ModerationConcernInput
  allModerationConcerns: [ModerationConcernInput!]
  approvedByRecipientAt: DateTimeISO

  """ID of the admin user that verified the tag."""
  verifiedBy: ID
  verifiedAt: DateTimeISO

  """ID of the admin user that dimsissed the tag."""
  dismissedBy: ID
  dismissedAt: DateTimeISO
}

input ModerationConcernInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  moderationConcernType: ModerationConcernType! = unknown
  name: String
  description: String
  value: String
  languageTextId: String

  """
  must match with capitalization; ignored if isRegex = true; default = true
  """
  isCaseSensitive: Boolean

  """only matches full words; ignored if isRegex = true; default = true"""
  isWord: Boolean

  """value is a regex expression without flags; default = false"""
  isRegex: Boolean

  """default = false"""
  isNameOfBadActor: Boolean

  """default = false"""
  isCompanyNameOfBadActor: Boolean

  """default = false"""
  isEmailOfBadActor: Boolean

  """default = false"""
  isPhoneNumberOfBadActor: Boolean

  """default = false"""
  isWebsiteOfBadActor: Boolean

  """delete the content, if a match is found; default = false"""
  deleteContent: Boolean

  """number of points to reduce a users trustLevel, if found"""
  trustLevelImpact: Int
  version: String
}

input GenRequest {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  clearDb: Boolean! = false
  channelCount: Int! = 0
  invitationCount: Int! = 0
  messageCount: Int! = 0
  users: GenUserInput! = {count: 0, emailDomain: "micromentor.org"}
  groups: GenGroupsInput! = {count: 0, specificGroups: []}
  publishAppEvents: Boolean! = false
}

input GenUserInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  count: Int! = 0
  dualRoleCount: Int
  emailDomain: String! = "micromentor.org"
  emailPrefix: String
  mentorCount: Int
  menteeCount: Int
  mentorRatio: Float
  password: String
}

input GenGroupsInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  count: Int! = 0
  specificGroups: [GenSpecificGroupInput!]! = []
}

input GenSpecificGroupInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  name: String! = ""
  mentorCount: Int
  menteeCount: Int
  groupRules: [GenGroupRuleInput!]
  matchingEngine: GenMatchingEngineInput
}

input GenGroupRuleInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  someGroupRule: Boolean! = false
  someOtherGroupRule: Boolean! = false
}

input GenMatchingEngineInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  someMatchingRule: Boolean! = false
  someOtherMatchingRule: Boolean! = false
}

input MenteesGroupMembershipInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  groupId: ID
  groupIdent: String
  userId: ID
  roles: [GroupMembershipRole!]

  """Must match expertise textIds."""
  soughtExpertisesTextIds: [String!]

  """Must match expertise textIds."""
  additionalSoughtExpertisesTextIds: [String!]

  """Must match industry textId."""
  industryTextId: String

  """Must match mm2 expertise textIds -- only used by synchronizer"""
  mm2SoughtExpertisesTextIds: [String!]

  """Must match mm2 industry textIds -- only used by synchronizer"""
  mm2IndustryTextId: String
  actionsTaken: String

  """From MM2, not used in MM3 (yet)"""
  currentChallenges: String

  """From MM2, not used in MM3 (yet)"""
  futureGoals: String

  """From MM2, not used in MM3 (yet)"""
  motivationsForMentorship: String
  reasonsForStartingBusiness: String
  howCanMentorSupportMe: String
}

input MentorsGroupMembershipInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  groupId: ID
  groupIdent: String
  userId: ID
  roles: [GroupMembershipRole!]

  """Must match expertise textIds."""
  expertisesTextIds: [String!]

  """Must match expertise textIds."""
  additionalExpertisesTextIds: [String!]

  """Must match industry textIds."""
  industriesTextIds: [String!]

  """Must match mm2 expertise textIds -- only used by synchronizer"""
  mm2ExpertisesTextIds: [String!]

  """Must match mm2 industry textIds -- only used by synchronizer"""
  mm2IndustriesTextIds: [String!]
  helpICanOffer: String
  expectationsForMentees: String
  menteePreparationInstructions: String
  endorsements: Int
  reasonsForMentoring: String
  howICanHelpMentees: String
}

input IqlaaGroupMembershipInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  groupId: ID
  groupIdent: String
  userId: ID
  roles: [GroupMembershipRole!]

  """Fathers name"""
  fatherName: String

  """Date of birth"""
  birthDate: DateTimeISO

  """
  "Is your business a home-based business?"
  """
  isBusinessHomeBased: Boolean

  """
  "Is the Business/ Project registered in the Companies Control Department -Ministry of industries and trading?"
  """
  isBusinessRegisteredWithCCD: Boolean

  """(Optional) Business registration number"""
  businessRegistrationNumber: String

  """Is the user a Jordan national?"""
  isJordanNational: Boolean
}

input MastercardGroupMembershipInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  groupId: ID
  groupIdent: String
  userId: ID
  roles: [GroupMembershipRole!]

  """Names of banks for reports"""
  bankNames: [String!]

  """Text IDs of banks for reports"""
  bankTextIds: [String!]

  """Small business card types"""
  smallBusinessCardTypes: [MastercardCardType!]

  """Personal card types"""
  personalCardTypes: [MastercardCardType!]
}

type SupportChannelConfig {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  groupId: ID!
  senderUserId: ID!

  """Is this welcome message active / should we send it to users?"""
  isActive: Boolean!
  createSupportChannelForMentees: Boolean!
  createSupportChannelForMentors: Boolean!

  """Any language option can be selected, not just a UiLanguage"""
  channelLanguageTextId: String
  filterByGenderTextIds: [String!]

  """The welcome message is a plain text field"""
  firstMessageText: String

  """
  Setting to false will ensure no notifications are sent. Setting to true will still check notification template settings.
  """
  sendNotifications: Boolean!
}

input SupportChannelConfigInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  groupId: ID
  groupIdent: String
  senderUserId: ID

  """Is this welcome message active / should we send it to users?"""
  isActive: Boolean
  createSupportChannelForMentees: Boolean
  createSupportChannelForMentors: Boolean

  """Any language option can be selected, not just a UiLanguage"""
  channelLanguageTextId: String
  filterByGenderTextIds: [String!]

  """The welcome message is a plain text"""
  firstMessageText: String

  """
  Setting to false will ensure no notifications are sent. Setting to true will still check notification template settings.
  """
  sendNotifications: Boolean
}

input NotificationInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  notificationType: NotificationType
  templateId: ID
  templateName: ID
  recipientId: ID
  multiStepActionId: ID
  initiatorId: ID
  replyingToId: ID
  title: String
  messageText: String
  shortMessageText: String
  htmlMessage: String
  language: UiLanguage
  isTranslated: Boolean
  appLink: String
  action0: AppAction
  action1: AppAction
  action2: AppAction
  actionTaken: AppAction
  sendEmail: Boolean
  sendInAppMessage: Boolean
  sendPushNotification: Boolean
  sendSms: Boolean
  allowSendingToSuspendedUser: Boolean
  emailSentAt: DateTimeISO
  inAppMessageSentAt: DateTimeISO
  inAppMessageReceivedAt: DateTimeISO
  pushNotificationSentAt: DateTimeISO
  smsSentAt: DateTimeISO
  emailSendReport: String
  pushNotificationSendReport: String
  smsSendReport: String
  sentMessagesCount: Int
  context: NotificationInput
}

input SendMultiStepActionNotificationInput {
  actionId: String! = ""
  notificationMethod: NotificationMethod
}

type NotificationTemplate {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  name: NotificationTemplateName!
  description: String!
  titleAr: String!
  messageTextAr: String!
  shortMessageTextAr: String!
  htmlMessageAr: String!
  titleEn: String!
  messageTextEn: String!
  shortMessageTextEn: String!
  htmlMessageEn: String!
  titleEs: String!
  messageTextEs: String!
  shortMessageTextEs: String!
  htmlMessageEs: String!
  titleId: String!
  messageTextId: String!
  shortMessageTextId: String!
  htmlMessageId: String!
  titleRu: String!
  messageTextRu: String!
  shortMessageTextRu: String!
  htmlMessageRu: String!
  titleSo: String!
  messageTextSo: String!
  shortMessageTextSo: String!
  htmlMessageSo: String!
  version: String!
  senderName: String
  senderEmail: String
  action0: AppAction
  action1: AppAction
  action2: AppAction
  sendEmail: Boolean!
  sendInAppMessage: Boolean!
  sendPushNotification: Boolean!
  sendSms: Boolean!
  isCore: Boolean!
}

enum NotificationTemplateName {
  accountDeletedConfirmation
  channelInvitationAcceptedForMentee
  channelInvitationAcceptedForMentor
  channelInvitationDeclinedForMentee
  channelInvitationDeclinedForMentor
  channelInvitationReceivedForMentee
  channelInvitationReceivedForMentor
  channelMessageReceivedForMentee
  channelMessageReceivedForMentor
  completeProfileForMentee
  completeProfileForMentor
  completeSignUpForMentee
  completeSignUpForMentor
  matchesRecommendationsForMentee
  matchesRecommendationsForMentor
  newPrivacyRules
  newsletter
  resetPasswordConfirmation
  resetPasswordConfirmToken
  sendFirstInvitationForMentee
  sendFirstInvitationForMentor
  unset
  welcomeForMentee
  welcomeForMentor
}

input NotificationTemplateInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  templateId: ID
  name: NotificationTemplateName
  description: String
  titleAr: String
  messageTextAr: String
  shortMessageTextAr: String
  htmlMessageAr: String
  titleEn: String
  messageTextEn: String
  shortMessageTextEn: String
  htmlMessageEn: String
  titleEs: String
  messageTextEs: String
  shortMessageTextEs: String
  htmlMessageEs: String
  titleId: String
  messageTextId: String
  shortMessageTextId: String
  htmlMessageId: String
  titleRu: String
  messageTextRu: String
  shortMessageTextRu: String
  htmlMessageRu: String
  titleSo: String
  messageTextSo: String
  shortMessageTextSo: String
  htmlMessageSo: String
  version: String
  senderName: String
  senderEmail: String
  action0: AppAction
  action1: AppAction
  action2: AppAction
  sendEmail: Boolean
  sendInAppMessage: Boolean
  sendPushNotification: Boolean
  sendSms: Boolean
  isCore: Boolean
}

type Mm2Synchronization {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  direction: Mm2SyncDirection!

  """If synchronizing a single object, set this to its ID."""
  objectId: String

  """If synchronizing a single object, set this to its model type."""
  mm2ModelType: Mm2ModelType

  """This is only useful for ChannelInvitation."""
  mm3ModelType: ModelType
  syncMode: Mm2SynchronizationMode!
  runMode: SyncRunMode
  skip: Int
  batchSize: Int

  """Number of objects to synchronize."""
  limit: Int

  """Set to true to run the synchronization right after creation."""
  autorun: Boolean!

  """
  Set to `info` or `error` to save Mm2SynchronizationResultItem objects to Mm2Synchronization.result. Using info on a large amount of items may expand the size of the MongoDB document beyond its limit, so only use it for local debugging. `none` disables saving any items to the synchronization object. Default: `error`
  """
  logLevel: Mm2SynchronizerLogLevel
  result: Mm2SynchronizationResult!
  duration: Int
  itemCount: Int
  pace: Int

  """Will only include objects with a newer then specified updatedAt date."""
  usersSinceUpdatedAt: String
  previousSyncAt: DateTimeISO
  startedAt: DateTimeISO
  finishedAt: DateTimeISO

  """
  Time this object will be deleted from the DB. Default = 10 days after creation.
  """
  expiresAt: DateTimeISO
}

enum Mm2SyncDirection {
  mm2ToMm3
  mm3ToMm2
}

enum Mm2ModelType {
  BlockedProfile
  Community
  Conversation
  Invitation
  Message
  Organization
  User
  ReportedMessage
  ReportedProfile
  MenteeExpertise
  MenteeWebsite
  MentorExpertise
  Profile
  SpokenLanguage
  TrainingContentPage
  TrainingPage
  TrainingSession
  TrainingPageCommunity
  TrainingPageCountry
  TrainingPageLanguage
  TrainingPageOrganization
  TrainingSessionCompletionInfo
  WagtailPage
}

enum Mm2SynchronizationMode {
  full
  incremental
  updated
  new
}

enum SyncRunMode {
  created
  running
  paused
  finished
}

enum Mm2SynchronizerLogLevel {
  info
  error
}

type Mm2SynchronizationResult {
  items: [Mm2SynchronizationResultItem!]
  createdCount: Int!
  deletedCount: Int!
  updatedCount: Int!
  skippedCount: Int!
  errorCount: Int!
  error: String
}

type Mm2SynchronizationResultItem {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  synchronizationId: ID!
  modelType: ModelType
  mm2ModelType: Mm2ModelType
  objectId: String!
  mm2ObjectId: String!
  name: String
  operation: SyncActionTaken!
  error: String
  duration: Int
}

enum SyncActionTaken {
  created
  updated
  deleted
  skipped
  unset
}

input Mm2SynchronizationInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  direction: Mm2SyncDirection! = mm2ToMm3
  objectId: String
  mm2ModelType: Mm2ModelType

  """This is only useful for ChannelInvitation."""
  mm3ModelType: ModelType
  syncMode: Mm2SynchronizationMode! = full
  runMode: SyncRunMode
  skip: Int
  batchSize: Int
  limit: Int
  autorun: Boolean! = true
  usersSinceUpdatedAt: String

  """
  Set to `info` or `error` to save Mm2SynchronizationResultItem objects to Mm2Synchronization.result. Using info on a large amount of items may expand the size of the MongoDB document beyond its limit, so only use it for local debugging. `none` disables saving any items to the synchronization object. Default: `error`
  """
  logLevel: Mm2SynchronizerLogLevel

  """
  Time this object will be deleted from the DB. Default = 10 days after creation.
  """
  expiresAt: DateTimeISO
}

input SidMultiStepActionInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  userId: ID
  userIdent: String
  userHandle: String
  email: String
  phoneNumber: String
  actionType: MultiStepActionType
  actionStatus: MultiStepActionStatus
  notificationMethod: NotificationMethod
  result: MultiStepActionResult
  confirmToken: String
  attemptCount: Int
  notificationSentAt: DateTimeISO
  notificationResult: MultiStepActionSendNotificationResult
  notificationId: String
  textData: String
  report: String
  emailPassed: Boolean
  emailUpdatedAt: DateTimeISO
  emailVerifiedAt: DateTimeISO
  errors: [MultiStepActionErrorInput!]
  password: String
  passwordPassed: Boolean
  passwordResettedAt: DateTimeISO
  passwordUpdatedAt: DateTimeISO
  phoneNumberPassed: Boolean
  phoneNumberUpdatedAt: DateTimeISO
  phoneNumberVerifiedAt: DateTimeISO
  signedInAt: DateTimeISO
  tfaBackupCodes: String
  expiresAt: DateTimeISO
}

input MultiStepActionErrorInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  key: String
  messageId: String
  message: String
}

input UserIdentInput {
  userId: String
  userIdent: String
  userHandle: String
  email: String
  phoneNumber: String
}

input VerifyMultiStepActionTokenInput {
  actionId: String! = ""
  token: String! = ""
  newPassword: String
}

type ContentStatus {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  optionsUpdatedAt: Long
  myUserUpdatedAt: Long
  myUserInboxUpdatedAt: Long
}

"""Long type for 64-bit integers"""
scalar Long

input MyUserInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  firstName: String
  lastName: String
  fullName: String
  userHandle: String
  phoneNumber: String
  phoneNumberUpdatedAt: DateTimeISO
  isPhoneNumberVerified: Boolean
  email: String
  emailUpdatedAt: DateTimeISO
  isEmailVerified: Boolean

  """The source of the email address, e.g. "google", "facebook", etc."""
  emailSource: String
  genderTextId: String
  cityOfResidence: String
  regionOfResidence: String
  countryOfResidenceTextId: String
  postalCode: String
  avatarUrl: String
  websites: [LabeledStringValueInput!]
  authType: AuthType
  currentPassword: String
  newPassword: String
  preferredLanguageTextId: String
  spokenLanguagesTextIds: [String!]
  selectedUiLanguageTextId: UiLanguage
  fallbackUiLanguageTextId: UiLanguage
  discoverable: Boolean
  roles: [UserRole!]
  appFeatures: [AppFeature!]
  source: String
  timezone: String
  preferences: UserPreferencesInput
  trustLevel: Int
  signedInAt: DateTimeISO
  signedOutAt: DateTimeISO
  latestActivityAt: DateTimeISO
  inactivatedAt: DateTimeISO
  inactivatedBy: ID
  termsAndConditionsAcceptedAt: DateTimeISO
  optIntoNewsletter: Boolean
  onboardingStage: String
  suspendedAt: DateTimeISO
  suspendedBy: ID
  syncedToAnalyticsAt: DateTimeISO
  companyIds: [ID!]

  """Used internally, will not work in GraphQL queries."""
  companies: [CompanyInput!]
  groupIds: [ID!]
  parentGroupIds: [ID!]
  externalGroupIds: [ID!]
  pronounsTextIds: [String!]
  groupMemberships: [GroupMembershipInput!]
  addToGroupIds: [String!]
  removeFromGroupIds: [String!]
  seeksHelp: Boolean
  offersHelp: Boolean
  birthYear: Int
  ethnicity: String
  educationLevelTextId: String
  personalBio: String
  yearsManagementExperience: Int
  yearsOwnershipExperience: Int
  academicExperienceIds: [ID!]

  """Specify a company you want to create and add the user to."""
  company: CompanyInput

  """
  Specify a list of academic experiences you want to create for the user.
  """
  academicExperiences: [AcademicExperienceInput!]
  businessExperienceIds: [ID!]

  """
  Specify a list of business experiences you want to create for the user.
  """
  businessExperiences: [BusinessExperienceInput!]
  cityOfOrigin: String
  regionOfOrigin: String

  """Users Country of origin. Use a Country Options textId."""
  countryOfOriginTextId: String
  isOnVacation: Boolean
  profileRoleHistory: [UserProfileRoleHistoryItemInput!]
  ssoIdp: String
}

input UserTrackingInput {
  id: ID
  adminNotes: String
  events: [ModelEventInput!]
  metadata: BaseModelMetadataInput
  createdAt: DateTimeISO
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
  userId: ID
  trackId: String
  metaPixelId: String

  """fbc cookie (FBCLID)"""
  metaClickId: String

  """fbp cookie"""
  metaBrowserId: String

  """This is the Google Analytics tracking ID"""
  googleId: String
  googleClickId: String
  cookieConsentChoice: CookieChoiceTextId
  allowToTrack: Boolean
  syncedToAnalyticsAt: DateTimeISO
}

type Subscription {
  channelChanged(channelId: ID! = ""): BgChannelChangedEvent!
  objectChanged(objectId: ID! = "", modelType: ModelType = UserInbox, ownerUserId: ID): ObjectChangedEvent!
}

type BgChannelChangedEvent {
  serviceRequest: ServiceRequest!
  channelId: ID
  invitationId: ID
  messageId: ID
  participantId: ID
  eventType: ChannelChangedEventType!
  requestId: ID
}

enum ChannelChangedEventType {
  channelDeleted
  channelUpdated
  invitationAccepted
  invitationCreated
  invitationDeclined
  invitationDeleted
  invitationUpdated
  messageCreated
  messageDeleted
  messageStatusChanged
  messageUpdated
  participantCreated
  participantDeleted
  participantUpdated
}

type ObjectChangedEvent {
  serviceRequest: ServiceRequest!
  objectId: ID!
  modelType: ModelType!
  ownerUserId: ID
  messageType: ObjectChangedEventType!
  requestId: String
  object: BaseModel
}

enum ObjectChangedEventType {
  created
  updated
  deleted
  anonymized
}

type BaseModel {
  id: ID!
  adminNotes: String
  events: [ModelEvent!]
  metadata: BaseModelMetadata
  createdAt: DateTimeISO!
  createdBy: ID
  updatedAt: DateTimeISO
  updatedBy: ID
  deletedAt: DateTimeISO
  deletedBy: ID
}