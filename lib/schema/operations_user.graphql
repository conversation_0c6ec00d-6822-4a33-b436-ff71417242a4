# ----------------------------------------------------------------------------------------------------------------
# Queries

query DoesUserExist($identType: UserIdentType!, $ident: String!) {
  doesUserExist(identType: $identType, ident: $ident)
}

query IsUserIdentAvailable($identType: UserIdentType!, $ident: String!) {
  isUserIdentAvailable(identType: $identType, ident: $ident)
}

query FindUsers($filter: UserListFilter) {
  findUsers(filter: $filter) {
    id
    firstName
    lastName
  }
}

query FindUserSearch($userSearchId: String!) {
  findUserSearchById(userSearchId: $userSearchId) {
    runInfos {
      finishedAt
      matchCount
    }
  }
}

query FindUserSearchResults(
  $userSearchId: String!
  $options: FindObjectsOptions
) {
  findUserSearchResults(userSearchId: $userSearchId, options: $options) {
    id
    userHandle
    isOnVacation
    firstName
    lastName
    avatarUrl
    offersHelp
    seeksHelp
    profileRole
    cityOfResidence
    regionOfResidence
    countryOfResidence {
      translatedValue
    }
    groups {
      name
      ident
      id
      badgeName
    }
    groupMemberships {
      id
      userId
      groupId
      groupIdent
      roles
      ... on MenteesGroupMembership {
        reasonsForStartingBusiness
        howCanMentorSupportMe
        additionalSoughtExpertisesTextIds
        actionsTaken
        currentChallenges
        futureGoals
        motivationsForMentorship
        industryTextId
        industry {
          id
          textId
          parentTextId
          isParent
          value
          translatedValue
        }
        soughtExpertisesTextIds
        soughtExpertises {
          id
          textId
          parentTextId
          isParent
          value
          translatedValue
        }
      }
      ... on MentorsGroupMembership {
        helpICanOffer
        expectationsForMentees
        menteePreparationInstructions
        endorsements
        reasonsForMentoring
        howICanHelpMentees
        expertisesTextIds
        expertises {
          id
          textId
          parentTextId
          isParent
          value
          translatedValue
        }
        additionalExpertisesTextIds
        industriesTextIds
        industries {
          id
          textId
          parentTextId
          isParent
          value
          translatedValue
        }
      }
    }
    businessExperiences {
      businessName
      jobTitle
    }
    companies {
      id
      userIds
      name
      description
      location
      companyTypeTextId
      companyType {
        textId
        translatedValue
      }
      companyStageTextId
      companyStage {
        textId
        translatedValue
      }
      websites {
        value
        label
        tags
      }
      industries
      isOperational
      isFundraising
      annualRevenue
      employeeCount
      foundedAt
      createdAt
      updatedAt
      # we may need to add city/state/country to this model for the mentee
    }
  }
}

query FindUserById($userId: String!) {
  findUserById(id: $userId) {
    # Account Information:
    id
    userHandle
    avatarUrl
    roles
    onboardingStage
    profileCompletionPercentage
    userBlocks {
      userId
      reasonTextId
      notes
      createdAt
    }
    optIntoNewsletter
    isOnVacation
    trustLevel
    updatedAt
    createdAt

    # Personal Information:
    firstName
    lastName
    genderTextId
    gender {
      textId
      translatedValue
    }
    pronounsDisplay # this handles some display logic, "she/they" for a user with both "they/them" and "she/her" pronouns
    pronounsTextIds
    pronouns {
      textId
      translatedValue
    }
    birthYear
    preferredLanguageTextId
    preferredLanguage {
      textId
      translatedValue
    }
    selectedUiLanguageTextId
    fallbackUiLanguageTextId
    spokenLanguages {
      textId
      translatedValue
    }
    groups {
      name
      ident
      id
      isMigratedToMm3
      mm2RedirectUrl
      badgeName
    }

    # Preferences:
    preferences {
        notificationOptions {
        enableEmail
        enablePushNotification
        notificationType
      }
    }

    # Contact Information:
    email
    phoneNumber

    # Location Information:
    cityOfResidence
    regionOfResidence
    countryOfResidenceTextId
    countryOfResidence {
      textId
      translatedValue
    }
    cityOfOrigin
    regionOfOrigin
    countryOfOriginTextId
    countryOfOrigin {
      textId
      translatedValue
    }

    # Mentee/Mentor Information:
    offersHelp
    seeksHelp
    profileRole
    groupMemberships {
      id
      userId
      groupId
      groupIdent
      roles
      ... on MenteesGroupMembership {
        reasonsForStartingBusiness
        howCanMentorSupportMe
        additionalSoughtExpertisesTextIds
        actionsTaken
        currentChallenges
        futureGoals
        motivationsForMentorship
        industryTextId
        industry {
          id
          textId
          parentTextId
          isParent
          value
          translatedValue
        }
        soughtExpertisesTextIds
        soughtExpertises {
          id
          textId
          parentTextId
          isParent
          value
          translatedValue
        }
      }
      ... on MentorsGroupMembership {
        helpICanOffer
        expectationsForMentees
        menteePreparationInstructions
        endorsements
        reasonsForMentoring
        howICanHelpMentees
        expertisesTextIds
        expertises {
          id
          textId
          parentTextId
          isParent
          value
          translatedValue
        }
        additionalExpertisesTextIds
        industriesTextIds
        industries {
          id
          textId
          parentTextId
          isParent
          value
          translatedValue
        }
      }
    }
    groupMembers {
      id
      userId
      groupId
      groupIdent
      roles
      ... on IqlaaGroupMembership {
        fatherName
        birthDate
        isBusinessHomeBased
        isBusinessRegisteredWithCCD
        businessRegistrationNumber
        isJordanNational
      }
      ... on MastercardGroupMembership {
        bankTextIds
        personalCardTypes
        smallBusinessCardTypes
      }
    }
    websites {
      value
      label # label == "linkedin" for the linkedin button
      tags
    }
    companies {
      id
      userIds
      name
      description
      location
      companyTypeTextId
      companyType {
        textId
        translatedValue
      }
      companyStageTextId
      companyStage {
        textId
        translatedValue
      }
      websites {
        value
        label
        tags
      }
      industries
      isOperational
      isFundraising
      annualRevenue
      employeeCount
      foundedAt
      createdAt
      updatedAt
      # we may need to add city/state/country to this model for the mentee
    }
    businessExperiences {
      id
      businessName
      jobTitle
      startDate
      endDate
      city
      state
      country
    }
    academicExperiences {
      id
      institutionName
      degreeType
      fieldOfStudy
      startDate
      endDate
    }

    # mm2 info
    originatedInMm2
    hasSignedInToMm3
    # hasSignedInToMm2

    hasTrainings
  }
}

query GetMyBlockedUsers {
  getMyBlockedUsers {
    id
    firstName
    lastName
    avatarUrl
  }
}

# Add this query to check if a user will receive a welcome message
query UserWillReceiveWelcomeMessage($userId: String!) {
  userWillReceiveWelcomeMessage(userId: $userId)
}

query FindUserCmsByUserId($userId: String!) {
  findUserCmsByUserId(userId: $userId) {
    userId
    groupCms {
      id
      groupId
      createdAt
      createdBy
      updatedAt
      updatedBy
      deletedAt
      deletedBy
      onboarding {
        allowProfileRoleOnSignUp
        showDataConsentPage
        showPreferredLanguagePage
        showLocationPage
        showPhoneNumberPage
        showGenderPage
        showBirthYearPage
        showProfileRolePage
        showExpertisesPage
        showIndustryPage
        showVentureNamePage
        showVentureStartDatePage
        showVentureStagePage
        showReasonToJoinPage
        showMentorRolePage
        showAcceptTermsPage
        nextRoute
      }
    }
  }
}


# ----------------------------------------------------------------------------------------------------------------
# Mutations

mutation AddUserToGroup(
  $addUserToGroupUserId: String!,
  $groupIdent: String,
  $roles: [GroupMembershipRole!]!
) {
  addUserToGroup(userId: $addUserToGroupUserId, groupIdent: $groupIdent, roles: $roles) {
    finishedAt
    id
  }
}

mutation BlockUserForMe(
  $userId: String!
  $notes: String
  $reasonTextId: String
) {
  blockUserForMe(userId: $userId, notes: $notes, reasonTextId: $reasonTextId)
}

mutation CreateOneTimeAuthTokenForMe(){
  createOneTimeAuthTokenForMe
}

mutation CreateUserSearch($input: UserSearchInput!) {
  createUserSearch(input: $input) {
    id
  }
}

mutation EndMySession($deviceUuid: String!) {
  endMySession(deviceUuid: $deviceUuid)
}

mutation ReportUser($input: ReportUserInput!) {
  reportUser(input: $input)
}

mutation SignInOauthUser($input: SignInOauthUserInput!) {
  signInOauthUser(input: $input) {
    userId
    firstName
    lastName
    authToken
    foundUser
    onboardingStage
  }
}

mutation SignInUser($input: SignInUserInput!) {
  signInUser(input: $input) {
    userId
    authToken
  }
}

mutation SignOutUser {
  signMeOut
}

mutation DeleteUser($deletePhysically: Boolean!, $userId: String!) {
  deleteUser(deletePhysically: $deletePhysically, userId: $userId)
}

mutation RemoveUserFromGroup($force: Boolean!, $userId: String!, $groupIdent: String) {
  removeUserFromGroup(force: $force, userId: $userId, groupIdent: $groupIdent)
}
mutation SignUpUser($input: SignUpUserInput!) {
  signUpUser(input: $input) {
    userId
    firstName
    lastName
    authToken
  }
}

mutation StartMySession($deviceUuid: String!, $pushNotificationToken: String) {
  startMySession(
    deviceUuid: $deviceUuid
    pushNotificationToken: $pushNotificationToken
  )
}

mutation UnblockUserForMe($userId: String!) {
  unblockUserForMe(userId: $userId)
}

mutation UpdateAcademicExperience($input: AcademicExperienceInput!) {
  updateAcademicExperience(input: $input) {
    id
  }
}

mutation UpdateBusinessExperience($input: BusinessExperienceInput!) {
  updateBusinessExperience(input: $input) {
    id
  }
}

mutation UpdateCompany($input: CompanyInput!) {
  updateCompany(input: $input) {
    id
  }
}

mutation UpdateIqlaaGroupMembership($input: IqlaaGroupMembershipInput!) {
  updateIqlaaGroupMembership(input: $input) {
    id
    createdAt
    updatedAt
    finishedAt
    expiresAt
  }
}

mutation UpdateMastercardGroupMembership($input: MastercardGroupMembershipInput!) {
  updateMastercardGroupMembership(input: $input) {
    id
  }
}

mutation UpdateMenteesGroupMembership($input: MenteesGroupMembershipInput!) {
  updateMenteesGroupMembership(input: $input) {
    id
  }
}

mutation UpdateMentorsGroupMembership($input: MentorsGroupMembershipInput!) {
  updateMentorsGroupMembership(input: $input) {
    id
  }
}

mutation UpdateUser($input: UserInput!) {
  updateUser(input: $input)
}

