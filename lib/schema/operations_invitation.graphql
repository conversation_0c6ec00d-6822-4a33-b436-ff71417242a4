query FindChannelInvitationById($channelInvitationId: String!) {
    findChannelInvitationById(id: $channelInvitationId) {
        createdAt
        messageText
        status
        readByRecipientAt
        sender {
            id
            firstName
            lastName
            avatarUrl
            offersHelp
            seeksHelp
            companies {
                id
                name
                companyStage {
                    translatedValue
                }
                companyType {
                    translatedValue
                }
            }
            countryOfResidence {
                translatedValue
            }
            groupMemberships {
                groupIdent
                ... on MentorsGroupMembership {
                    endorsements
                    industries {
                        translatedValue
                    }
                    expertises {
                        translatedValue
                    }
                }
                ... on MenteesGroupMembership {
                    industry {
                        translatedValue
                    }
                    soughtExpertises {
                        translatedValue
                    }
                }
            }
            seeksHelp
            businessExperiences {
                businessName
                jobTitle
            }
        }
        recipient {
            id
            firstName
            lastName
            avatarUrl
            offersHelp
            seeksHelp
            companies {
                id
                name
                companyStage {
                    translatedValue
                }
                companyType {
                    translatedValue
                }
            }
            countryOfResidence {
                translatedValue
            }
            groupMemberships {
                groupIdent
                ... on MentorsGroupMembership {
                    endorsements
                    industries {
                        translatedValue
                    }
                    expertises {
                        translatedValue
                    }
                }
                ... on MenteesGroupMembership {
                    industry {
                        translatedValue
                    }
                    soughtExpertises {
                        translatedValue
                    }
                }
            }
            seeksHelp
            businessExperiences {
                businessName
                jobTitle
            }
            hasSignedInToMm3
        }
    }
}

query MyChannelInvitations($direction: ChannelInvitationDirection, $onlyPending: Boolean, $onlyUnseen: Boolean, $options: FindObjectsOptions) {
    myChannelInvitations(direction: $direction, onlyPending: $onlyPending, onlyUnseen: $onlyUnseen, options: $options) {
        channelId
        createdAt
        id
        messageText
        status
        readByRecipientAt
        sender {
            avatarUrl
            firstName
            lastName
            companies {
                name
            }
            seeksHelp
            businessExperiences {
                businessName
                jobTitle
            }
            id
        }
        recipient {
            avatarUrl
            firstName
            lastName
            companies {
                name
            }
            seeksHelp
            businessExperiences {
                businessName
                jobTitle
            }
            id
            hasSignedInToMm3
        }
    }
}

mutation AcceptChannelInvitation($channelInvitationId: String!) {
    acceptChannelInvitation(channelInvitationId: $channelInvitationId)
}

mutation CreateChannelInvitation($channelInvitationInput: ChannelInvitationInput!) {
    createChannelInvitation(input: $channelInvitationInput) {
        id
    }
}

mutation DeclineChannelInvitation(
    $channelInvitationId: String!,
    $reasonTextId: DeclineChannelInvitationReasonTextId!
) {
    declineChannelInvitation(
        channelInvitationId: $channelInvitationId,
        reasonTextId: $reasonTextId,
    )
}

mutation DeleteChannelInvitation($deletePhysically: Boolean!, $channelInvitationId: String!) {
    deleteChannelInvitation(deletePhysically: $deletePhysically, channelInvitationId: $channelInvitationId)
}

mutation MarkChannelInvitationAsSeenByMe($channelInvitationInput: ChannelInvitationInput!) {
    updateChannelInvitation(input: $channelInvitationInput)
}
