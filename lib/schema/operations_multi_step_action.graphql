# ----------------------------------------------------------------------------------------------------------------
# Queries

query GetMultiStepActionProgress($actionId: String!, $confirmToken: String) {
  getMultiStepActionProgress(actionId: $actionId, confirmToken: $confirmToken) {
    actionId
    userId
    actionType
    actionStatus
    notificationMethod
    result
    attemptCount
    notificationSentAt
    notificationResult
    notificationId
    textData
    report
    emailPassed
    emailUpdatedAt
    emailVerifiedAt
    authToken
    authTokenExpiresAt
    passwordPassed
    passwordResettedAt
    passwordUpdatedAt
    phoneNumberPassed
    phoneNumberUpdatedAt
    phoneNumberVerifiedAt
    signedInAt
    expiresAt
    createdAt
    createdBy
    updatedAt
    updatedBy
    deletedAt
    deletedBy
    events {
      time
      modelEventType
      message
    }
    errors {
      key
      messageId
      message
      createdAt
    }
  }
}

# ----------------------------------------------------------------------------------------------------------------
# Mutations

mutation StartResetPassword($input: UserIdentInput!) {
  startResetPassword(input: $input) {
    actionId
    authToken
    errors {
      key
      messageId
      message
      createdAt
    }
  }
}

mutation VerifyMultiStepActionToken($input: VerifyMultiStepActionTokenInput!) {
  verifyMultiStepActionToken(input: $input) {
    actionId
    userId
    actionType
    actionStatus
    notificationMethod
    result
    attemptCount
    notificationSentAt
    notificationResult
    notificationId
    textData
    report
    emailPassed
    emailUpdatedAt
    emailVerifiedAt
    authToken
    authTokenExpiresAt
    passwordPassed
    passwordResettedAt
    passwordUpdatedAt
    phoneNumberPassed
    phoneNumberUpdatedAt
    phoneNumberVerifiedAt
    signedInAt
    expiresAt
    createdAt
    updatedAt
    events {
      time
      modelEventType
      message
    }
    errors {
      key
      messageId
      message
    }
  }
}
