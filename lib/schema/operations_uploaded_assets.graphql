query FindUploadedAssetsForUser($userId: String!, $options: FindObjectsOptions) {
  findUploadedAssetsForUser(userId: $userId, options: $options) {
    id
    adminNotes
    createdAt
    createdBy
    updatedAt
    updatedBy
    deletedAt
    deletedBy
    ownerId
    ownerModelType
    assetType
    hostingService
    url
    path
    s3Bucket
    s3Key
    mimeType
    uploadUrl
    uploadUrlExpiresAt
    uploadedAt
    expiresAt
  }
}

mutation CreateUploadedAsset($input: UploadedAssetInput!) {
  createUploadedAsset(input: $input) {
    id
    adminNotes
    createdAt
    createdBy
    updatedAt
    updatedBy
    deletedAt
    deletedBy
    ownerId
    ownerModelType
    assetType
    hostingService
    url
    path
    s3Bucket
    s3Key
    mimeType
    uploadUrl
    uploadUrlExpiresAt
    uploadedAt
    expiresAt
  }
}

mutation InitAssetUpload ($input: UploadedAssetInput!) {
  initAssetUpload(input: $input) {
    id
    adminNotes
    createdAt
    createdBy
    updatedAt
    updatedBy
    deletedAt
    deletedBy
    ownerId
    ownerModelType
    assetType
    hostingService
    url
    path
    s3Bucket
    s3Key
    mimeType
    uploadUrl
    uploadUrlExpiresAt
    uploadedAt
    expiresAt
  }
}

mutation UpdateUploadedAsset ($input: UploadedAssetInput!) {
  updateUploadedAsset(input: $input)
}