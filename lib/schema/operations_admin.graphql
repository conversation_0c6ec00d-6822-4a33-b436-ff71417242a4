# ----------------------------------------------------------------------------------------------------------------
# Queries

query FindAdminTaskById($adminTaskId: String!) {
  findAdminTaskById(adminTaskId: $adminTaskId) {
    id
    adminNotes
    createdAt
    createdBy
    updatedAt
    updatedBy
    deletedAt
    deletedBy
    adminTaskType
    result
    resultMessage
    error
    args
    timeout
    autoRun
    synchronous
    startedAt
    expiresAt
    finishedAt
  }
}

query FindAdminTaskDefs {
  findAdminTaskDefs {
    adminTaskType
    label
    description
    timeout
    args {
      name
      label
      dataType
      choices
      optional
      description
    }
  }
}


# ----------------------------------------------------------------------------------------------------------------
# Mutations

mutation CreateAdminTask($adminTaskInput: AdminTaskInput!) {
  createAdminTask(adminTaskInput: $adminTaskInput) {
    id
    adminNotes
    createdAt
    createdBy
    updatedAt
    updatedBy
    deletedAt
    deletedBy
    adminTaskType
    result
    resultMessage
    error
    args
    timeout
    autoRun
    synchronous
    startedAt
    expiresAt
    finishedAt
  }
}
