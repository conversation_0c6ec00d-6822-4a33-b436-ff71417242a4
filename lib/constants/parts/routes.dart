part of '../constants.dart';

class AppRoute {
  final String name;
  final String path;

  AppRoute({required this.name, required this.path});
}

class AppRoutes {
  AppRoutes._private();
  static AppRoute root = AppRoute(name: 'root', path: '/');

  // Admin Routes:
  static AppRoute admin = AppRoute(name: 'admin', path: '/admin');
  static AppRoute adminTask = AppRoute(name: 'adminTask', path: '${admin.path}/adminTask');
  static AppRoute impersonate = AppRoute(
    name: 'impersonate',
    path: '${admin.path}/impersonate/:${RouteParams.userId}',
  );

  // Welcome Routes:
  static AppRoute welcome = AppRoute(name: 'welcome', path: '/welcome');

  // Data use consent:
  static AppRoute dataUseConsent = AppRoute(name: 'dataUseConsent', path: '/dataUseConsent');

  // Account Settings Routes:
  static AppRoute accountSettings = AppRoute(name: 'accountSettings', path: '/accountSettings');
  static AppRoute accountSettingAppLanguage = AppRoute(
    name: 'accountSettingAppLanguage',
    path: '${accountSettings.path}/appLanguage',
  );
  static AppRoute accountSettingBirthYear = AppRoute(
    name: 'accountSettingBirthYear',
    path: '${accountSettings.path}/birtYear',
  );
  static AppRoute accountSettingEmail = AppRoute(
    name: 'accountSettingEmail',
    path: '${accountSettings.path}/email',
  );
  static AppRoute accountSettingGender = AppRoute(
    name: 'accountSettingGender',
    path: '${accountSettings.path}/gender',
  );
  static AppRoute accountSettingName = AppRoute(
    name: 'accountSettingName',
    path: '${accountSettings.path}/name',
  );
  static AppRoute accountSettingPassword = AppRoute(
    name: 'accountSettingPassword',
    path: '${accountSettings.path}/password',
  );
  static AppRoute accountSettingPhone = AppRoute(
    name: 'accountSettingPhone',
    path: '${accountSettings.path}/phone',
  );
  static AppRoute accountSettingRole = AppRoute(
    name: 'accountSettingRole',
    path: '${accountSettings.path}/role',
  );
  static AppRoute accountSettingViewBlockedUsers = AppRoute(
    name: 'accountSettingViewBlockedUsers',
    path: '${accountSettings.path}/viewBlockedUsers',
  );
  static AppRoute accountSettingViewNotification = AppRoute(
    name: 'accountSettingViewNotification',
    path: '${accountSettings.path}/viewNotification',
  );

  // Authentication/Onboarding Routes:
  static AppRoute signin = AppRoute(name: 'signin', path: '/signin');
  static AppRoute signup = AppRoute(name: 'signup', path: '/signup');
  static AppRoute resetpassword = AppRoute(name: 'resetPassword', path: '/resetPassword');
  static AppRoute signupCreateAccount = AppRoute(
    name: 'signupCreateAccount',
    path: '${signup.path}/signupCreateAccount',
  );
  static AppRoute signupNotifications = AppRoute(
    name: 'signupNotifications',
    path: '${signup.path}/notifications',
  );
  static AppRoute signupPhoneNumber = AppRoute(
    name: 'signupPhoneNumber',
    path: '${signup.path}/phoneNumber',
  );
  static AppRoute signupBirthYear = AppRoute(
    name: 'signupYearOfBirth',
    path: '${signup.path}/birthYear',
  );
  static AppRoute signupGender = AppRoute(name: 'signupGender', path: '${signup.path}/gender');
  static AppRoute signupLocation = AppRoute(
    name: 'signupLocation',
    path: '${signup.path}/location',
  );
  static AppRoute signupLanguage = AppRoute(
    name: 'signupLanguage',
    path: '${signup.path}/language',
  );
  static AppRoute signUpProfileRole = AppRoute(
    name: 'signUpProfileRole',
    path: '${signup.path}/profileRole',
  );
  static AppRoute signupVentureStage = AppRoute(
    name: 'signupVentureStage',
    path: '${signup.path}/signupVentureStage',
  );
  static AppRoute signupExpertises = AppRoute(
    name: 'signupExpertises',
    path: '${signup.path}/expertises',
  );
  static AppRoute signupVentureName = AppRoute(
    name: 'signupVentureName',
    path: '${signup.path}/signupVentureName',
  );
  static AppRoute signupVentureYear = AppRoute(
    name: 'signupVentureYear',
    path: '${signup.path}/signupVentureYear',
  );
  static AppRoute signupReasonToJoin = AppRoute(
    name: 'signupReasonToJoin',
    path: '${signup.path}/reasonToJoin',
  );
  static AppRoute signupAcceptTerms = AppRoute(
    name: 'signupAcceptTerms',
    path: '${signup.path}/acceptTerms',
  );
  static AppRoute signupMentorInfo = AppRoute(
    name: 'signupMentorInfo',
    path: '${signup.path}/mentorRole',
  );
  static AppRoute resetPasswordEnterCode = AppRoute(
    name: 'resetPasswordEnterCode',
    path: '${resetpassword.path}/resetPasswordEnterCode',
  );
  static AppRoute resetPasswordEnterNewPassword = AppRoute(
    name: 'resetPasswordEnterNewPassword',
    path: '${resetpassword.path}/resetPasswordEnterNewPassword',
  );
  static AppRoute resetPasswordCompleted = AppRoute(
    name: 'resetPasswordCompleted',
    path: '${resetpassword.path}/resetPasswordCompleted',
  );
  static AppRoute loading = AppRoute(name: 'loading', path: '/loading');
  static AppRoute home = AppRoute(name: 'home', path: '/home');
  static AppRoute explore = AppRoute(name: 'explore', path: '/explore');
  static AppRoute exploreFilters = AppRoute(
    name: 'exploreFilters',
    path: '${explore.path}/filters',
  );
  static AppRoute inbox = AppRoute(name: 'inbox', path: '/inbox');
  static AppRoute inboxChats = AppRoute(name: 'inboxChats', path: '${inbox.path}/chats');
  static AppRoute newInviteReceivedWeb = AppRoute(
    name: 'newInviteReceivedWeb',
    path: '${inbox.path}/web/invites/received',
  );
  static AppRoute inviteSentWeb = AppRoute(
    name: 'inviteSentWeb',
    path: '${inbox.path}/web/invites/sent',
  );
  static AppRoute inboxChatsChannelId = AppRoute(
    name: 'inboxChatsChannelId',
    path: '${inboxChats.path}/:${RouteParams.channelId}',
  );
  static AppRoute inboxInvites = AppRoute(name: 'inboxInvites', path: '${inbox.path}/invites');
  static AppRoute inboxInvitesReceived = AppRoute(
    name: 'inboxInvitesReceived',
    path: '${inboxInvites.path}/received',
  );
  static AppRoute inboxInvitesReceivedId = AppRoute(
    name: 'inboxInvitesReceivedId',
    path: '${inboxInvitesReceived.path}/:${RouteParams.channelInvitationId}',
  );
  static AppRoute inboxAcceptInvitation = AppRoute(
    name: 'inboxAcceptInvitation',
    path: '${inboxInvitesReceivedId.path}/accept',
  );
  static AppRoute inboxInvitesSent = AppRoute(
    name: 'inboxInvitesSent',
    path: '${inboxInvites.path}/sent',
  );
  static AppRoute inboxInvitesSentId = AppRoute(
    name: 'inboxInvitesSentId',
    path: '${inboxInvitesSent.path}/:${RouteParams.channelInvitationId}',
  );
  static AppRoute inboxArchived = AppRoute(name: 'inboxArchived', path: '${inbox.path}/archived');
  static AppRoute inboxArchivedChannelId = AppRoute(
    name: 'inboxArchivedChannelId',
    path: '${inboxArchived.path}/:${RouteParams.channelId}',
  );
  static AppRoute profile = AppRoute(name: 'profile', path: '/profile');
  static AppRoute profileId = AppRoute(
    name: 'profileId',
    path: '${profile.path}/:${RouteParams.userId}',
  );
  static AppRoute profileInvite = AppRoute(name: 'profileInvite', path: '${profile.path}/invite');
  static AppRoute profileInviteId = AppRoute(
    name: 'profileInviteId',
    path: '${profileInvite.path}/:${RouteParams.userId}',
  );
  static AppRoute profileEdit = AppRoute(name: 'profileEdit', path: '${profile.path}/edit');
  static AppRoute profileEditPronouns = AppRoute(
    name: 'profileEditPronouns',
    path: '${profileEdit.path}/pronouns',
  );
  static AppRoute profileEditLinkedin = AppRoute(
    name: 'profileEditLinkedin',
    path: '${profileEdit.path}/linkedin',
  );
  static AppRoute profileEditCurrentLocation = AppRoute(
    name: 'profileEditCity',
    path: '${profileEdit.path}/city',
  );
  static AppRoute profileEditOriginLocation = AppRoute(
    name: 'profileEditOriginLocation',
    path: '${profileEdit.path}/originLocation',
  );
  static AppRoute profileEditLanguagePreferred = AppRoute(
    name: 'profileEditLanguagePreferred',
    path: '${profileEdit.path}/preferredLanguage',
  );
  static AppRoute profileEditLanguageOthers = AppRoute(
    name: 'profileEditLanguageOthers',
    path: '${profileEdit.path}/otherLanguages',
  );
  static AppRoute profileEditEducation = AppRoute(
    name: 'profileEditEducation',
    path: '${profileEdit.path}/education',
  );
  static AppRoute profileEditEducationIndex = AppRoute(
    name: 'profileEditEducationIndex',
    path: '${profileEdit.path}/education/:${RouteParams.experienceIndex}',
  );
  static AppRoute profileEditEducationNew = AppRoute(
    name: 'profileEditEducationNew',
    path: '${profileEdit.path}/newEducation',
  );
  static AppRoute profileEditExperience = AppRoute(
    name: 'profileEditExperience',
    path: '${profileEdit.path}/experience',
  );
  static AppRoute profileEditExperienceIndex = AppRoute(
    name: 'profileEditExperienceIndex',
    path: '${profileEdit.path}/experience/:${RouteParams.experienceIndex}',
  );
  static AppRoute profileEditExperienceNew = AppRoute(
    name: 'profileEditExperienceNew',
    path: '${profileEdit.path}/newExperience',
  );
  static AppRoute profileEditExpertisesTop = AppRoute(
    name: 'profileEditExpertisesTop',
    path: '${profileEdit.path}/topExpertises',
  );
  static AppRoute profileEditExpertisesAdditional = AppRoute(
    name: 'profileEditExpertisesAdditional',
    path: '${profileEdit.path}/additionalExpertises',
  );
  static AppRoute profileEditIndustries = AppRoute(
    name: 'profileEditIndustries',
    path: '${profileEdit.path}/industries',
  );
  static AppRoute profileEditMentoringPreferences = AppRoute(
    name: 'profileEditMentoringPreferences',
    path: '${profileEdit.path}/mentoringPreferences',
  );
  static AppRoute profileEditReasonsForMentoring = AppRoute(
    name: 'profileEditReasonsForMentoring',
    path: '${profileEdit.path}/reasonsForMentoring',
  );
  static AppRoute profileEditHowICanHelpMentees = AppRoute(
    name: 'profileEditHowICanHelpMentees',
    path: '${profileEdit.path}/howICanHelpMentees',
  );
  static AppRoute profileEditCompanyStage = AppRoute(
    name: 'profileEditCompanyStage',
    path: '${profileEdit.path}/companyStage',
  );
  static AppRoute profileEditCompanyName = AppRoute(
    name: 'profileEditCompanyName',
    path: '${profileEdit.path}/companyName',
  );
  static AppRoute profileEditCompanyWebsite = AppRoute(
    name: 'profileEditCompanyWebsite',
    path: '${profileEdit.path}/companyWebsite',
  );
  static AppRoute profileEditCompanyLocation = AppRoute(
    name: 'profileEditCompanyLocation',
    path: '${profileEdit.path}/companyLocation',
  );
  static AppRoute profileEditCompanyMission = AppRoute(
    name: 'profileEditCompanyMission',
    path: '${profileEdit.path}/companyMission',
  );
  static AppRoute profileEditCompanyReason = AppRoute(
    name: 'profileEditCompanyReason',
    path: '${profileEdit.path}/companyReason',
  );
  static AppRoute profileEditBusinessChallenge = AppRoute(
    name: 'profileEditBusinessChallenge',
    path: '${profileEdit.path}/businessChalleng',
  );
  static AppRoute profileEditHowCanMentorSupportMe = AppRoute(
    name: 'profileEditHowCanMentorSupportMe',
    path: '${profileEdit.path}/howCanMentorSupportMe',
  );
  static AppRoute trainings = AppRoute(name: 'trainings', path: '/trainings');

  static AppRoute trainingSeries = AppRoute(
    name: 'trainingSeries',
    path: '/trainingSeries/:${RouteParams.trainingSeriesId}',
  );

  static AppRoute individualTraining = AppRoute(
    name: 'individualTraining',
    path: '/trainings/:${RouteParams.individualTrainingId}',
  );

  static AppRoute webView = AppRoute(
    name: 'webView',
    path: '/webView/:${RouteParams.webviewTitle}/:${RouteParams.targetUrl}',
  );

  static AppRoute iqlaaSignup = AppRoute(name: 'iqlaaSignup', path: '${signup.path}/iqlaa');

  static AppRoute iqlaaConsentPage = AppRoute(
    name: 'iqlaaConsentPage',
    path: '${signup.path}/iqlaaConsentPage',
  );

  static AppRoute iqlaaEmployeeNumber = AppRoute(
    name: 'iqlaaEmployeeNumber',
    path: '${signup.path}/iqlaaEmployeeNumber',
  );

  static AppRoute iqlaaNationalityPage = AppRoute(
    name: 'iqlaaSignupNationality',
    path: '${signup.path}/iqlaaSignupNationality',
  );

  static AppRoute iqlaaHomeBasedBusiness = AppRoute(
    name: 'iqlaaHomeBasedBusiness',
    path: '${signup.path}/iqlaaHomeBasedBusiness',
  );

  static AppRoute signUpIndustries = AppRoute(
    name: 'signUpIndustries',
    path: '${signup.path}/signUpIndustries',
  );

  static AppRoute iqlaaBusinessRegistration = AppRoute(
    name: 'iqlaaBusinessRegistration',
    path: '${signup.path}/iqlaaBusinessRegistration',
  );

  static AppRoute iqlaaSignin = AppRoute(name: 'iqlaaSignin', path: '${signin.path}/iqlaa');

  static AppRoute ebrdSignup = AppRoute(name: 'ebrdSignup', path: '${signup.path}/ebrd');

  static AppRoute ebrdSignIn = AppRoute(name: 'ebrdSignin', path: '${signin.path}/ebrd');

  static AppRoute ebrdKmfSignup = AppRoute(name: 'ebrdKmfSignup', path: '${signup.path}/kmf');

  static AppRoute ebrdKmfSignIn = AppRoute(name: 'ebrdKmfSignin', path: '${signin.path}/kmf');

  static AppRoute ebrdHamkorBankSignup = AppRoute(
    name: 'ebrdHamkorBankSignup',
    path: '${signup.path}/hamkorbank',
  );

  static AppRoute ebrdHamkorBankSignIn = AppRoute(
    name: 'ebrdHamkorBankSignIn',
    path: '${signin.path}/hamkorbank',
  );

  static AppRoute striveIndonesiaSignup = AppRoute(
    name: 'striveIndonesiaSignup',
    path: '${signup.path}/striveIndonesia',
  );

  static AppRoute striveIndonesiaSignIn = AppRoute(
    name: 'striveIndonesiaSignIn',
    path: '${signin.path}/striveIndonesia',
  );

  static AppRoute signupMastercardBankStage = AppRoute(
    name: 'signupMastercardBankStage',
    path: '${signup.path}/bankDetails',
  );

  static AppRoute signUpBusinessChallenge = AppRoute(
    name: 'signUpBusinessChallenge',
    path: '${signup.path}/challenge',
  );

  static AppRoute signUpHowCanMentorSupportMe = AppRoute(
    name: 'signUpHowCanMentorSupportMe',
    path: '${signup.path}/support',
  );

  static AppRoute signUpHowICanHelpMentees = AppRoute(
    name: 'signUpHowICanHelpMentees',
    path: '${signup.path}/howICanHelpMentees',
  );
}

class RouteParams {
  RouteParams._private();
  static const String channelId = 'channelId';
  static const String channelInvitationId = 'channelInvitationId';
  static const String userId = 'userId';
  static const String nextRouteName = 'nextRouteName';
  static const String experienceIndex = 'experienceIndex';
  static const String trainingSeriesId = 'trainingSeriesId';
  static const String individualTrainingId = 'trainingId';
  static const String targetUrl = 'targetUrl';
  static const String webviewTitle = 'webviewTitle';
  static const String trackId = 't';
  static const String groupIdent = 'groupIdent';
}
