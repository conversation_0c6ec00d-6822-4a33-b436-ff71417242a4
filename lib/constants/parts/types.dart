part of '../constants.dart';

typedef User = Query$FindUserById$findUserById;
typedef UserBySearch = Query$FindUserSearchResults$findUserSearchResults;
typedef BlockedUser = Query$GetMyBlockedUsers$getMyBlockedUsers;
typedef UserCms = Query$FindUserCmsByUserId$findUserCmsByUserId;
typedef Company = Query$FindUserById$findUserById$companies;
typedef Website = Query$FindUserById$findUserById$companies$websites;
typedef UserSearchResult = Query$FindUserSearchResults$findUserSearchResults;
typedef MenteesGroupMembership =
    Query$FindUserById$findUserById$groupMemberships$$MenteesGroupMembership;
typedef MentorsGroupMembership =
    Query$FindUserById$findUserById$groupMemberships$$MentorsGroupMembership;
typedef BusinessExperience = Query$FindUserById$findUserById$businessExperiences;
typedef ServiceRequest = Query$FindServiceRequestById$findServiceRequestById;
typedef RecipientUser = Query$FindChannelInvitationById$findChannelInvitationById$recipient;
typedef SenderUser = Query$FindChannelInvitationById$findChannelInvitationById$sender;

class IdentityProviderConfig {
  final Enum$IdentityProvider provider;
  final String label;
  final String? iconAsset;
  final IconData? iconData;
  final bool isActive;

  IdentityProviderConfig({
    required this.provider,
    required this.label,
    this.iconAsset,
    this.iconData,
    this.isActive = false,
  });
}

class Option {
  final String value;
  final String label;

  Option({required this.value, required this.label});
}

class LoadObjectResult<T> {
  final T? object;
  final Enum$ErrorCode? errorCode;
  final String? diagnosticsMessage;
  final OperationException? operationException;
  final ServiceRequest? serviceRequest;

  LoadObjectResult({
    this.object,
    this.errorCode,
    this.diagnosticsMessage,
    this.operationException,
    this.serviceRequest,
  });

  bool get hasError => errorCode != null || operationException != null;
}

const Duration defaultDataPollInterval = Duration(seconds: 1);
const int defaultMaxPollCount = 10;

class PollingConfig<T> {
  final int maxPollCount;
  int curPollIndex = 0;
  final bool useServiceRequest;
  final int initialDelayMs;
  final int intervalMs;
  bool Function(T object)? isUpdatedFunc;
  DateTime? prevUpdatedAt;

  PollingConfig({
    this.maxPollCount = defaultMaxPollCount,
    this.curPollIndex = 0,
    this.useServiceRequest = false,
    this.initialDelayMs = 1000,
    this.intervalMs = 1000,
    this.isUpdatedFunc,
    this.prevUpdatedAt,
  });
}

class PollingConfigForServiceRequest extends PollingConfig<ServiceRequest> {
  PollingConfigForServiceRequest({
    super.maxPollCount = defaultMaxPollCount,
    super.curPollIndex = 0,
    super.initialDelayMs = 1000,
    super.intervalMs = 1000,
  }) {
    isUpdatedFunc = (ServiceRequest sr) {
      return (sr.result == Enum$ServiceRequestResult.ok ||
          sr.result == Enum$ServiceRequestResult.error);
    };
  }
}

class LoadObjectOptions<T> {
  final FetchPolicy fetchPolicy;
  PollingConfig<T>? pollingConfig;
  Completer<LoadObjectResult<T>>? completer;

  LoadObjectOptions({
    this.fetchPolicy = FetchPolicy.networkOnly,
    this.pollingConfig,
    this.completer,
  });
}

class LoadObjectOptionsForServiceRequest extends LoadObjectOptions<ServiceRequest> {
  final bool waitForFinished;

  LoadObjectOptionsForServiceRequest({
    this.waitForFinished = true,
    super.fetchPolicy = FetchPolicy.networkOnly,
    super.pollingConfig,
    super.completer,
  });
}

class UpdateObjectOptions<T> extends LoadObjectOptions {
  final bool useServiceRequest;
  final LoadObjectOptionsForServiceRequest? loadServiceRequestOptions;
  final LoadObjectOptions<T>? loadObjectOptions;

  UpdateObjectOptions({
    this.useServiceRequest = false,
    this.loadServiceRequestOptions,
    this.loadObjectOptions,
  });
}
