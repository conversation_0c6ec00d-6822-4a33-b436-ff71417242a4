part of '../constants.dart';

class Identifiers {
  Identifiers._private();

  static const String appName = 'Micromentor';
  static const String notificationOverflow = '99+';
  static const String contactUrl = 'https://resources.micromentor.org/uxr-contact';
  static const String arabicContactUrl = 'https://resources.micromentor.org/ar/uxr-contact';
  static const String helpCenterUrl = 'https://resources.micromentor.org/help-center';
  static const String arabicHelpCenterUrl = 'https://resources.micromentor.org/ar/help-center';
  static const String classicAdminDashboardUrl = 'https://classic.micromentor.org/dashboard';

  static const String codeOfConductUrl = 'https://micromentor.org/code-of-conduct';
  static const String termsOfUseUrl = 'https://micromentor.org/terms-of-use';
  static const String privacyPolicyUrl = 'https://micromentor.org/privacy-policy';

  static const String mm2TestVtsOtpUrl =
      'MM2_DOMAIN/accounts/otp/?otp=REPLACE_OTP&user_ident=REPLACE_USER_IDENT&next=REDIRECT_URL_PATH';
  static const String vtsBeginTrainingPath = '/vts/begin-training/';
  static const String vtsReviewTrainingPath = '/vts/begin-review/';
  static const String vtsDownloadCertificatePath = '/vts/download-certificate/';
  static const String certificateName = 'certificate-REPLACE_TRAINING_NAME.pdf';

  // dummy data
  static const String dummyTrainingUrlPath = '/dev-vts-flow/dev-created-test-training';
}
