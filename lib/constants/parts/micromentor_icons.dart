import 'package:flutter/widgets.dart';

/// Flutter icons MicromentorIcons
/// Copyright (C) 2025 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  MicromentorIcons
///      fonts:
///       - asset: fonts/MicromentorIcons.ttf
///
///
/// * Material Design Icons, Copyright (C) Google, Inc
///         Author:    Google
///         License:   Apache 2.0 (https://www.apache.org/licenses/LICENSE-2.0)
///         Homepage:  https://design.google.com/icons/
///

class MicromentorIcons {
  MicromentorIcons._();

  static const _kFontFam = 'MicromentorIcons';
  static const String? _kFontPkg = null;

  static const IconData add = IconData(0xe800, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData arrowDropdown = IconData(
    0xe801,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData chatBubbleOutline = IconData(
    0xe802,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData close = IconData(0xe803, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData language = IconData(0xe806, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData search = IconData(0xe807, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData remove = IconData(0xe808, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData error = IconData(0xe80c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData deleteForeverOutline = IconData(
    0xe80d,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData imageOutline = IconData(
    0xe80e,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData circle = IconData(0xe810, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData infoOutline = IconData(
    0xe811,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData emailOutline = IconData(
    0xe812,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData editOutline = IconData(
    0xe813,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData keyboardArrowDown = IconData(
    0xe815,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData person = IconData(0xe816, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData clear = IconData(0xe817, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData mail = IconData(0xe818, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData keyboardArrowUp = IconData(
    0xe819,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData refresh = IconData(0xe81a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData block = IconData(0xe81b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData deleteOutline = IconData(
    0xe81e,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData warningOutline = IconData(
    0xe820,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData checkCircleOutline = IconData(
    0xe821,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData settingOutline = IconData(
    0xe823,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData adminPanelSettings = IconData(
    0xe824,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData personSearch = IconData(
    0xe826,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData chevronLeft = IconData(
    0xe828,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData addCircleOutlined = IconData(
    0xe829,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData delete = IconData(0xe82b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData chevronRight = IconData(
    0xe830,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData checkCircle = IconData(
    0xe832,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData apple = IconData(0xe835, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData unarchiveOutlined = IconData(
    0xe836,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData addCircle = IconData(0xe839, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData archiveOutlined = IconData(
    0xe83f,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData warningRounded = IconData(
    0xe840,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData menuRounded = IconData(
    0xe841,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData visibilityOutlined = IconData(
    0xe842,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData visibilityOffOutlined = IconData(
    0xe843,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData flightTakeoffRounded = IconData(
    0xe844,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData headphonesRounded = IconData(
    0xe845,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData sendRounded = IconData(
    0xe847,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData linkedin = IconData(0xe848, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData logout = IconData(0xe849, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData logoutRounded = IconData(
    0xe84a,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData arrowForwardIosRounded = IconData(
    0xe84b,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData arrowBackIosRounded = IconData(
    0xe84c,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData arrowBackIosNewRounded = IconData(
    0xe84d,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData arrowBackRounded = IconData(
    0xe84e,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData arrowForwardRounded = IconData(
    0xe84f,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData moreVertRounded = IconData(
    0xe850,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData mailRounded = IconData(
    0xe851,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData warningAmberRounded = IconData(
    0xe852,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData thumbUpOffAltOutlined = IconData(
    0xe853,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData businessCenterOutlined = IconData(
    0xe854,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData locationOnOutlined = IconData(
    0xe855,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData rotate90degreesCcwOutlined = IconData(
    0xe857,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData rotate90degreesCwOutlined = IconData(
    0xe858,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData cameraAltOutlined = IconData(
    0xe859,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData tune = IconData(0xe85a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData inventory2outlined = IconData(
    0xe85b,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData personAddOutlined = IconData(
    0xe85c,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
}
