part of '../constants.dart';

class Limits {
  Limits._private();
  static const maxNotificationsDisplayed = 99;
  static const chatMessagesPageSize = 25;
  static const searchResultsBatchSize = 100;
  static const searchResultsPageSize = 5;
  static const searchResultsWebPageSize = 9;
  static const initialChannelsPageSize = 20;
  static const channelsPageSize = 10;
  static const invitationsPageSize = 10;
  static const homeRecommendedUsersMaxSize = 4;
  static const homeInvitationsListMaxSize = 3;
  static const expertisesQuickViewMaxChips = 3;
  static const expertisesProfileViewMaxChips = 6;
  static const profileEducationMaxSize = 3;
  static const profileExperienceMaxSize = 3;
  static const profileExpertiseMaxSize = 3;
  static const profileEntrepreneurIndustryMaxSize = 1;
  static const profileMentorIndustryMaxSize = 3;
}
