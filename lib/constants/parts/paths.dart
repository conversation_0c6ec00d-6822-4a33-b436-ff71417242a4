part of '../constants.dart';

class Assets {
  Assets._private();
  static const String brandLogoWithName = 'assets/images/mm_logo.png';
  static const String blankAvatar = 'assets/images/blank-avatar.png';
  static const String startScreenStockImage1 = 'assets/images/StockImage11.png';
  static const String startScreenStockImage2 = 'assets/images/StockImage13.png';
  static const String startScreenStockImage3 = 'assets/images/StockImage12.png';
  static const String identityProviderFacebookIcon = 'assets/images/sso_facebook.png';
  static const String identityProviderGoogleIcon = 'assets/images/sso_google.png';
  static const String identityProviderLinkedInIcon = 'assets/images/sso_linkedin.png';
  static const String identityProviderTelegramIcon = 'assets/images/sso_telegram.png';
  static const String ssoWhatsAppIcon = 'assets/images/sso_whatsapp.png';
  static const String entrepreneurIcon = 'assets/images/new_entrepreneur.png';
  static const String mentorIcon = 'assets/images/new_mentor.png';
  static const String whiteMicromentorLogoWithName =
      'assets/images/micromentor_logo_small_white_stacked.png';
  static const String whiteMicromentorDesktopLogoWithName =
      'assets/images/micromentor_desktop_logo_horizontal.png';
  static const String ideaStage = 'assets/icon/idea_stage.png';
  static const String operationalStage = 'assets/icon/operational_stage.png';
  static const String revenueStage = 'assets/icon/revenue_stage.png';
  static const String profitableStage = 'assets/icon/profitable_stage.png';
  static const String ideaStageWhite = 'assets/icon/idea_stage_white.png';
  static const String operationalStageWhite = 'assets/icon/operational_stage_white.png';
  static const String revenueStageWhite = 'assets/icon/revenue_stage_white.png';
  static const String profitableStageWhite = 'assets/icon/profitable_stage_white.png';
  static const String successful = 'assets/images/successful.png';
  static const String homeDefault = 'assets/icon/home-default.png';
  static const String homeSelected = 'assets/icon/home-selected.png';
  static const String exploreDefault = 'assets/icon/explore-default.png';
  static const String exploreSelected = 'assets/icon/explore-selected.png';
  static const String inboxDefault = 'assets/icon/inbox-default.png';
  static const String inboxSelected = 'assets/icon/inbox-selected.png';
  static const String profileDefault = 'assets/icon/profile-default.png';
  static const String profileSelected = 'assets/icon/profile-selected.png';
  static const String pencil = 'assets/icon/pencil_small.png';
  static const String emptyInbox = 'assets/images/empty-inbox.png';
  static const String noInvites = 'assets/images/no-invites.png';
  static const String vacationDialogImage = 'assets/images/vacation_dialog_image.png';
  static const String invitationSendNewImage = 'assets/images/invitation_send_image.png';
  static const String exploreFilterIcon = 'assets/icon/filter.png';
  static const String noChatImage = 'assets/images/no_chat_image.png';
  static const String certificateIcon = 'assets/icon/certificate.png';
  static const String certificateSelectedIcon = 'assets/icon/certificate_selected.png';
  static const String certificateDownloadIcon = 'assets/icon/certificate_download.png';
  static const String certificateViewIcon = 'assets/icon/certificate_view.png';
  static const String menuIcon = 'assets/icon/menu_book_default.png';
  static const String menuSelectedIcon = 'assets/icon/menu_book_selected.png';
  static const String openInNew = 'assets/icon/open_in_new.png';
}
