part of '../constants.dart';

enum AsyncState { loading, error, ready }

enum BadgeType { all, messages, invitations }

enum OnboardingStage {
  acceptTerms,
  authType,
  birthYear,
  createAccount,
  notifications,
  expertises,
  finished,
  gender,
  language,
  location,
  mentorInfo,
  phoneNumber,
  profileRole,
  reasonToJoin,
  ventureName,
  ventureStage,
  iqlaaConsent,
  iqlaaEmployeeNumber,
  industries,
  iqlaaNationality,
  iqlaaHomeBasedBusiness,
  iqlaaBusinessRegistration,
  striveIndonesiaVentureStartDate,
  mastercardBankDetails,
  businessChallenge,
  howCanMentorSupportMe,
  howICanHelpMentees,
}

enum MessageDirection { sent, received, unset, self, connected }

enum InviteTab { received, sent }

enum GroupIdent {
  mentors,
  mentees,
  // USAID iqlaa:
  iqlaa,
  // ebrd groups:
  ebrdCentralAsia,
  kmf,
  hamkor,
  // strive-indonesia
  striveIndonesia,
  mastercard,
  finsus,
  banbajio,
  proempleo,
  finvero,
  promover,
}

enum WebsiteLabels { facebook, twitter, venture, linkedIn, other }

enum CompanyStageTextId { idea, operational, earning, profitable }

enum ChipBackgroundColor { primary, secondary, tertiary }

enum Tabs { home, explore, inbox, trainings, profile }

enum ButtonSize { small, medium, large, xLarge }

enum ButtonType { elevated, text, outline }

enum IconPosition { atLeft, atRight, atCenter }

enum InboxDrawersMenu { chats, invites, archivedChats }

enum EditAccountSettingOptions {
  name,
  emailAddress,
  birthYear,
  phoneNumber,
  gender,
  profileRole,
  appLanguage,
  passwordAndSecurity,
  blockedUsers,
  notifications,
}

enum EditProfileOptions {
  pronouns,
  linkedin,
  country,
  originLocation,
  prefferedLanguages,
  otherLanguages,
  expertiseTop,
  expertiseAdditional,
  industries,
  mentoringPreference,
  mentoringReason,
  howICanHelpMentees,
  companyName,
  companyWebsite,
  companyLocation,
  industryName,
  companyStage,
  menteeBusinessReason,
  newExperience,
  editExperience,
  newEducation,
  editEducation,
  businessChallenge,
  howCanMentorSupportMe,
}

enum TrainingStatus { notStarted, inProgress, passed, failed, tags }

enum IQLAASignUpNationality { jordanian, other }

enum LegalDocumentType { privacyPolicy, termsOfUse, codeOfConduct }
