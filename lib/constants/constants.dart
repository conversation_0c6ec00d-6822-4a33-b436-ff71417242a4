library constants;

import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/__generated/schema/operations_invitation.graphql.dart';
import 'package:mm_flutter_app/__generated/schema/operations_system.graphql.dart';
import 'package:mm_flutter_app/__generated/schema/operations_user.graphql.dart';

import '../__generated/schema/schema.graphql.dart';

part 'parts/enums.dart';
part 'parts/identifiers.dart';
part 'parts/numerical.dart';
part 'parts/paths.dart';
part 'parts/routes.dart';
part 'parts/static_app_features.dart';
part 'parts/types.dart';
