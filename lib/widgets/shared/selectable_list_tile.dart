import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../../../services/extensions.dart';

class SelectableChip {
  final String chipContent;
  final String textId;

  SelectableChip({required this.chipContent, required this.textId});
}

class SelectableListTile extends StatefulWidget {
  final List<SelectableChip> chips;
  final int? maxSelection;
  final Function(List<SelectableChip>)? onSelect;
  final List<SelectableChip> initialSelection;
  final bool showRadioButton;

  const SelectableListTile.checkbox({
    super.key,
    required this.chips,
    this.maxSelection,
    this.onSelect,
    this.initialSelection = const [],
  }) : showRadioButton = false;

  const SelectableListTile.radio({
    super.key,
    required this.chips,
    this.onSelect,
    this.initialSelection = const [],
  }) : showRadioButton = true,
       maxSelection = 1;

  @override
  State<SelectableListTile> createState() => _SelectableListTileState();
}

class _SelectableListTileState extends State<SelectableListTile> {
  final Set<int> _childIsSelected = {};
  String? radioGroupValue;

  @override
  void initState() {
    super.initState();
    // Initialize with preselected values
    for (int i = 0; i < widget.chips.length; i++) {
      if (widget.initialSelection.any((e) => e.textId == widget.chips[i].textId)) {
        _childIsSelected.add(i);
      }
    }

    if (widget.showRadioButton && _childIsSelected.isNotEmpty) {
      radioGroupValue = widget.initialSelection.first.textId;
    }
  }

  // Callback function to be called by the child widget when checkbox is selected
  void onChecked(bool? isChecked, int index) {
    ((isChecked ?? false) &&
            (widget.maxSelection != null
                ? (_childIsSelected.length < (widget.maxSelection!))
                : true))
        ? _childIsSelected.add(index)
        : _childIsSelected.remove(index);

    widget.onSelect?.call(_childIsSelected.map((i) => widget.chips[i]).toList());
  }

  void onSelect(String? value, int index) {
    setState(() {
      radioGroupValue = value;
    });
    widget.onSelect?.call([widget.chips[index]]);
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> listTiles = [];

    for (int i = 0; i < widget.chips.length; i++) {
      var tile = Padding(
        padding: EdgeInsets.only(
          right: context.theme.d.paddingXxSmall,
          bottom: (kIsWeb) ? context.theme.d.paddingXSmall : context.theme.d.zero,
        ),
        child: widget.showRadioButton ? _radioTileWidget(i) : _checkBoxTileWidget(i),
      );
      listTiles.add(tile);
    }

    return Wrap(
      spacing: context.theme.d.paddingMedium,
      runSpacing: context.theme.d.paddingSmall,
      alignment: WrapAlignment.center,
      children: listTiles,
    );
  }

  Widget _checkBoxTileWidget(int index) {
    final bool isSelected = _childIsSelected.contains(index);
    return CheckboxListTile(
      selected: isSelected,
      value: isSelected,
      tileColor: context.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
      selectedTileColor: context.colorScheme.secondaryContainer,
      dense: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
        side: isSelected ? BorderSide(color: context.colorScheme.primary) : BorderSide.none,
      ),
      onChanged: (isChecked) => onChecked(isChecked, index),
      title: Text(widget.chips[index].chipContent, style: context.textTheme.labelLarge),
    );
  }

  Widget _radioTileWidget(int index) {
    final bool isSelected = radioGroupValue == widget.chips[index].textId;
    return RadioListTile<String>(
      selected: isSelected,
      value: widget.chips[index].textId,
      groupValue: radioGroupValue,
      controlAffinity: ListTileControlAffinity.trailing,
      tileColor: context.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
      selectedTileColor: context.colorScheme.secondaryContainer,
      dense: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
        side: isSelected ? BorderSide(color: context.colorScheme.primary) : BorderSide.none,
      ),
      onChanged: (value) => onSelect(value, index),
      title: Text(widget.chips[index].chipContent, style: context.textTheme.labelLarge),
    );
  }
}
