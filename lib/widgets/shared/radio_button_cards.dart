import 'package:flutter/material.dart';

import '../../../../services/extensions.dart';

class RadioButtonCards extends StatefulWidget {
  final List<RadioCard> cards;
  final Function(int)? onSelectedCardChanged;
  final int initialSelection;

  const RadioButtonCards({
    super.key,
    required this.cards,
    this.onSelectedCardChanged,
    this.initialSelection = 0,
  });

  @override
  State<RadioButtonCards> createState() => _RadioButtonCardsState();
}

class _RadioButtonCardsState extends State<RadioButtonCards> {
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.initialSelection;
  }

  @override
  Column build(BuildContext context) {
    List<Widget> cardWidgets = [];

    for (int i = 0; i < widget.cards.length; ++i) {
      Color startingColor =
          (i == _selectedIndex)
              ? context.colorScheme.primary
              : context.colorScheme.secondaryContainer;

      cardWidgets.add(
        InkWell(
          borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
          onTap:
              () => setState(() {
                _selectedIndex = i;
                widget.onSelectedCardChanged?.call(_selectedIndex);
              }),
          child: Container(
            padding: EdgeInsets.all(context.theme.d.paddingLarge),
            decoration: BoxDecoration(
              color: context.colorScheme.onPrimary,
              border: Border.all(color: startingColor),
              borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
              boxShadow: [BoxShadow(color: startingColor, blurRadius: 3, spreadRadius: 1)],
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  flex: 3,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          //the code snippet below checks if the titleIcon is null, and if it is not null, presents it
                          if (widget.cards[i].leadingImage != null) ...[
                            widget.cards[i].leadingImage!,
                            SizedBox(width: context.theme.d.paddingMedium),
                          ],

                          Expanded(
                            child: Text(
                              widget.cards[i].title,
                              style: context.theme.textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: context.theme.d.paddingSmall),
                      Padding(
                        padding: EdgeInsets.only(
                          left:
                              widget.cards[i].leadingImage != null
                                  ? context.theme.d.radioButtonPadding
                                  : context.theme.d.zero,
                        ),
                        child: Text(
                          widget.cards[i].subtitle,
                          style: context.theme.textTheme.titleMedium,
                        ),
                      ),
                    ],
                  ),
                ),
                if (widget.cards[i].trailingImage != null) ...[
                  SizedBox(width: context.theme.d.paddingMedium),
                  SizedBox(
                    width: context.theme.d.boxSizeMedium,
                    height: context.theme.d.boxSizeXLarge,
                    child: widget.cards[i].trailingImage,
                  ),
                ],
              ],
            ),
          ),
        ),
      );

      cardWidgets.add(SizedBox(height: context.theme.d.paddingMedium));
    }
    return Column(mainAxisSize: MainAxisSize.min, children: cardWidgets);
  }
}

class RadioCard {
  final String title;
  final String subtitle;
  final Image? leadingImage;
  final Image? trailingImage;

  RadioCard({required this.title, required this.subtitle, this.trailingImage, this.leadingImage});
}
