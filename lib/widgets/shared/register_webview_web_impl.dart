// This file is only used in web
// Using package:web and dart:js_interop as recommended instead of dart:html
import 'package:web/web.dart' as web;
import 'dart:ui_web' as ui;

void registerWebViewFactory({required String targetUrl}) {
  // Register a factory for creating IFrameElements
  ui.platformViewRegistry.registerViewFactory(targetUrl, (int viewId) {
    final iframe = web.document.createElement('iframe') as web.HTMLIFrameElement;
    iframe.src = targetUrl;
    iframe.style.border = 'none';
    iframe.setAttribute('loading', 'lazy');
    // iframe.style.width = '100%';
    // iframe.style.height = '100%';
    return iframe;
  });
}

String getDomainName() {
  return web.window.location.host;
}
