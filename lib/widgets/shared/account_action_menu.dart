import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/models/locale_model.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/firebase/analytic_service.dart';
import 'package:mm_flutter_app/services/graphql/providers/user_provider.dart';
import 'package:mm_flutter_app/utilities/loading/loading_provider.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';
import 'package:provider/provider.dart';

import '../../constants/parts/micromentor_icons.dart';
import 'shared.dart';

class AccountActionMenu extends StatelessWidget {
  const AccountActionMenu({super.key});

  @override
  Widget build(BuildContext context) {
    UserProvider? userProvider = Provider.of<UserProvider>(context, listen: false);
    LocaleModel? localModel = Provider.of<LocaleModel>(context, listen: false);
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall),
      child: CustomPopupMenu(
        options: [
          CustomPopupMenuItem(
            key: const Key('vacationModeMenuItem'),
            title: AppLocale.current.vacationModeTitle,
            icon: MicromentorIcons.flightTakeoffRounded,
            onTap: () => _showVacationDialog(context),
          ),
          CustomPopupMenuItem(
            key: const Key('accountSettingsMenuItem'),
            title: AppLocale.current.accountHeader,
            icon: MicromentorIcons.settingOutline,
            onTap: () {
              if (context.mounted) {
                AppUtility.displayDesktopUI(context)
                    ? context.go(AppRoutes.accountSettings.path)
                    : context.push(AppRoutes.accountSettings.path);
              }
            },
          ),
          if (userProvider.isImpersonating)
            CustomPopupMenuItem(
              key: const Key('clearImpersonationMenuItem'),
              title: AppLocale.current.actionClearImpersonation,
              icon: MicromentorIcons.person,
              onTap: () async {
                await userProvider.clearImpersonation();
                if (context.mounted) {
                  context.push(AppRoutes.home.path);
                }
              },
            ),
          CustomPopupMenuItem(
            key: const Key('helpCenterMenuItem'),
            title: AppLocale.current.helpCenterTitle,
            icon: MicromentorIcons.headphonesRounded,
            onTap: () async {
              await AppUtility.openLink(
                localModel.isArabic() ? Identifiers.arabicHelpCenterUrl : Identifiers.helpCenterUrl,
              );
            },
          ),
          if (userProvider.isAdmin)
            CustomPopupMenuItem(
              key: const Key('runAdminTaskMenuItem'),
              title: AppLocale.current.runAdminTaskTitle,
              icon: MicromentorIcons.adminPanelSettings,
              onTap: () => context.push(AppRoutes.admin.path),
            ),
          if (userProvider.isAdmin)
            CustomPopupMenuItem(
              key: const Key('ClassicAdminDashboard'),
              title: AppLocale.current.classicAdminDashboard,
              icon: MicromentorIcons.adminPanelSettings,
              onTap: () => AppUtility.openLink(Identifiers.classicAdminDashboardUrl),
            ),
          CustomPopupMenuItem(
            key: const Key('logoutMenuItem'),
            title: AppLocale.current.actionLogOut,
            icon: MicromentorIcons.logoutRounded,
            onTap: () => _showLogoutDialog(context),
          ),
        ],
      ),
    );
  }

  _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) {
        return LogoutDialog(
          isDesktop: AppUtility.displayDesktopUI(context),
          onConfirm: () async {
            await Provider.of<UserProvider>(context, listen: false).signOutUser(context);
            if (dialogContext.mounted) {
              dialogContext.pop();
            }
          },
        );
      },
    );
  }

  _showVacationDialog(BuildContext context) {
    var userProvider = Provider.of<UserProvider>(context, listen: false);
    bool isOnVacationOldValue = userProvider.myUser?.isOnVacation ?? false;
    bool isSwitched = isOnVacationOldValue;

    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (dialogContext) {
        return Center(
          child: PointerInterceptor(
            child: StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
                return AlertDialog(
                  icon: Align(
                    alignment: Alignment.centerRight,
                    child: IconButton(
                      icon: const Icon(MicromentorIcons.close),
                      onPressed: () async {
                        if (isOnVacationOldValue != isSwitched) {
                          Loader.show(context);

                          await userProvider.updateUser(
                            input: Input$UserInput(
                              id: userProvider.myUser?.id,
                              isOnVacation: isSwitched,
                            ),
                          );
                          AnalyticService.vacationTracking(isSwitched);
                        }
                        if (context.mounted) {
                          Loader.hide(context);
                          Navigator.pop(context);
                        }
                      },
                    ),
                  ),
                  title: Image.asset(
                    Assets.vacationDialogImage,
                    height: context.theme.d.vacationDialogImageHeight,
                  ),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(height: context.theme.d.paddingMedium),
                      Text(
                        AppLocale.current.vacationModeTitle,
                        style: context.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: context.colorScheme.surfaceBright,
                        ),
                      ),
                      SizedBox(height: context.theme.d.paddingMedium),
                      CustomSwitch(
                        initialValue: isSwitched,
                        onChanged: (value) {
                          setState(() {
                            isSwitched = value;
                          });
                        },
                      ),
                      SizedBox(height: context.theme.d.paddingMedium),
                      SizedBox(
                        width: context.theme.d.vacationDialogContentWidth,
                        child: Text(
                          isSwitched
                              ? AppLocale.current.vacationOnModeSubTitle
                              : AppLocale.current.vacationOffModeSubTitle,
                          textAlign: TextAlign.center,
                          style: context.theme.textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }
}
