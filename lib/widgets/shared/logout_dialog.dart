import 'package:flutter/material.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';

import '../../generatedAppLocale/l10n.dart';
import '../widgets.dart';

class LogoutDialog extends StatelessWidget {
  final bool isDesktop;
  final Function() onConfirm;
  final Function()? onCancel;
  const LogoutDialog({required this.isDesktop, required this.onConfirm, this.onCancel, super.key});

  @override
  Widget build(BuildContext context) {
    //PointerInterceptor is a widget that prevents mouse events from being captured by an underlying HtmlElementView in web
    return PointerInterceptor(
      key: const Key('LogoutKey'),
      child: DialogTemplate(
        title: AppLocale.current.logOutDialogHeading,
        description: AppLocale.current.logOutSubtitle,
        showTitleInCenter: !isDesktop,
        isFullWidthButtons: !isDesktop,
        onAction: onConfirm,
        onCancel: onCancel,
      ),
    );
  }
}
