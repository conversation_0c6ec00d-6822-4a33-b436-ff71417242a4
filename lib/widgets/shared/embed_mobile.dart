import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:webview_flutter/webview_flutter.dart';

class MobileEmbedWidget extends StatelessWidget {
  final String targetUrl;
  final String webviewTitle;

  final Set<String> activeRequests = {};
  final WebViewController controller =
      WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setBackgroundColor(const Color(0x00000000));

  String normalizeUrl(String url) {
    final uri = Uri.parse(url);
    return Uri(
      scheme: uri.scheme,
      host: uri.host,
      path: uri.path,
      queryParameters: uri.queryParameters,
    ).toString();
  }

  MobileEmbedWidget({super.key, required this.targetUrl, required this.webviewTitle}) {
    controller.setNavigationDelegate(
      NavigationDelegate(
        onProgress: (int progress) {
          // Update loading bar using inbox.signals
        },
        onPageStarted: (String url) {},
        onPageFinished: (String url) {
          final normalizedUrl = normalizeUrl(url);
          activeRequests.remove(normalizedUrl);
          // if (activeRequests.remove(normalizedUrl)) {
          //   debugPrint('URL removed from activeRequests: $normalizedUrl');
          // } else {
          //   debugPrint('URL not found in activeRequests: $normalizedUrl');
          // }
        },
        onNavigationRequest: (NavigationRequest request) {
          // debugPrint('navigationDelegate.onNavigationRequest: called for $targetUrl');
          final normalizedUrl = normalizeUrl(request.url);

          if (normalizedUrl.startsWith('https://www.youtube.com/')) {
            return NavigationDecision.prevent;
          }

          if (activeRequests.contains(normalizedUrl)) {
            debugPrint('Duplicate navigation request blocked: $normalizedUrl');
            return NavigationDecision.prevent;
          }

          activeRequests.add(normalizedUrl);
          // debugPrint('Active request added: $normalizedUrl');
          // debugPrint('Navigation allowed: ${request.url}');
          return NavigationDecision.navigate;
        },
        onWebResourceError: (WebResourceError error) {},
      ),
    );
    controller.loadRequest(Uri.parse(targetUrl));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(webviewTitle)),
      body: SizedBox(
        width: context.mediaQuerySize.width,
        height: context.mediaQuerySize.height,
        child: WebViewWidget(key: const ValueKey('webview'), controller: controller),
      ),
    );
  }
}
