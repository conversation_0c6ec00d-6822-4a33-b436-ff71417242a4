import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/router/app_router.dart';
import 'package:mm_flutter_app/services/graphql/providers/providers.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../services/extensions.dart';
import '../../constants/parts/micromentor_icons.dart';
import '../../models/models.dart';
import '../widgets.dart';

class AppWrapper extends StatefulWidget {
  const AppWrapper({super.key, required this.child});

  final Widget child;

  @override
  State<AppWrapper> createState() => _AppWrapperState();
}

class _AppWrapperState extends State<AppWrapper> {
  late ScaffoldModel scaffoleModelProvider;
  late UserProvider _userProvider;
  bool hasTraining = true;
  List<Tabs> visibleMobileTabs = [];
  List<Tabs> visibleDesktopTabs = [];

  @override
  void initState() {
    super.initState();
    scaffoleModelProvider = Provider.of<ScaffoldModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    hasTraining = _userProvider.myUser?.hasTrainings ?? false;
    visibleMobileTabs = AppUtility.mobileNavigationTabs(hasTraining);
    visibleDesktopTabs = AppUtility.desktopNavigationTabs(hasTraining);
  }

  @override
  void didUpdateWidget(covariant AppWrapper oldWidget) {
    if (!AppUtility.displayDesktopUI(context)) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (oldWidget.child != widget.child && scaffoleModelProvider.showDesktopFooter) {
        scaffoleModelProvider.shouldShowFooter(false);
      }
    });
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppUtility.displayDesktopUI(context)
        ? _createDesktopScaffold(context)
        : _createMobileScaffold(context);
  }

  Widget _createMobileScaffold(BuildContext context) {
    return Consumer<ScaffoldModel>(
      builder: (context, scaffoldModel, child) {
        final Scaffold mainScaffold = Scaffold(
          key: scaffoldModel.scaffoldKey,
          body: child,
          appBar: scaffoldModel.appBar,
          drawer: scaffoldModel.drawer,
          bottomNavigationBar: _bottomNavigationBar(context, scaffoldModel),
        );

        //checking for TabBar in AppBar-bottom
        TabBar? navBarTabBar;
        try {
          navBarTabBar = (scaffoldModel.appBar as AppBar?)?.bottom as TabBar?;
        } catch (_) {
          navBarTabBar = null;
        }

        return (navBarTabBar?.tabs.length ?? 0) == 0
            ? mainScaffold
            : DefaultTabController(length: navBarTabBar!.tabs.length, child: mainScaffold);
      },
      child: widget.child,
    );
  }

  Widget _createDesktopScaffold(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Consumer<ScaffoldModel>(
        builder: (context, scaffoldModel, child) {
          return Scaffold(
            key: scaffoldModel.scaffoldKey,
            body: NotificationListener<ScrollNotification>(
              onNotification: (ScrollNotification notification) {
                if (notification.metrics.axis == Axis.vertical) {
                  if (notification is ScrollEndNotification) {
                    final ScrollMetrics metrics = notification.metrics;

                    if (metrics.atEdge) {
                      scaffoldModel.shouldShowFooter(metrics.pixels == metrics.maxScrollExtent);
                    }
                  }
                }
                return true;
              },
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal:
                      visibleDesktopTabs.indexOf(Tabs.inbox) == scaffoldModel.selectedTabIndex
                          ? context.theme.d.zero
                          : context.theme.d.paddingXLarge,
                ),
                child: child!,
              ),
            ),
            appBar: _desktopAppbar(context, scaffoldModel),
            drawer: scaffoldModel.drawer,
            bottomNavigationBar: scaffoldModel.showDesktopFooter ? _desktopFooter(context) : null,
          );
        },
        child: widget.child,
      ),
    );
  }

  _bottomNavigationBar(BuildContext context, ScaffoldModel scaffoldModel) {
    if (scaffoldModel.hideNavBar) return null;
    var localDataModel = Provider.of<LocalDataModel>(context, listen: false);

    // TODO - remove after VTS
    if ((scaffoldModel.selectedTabIndex ?? localDataModel.lastSelectedTab) == 4) {
      scaffoldModel.setParams(index: 3);
    }

    return NavigationBar(
      indicatorColor: Colors.transparent,
      labelBehavior: NavigationDestinationLabelBehavior.alwaysHide,
      destinations:
          visibleMobileTabs.map((visibleTab) {
            return getBottomNavigationTab(visibleTab);
          }).toList(),
      selectedIndex: scaffoldModel.selectedTabIndex ?? localDataModel.lastSelectedTab,
      onDestinationSelected: (int index) {
        if (index == scaffoldModel.selectedTabIndex &&
            AppRouter.getLastSubroutePath(context) == getRouteName(visibleMobileTabs[index])) {
          return;
        }
        scaffoldModel.setParams(index: index);
        localDataModel.lastSelectedTab = index;
        context.go(getRouteName(visibleMobileTabs[index]));
      },
    );
  }

  Widget _inboxTabIcon(BuildContext context, {bool isSelected = false}) {
    return Stack(
      alignment: AlignmentDirectional.topEnd,
      children: [
        Padding(
          padding: EdgeInsets.only(
            left: context.theme.d.paddingSmall,
            right: context.theme.d.paddingSmall,
            bottom: context.theme.d.paddingXxSmall,
          ),
          child:
              AppUtility.displayDesktopUI(context)
                  ? _navigationDesktopIcon(isSelected ? Assets.inboxSelected : Assets.inboxDefault)
                  : _navigationIcon(
                    isSelected ? Assets.inboxSelected : Assets.inboxDefault,
                    AppLocale.current.navTabInbox,
                    isSelected,
                  ),
        ),
        const NotificationBubble(badgeType: BadgeType.all),
      ],
    );
  }

  String getRouteName(Tabs selectedTab) {
    switch (selectedTab) {
      case Tabs.home:
        return AppRoutes.home.path;
      case Tabs.explore:
        return AppRoutes.explore.path;
      case Tabs.inbox:
        return AppRoutes.inboxChats.path;
      case Tabs.trainings:
        return AppRoutes.trainings.path;
      case Tabs.profile:
        return AppRoutes.profile.path;
    }
  }

  AppBar _desktopAppbar(BuildContext context, ScaffoldModel scaffoldModel) {
    UserProvider? userProvider = Provider.of<UserProvider>(context, listen: false);
    LocalDataModel localDataModel = Provider.of<LocalDataModel>(context, listen: false);
    int selectedIndex = scaffoldModel.selectedTabIndex ?? localDataModel.lastSelectedTab;

    return AppBar(
      bottom:
          scaffoldModel.appBar != null
              ? PreferredSize(
                preferredSize: Size.fromHeight(
                  (scaffoldModel.appBar)?.preferredSize.height ??
                      context.theme.d.customToolbarHeight,
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingXLarge),
                  child: scaffoldModel.appBar,
                ),
              )
              : null,
      toolbarHeight: context.theme.d.customToolbarHeight,
      shape:
          scaffoldModel.appBar == null
              ? Border(bottom: BorderSide(color: context.colorScheme.primaryContainer))
              : null,
      title: Row(
        children: [
          InkWell(
            hoverColor: context.colorScheme.secondaryContainer,
            borderRadius: BorderRadius.circular(context.theme.d.paddingSmall),
            key: const Key('homeTab'),
            onTap: () {
              int index = visibleDesktopTabs.indexOf(Tabs.home); // Tabs.values.indexOf(Tabs.home);
              if (index == scaffoldModel.selectedTabIndex &&
                  AppRouter.getLastSubroutePath(context) == getRouteName(Tabs.home)) {
                return;
              }

              if (context.canPop() && index == scaffoldModel.selectedTabIndex) {
                context.pop();
                scaffoldModel.setParams(index: index);
                return;
              }
              scaffoldModel.setParams(index: index);
              Provider.of<LocalDataModel>(context, listen: false).lastSelectedTab = index;
              context.go(getRouteName(Tabs.home));
            },
            child: Image.asset(Assets.brandLogoWithName, height: context.theme.d.imageSizeSmall),
          ),
          SizedBox(width: context.theme.d.paddingLarge),
          _topNavigationItem(
            key: const Key('exploreTab'),
            Tabs.explore,
            context,
            scaffoldModel,
            selectedIndex,
            title: AppLocale.current.navTabExplore,
            iconData: MicromentorIcons.search,
          ),
        ],
      ),
      actions: [
        if (StaticAppFeatures.vts && hasTraining) ...[
          _topNavigationItem(
            key: const Key('trainingsTab'),
            Tabs.trainings,
            context,
            scaffoldModel,
            selectedIndex,
            customChild: _navigationDesktopIcon(
              selectedIndex == Tabs.trainings.index ? Assets.menuSelectedIcon : Assets.menuIcon,
            ),
          ),
          SizedBox(width: context.theme.d.paddingMedium),
        ],
        SizedBox(width: context.theme.d.paddingSmall),
        _topNavigationItem(
          key: const Key('inboxTab'),
          Tabs.inbox,
          context,
          scaffoldModel,
          selectedIndex,
          customChild: _inboxTabIcon(
            context,
            isSelected: visibleDesktopTabs.indexOf(Tabs.inbox) == selectedIndex,
          ),
        ),
        SizedBox(width: context.theme.d.paddingLarge),
        _topNavigationItem(
          key: const Key('profileTab'),
          Tabs.profile,
          context,
          scaffoldModel,
          selectedIndex,
          customChild: ProfileImageWidget(
            avatarUrl: userProvider.myUser?.avatarUrl,
            size: context.theme.d.profilePhotoRadiusMedium,
          ),
        ),
        const AccountActionMenu(),
      ],
    );
  }

  Widget _topNavigationItem(
    Tabs label,
    BuildContext context,
    ScaffoldModel scaffoldModel,
    int selectedIndex, {
    required Key key,
    Widget? customChild,
    String? title,
    IconData? iconData,
  }) {
    int index = visibleDesktopTabs.indexOf(label);
    bool isSelected = (index == selectedIndex);

    return InkWell(
      hoverColor:
          label == Tabs.profile
              ? context.colorScheme.primary.withValues(alpha: 0.15)
              : context.colorScheme.secondaryContainer,
      borderRadius: BorderRadius.circular(context.theme.d.paddingSmall),
      onTap: () {
        if (index == scaffoldModel.selectedTabIndex &&
            AppRouter.getLastSubroutePath(context) == getRouteName(label)) {
          return;
        }

        if (context.canPop() && (index == scaffoldModel.selectedTabIndex)) {
          context.pop();
          scaffoldModel.setParams(index: index);
          return;
        }
        scaffoldModel.setParams(index: index);
        Provider.of<LocalDataModel>(context, listen: false).lastSelectedTab = index;
        context.go(getRouteName(label));
      },
      child: Padding(
        key: key,
        padding:
            label == Tabs.profile
                ? EdgeInsets.zero
                : EdgeInsets.symmetric(
                  horizontal: context.theme.d.paddingXSmall,
                  vertical: context.theme.d.paddingXxSmall,
                ),
        child:
            customChild ??
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (iconData != null) ...[
                  Icon(
                    iconData,
                    color: isSelected ? context.colorScheme.primary : context.colorScheme.onSurface,
                  ),
                  SizedBox(width: context.theme.d.paddingSmall),
                ],
                Text(
                  '$title',
                  style: context.textTheme.labelLarge?.copyWith(
                    color: isSelected ? context.colorScheme.primary : context.colorScheme.onSurface,
                    fontWeight: isSelected ? FontWeight.w800 : FontWeight.w500,
                    fontSize: context.theme.d.fontSizeMedium18,
                  ),
                ),
              ],
            ),
      ),
    );
  }

  Widget _desktopFooter(BuildContext context) {
    final LocaleModel localeModel = Provider.of<LocaleModel>(context, listen: false);

    return Container(
      height: context.theme.d.footerHeight,
      color: context.colorScheme.onSurfaceVariant,
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingLarge),
      child: Row(
        children: [
          _footerItem(
            context,
            ' ${AppLocale.current.desktopBannerMincromentorName(DateTime.now().year)}  • ',
            onTap: null,
          ),
          _footerItem(
            context,
            ' ${AppLocale.current.accountSettingLegalPrivacyPolicy}  • ',
            onTap: () {
              AppUtility.openLink(
                getLocalizedUrl(urlType: LegalDocumentType.privacyPolicy, localeModel: localeModel),
              );
            },
          ),
          _footerItem(
            context,
            ' ${AppLocale.current.accountSettingLegalTermsOfUse}  • ',
            onTap: () {
              AppUtility.openLink(
                getLocalizedUrl(urlType: LegalDocumentType.termsOfUse, localeModel: localeModel),
              );
            },
          ),
          _footerItem(
            context,
            ' ${AppLocale.current.desktopBannerCodeOfConductLabel}',
            onTap: () {
              AppUtility.openLink(
                getLocalizedUrl(urlType: LegalDocumentType.codeOfConduct, localeModel: localeModel),
              );
            },
          ),
          const Spacer(),
          _footerItem(
            context,
            ' ${AppLocale.current.desktopBannerHelpCenterLabel}',
            onTap: () {
              AppUtility.openLink(
                localeModel.isArabic()
                    ? Identifiers.arabicHelpCenterUrl
                    : Identifiers.helpCenterUrl,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _footerItem(BuildContext context, String label, {Function()? onTap}) {
    Color textColor = context.colorScheme.onPrimary;
    return StatefulBuilder(
      builder: (BuildContext context, StateSetter setState) {
        return InkWell(
          onHover: (value) {
            setState(() {
              textColor =
                  value
                      ? context.colorScheme.onPrimary.withValues(alpha: 0.6)
                      : context.colorScheme.onPrimary;
            });
          },
          onTap: onTap,
          child: Text(
            label,
            style: context.textTheme.bodyLarge?.copyWith(
              color: textColor,
              fontSize: context.theme.d.fontSizeMedium - 1,
            ),
          ),
        );
      },
    );
  }

  Widget _navigationIcon(String iconImage, String tabName, bool isSelected) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: context.theme.d.padding15),
        Image.asset(iconImage, scale: 1.8, height: context.theme.d.tabIconHeight),
        SizedBox(height: context.theme.d.paddingXxSmall),
        Text(
          tabName,
          textAlign: TextAlign.center,
          style:
              isSelected
                  ? context.theme.textTheme.bodySmall!.copyWith(
                    color: context.colorScheme.surfaceBright,
                    fontWeight: FontWeight.w700,
                  )
                  : context.theme.textTheme.bodySmall!.copyWith(
                    color: context.colorScheme.onSurface,
                    fontWeight: FontWeight.w400,
                  ),
        ),
      ],
    );
  }

  _navigationDesktopIcon(String iconImage) {
    return Image.asset(iconImage, scale: 1.8);
  }

  Widget getBottomNavigationTab(Tabs tab) {
    switch (tab) {
      case Tabs.home:
        return NavigationDestination(
          key: const Key('homeTab'),
          selectedIcon: _navigationIcon(Assets.homeSelected, AppLocale.current.navTabHome, true),
          icon: _navigationIcon(Assets.homeDefault, AppLocale.current.navTabHome, false),
          label: '',
        );
      case Tabs.explore:
        return NavigationDestination(
          key: const Key('exploreTab'),
          selectedIcon: _navigationIcon(
            Assets.exploreSelected,
            AppLocale.current.navTabExplore,
            true,
          ),
          icon: _navigationIcon(Assets.exploreDefault, AppLocale.current.navTabExplore, false),
          label: '',
        );
      case Tabs.inbox:
        return NavigationDestination(
          key: const Key('inboxTab'),
          selectedIcon: _inboxTabIcon(context, isSelected: true),
          icon: _inboxTabIcon(context),
          label: '',
        );
      case Tabs.trainings:
        return NavigationDestination(
          key: const Key('trainingsTab'),
          selectedIcon: _navigationIcon(
            Assets.menuSelectedIcon,
            AppLocale.current.navTabTrainings,
            true,
          ),
          icon: _navigationIcon(Assets.menuIcon, AppLocale.current.navTabTrainings, false),
          label: '',
        );

      case Tabs.profile:
        return NavigationDestination(
          key: const Key('profileTab'),
          selectedIcon: _navigationIcon(
            Assets.profileSelected,
            AppLocale.current.navTabProfile,
            true,
          ),
          icon: _navigationIcon(Assets.profileDefault, AppLocale.current.navTabProfile, false),
          label: '',
        );
    }
  }
}
