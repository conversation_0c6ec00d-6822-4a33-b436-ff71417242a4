import 'package:flutter/material.dart';

import '../../../../services/extensions.dart';
import '../../constants/constants.dart';

class ExpertiseChip extends StatelessWidget {
  final String expertise;
  final IconData? icon;
  final ChipBackgroundColor? chipBackgroundColor;

  const ExpertiseChip({
    super.key,
    required this.expertise,
    this.icon,
    this.chipBackgroundColor = ChipBackgroundColor.secondary,
  });

  @override
  Widget build(BuildContext context) {
    var (bgColor, textIconColor) = _getChipBackgroundColor(
      context,
      this.chipBackgroundColor ?? ChipBackgroundColor.secondary,
    );
    Color chipBackgroundColor = bgColor;
    Color iconColor = textIconColor;

    TextStyle? chipTextStyle = context.theme.textTheme.labelMedium?.copyWith(
      color: textIconColor,
      fontSize: context.theme.d.fontSizeSmall,
    );
    return Chip(
      avatar: icon != null ? Icon(icon, color: iconColor) : null,
      label: Text(expertise, style: chipTextStyle, overflow: TextOverflow.ellipsis),
      backgroundColor: chipBackgroundColor,
      side: BorderSide.none,
      visualDensity: const VisualDensity(
        horizontal: VisualDensity.minimumDensity,
        vertical: VisualDensity.minimumDensity,
      ),
      labelPadding:
          icon != null
              ? EdgeInsetsDirectional.only(end: context.theme.d.paddingSmall, top: 2, bottom: 2)
              : EdgeInsets.symmetric(vertical: 2, horizontal: context.theme.d.paddingSmall),
      padding: EdgeInsets.zero,
    );
  }
}

(Color bgColor, Color textIconColor) _getChipBackgroundColor(
  BuildContext context,
  ChipBackgroundColor chipBackgroundColor,
) {
  switch (chipBackgroundColor) {
    case ChipBackgroundColor.primary:
      return (context.theme.colorScheme.inversePrimary, context.theme.colorScheme.shadow);
    case ChipBackgroundColor.secondary:
      return (
        context.theme.colorScheme.secondaryContainer,
        context.theme.colorScheme.onSecondaryContainer,
      );
    case ChipBackgroundColor.tertiary:
      return (context.theme.colorScheme.surfaceBright, context.theme.colorScheme.surface);
  }
}
