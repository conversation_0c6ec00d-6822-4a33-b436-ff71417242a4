import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:mm_flutter_app/models/locale_model.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../constants/parts/micromentor_icons.dart';
import 'shared.dart';

class AutocompleteTextField<T> extends StatelessWidget {
  final TextEditingController textEditingController;
  final String? label;
  final String? hint;
  final bool showDropdown;
  final bool showClose;
  final bool showPrefixSearchIcon;
  final FutureOr<List<T>?> Function(String) suggestionsCallback;
  final Widget Function(BuildContext, T) itemBuilder;
  final Widget Function(BuildContext)? emptyBuilder;
  final String? Function(String?)? validator;
  final String? validatorMessage;
  final Function(TextEditingController)? onClose;
  final Function()? onChange;
  final Function(T)? onSelected;
  final double? scrollableBottomPadding;

  const AutocompleteTextField({
    super.key,
    required this.textEditingController,
    this.label,
    this.hint,
    this.showDropdown = false,
    this.showClose = false,
    this.showPrefixSearchIcon = false,
    required this.itemBuilder,
    required this.suggestionsCallback,
    this.emptyBuilder,
    this.validator,
    this.onClose,
    this.onChange,
    this.onSelected,
    this.validatorMessage,
    this.scrollableBottomPadding,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Stack(
          alignment: Alignment.centerLeft,
          children: [
            TextFormFieldWidget(
              prefixIcon: showPrefixSearchIcon ? const Icon(MicromentorIcons.search) : null,
              label: label,
              suffixIcon: showDropdown ? _dropdownIcon(context) : null,
              readOnly: true,
              contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
            ),
            TypeAheadField(
              controller: textEditingController,
              builder: (context, controller, focusNode) {
                return TextFormField(
                  scrollPadding: EdgeInsets.only(
                    bottom: scrollableBottomPadding ?? context.theme.d.zero,
                  ),
                  controller: controller,
                  focusNode: focusNode,
                  textCapitalization: TextCapitalization.words,
                  textAlign:
                      Provider.of<LocaleModel>(context, listen: false).isArabic()
                          ? TextAlign.right
                          : TextAlign.left,
                  textDirection:
                      Provider.of<LocaleModel>(context, listen: false).isArabic()
                          ? TextDirection.rtl
                          : TextDirection.ltr,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: hint,
                    hintStyle: context.textTheme.bodyMedium?.copyWith(
                      fontStyle: FontStyle.italic,
                      color: context.colorScheme.outline,
                    ),
                    contentPadding: EdgeInsets.only(
                      top: context.theme.d.paddingLarge,
                      left:
                          showPrefixSearchIcon
                              ? context.theme.d.boxSizeSmall
                              : context.theme.d.paddingMedium,
                      right:
                          showPrefixSearchIcon
                              ? context.theme.d.boxSizeSmall
                              : context.theme.d.paddingMedium,
                    ),
                    suffixIconConstraints: BoxConstraints(
                      minWidth: context.theme.d.paddingXxSmall,
                      minHeight: context.theme.d.paddingXxSmall,
                    ),
                    suffixIcon: showClose ? _closeIcon(context, controller) : null,
                  ),
                  onChanged: (_) => onChange?.call(),
                );
              },
              itemBuilder: itemBuilder,
              onSelected: (selectedItem) {
                onSelected?.call(selectedItem);
              },
              emptyBuilder: emptyBuilder,
              constraints: BoxConstraints(maxHeight: context.theme.d.dropdownHeight),
              suggestionsCallback: suggestionsCallback,
            ),
          ],
        ),
        if (validatorMessage != null)
          Padding(
            padding: EdgeInsets.all(context.theme.d.paddingSmall),
            child: Text(
              validatorMessage ?? '',
              style: TextStyle(
                color: context.theme.colorScheme.error,
                fontSize: context.theme.d.fontSizeSmall,
              ),
            ),
          ),
      ],
    );
  }

  _closeIcon(BuildContext context, TextEditingController controller) {
    return GestureDetector(
      child: Padding(
        padding: EdgeInsets.only(
          right: context.theme.d.paddingMedium,
          left: context.theme.d.paddingMedium,
          top: context.theme.d.paddingLarge,
        ),
        child: Icon(
          MicromentorIcons.clear,
          color: context.theme.colorScheme.secondary,
          size: context.theme.d.iconSizeMedium,
        ),
      ),
      onTap: () {
        onClose?.call(controller);
      },
    );
  }

  _dropdownIcon(BuildContext context) {
    return Icon(MicromentorIcons.arrowDropdown, color: context.theme.colorScheme.onSurfaceVariant);
  }
}
