import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/vts_provider.dart';

class NestedListView extends StatelessWidget {
  final List<MyLessonPlanModel> items;

  const NestedListView({super.key, required this.items});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: buildNestedList(context, items),
    );
  }

  List<Widget> buildNestedList(
    BuildContext context,
    List<MyLessonPlanModel> items, {
    double currentIndex = 0,
    double increment = 1,
    int level = 0,
  }) {
    List<Widget> widgets = [];

    for (var item in items) {
      currentIndex += increment;
      final bulletNumber =
          currentIndex.isInteger() ? currentIndex : currentIndex.toStringAsFixed(1);
      widgets.add(
        Padding(
          padding: EdgeInsets.only(
            left: level * context.theme.d.nestedListViewIndent,
            bottom: context.theme.d.paddingXxSmall,
          ), // Indent based on level
          child: Text(
            '$bulletNumber  ${item.title}',
            style: context.textTheme.bodyLarge?.copyWith(color: context.colorScheme.onSurface),
          ),
        ),
      );

      if (item.children != null && item.children.isNotEmpty) {
        widgets.addAll(
          buildNestedList(
            context,
            item.children,
            currentIndex: currentIndex,
            increment: 0.1,
            level: level + 1,
          ),
        );
      }
    }

    return widgets;
  }
}
