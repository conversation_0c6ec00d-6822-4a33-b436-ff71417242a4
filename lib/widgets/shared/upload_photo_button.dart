import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';

import '../../../../services/extensions.dart';
import '../../constants/parts/micromentor_icons.dart';

class UploadPhotoButton extends StatelessWidget {
  const UploadPhotoButton({super.key});

  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
      onPressed: () {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return const UploadPhotoDialog();
          },
        );
      },
      style: ButtonStyle(
        shape: WidgetStatePropertyAll(
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(context.theme.d.paddingSmall)),
        ),
        backgroundColor: WidgetStatePropertyAll(context.theme.colorScheme.secondaryContainer),
        side: const WidgetStatePropertyAll(BorderSide.none),
      ),
      child: SizedBox(
        width: context.theme.d.dropdownHeight,
        height: context.theme.d.dropdownHeight,
        child: Center(
          child: Icon(
            MicromentorIcons.addCircleOutlined,
            color: context.theme.colorScheme.onSecondaryContainer,
          ),
        ),
      ),
    );
  }
}

class UploadPhotoDialog extends StatelessWidget {
  const UploadPhotoDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(context.theme.d.paddingSmall),
      ),
      //converting secondary color to one with 95% brightness below:
      backgroundColor:
          HSLColor.fromColor(context.theme.colorScheme.secondary).withLightness(0.95).toColor(),
      contentPadding: EdgeInsets.zero,
      content: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.only(
              top: context.theme.d.paddingMedium,
              bottom: context.theme.d.paddingSmall,
            ),
            child: OutlinedButton(
              onPressed: () {},
              style: ButtonStyle(
                foregroundColor: WidgetStatePropertyAll(
                  context.theme.colorScheme.onSecondaryContainer,
                ),
                side: const WidgetStatePropertyAll(BorderSide.none),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(MicromentorIcons.imageOutline),
                  SizedBox(width: context.theme.d.paddingSmall),
                  Text(AppLocale.current.profilePhotoUpload),
                ],
              ),
            ),
          ),
          Divider(color: context.theme.colorScheme.outlineVariant),
          Padding(
            padding: EdgeInsets.only(
              bottom: context.theme.d.paddingMedium,
              top: context.theme.d.paddingSmall,
            ),
            child: OutlinedButton(
              onPressed: () {},
              style: ButtonStyle(
                foregroundColor: WidgetStatePropertyAll(
                  context.theme.colorScheme.onSecondaryContainer,
                ),
                side: const WidgetStatePropertyAll(BorderSide.none),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(MicromentorIcons.cameraAltOutlined),
                  SizedBox(width: context.theme.d.paddingSmall),
                  Text(AppLocale.current.profilePhotoTake),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
