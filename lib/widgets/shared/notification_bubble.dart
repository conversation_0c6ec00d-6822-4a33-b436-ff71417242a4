import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../constants/constants.dart';
import '../../services/extensions.dart';
import '../../services/graphql/providers/providers.dart';

class NotificationBubble extends StatelessWidget {
  final BadgeType? badgeType;
  final String? channelId;
  final int? value;
  final Color? bubbleColor;
  final Color? textColor;
  final bool enlarge;
  final bool showEmptyBubble;

  static const double _notificationBubbleHeight = 20.0;
  static const double _notificationBubbleSingleCharWidth = 20.0;
  static const double _notificationBubbleDoubleCharWidth = 24.0;
  static const double _notificationBubbleTripleCharWidth = 28.0;
  static const double _enlargeFactor = 1.2;

  const NotificationBubble({
    super.key,
    this.value,
    this.badgeType,
    this.channelId,
    this.bubbleColor,
    this.textColor,
    this.enlarge = false,
    this.showEmptyBubble = false,
  });

  Widget _buildBubble(int val, BuildContext context) {
    if (val < 1) {
      return const SizedBox();
    }

    double bubbleWidth;
    double bubbleHeight = _notificationBubbleHeight;
    String notificationText;
    if (val > Limits.maxNotificationsDisplayed) {
      bubbleWidth = _notificationBubbleTripleCharWidth;
      notificationText = Identifiers.notificationOverflow;
    } else if (val > 9) {
      bubbleWidth = _notificationBubbleDoubleCharWidth;
      notificationText = val.toString();
    } else {
      bubbleWidth = _notificationBubbleSingleCharWidth;
      notificationText = val.toString();
    }
    TextStyle? textStyle = context.theme.textTheme.labelSmall;
    if (enlarge) {
      textStyle = context.theme.textTheme.labelLarge;
      bubbleWidth *= _enlargeFactor;
      bubbleHeight *= _enlargeFactor;
    }
    return Container(
      width: bubbleWidth,
      height: bubbleHeight,
      decoration: BoxDecoration(
        color: bubbleColor ?? context.theme.colorScheme.error,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          notificationText,
          style: textStyle?.copyWith(color: textColor ?? context.theme.colorScheme.onError),
          maxLines: 1,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (value != null) {
      return _buildBubble(value!, context);
    }
    if (value == null && showEmptyBubble) {
      return _emptyBubble(context);
    }

    return Selector<InboxProvider, int>(
      selector: (_, inboxProvider) {
        if (channelId?.isNotEmpty == true) {
          return inboxProvider.badge.messagesByChannel[channelId!] ?? 0;
        }
        return inboxProvider.badge.valueByType(badgeType ?? BadgeType.all);
      },
      builder: (context, badgeValue, _) => _buildBubble(badgeValue, context),
    );
  }

  Widget _emptyBubble(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: context.theme.d.paddingXxSmall),
      width: context.theme.d.paddingSmall,
      height: context.theme.d.paddingSmall,
      decoration: BoxDecoration(
        color: bubbleColor ?? context.theme.colorScheme.error,
        shape: BoxShape.circle,
      ),
    );
  }
}
