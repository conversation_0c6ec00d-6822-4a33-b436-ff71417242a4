import 'package:flutter/material.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/providers.dart';
import 'package:provider/provider.dart';

import '../../__generated/schema/schema.graphql.dart';
import '../../constants/parts/micromentor_icons.dart';
import '../../models/locale_model.dart';
import '../../services/graphql/providers/base/operation_result.dart';
import '../../utilities/utility.dart';

class LanguageDropdown extends StatefulWidget {
  const LanguageDropdown({super.key});

  @override
  State<LanguageDropdown> createState() => _LanguageDropdownState();
}

class _LanguageDropdownState extends State<LanguageDropdown> {
  Language? _selectedValue;
  List<Language> _options = [];
  late final ContentProvider _contentProvider;
  late LocaleModel localeModel;
  late String deviceLocale;
  Future<OperationResult<AllOptionsByType>>? _findAllOptionsByTypeOperationResult;

  late bool includeL10nText;

  @override
  void initState() {
    localeModel = Provider.of<LocaleModel>(context, listen: false);
    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    deviceLocale = localeModel.getCurrentLanguageCode();

    if ((_contentProvider.languageOptions ?? []).isNotEmpty) {
      _setSelectedLanguageAndOptions();
    }
    super.initState();
  }

  @override
  void didChangeDependencies() {
    if ((_contentProvider.languageOptions ?? []).isEmpty && !_contentProvider.isFetchingData) {
      _findAllOptionsByTypeOperationResult = _contentProvider.findAllOptionsByType(
        fetchPolicy: FetchPolicy.networkOnly,
        fallbackUiLanguage: Enum$UiLanguage.values.byName(deviceLocale),
      );
    }
    includeL10nText =
        context.mediaQuerySize.width >= context.theme.d.layoutBreakpointMediumWidthMin;
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    if (_findAllOptionsByTypeOperationResult == null) {
      return _contentProvider.isFetchingData
          ? Consumer<ContentProvider>(
            builder: (context, newvalue, _) {
              _setSelectedLanguageAndOptions();
              return _body();
            },
          )
          : _body();
    }

    return FutureBuilder<OperationResult<AllOptionsByType>>(
      future: _findAllOptionsByTypeOperationResult!,
      builder: (
        BuildContext context,
        AsyncSnapshot<OperationResult<AllOptionsByType>> optionsSnapshot,
      ) {
        return AppUtility.widgetForAsyncSnapshot(
          snapshot: optionsSnapshot,
          isAppLaunch: true,
          onLoading: () => _loader(),
          onError: () => const SizedBox(),
          onReady: () {
            _setSelectedLanguageAndOptions();
            return _body();
          },
        );
      },
    );
  }

  Widget _body() {
    if (_options.isEmpty || _selectedValue == null) return _refreshWidget();

    return Consumer<ContentProvider>(
      builder: (context, contentProviderObj, _) {
        return PopupMenuButton<Language>(
          padding: EdgeInsets.zero,
          icon: Row(
            children: [
              Icon(
                MicromentorIcons.language,
                color: context.theme.colorScheme.onSurface,
                size: context.theme.d.iconSizeMedium,
              ),
              Visibility(
                visible: includeL10nText,
                child: Padding(
                  padding: EdgeInsets.only(left: context.theme.d.paddingSmall),
                  child: Text(
                    '${_selectedValue?.translatedValue}',
                    style: context.textTheme.bodyLarge,
                  ),
                ),
              ),
              SizedBox(width: context.theme.d.paddingSmall),
              Icon(
                MicromentorIcons.keyboardArrowDown,
                color: context.theme.colorScheme.onSurface,
                size: context.theme.d.iconSizeMedium,
              ),
            ],
          ),
          iconColor: context.colorScheme.primary,
          offset: Offset(context.theme.d.zero, context.theme.d.popupYOffset),
          elevation: context.theme.d.elevationLevel2,
          itemBuilder:
              (BuildContext context) =>
                  _options.map<PopupMenuEntry<Language>>((option) {
                    return PopupMenuItem(
                      padding: EdgeInsets.zero,
                      onTap: () async {
                        if (_selectedValue == option) return;
                        await _onLanguageChange(contentProviderObj, option);
                        _postLanguageChange(contentProviderObj, option);
                      },
                      child: ListTile(
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: context.theme.d.paddingSmall,
                        ),
                        tileColor:
                            _selectedValue?.textId == option.textId
                                ? context.colorScheme.surfaceTint
                                : null,
                        title: Text(
                          // TODO: Temporary workaround: Using hardcoded translations on the frontend
                          // until the backend provides the necessary translations.(Remove once backend provides translation)
                          contentProviderObj.getNativeLanguageTitle(
                            Enum$UiLanguage.values.byName(option.textId),
                          ), // '${option.translatedValue}',
                          style: context.textTheme.bodyLarge,
                        ),
                        leading: Icon(
                          MicromentorIcons.language,
                          color: context.theme.colorScheme.onSurface,
                          size: context.theme.d.iconSizeMedium,
                        ),
                      ),
                    );
                  }).toList(),
        );
      },
    );
  }

  _refreshWidget() {
    return Row(
      children: [
        Icon(
          MicromentorIcons.language,
          color: context.theme.colorScheme.onSurface,
          size: context.theme.d.iconSizeMedium,
        ),
        IconButton(
          onPressed: () {
            setState(() {
              _findAllOptionsByTypeOperationResult = _contentProvider.findAllOptionsByType(
                fetchPolicy: FetchPolicy.networkOnly,
                fallbackUiLanguage: Enum$UiLanguage.values.byName(deviceLocale),
              );
            });
          },
          icon: Icon(MicromentorIcons.refresh, color: context.theme.colorScheme.onSurfaceVariant),
        ),
      ],
    );
  }

  Widget _loader() {
    return Center(
      child: SizedBox(
        width: context.theme.d.iconSizeMedium,
        height: context.theme.d.iconSizeMedium,
        child: CircularProgressIndicator(strokeWidth: context.theme.d.elevationLevel2),
      ),
    );
  }

  void _setSelectedLanguageAndOptions() {
    _options = (_contentProvider.hardCodedUiLanguages(localeModel) ?? []);
    _selectedValue =
        ((_contentProvider.languageOptions ?? []).where((element) {
          return element.textId.trim() == localeModel.getCurrentLanguageCode();
        }).firstOrNull);
  }

  void _postLanguageChange(ContentProvider contentProviderObj, Language option) {
    localeModel.setLanguage(Enum$UiLanguage.values.byName(option.textId));
    _options = (contentProviderObj.hardCodedUiLanguages(localeModel) ?? []);

    _selectedValue =
        (contentProviderObj.languageOptions ?? []).where((element) {
          return element.textId.trim() == option.textId;
        }).firstOrNull;
  }

  _onLanguageChange(ContentProvider contentProviderObj, Language option) async {
    await contentProviderObj.findAllOptionsByType(
      fetchPolicy: FetchPolicy.networkOnly,
      fallbackUiLanguage: Enum$UiLanguage.values.byName(option.textId),
    );
  }
}
