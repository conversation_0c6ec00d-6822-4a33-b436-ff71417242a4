import 'package:flutter/material.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/content_provider.dart';
import 'package:provider/provider.dart';

import 'shared.dart';

class DeclineReason extends StatefulWidget {
  final String? name;
  final Function(Enum$DeclineChannelInvitationReasonTextId) continueAction;
  const DeclineReason({super.key, required this.name, required this.continueAction});

  @override
  State<DeclineReason> createState() => _DeclineReasonState();
}

class _DeclineReasonState extends State<DeclineReason> {
  late final ContentProvider _contentProvider;

  Enum$DeclineChannelInvitationReasonTextId? _choosenReason;

  @override
  void initState() {
    super.initState();
    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
  }

  @override
  Widget build(BuildContext context) {
    return DialogTemplate.withCustomContent(
      title: AppLocale.current.inviteDeclineTitle,
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: context.theme.d.paddingSmall),
          Text(
            AppLocale.current.inviteDeclineSubtitle(widget.name ?? ''),
            style: context.textTheme.bodyMedium!.copyWith(
              color: context.colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: context.theme.d.paddingSmall),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children:
                    (_contentProvider.declineChannelInvitationReasons ?? []).map((reason) {
                      return Column(
                        children: [
                          RadioTileWidget<Enum$DeclineChannelInvitationReasonTextId>(
                            title: reason.translatedValue,
                            value: Enum$DeclineChannelInvitationReasonTextId.values.byName(
                              reason.textId,
                            ),
                            groupValue: _choosenReason,
                            onChanged:
                                (value) => setState(() {
                                  _choosenReason = value;
                                }),
                          ),
                          const Divider(),
                        ],
                      );
                    }).toList(),
              ),
            ),
          ),
        ],
      ),
      actionButtonTitle: AppLocale.current.actionDecline,
      onAction:
          _choosenReason == null
              ? null
              : () {
                widget.continueAction(_choosenReason!);
                Navigator.pop(context);
              },
    );
  }
}
