import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';

import '../../constants/parts/micromentor_icons.dart';

class CustomPopupMenu extends StatelessWidget {
  final List<CustomPopupMenuItem> options;
  const CustomPopupMenu({required this.options, super.key});

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton(
      key: const Key('homeMenu'),
      icon: const Icon(MicromentorIcons.moreVertRounded),
      iconColor: context.colorScheme.primary,
      offset: Offset(-context.theme.d.paddingSmall, context.theme.d.popupYOffset),
      elevation: context.theme.d.elevationLevel2,
      itemBuilder:
          (BuildContext context) =>
              options.map<PopupMenuEntry>((option) {
                return PopupMenuItem(
                  key: option.key,
                  onTap: option.onTap,
                  child: PointerInterceptor(child: menuItem(context, option)),
                );
              }).toList(),
    );
  }

  Widget menuItem(BuildContext context, CustomPopupMenuItem option) {
    return ListTile(
      title: Text(option.title, style: context.textTheme.bodyLarge),
      leading: Icon(
        option.icon,
        color: context.theme.colorScheme.onSurface,
        size: context.theme.d.iconSizeLarge,
      ),
    );
  }
}

class CustomPopupMenuItem {
  Key key;
  String title;
  IconData icon;
  Function()? onTap;

  CustomPopupMenuItem({
    required this.key,
    required this.title,
    required this.icon,
    required this.onTap,
  });
}
