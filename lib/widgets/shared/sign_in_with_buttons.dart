import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';

import '../../__generated/schema/schema.graphql.dart';
import '../../services/graphql/providers/user_provider.dart';
import 'sign_in_with_button.dart';

class SignInWithButtons extends StatelessWidget {
  final bool showLabel;
  final bool divider;
  final bool showDisabledTrackingHelpText;
  final bool showOwnIdentityProvider;
  final Function(Enum$IdentityProvider) onSelectIdentityProvider;

  const SignInWithButtons({
    super.key,
    required this.showLabel,
    this.divider = true,
    this.showDisabledTrackingHelpText = false,
    required this.showOwnIdentityProvider,
    required this.onSelectIdentityProvider,
  });

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    final providers = userProvider.getIdentityProviderConfigs(
      includeOwn: showOwnIdentityProvider,
      excludeInactive: true,
    );
    final lastProvider = providers.last.provider;
    List<Widget> list = [];
    final List<Widget> buttonList =
        providers
            .map(
              (provider) => SignInWithButton(
                divider: divider,
                provider: provider,
                showLabel: showLabel,
                addBottomPadding: provider.provider != lastProvider,
                onPressed: () async {
                  if (!userProvider.localData.dataUseConsent) {
                    bool consentGiven;
                    //checking: it is desktop view or mobile
                    consentGiven =
                        AppUtility.displayDesktopUI(context)
                            ? await context.push(AppRoutes.dataUseConsent.path) as bool
                            : await userProvider.localData.showDataUseConsentModal(context);

                    if (!consentGiven) return;
                  }
                  onSelectIdentityProvider(provider.provider);
                },
                tempFlagForFacebook:
                    showDisabledTrackingHelpText &&
                    provider.provider == Enum$IdentityProvider.facebook,
              ),
            )
            .toList();
    // Also part of the temp Facebook fix, won't need to add caveat copy when we support Limited Login
    list.addAll(buttonList);

    if (!kIsWeb && Platform.isIOS && showDisabledTrackingHelpText) {
      int buttonIndexBeforeAlternativeMethod = showLabel ? list.length - 2 : list.length - 1;
      int facebookButtonIndex = showLabel ? list.length - 3 : list.length - 2;
      Widget facebookButton = list.removeAt(facebookButtonIndex);
      list.insertAll(buttonIndexBeforeAlternativeMethod, [
        facebookButton,
        SizedBox(
          width: context.theme.d.socialSigninButtonSize.width,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
            child: MarkdownBody(
              data: AppLocale.current.identityProviderTrackingCaveat('_'),
              styleSheet: MarkdownStyleSheet.fromTheme(context.theme).copyWith(
                p: context.textTheme.labelLarge?.copyWith(
                  color: context.colorScheme.scrim,
                  fontWeight: FontWeight.w500,
                ),
                a: context.textTheme.labelLarge?.copyWith(
                  color: context.colorScheme.tertiary,
                  fontWeight: FontWeight.w500,
                ),
              ),
              onTapLink: (_, url, __) => openAppSettings(),
            ),
          ),
        ),
      ]);
    }

    return Wrap(
      alignment: WrapAlignment.center,
      runSpacing: context.theme.d.iconSizeMedium,
      spacing: context.theme.d.iconSizeMedium,
      children: list,
    );
  }
}
