import 'package:flutter/material.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/content_provider.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import 'shared.dart';

class DeclineChannelInvitationDialog extends StatefulWidget {
  final String? name;
  final Function(Enum$DeclineChannelInvitationReasonTextId) continueAction;
  const DeclineChannelInvitationDialog({
    super.key,
    required this.name,
    required this.continueAction,
  });

  @override
  State<DeclineChannelInvitationDialog> createState() => _DeclineChannelInvitationDialogState();
}

class _DeclineChannelInvitationDialogState extends State<DeclineChannelInvitationDialog> {
  late final ContentProvider _contentProvider;

  Enum$DeclineChannelInvitationReasonTextId? _choosenReason;
  late bool isDesktop;

  @override
  void initState() {
    super.initState();
    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
  }

  @override
  void didChangeDependencies() {
    isDesktop = AppUtility.displayDesktopUI(context, isChatScreen: true);
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return DialogTemplate.withCustomContent(
      title: AppLocale.current.inviteDeclineTitle,
      isFullWidthButtons: isDesktop ? false : true,
      showTitleInCenter: false,
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: context.theme.d.paddingSmall),
          Text(
            AppLocale.current.inviteDeclineSubtitle(widget.name ?? ''),
            style: context.textTheme.bodyMedium?.copyWith(color: context.colorScheme.onSurface),
          ),
          SizedBox(height: context.theme.d.paddingSmall),
          _wrapper(
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children:
                    (_contentProvider.declineChannelInvitationReasons ?? []).map((reason) {
                      return RadioTileWidget<Enum$DeclineChannelInvitationReasonTextId>(
                        title: reason.translatedValue,
                        value: Enum$DeclineChannelInvitationReasonTextId.values.byName(
                          reason.textId,
                        ),
                        groupValue: _choosenReason,
                        onChanged: (value) {
                          setState(() {
                            _choosenReason = value;
                          });
                        },
                      );
                    }).toList(),
              ),
            ),
          ),
        ],
      ),
      actionButtonTitle: AppLocale.current.actionDecline,
      onAction:
          _choosenReason == null
              ? null
              : () {
                widget.continueAction.call(_choosenReason!);
                Navigator.pop(context);
              },
    );
  }

  _wrapper({required SingleChildScrollView child}) {
    return isDesktop ? Flexible(child: child) : Expanded(child: child);
  }
}
