import 'package:another_flushbar/flushbar.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/constants/parts/micromentor_icons.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/models/scaffold_model.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/loading/loading_provider.dart';
import 'package:provider/provider.dart';

import '../../services/graphql/providers/providers.dart';
import '../../utilities/debug_logger.dart';
import '../../utilities/errors/errors.dart';
import '../../utilities/utility.dart';
import '../widgets.dart';

class UserActionPopupMenu extends StatefulWidget {
  final bool includeArchiveOption;
  final bool includeUnarchiveOption;
  final bool includeBlockUserOption;
  final bool includeReportOption;
  final String userFullName;
  final String userId;
  final String? channelId;
  final Function? onArchiveUnarchiveAction;
  final Function? onBlockUnblockAction;

  const UserActionPopupMenu({
    super.key,
    this.includeArchiveOption = false,
    this.includeUnarchiveOption = false,
    this.includeBlockUserOption = false,
    this.includeReportOption = true,
    required this.userFullName,
    required this.userId,
    this.channelId,
    this.onArchiveUnarchiveAction,
    this.onBlockUnblockAction,
  });

  @override
  State<UserActionPopupMenu> createState() => _UserActionPopupMenuState();
}

class _UserActionPopupMenuState extends State<UserActionPopupMenu> {
  late final UserProvider _userProvider;
  late final ChannelsProvider _channelsProvider;
  late bool _isBlocked;
  late List<String> blockedUsersForMe;
  late bool isDesktop;
  late final ScaffoldModel _scaffoldModel;

  GoRouter get _router => GoRouter.of(context);

  @override
  void initState() {
    super.initState();

    _channelsProvider = Provider.of<ChannelsProvider>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _scaffoldModel = Provider.of<ScaffoldModel>(context, listen: false);

    if (widget.includeBlockUserOption) {
      _isBlocked = AppUtility.isUserBlocked(_userProvider.myUser, widget.userId);
    }
  }

  @override
  void didChangeDependencies() {
    isDesktop = AppUtility.displayDesktopUI(context, isChatScreen: true);
    super.didChangeDependencies();
  }

  Future<void> _archiveChannel() async {
    await _channelsProvider.archiveChannelForMe(
      channelId: widget.channelId!,
      loadChannels: !isDesktop,
    );
    (isDesktop) ? widget.onArchiveUnarchiveAction?.call() : _router.pop();
  }

  _blockOrUnblockUser(BuildContext context) async {
    try {
      Loader.show(context);
      _isBlocked
          ? await _userProvider.unblockUser(userId: widget.userId)
          : await _userProvider.blockUser(userId: widget.userId);

      // Getting an updated blocked users list:
      await _userProvider.findUserById(userId: _userProvider.myUser?.id);

      if (context.mounted) {
        Loader.hide(context);
      }
      _showMsgOnSnackbar(
        _isBlocked
            ? AppLocale.current.userUnblockSuccessMsg(widget.userFullName)
            : AppLocale.current.userBlockSuccessMsg(widget.userFullName),
      );
    } catch (error) {
      DebugLogger.error('Error while blocking/unblocking user: $error');

      _showMsgOnSnackbar(
        _isBlocked
            ? AppLocale.current.userUnblockErrorMsg(widget.userFullName)
            : AppLocale.current.userBlockErrorMsg(widget.userFullName),
      );
    } finally {
      if (context.mounted) {
        Loader.hide(context);
      }
    }
  }

  Future<void> _unarchiveChannel() async {
    await _channelsProvider.unarchiveChannelForMe(
      channelId: widget.channelId!,
      loadChannels: !isDesktop,
    );
    (isDesktop) ? widget.onArchiveUnarchiveAction?.call() : _router.pop();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.includeArchiveOption && widget.includeUnarchiveOption) {
      throw UnexpectedStateError(
        message: 'Cannot show options to archive and unarchive simultaneously',
      );
    }
    if ((widget.includeArchiveOption || widget.includeUnarchiveOption) &&
        widget.channelId == null) {
      throw UnexpectedStateError(message: 'ChannelId needed to archive or unarchive chat');
    }

    return Consumer<UserProvider>(
      builder: (context, newUserProvider, _) {
        _isBlocked = AppUtility.isUserBlocked(newUserProvider.myUser, widget.userId);
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall),
          child: CustomPopupMenu(
            options: [
              if (widget.includeArchiveOption)
                CustomPopupMenuItem(
                  key: const Key('archiveChatMenuItem'),
                  title: AppLocale.current.userOverflowActionArchive,
                  icon: MicromentorIcons.archiveOutlined,
                  onTap: () => _archiveChannel(),
                ),
              if (widget.includeUnarchiveOption)
                CustomPopupMenuItem(
                  key: const Key('unarchiveChatMenuItem'),
                  title: AppLocale.current.userOverflowActionUnarchive,
                  icon: MicromentorIcons.unarchiveOutlined,
                  onTap: () => _unarchiveChannel(),
                ),
              if (widget.includeBlockUserOption)
                CustomPopupMenuItem(
                  key: Key(_isBlocked ? 'unblockUser' : 'blockUser'),
                  title:
                      _isBlocked
                          ? AppLocale.current.userOverflowActionUnblockUser
                          : AppLocale.current.userOverflowActionBlock,
                  icon: MicromentorIcons.block,
                  onTap: () {
                    if (_scaffoldModel.scaffoldKey.currentContext == null) return;
                    showDialog(
                      context: _scaffoldModel.scaffoldKey.currentContext!,
                      builder: (dialogContext) {
                        return BlockUnblockUserDialog(
                          userId: widget.userId,
                          userFullName: widget.userFullName,
                          isItUnblockAction: _isBlocked,
                          performAction: () async {
                            await _blockOrUnblockUser(_scaffoldModel.scaffoldKey.currentContext!);
                            widget.onBlockUnblockAction?.call();
                          },
                        );
                      },
                    );
                  },
                ),
              if (widget.includeReportOption)
                CustomPopupMenuItem(
                  key: const Key('reportUserMenuItem'),
                  title: AppLocale.current.userOverflowActionReport,
                  icon: MicromentorIcons.warningAmberRounded,
                  onTap: () {
                    if (_scaffoldModel.scaffoldKey.currentContext == null) return;
                    showDialog(
                      context: _scaffoldModel.scaffoldKey.currentContext!,
                      builder:
                          (context) => ReportUserDialog(
                            userId: widget.userId,
                            userFullName: widget.userFullName,
                          ),
                    );
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  void _showMsgOnSnackbar(String msg) {
    if (_scaffoldModel.scaffoldKey.currentContext == null) return;
    final context = _scaffoldModel.scaffoldKey.currentContext!;
    Flushbar(
      message: msg,
      messageColor: context.theme.colorScheme.surface,
      messageSize: context.theme.d.fontSizeMedium,
      duration: AppUtility.snackBarDuration,
    ).show(context);
  }
}
