import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../services/extensions.dart';
import '../../constants/constants.dart';
import '../features/featured_widgets.dart';

class ProfileQuickViewCard extends StatelessWidget {
  static int maxExpertiseChips = 3;
  final RecipientUser? recipient;
  final SenderUser? sender;

  const ProfileQuickViewCard({super.key, this.recipient, this.sender});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.all(context.theme.d.paddingLarge),
      color: context.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      elevation: context.theme.d.elevationLevel0,
      child: InkWell(
        onTap: () {
          GoRouter.of(context).replaceNamed(
            AppRoutes.profileId.name,
            pathParameters: {RouteParams.userId: (recipient?.id ?? sender?.id) ?? ''},
          );
        },
        child: Ink(
          decoration: ShapeDecoration(
            color: context.colorScheme.onPrimary,
            shape: RoundedRectangleBorder(
              side: BorderSide(
                width: context.theme.d.borderWidthRegular,
                color: context.colorScheme.outlineVariant,
              ),
              borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
            ),
          ),
          child: Padding(
            padding: EdgeInsets.all(context.theme.d.paddingMedium),
            child: UserProfileBasicDetails(recipient: recipient, sender: sender),
          ),
        ),
      ),
    );
  }
}
