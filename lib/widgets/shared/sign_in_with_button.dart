import 'package:flutter/material.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';

import '../../../../services/extensions.dart';
import '../../constants/constants.dart';

class SignInWithButton extends StatelessWidget {
  final VoidCallback onPressed;
  final IdentityProviderConfig provider;
  final bool showLabel;
  final bool divider;
  final bool addBottomPadding;
  final bool tempFlagForFacebook;

  // Until we have support for Limited Login via `flutter_facebook_auth`, we
  // are going to disable the buttons if the user has asked us not to track.
  // * https://github.com/darwin-morocho/flutter-facebook-auth/issues/397#issuecomment-**********

  const SignInWithButton({
    super.key,
    required this.provider,
    required this.onPressed,
    this.showLabel = false,
    this.tempFlagForFacebook = false,
    this.divider = true,
    this.addBottomPadding = true,
  });

  @override
  Widget build(BuildContext context) {
    final icon =
        provider.iconData != null
            ? Icon(
              provider.iconData,
              color: context.theme.colorScheme.onPrimaryContainer,
              size: context.theme.d.iconSizeLarge,
            )
            : Image.asset(
              provider.iconAsset!,
              width: context.theme.d.iconSizeLarge,
              height: context.theme.d.iconSizeLarge,
            );

    bool tempDisableFacebook =
        provider.provider == Enum$IdentityProvider.facebook && tempFlagForFacebook;
    return Column(
      children: [
        if (!addBottomPadding && divider)
          Padding(
            padding: EdgeInsets.only(
              bottom: context.theme.d.paddingMedium,
              left: context.theme.d.paddingMedium,
              right: context.theme.d.paddingMedium,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Expanded(child: Divider()),
                SizedBox(width: context.theme.d.paddingMedium),
                Text(
                  AppLocale.current.or,
                  style: context.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
                ),
                SizedBox(width: context.theme.d.paddingMedium),
                const Expanded(child: Divider()),
              ],
            ),
          ),
        SizedBox(
          height: context.theme.d.boxSizeSmall,
          child: OutlinedButton(
            style: OutlinedButton.styleFrom(
              fixedSize: (showLabel) ? context.theme.d.socialSigninButtonSize : null,
              backgroundColor: tempDisableFacebook ? Colors.grey[300] : null,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusSmall),
              ),
              padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall),
              side: BorderSide(color: context.theme.colorScheme.outlineVariant),
            ),
            onPressed: tempDisableFacebook ? null : onPressed,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                icon,
                if (showLabel) SizedBox(width: context.theme.d.paddingSmall),
                if (showLabel)
                  Text(
                    provider.label,
                    style: context.theme.textTheme.titleMedium?.copyWith(
                      color: context.theme.colorScheme.onPrimaryContainer,
                    ),
                  ),
              ],
            ),
          ),
        ),
        // if (addBottomPadding) SizedBox(height: context.theme.d.paddingMedium),
      ],
    );
  }
}
