// can't import dart:html on mobile, see https://dart.dev/tools/linter-rules/avoid_web_libraries_in_flutter
// import 'dart:html';
// instead, we could either use universal_html, or implement a dummy PlatformViewRegistry for non-web platforms.
// To avoid the extra dependency, we implemented a dummy PlatformViewRegistry in register_webview_non_web_impl.dart
import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';

import 'register_webview.dart';

class WebEmbedWidget extends StatelessWidget {
  final String targetUrl;
  final String webviewTitle;

  const WebEmbedWidget({super.key, required this.targetUrl, required this.webviewTitle});

  @override
  Widget build(BuildContext context) {
    // The below code only works on web.
    // alternatively, we could use a package which exposes a custom dummy PlatformViewRegistry that does nothing
    // on all platforms, and use the real PlatformViewRegistry on web.
    // example implementation: https://github.com/linagora/tmail-flutter/blob/739751904812b6a9b9f3e690a0fa4290bb11e014/core/lib/presentation/utils/shims/dart_ui.dart

    registerWebViewFactory(targetUrl: targetUrl);

    return Scaffold(
      appBar: AppBar(title: Text(webviewTitle)),
      body: SizedBox(
        width: context.mediaQuerySize.width,
        height: context.mediaQuerySize.height,
        child: HtmlElementView(viewType: targetUrl),
      ),
    );
  }
}
