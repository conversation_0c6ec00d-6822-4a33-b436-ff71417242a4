import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';

import '../../../../services/extensions.dart';
import '../../constants/parts/micromentor_icons.dart';

class ExpandableContainer extends StatefulWidget {
  final String title;
  final String description;
  final int maxLine;

  const ExpandableContainer({
    super.key,
    required this.title,
    required this.description,
    this.maxLine = 5,
  });

  @override
  State<ExpandableContainer> createState() => _ExpandableContainerState();
}

class _ExpandableContainerState extends State<ExpandableContainer> {
  bool _isExpanded = false;
  bool isTextOverflow = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.title,
          style: context.theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w700,
            color: context.colorScheme.surfaceBright,
          ),
        ),
        SizedBox(height: context.theme.d.paddingSmall),
        LayoutBuilder(
          builder: (context, constraints) {
            final textSpan = TextSpan(
              text: widget.description,
              style: TextStyle(fontSize: context.theme.d.paddingMedium),
            );
            final textPainter = TextPainter(
              text: textSpan,
              maxLines: widget.maxLine,
              textDirection: TextDirection.ltr,
            );
            textPainter.layout(maxWidth: constraints.maxWidth);

            WidgetsBinding.instance.addPostFrameCallback(
              (_) => setState(() {
                isTextOverflow = textPainter.didExceedMaxLines;
              }),
            );

            return AnimatedContainer(
              duration: Duration.zero,
              curve: Curves.easeInOut,
              child: Text(
                widget.description,
                style: context.theme.textTheme.bodyMedium,
                overflow: _isExpanded ? null : TextOverflow.ellipsis,
                maxLines: _isExpanded ? null : widget.maxLine,
              ),
            );
          },
        ),
        SizedBox(height: context.theme.d.paddingSmall),
        if (isTextOverflow)
          GestureDetector(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            child: Row(
              children: [
                Text(
                  _isExpanded
                      ? AppLocale.current.expandableContainerViewLess
                      : AppLocale.current.expandableContainerViewMore,
                  style: context.theme.textTheme.labelLarge?.copyWith(
                    color: context.theme.colorScheme.tertiary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Icon(
                  _isExpanded
                      ? MicromentorIcons.keyboardArrowUp
                      : MicromentorIcons.keyboardArrowDown,
                  color: context.theme.colorScheme.tertiary,
                ),
              ],
            ),
          ),
      ],
    );
  }
}
