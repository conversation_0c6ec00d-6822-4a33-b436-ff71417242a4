import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';

import '../../constants/constants.dart';
import '../../utilities/utility.dart';
import 'shared.dart';

class AppOnboardingTemplate extends StatelessWidget {
  final Widget body;
  final GlobalKey<FormState>? formKey;
  final Function? onBack;
  final bool showBackButton;
  final bool? resizeToAvoidBottomInset;

  const AppOnboardingTemplate({
    required this.body,
    this.formKey,
    this.onBack,
    this.showBackButton = false,
    this.resizeToAvoidBottomInset,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (!AppUtility.displayDesktopUI(context)) {
      return _mobileView(context);
    }
    return _desktopView(context);
  }

  Widget _mobileView(BuildContext context) {
    return Scaffold(
      key: formKey,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      appBar:
          showBackButton
              ? AppBar(
                centerTitle: false,
                leading: backButton(context, onPressed: () => onBack?.call()),
              )
              // An empty AppBar is added to prevent the background content from appearing beneath it during scrolling.
              : AppBar(toolbarHeight: 0),
      body: body,
    );
  }

  Widget _desktopView(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(context.theme.d.paddingMedium),
      width: double.infinity,
      height: double.infinity,
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: const Alignment(0.06, -1.00),
          end: const Alignment(-0.06, 1),
          colors: [context.colorScheme.primary, context.colorScheme.secondary],
        ),
      ),
      child: Stack(
        alignment: Alignment.topLeft,
        children: [
          Padding(
            padding: EdgeInsets.only(
              left: context.theme.d.paddingMedium,
              right: context.theme.d.paddingMedium,
              top: context.theme.d.paddingMedium,
            ),
            child: Image.asset(
              Assets.whiteMicromentorDesktopLogoWithName,
              height: context.theme.d.iconSizeXLarge,
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: Container(
                  constraints: BoxConstraints(maxWidth: context.theme.d.maxDesktopAppWidth),
                  padding: EdgeInsets.only(
                    top: context.theme.d.boxSizeMedium,
                    bottom: context.theme.d.paddingSmall,
                  ),
                  child: Material(
                    color: context.colorScheme.surface,
                    borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXLarge),
                    clipBehavior: Clip.hardEdge,
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            color: context.colorScheme.surface,
                            height: context.theme.d.paddingSmall,
                          ),
                          if (showBackButton) ...[
                            Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal: context.theme.d.paddingSmallMedium,
                              ),
                              child: Row(
                                children: [
                                  backButton(
                                    context,
                                    onPressed: () => onBack?.call(),
                                    color: context.colorScheme.onSurface,
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: context.theme.d.paddingSmall),
                          ],
                          Flexible(child: body),
                          Container(
                            color: context.colorScheme.surface,
                            height: context.theme.d.paddingSmall,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
