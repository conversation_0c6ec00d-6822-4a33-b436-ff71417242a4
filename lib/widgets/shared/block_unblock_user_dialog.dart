import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';

import 'common_dialog_template.dart';

class BlockUnblockUserDialog extends StatelessWidget {
  final String userFullName;
  final String userId;
  final bool isItUnblockAction;

  final Function() performAction;

  const BlockUnblockUserDialog({
    required this.userId,
    required this.userFullName,
    required this.isItUnblockAction,
    required this.performAction,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    String title =
        isItUnblockAction
            ? AppLocale.current.userUnblockTitle(userFullName)
            : AppLocale.current.userBlockTitle(userFullName);
    String description =
        isItUnblockAction
            ? AppLocale.current.userUnblockSubtitle(userFullName)
            : AppLocale.current.userBlockSubtitle(userFullName);

    return DialogTemplate(
      title: title,
      description: description,
      actionButtonTitle:
          isItUnblockAction ? AppLocale.current.actionUnblock : AppLocale.current.actionBlock,
      isFullWidthButtons: false,
      showTitleInCenter: false,
      onAction: () {
        Navigator.pop(context);
        performAction.call();
      },
    );
  }
}
