import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:phone_numbers_parser/phone_numbers_parser.dart';

import '../../../services/graphql/providers/providers.dart';

class PhoneNumberInputInfo {
  final Country? country;
  final String? phoneNumber;
  final bool isValid;

  PhoneNumberInputInfo({required this.country, required this.phoneNumber, required this.isValid});

  factory PhoneNumberInputInfo.fromInternationalNumber(
    String? internationalPhoneNumber,
    List<Country> countries,
    bool isValid,
  ) {
    if (internationalPhoneNumber?.isNotEmpty != true) {
      return PhoneNumberInputInfo(country: null, phoneNumber: null, isValid: false);
    }
    PhoneNumber phoneNumber = PhoneNumber.parse(internationalPhoneNumber!);
    final country = countries.firstWhere((c) => c.phoneCode == phoneNumber.countryCode);

    return PhoneNumberInputInfo(country: country, phoneNumber: phoneNumber.nsn, isValid: isValid);
  }

  bool get isEmpty => country == null || phoneNumber?.isNotEmpty != true;
  bool get isNotEmpty => country != null && phoneNumber?.isNotEmpty == true;

  String get internationalNumber {
    if (country == null || phoneNumber?.isNotEmpty != true) {
      return '';
    }
    return AppUtility.formatPhoneNumber(country!.phoneCode, phoneNumber);
  }

  static bool checkIsValid(
    String? countryCode,
    String? phoneNumber,
    String? Function(String?) validator,
  ) {
    if (countryCode?.isNotEmpty != true || phoneNumber?.isNotEmpty != true) {
      return false;
    }
    return validator(AppUtility.formatPhoneNumber(countryCode, phoneNumber)) == null;
  }
}
