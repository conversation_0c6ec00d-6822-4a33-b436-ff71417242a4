import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/content_provider.dart';
import 'package:mm_flutter_app/services/graphql/providers/user_provider.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../../constants/parts/micromentor_icons.dart';
import '../shared.dart';

class PhoneNumberForm extends StatefulWidget {
  final PhoneNumberInputInfo? value;
  final bool checkIfAvailable;
  final bool isOptional;
  final Function(PhoneNumberInputInfo) onChanged;
  final Function? onFieldSubmitted;

  const PhoneNumberForm({
    super.key,
    required this.value,
    required this.checkIfAvailable,
    this.isOptional = false,
    required this.onChanged,
    this.onFieldSubmitted,
  });

  @override
  State<PhoneNumberForm> createState() => _PhoneNumberFormState();
}

class _PhoneNumberFormState extends State<PhoneNumberForm> {
  late final ContentProvider _contentProvider;
  late final UserProvider _userProvider;
  late final TextEditingController _countryCodeController;
  late final TextEditingController _phoneNumberController;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late Country? _selectedCountry;
  late bool _processing;
  String? errorText;

  @override
  void initState() {
    super.initState();
    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _selectedCountry = null;
    _processing = false;

    if (widget.value?.isNotEmpty == true) {
      try {
        // todo: This does not work well when multiple countries share the same `phoneCode`.
        // Temporary set US for country code 1
        if (widget.value?.country?.phoneCode == '1') {
          _selectedCountry =
              _contentProvider.countryOptions?.where((c) => c.textId == 'US').firstOrNull;
        } else {
          _selectedCountry =
              _contentProvider.countryOptions
                  ?.where((c) => c.phoneCode == widget.value?.country?.phoneCode)
                  .firstOrNull;
        }

        _phoneNumberController = TextEditingController(text: widget.value?.phoneNumber);
      } catch (_) {
        _phoneNumberController = TextEditingController();
      }
    } else {
      _phoneNumberController = TextEditingController();
    }

    _selectedCountry ??=
        _contentProvider.countryOptions
            ?.where((c) => c.textId == _userProvider.myUser?.countryOfResidenceTextId)
            .firstOrNull;
    final countryCode =
        _selectedCountry == null
            ? ''
            : '+${_selectedCountry?.phoneCode} - ${_selectedCountry?.translatedValue}';
    _countryCodeController = TextEditingController(text: countryCode);
  }

  @override
  void dispose() {
    try {
      _phoneNumberController.dispose();
      super.dispose();
    } catch (_) {}
  }

  Future<bool> _checkAvailability(String countryCode, String phoneNumber) async {
    String fullNumber = AppUtility.formatPhoneNumber(countryCode, phoneNumber);
    return await _userProvider.isUserIdentAvailable(
      text: fullNumber,
      identType: Enum$UserIdentType.phoneNumber,
    );
  }

  String? validator(String? value) {
    return context.theme.validator.validatePhoneNumber(_selectedCountry?.phoneCode, value);
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _countryWidget(),
          SizedBox(height: context.theme.d.paddingMedium),
          _phoneNumberWidget(),
          SizedBox(height: context.theme.d.paddingSmall),
          if (errorText != null) ...[
            SizedBox(width: context.theme.d.paddingXLarge),
            Text(
              errorText ?? '',
              style: context.theme.textTheme.labelSmall?.copyWith(
                color: context.colorScheme.error,
                overflow: TextOverflow.clip,
              ),
            ),
            Center(
              child: Padding(
                padding: EdgeInsets.only(top: context.theme.d.paddingMedium),
                child: SizedBox(
                  width: context.theme.d.iconSizeMedium,
                  height: context.theme.d.iconSizeMedium,
                  child: _processing ? const CircularProgressIndicator() : null,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  _countryWidget() {
    return SizedBox(
      // width: context.theme.d.countryCodeWidth,
      child: AutocompleteTextField<Country>(
        textEditingController: _countryCodeController,
        label:
            widget.isOptional
                ? AppLocale.current.countryCodeOptional
                : AppLocale.current.phoneNumberInputCountryLabel,
        hint: AppLocale.current.countryCodeInputHint,
        showDropdown: true,
        itemBuilder: (context, country) {
          return ListTile(title: Text('+${country.phoneCode} - ${country.translatedValue}'));
        },
        suggestionsCallback: (String search) {
          final textToSearch = search.replaceAll('+', '').trim().toLowerCase();
          return _getCountries().where((country) {
            if (search.length < 3) {
              return ('${country.phoneCode} - ${country.translatedValue}').toLowerCase().contains(
                    textToSearch,
                  ) ==
                  true;
            }
            String? countryName =
                search.length == 3
                    ? country.translatedValue?.substring(0, 3)
                    : country.translatedValue;

            return ('${country.phoneCode} - $countryName').toLowerCase().contains(textToSearch) ==
                true;
          }).toList();
        },
        validator: (value) {
          setState(() {
            errorText = validator(value);
          });
          return null;
        },
        onChange: () {
          _selectedCountry = null;
          _onContentChange();
        },
        onSelected: (country) {
          _countryCodeController.text = '+${country.phoneCode} - ${country.translatedValue}';
          _selectedCountry = country;
          _onContentChange();
        },
      ),
    );
  }

  List<Country> _getCountries() {
    if (_contentProvider.countryOptions == null ||
        _contentProvider.countryOptions?.isNotEmpty != true) {
      return [];
    }
    _contentProvider.countryOptions?.sort(
      (Country a, Country b) => a.translatedValue!.compareTo(b.translatedValue!),
    );
    return _contentProvider.countryOptions ?? [];
  }

  _phoneNumberWidget() {
    return TextFormFieldWidget(
      keyboardType: TextInputType.phone,
      inputFormatters: <TextInputFormatter>[FilteringTextInputFormatter.digitsOnly],
      label:
          widget.isOptional
              ? AppLocale.current.phoneNumberOptional
              : AppLocale.current.phoneNumberInputLabel,
      hint: AppLocale.current.phoneNumberInputHint,
      contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
      hintStyle: context.textTheme.bodyMedium?.copyWith(
        fontStyle: FontStyle.italic,
        color: context.colorScheme.outline,
      ),
      textController: _phoneNumberController,
      onChanged: (_) {
        _onContentChange();
      },
      focusedBorderColor: (errorText != null) ? context.colorScheme.error : null,
      suffixIcon:
          (_phoneNumberController.text.isNotEmpty)
              ? (errorText != null)
                  ? Icon(MicromentorIcons.warningRounded, color: context.colorScheme.error)
                  : Icon(MicromentorIcons.checkCircle, color: context.colorScheme.primary)
              : null,
      validator: (value) {
        setState(() {
          errorText = validator(value);
        });
        return null;
      },
    );
  }

  _onContentChange() async {
    _formKey.currentState?.validate();
    if (errorText != null) {
      widget.onChanged(
        PhoneNumberInputInfo(
          country: _selectedCountry,
          phoneNumber: _phoneNumberController.text.trim(),
          isValid: false,
        ),
      );
      return;
    }

    setState(() {
      _processing = true;
    });
    final available = await _checkAvailability(
      _selectedCountry?.phoneCode ?? '',
      _phoneNumberController.text,
    );

    setState(() {
      _processing = false;
      if (!available) {
        errorText = AppLocale.current.validationPhoneNumberNotAvailable;
      }
    });

    widget.onChanged(
      PhoneNumberInputInfo(
        country: _selectedCountry,
        phoneNumber: _phoneNumberController.text.trim(),
        isValid: errorText == null,
      ),
    );
  }
}
