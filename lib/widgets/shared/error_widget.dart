import 'package:flutter/material.dart';

import '../../../../services/extensions.dart';
import '../../constants/parts/micromentor_icons.dart';

class CustomErrorWidget extends StatelessWidget {
  const CustomErrorWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Icon(
        MicromentorIcons.error,
        size: context.theme.d.imageSizeSmall,
        color: context.theme.colorScheme.error,
      ),
    );
  }
}
