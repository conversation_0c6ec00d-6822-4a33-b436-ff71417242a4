import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../constants/parts/micromentor_icons.dart';
import '../../models/locale_model.dart';

Widget backButton(BuildContext context, {required VoidCallback? onPressed, Color? color}) {
  final isArabic = context.read<LocaleModel>().isArabic();

  IconData iconData;
  if (kIsWeb) {
    iconData = isArabic ? MicromentorIcons.arrowForwardRounded : MicromentorIcons.arrowBackRounded;
  } else if (AppUtility.isApplePlatform()) {
    iconData =
        isArabic ? MicromentorIcons.arrowForwardIosRounded : MicromentorIcons.arrowBackIosRounded;
  } else {
    iconData = isArabic ? MicromentorIcons.arrowForwardRounded : MicromentorIcons.arrowBackRounded;
  }

  return IconButton(onPressed: onPressed, icon: Icon(iconData, color: color));
}
