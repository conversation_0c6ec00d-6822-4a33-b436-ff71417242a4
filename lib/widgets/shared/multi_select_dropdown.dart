import 'package:flutter/material.dart';
import 'package:textfield_tags/textfield_tags.dart';

import '../../../../services/extensions.dart';
import '../../constants/parts/micromentor_icons.dart';

class MultiSelectDropdown extends StatelessWidget {
  final StringTagController tagsController;
  final String? label;
  final String? hint;
  final bool editable;
  final int? maxSelection;
  final List<String> options;
  final String Function(String)? optionsTranslations;
  final Set<String>? selectedOptions;
  final bool singleSelect;
  final String? notFoundMessage;
  final Function? onChange;
  final Function(double)? onDropdownSizeChanged;
  final String? requiredValueError;

  const MultiSelectDropdown({
    super.key,
    this.label,
    this.hint,
    this.maxSelection,
    this.onChange,
    required this.tagsController,
    required this.options,
    this.optionsTranslations,
    this.editable = false,
    this.selectedOptions,
    this.singleSelect = false,
    this.notFoundMessage,
    this.onDropdownSizeChanged,
    this.requiredValueError,
  });
  @override
  Widget build(BuildContext context) {
    return CustomDropdown(
      label: label,
      hint: hint,
      options: options.toList(),
      optionsTranslations: optionsTranslations ?? (s) => s,
      selectedOptions: selectedOptions?.toList() ?? [],
      maxSelection: maxSelection,
      tagsController: tagsController,
      singleSelect: (singleSelect || maxSelection == 1),
      onChange: onChange,
      editable: editable,
      notResultFoundOption: notFoundMessage,
      requiredValueError: requiredValueError,
      onDropdownSizeChanged: onDropdownSizeChanged,
    );
  }
}

class CustomDropdown extends StatefulWidget {
  final String? label;
  final String? hint;
  final List<String> options;
  final String Function(String) optionsTranslations;
  final Function? onChange;
  final List<String> selectedOptions;
  final int? maxSelection;
  final bool singleSelect;
  final bool editable;
  final String? notResultFoundOption;
  final Function(double)? onDropdownSizeChanged;
  final String? requiredValueError;
  final StringTagController tagsController;

  const CustomDropdown({
    super.key,
    this.label,
    this.hint,
    required this.options,
    required this.selectedOptions,
    this.maxSelection,
    this.onChange,
    this.singleSelect = false,
    required this.tagsController,
    required this.optionsTranslations,
    this.editable = false,
    this.notResultFoundOption,
    this.onDropdownSizeChanged,
    this.requiredValueError,
  });

  @override
  State<CustomDropdown> createState() => _CustomDropdownState();
}

class _CustomDropdownState extends State<CustomDropdown> {
  late LayerLink _layerLink;
  late bool _isDropdownVisible;
  late double _dropdownMaxWidth;
  late TextEditingController _textEditingController;
  late FocusNode _textFieldFocusNode;
  late ValueNotifier<List<String>> _filteredOptionNotifier;
  OverlayEntry? _dropdownOverlay;
  GlobalKey dropdownKey = GlobalKey();
  late ScrollController _scrollController;

  @override
  void initState() {
    _layerLink = LayerLink();
    _isDropdownVisible = false;
    _textEditingController = TextEditingController();
    _textFieldFocusNode = FocusNode();
    _filteredOptionNotifier = ValueNotifier<List<String>>(widget.options);
    _scrollController = ScrollController();

    super.initState();
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    _dropdownOverlay?.dispose();
    _filteredOptionNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        _dropdownMaxWidth = constraints.maxWidth;
        return CompositedTransformTarget(
          link: _layerLink,
          child: TextFieldTags<String>(
            textEditingController: _textEditingController,
            focusNode: _textFieldFocusNode,
            textfieldTagsController: widget.tagsController,
            initialTags: widget.selectedOptions,
            inputFieldBuilder: (context, inputFieldValues) {
              bool shouldShowError =
                  (inputFieldValues.tags.isEmpty &&
                      _textEditingController.text.isEmpty &&
                      widget.requiredValueError != null);
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.label ?? '',
                    style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
                  ),
                  SizedBox(height: context.theme.d.paddingSmall),
                  TextField(
                    focusNode: inputFieldValues.focusNode,
                    scrollPadding: EdgeInsets.only(bottom: context.theme.d.dropdownHeight * 1.2),
                    controller: inputFieldValues.textEditingController,
                    textCapitalization: TextCapitalization.sentences,
                    onTap: () => _showDropdown(inputFieldValues, context),
                    readOnly:
                        (!widget.editable) ||
                        (widget.singleSelect && inputFieldValues.tags.isNotEmpty),
                    onChanged: (str) {
                      List<String> filteredOptions = [];
                      if (str.trim().isEmpty) {
                        filteredOptions = List.of(widget.options);
                      }
                      filteredOptions =
                          widget.options
                              .where(
                                (option) => widget
                                    .optionsTranslations(option)
                                    .toLowerCase()
                                    .contains(str.trim().toLowerCase()),
                              )
                              .toList();

                      if (widget.notResultFoundOption != null && filteredOptions.isEmpty) {
                        filteredOptions = [widget.notResultFoundOption ?? ''];
                      }
                      _updateNotifyFilter(filteredOptions);
                      _updateDropdownHeight();
                    },
                    decoration: InputDecoration(
                      isDense: true,
                      floatingLabelBehavior: FloatingLabelBehavior.always,
                      floatingLabelAlignment: FloatingLabelAlignment.start,
                      hintTextDirection: TextDirection.ltr,
                      hintText: inputFieldValues.tags.isEmpty == true ? widget.hint : null,
                      hintStyle: context.theme.textTheme.bodyLarge?.copyWith(
                        color: context.theme.colorScheme.outline,
                      ),
                      border: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: context.theme.colorScheme.outline,
                          width: context.theme.d.borderWidthMedium,
                        ),
                        borderRadius: BorderRadius.circular(
                          context.theme.d.profilePhotoRadiusMedium,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: context.theme.colorScheme.primary,
                          width: context.theme.d.borderWidthMedium,
                        ),
                        borderRadius: BorderRadius.circular(
                          context.theme.d.profilePhotoRadiusMedium,
                        ),
                      ),
                      suffixIcon:
                          shouldShowError || inputFieldValues.error?.isNotEmpty == true
                              ? Icon(MicromentorIcons.error, color: context.theme.colorScheme.error)
                              : Icon(
                                MicromentorIcons.keyboardArrowDown,
                                color: context.theme.colorScheme.onSurface,
                              ),
                      errorText:
                          shouldShowError ? widget.requiredValueError : inputFieldValues.error,
                      prefixIcon:
                          inputFieldValues.tags.isEmpty
                              ? Icon(
                                MicromentorIcons.search,
                                color: context.theme.colorScheme.onSurface,
                              )
                              : Padding(
                                padding: EdgeInsets.symmetric(
                                  vertical: context.theme.d.paddingXSmall,
                                  horizontal: context.theme.d.paddingSmall,
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      MicromentorIcons.search,
                                      color: context.theme.colorScheme.onSurface,
                                    ),
                                    Expanded(
                                      child: Wrap(
                                        runAlignment: WrapAlignment.center,
                                        children:
                                            (inputFieldValues.tags).map((tag) {
                                              return Container(
                                                decoration: BoxDecoration(
                                                  color: context.theme.colorScheme.secondary,
                                                  borderRadius: BorderRadius.circular(
                                                    context.theme.d.roundedRectRadiusSmall,
                                                  ),
                                                ),
                                                margin: EdgeInsets.all(
                                                  context.theme.d.paddingXxSmall,
                                                ),
                                                padding: EdgeInsets.symmetric(
                                                  horizontal: context.theme.d.paddingSmall,
                                                  vertical: context.theme.d.paddingXxSmall,
                                                ),
                                                child: InkWell(
                                                  onTap: () {
                                                    setState(() {
                                                      _onUnselectOption(tag);
                                                      _hideDropdown();
                                                      widget.onChange?.call();
                                                    });
                                                  },
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.spaceBetween,
                                                    mainAxisSize: MainAxisSize.min,
                                                    children: [
                                                      Text(
                                                        widget.optionsTranslations.call(tag),
                                                        style: TextStyle(
                                                          color:
                                                              context.theme.colorScheme.onPrimary,
                                                          fontWeight: FontWeight.w500,
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: context.theme.d.paddingXxSmall,
                                                      ),
                                                      Icon(
                                                        MicromentorIcons.close,
                                                        size: context.theme.d.fontSizeLarge,
                                                        color: context.theme.colorScheme.onPrimary,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              );
                                            }).toList(),
                                      ),
                                    ),
                                    SizedBox(width: context.theme.d.fontSizeMediumLarge),
                                  ],
                                ),
                              ),
                    ),
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  void _showDropdown(InputFieldValues<String> inputFieldValues, BuildContext context) {
    _dropdownOverlay = OverlayEntry(
      maintainState: false,
      builder:
          (context) => GestureDetector(
            onTap: _hideDropdown,
            child: Container(
              color: Colors.transparent,
              child: CompositedTransformFollower(
                link: _layerLink,
                showWhenUnlinked: false,
                targetAnchor: Alignment.bottomCenter,
                followerAnchor: Alignment.topCenter,
                child: Align(
                  alignment: Alignment.topCenter,
                  child: Material(
                    elevation: context.theme.d.elevationLevel2,
                    color: context.colorScheme.onPrimary,
                    child: StatefulBuilder(
                      builder: (context, setState1) {
                        return Container(
                          decoration: BoxDecoration(
                            color: context.colorScheme.surfaceTint.withValues(alpha: 0.08),
                            borderRadius: BorderRadius.circular(
                              context.theme.d.roundedRectRadiusLarge,
                            ),
                          ),
                          constraints: BoxConstraints(
                            maxHeight: context.theme.d.dropdownHeight,
                            maxWidth: _dropdownMaxWidth,
                          ),
                          child: ValueListenableBuilder<List<String>>(
                            valueListenable: _filteredOptionNotifier,
                            key: dropdownKey,
                            builder: (context, value, child) {
                              bool showNoResultTag =
                                  (widget.notResultFoundOption != null &&
                                      widget.notResultFoundOption ==
                                          _filteredOptionNotifier.value.first);

                              return ScrollbarTheme(
                                data: ScrollbarThemeData(
                                  thumbColor: WidgetStateProperty.all<Color>(
                                    context.colorScheme.scrim,
                                  ), // Change color here
                                ),
                                child: Scrollbar(
                                  controller: _scrollController,
                                  thumbVisibility: true,
                                  child: ListView.builder(
                                    controller: _scrollController,
                                    padding: EdgeInsets.zero,
                                    shrinkWrap: true,
                                    itemCount: value.length,
                                    itemBuilder: (context, i) {
                                      return InkWell(
                                        onTap:
                                            showNoResultTag
                                                ? null
                                                : () {
                                                  setState(() {
                                                    setState1(() {
                                                      inputFieldValues.tags.contains(value[i])
                                                          ? _onUnselectOption(value[i])
                                                          : _onSelectOption(
                                                            value[i],
                                                            inputFieldValues,
                                                          );
                                                    });
                                                  });
                                                  widget.onChange?.call();
                                                },
                                        child: ListTile(
                                          selected: inputFieldValues.tags.contains(value[i]),
                                          selectedColor: context.colorScheme.secondary,
                                          leading:
                                              showNoResultTag
                                                  ? null
                                                  : Checkbox(
                                                    value: inputFieldValues.tags.contains(value[i]),
                                                    onChanged: (bool? isSelected) {
                                                      setState(() {
                                                        setState1(() {
                                                          (isSelected ?? false)
                                                              ? _onSelectOption(
                                                                value[i],
                                                                inputFieldValues,
                                                              )
                                                              : _onUnselectOption(value[i]);
                                                        });
                                                      });
                                                      widget.onChange?.call();
                                                    },
                                                  ),
                                          title: Text(
                                            widget.optionsTranslations(value[i]),
                                            textAlign: showNoResultTag ? TextAlign.center : null,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          ),
    );
    _isDropdownVisible = true;
    _dropdownOverlay?.addListener(() {
      _updateDropdownHeight(
        height: _isDropdownVisible ? context.theme.d.dropdownHeight : context.theme.d.zero,
      );
    });
    return Overlay.of(context, rootOverlay: true).insert(_dropdownOverlay!);
  }

  _onSelectOption(String selectedTag, InputFieldValues<String> inputFieldValues) {
    _textEditingController.clear();
    _updateNotifyFilter(List.of(widget.options));

    if (widget.singleSelect && inputFieldValues.tags.isNotEmpty) {
      widget.tagsController.clearTags();
    } else if (widget.maxSelection != null && inputFieldValues.tags.length == widget.maxSelection) {
      widget.tagsController.onTagRemoved(inputFieldValues.tags.last);
    }
    if (selectedTag.isNotEmpty) {
      widget.tagsController.onTagSubmitted(selectedTag);
    }
    widget.onChange?.call();

    if (widget.singleSelect) {
      _hideDropdown();
    }
  }

  _onUnselectOption(String tag) {
    //This is must have logic:
    //on some screen we need to disable back button if there is no data in tagController
    //so this code will trigger the parent widget to refresh data with updated values
    //and back button enable or disable accordingly
    if (widget.onChange == null &&
        widget.tagsController.getTags?.length == 1 &&
        widget.requiredValueError != null) {
      widget.tagsController.onTagRemoved(tag);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onDropdownSizeChanged?.call(context.theme.d.dropdownHeight);
      });
    } else {
      widget.tagsController.onTagRemoved(tag);
    }
  }

  void _updateDropdownHeight({double? height}) {
    if (widget.editable && widget.onDropdownSizeChanged != null) {
      if (height == null) {
        if (dropdownKey.currentContext != null) {
          final RenderBox overlayBox = dropdownKey.currentContext!.findRenderObject() as RenderBox;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            widget.onDropdownSizeChanged?.call(overlayBox.size.height);
          });
        }
      } else {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          widget.onDropdownSizeChanged?.call(height);
        });
      }
    }
  }

  void _hideDropdown() {
    if (_isDropdownVisible) {
      _dropdownOverlay?.remove();
      setState(() {
        _isDropdownVisible = false;
      });
      _textFieldFocusNode.unfocus();
    }
  }

  _updateNotifyFilter(List<String> options) {
    if (widget.editable) {
      _filteredOptionNotifier.value = options;
    }
  }
}
