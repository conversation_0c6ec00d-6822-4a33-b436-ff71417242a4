import 'package:flutter/material.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import '../../constants/parts/micromentor_icons.dart';
import 'common_button.dart';

class DialogTemplate extends StatelessWidget {
  final Image? titleImage;
  final String? title;
  final String? description;
  final Widget? content;
  final String? actionButtonTitle;
  final String? cancelButtonTitle;
  final Function()? onAction;
  final Function()? onCancel;
  final TextStyle? actionButtonTextStyle;
  final bool showButtons;
  final bool showCloseButton;
  final bool isFullWidthButtons;
  final bool showTitleInCenter;
  final EdgeInsets? contentPadding;
  final Widget? actionButtonIcon;

  const DialogTemplate({
    this.titleImage,
    this.title,
    required this.description,
    this.actionButtonTitle,
    this.cancelButtonTitle,
    this.actionButtonTextStyle,
    this.onAction,
    this.onCancel,
    this.contentPadding,
    this.showButtons = true,
    this.isFullWidthButtons = true,
    this.showTitleInCenter = true,
    this.actionButtonIcon,
    this.showCloseButton = true,
    super.key,
  }) : content = null;

  const DialogTemplate.withCustomContent({
    this.titleImage,
    this.title,
    this.actionButtonTitle,
    this.cancelButtonTitle,
    this.actionButtonTextStyle,
    this.showButtons = true,
    required this.content,
    this.onAction,
    this.onCancel,
    this.contentPadding,
    this.isFullWidthButtons = true,
    this.showTitleInCenter = true,
    this.actionButtonIcon,
    this.showCloseButton = true,
    super.key,
  }) : description = null;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: _titleWidget(context),
      contentPadding: contentPadding,
      content: SizedBox(
        width:
            AppUtility.displayDesktopUI(context)
                ? context.theme.d.dialogSmallWidthWeb
                : double.maxFinite,
        child: _content(context),
      ),
      actions:
          showButtons
              ? isFullWidthButtons
                  ? [
                    _actionButton(context),
                    SizedBox(height: context.theme.d.boxHeight),
                    _cancelButton(context),
                  ]
                  : [
                    _cancelButton(context),
                    SizedBox(height: context.theme.d.boxHeight),
                    _actionButton(context),
                  ]
              : null,
    );
  }

  Widget _titleWidget(BuildContext context) {
    final titleColumn = Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (titleImage != null) ...[titleImage!, SizedBox(height: context.theme.d.paddingMedium)],
        Padding(
          padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingXSmall),
          child: Text(
            title ?? '',
            style: context.theme.textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.surfaceBright,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
      ],
    );

    return Stack(
      children: [
        showTitleInCenter ? Center(child: titleColumn) : titleColumn,
        if (!showButtons && showCloseButton) _closeIcon(context),
      ],
    );
  }

  _closeIcon(BuildContext context) {
    return Positioned(
      top: 0.0,
      right: 0.0,
      child: IconButton(
        onPressed: () {
          Navigator.pop(context);
        },
        icon: Icon(MicromentorIcons.close, color: context.colorScheme.onSurface),
      ),
    );
  }

  _actionButton(BuildContext context) {
    return CommonButton.primaryRoundedRectangle(
      key: Key(actionButtonTitle ?? AppLocale.current.actionConfirm),
      context: context,
      title: actionButtonTitle ?? AppLocale.current.actionConfirm,
      onPressed: onAction,
      isFullWidth: isFullWidthButtons,
      icon: actionButtonIcon,
      iconPosition: IconPosition.atRight,
      buttonSize: ButtonSize.small,
    );
  }

  _cancelButton(BuildContext context) {
    return CommonButton.outlinedButton(
      key: Key(cancelButtonTitle ?? AppLocale.current.actionCancel),
      context: context,
      title: cancelButtonTitle ?? AppLocale.current.actionCancel,
      isFullWidth: isFullWidthButtons,
      onPressed: onCancel ?? () => Navigator.pop(context),
      buttonSize: ButtonSize.small,
    );
  }

  _content(BuildContext context) {
    return content ??
        ((description != null)
            ? Text(
              description!,
              style: context.theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w400,
                color: context.colorScheme.onSurface,
              ),
            )
            : null);
  }
}
