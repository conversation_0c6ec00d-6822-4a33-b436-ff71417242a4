import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';

import '../../constants/constants.dart';
import 'radio_tile_widget.dart';

class SelectOptionWidget extends StatelessWidget {
  final List<Option> options;
  final String? value;
  final void Function(String) onChanged;

  const SelectOptionWidget({
    super.key,
    required this.options,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children:
          options.map((option) {
            return Padding(
              padding:
                  options.indexOf(option) == 0
                      ? EdgeInsets.zero
                      : EdgeInsets.only(top: context.theme.d.paddingLarge),
              child: RadioTileWidget<String>(
                title: option.label,
                titleStyle: context.theme.textTheme.bodyMedium?.copyWith(
                  color: context.theme.colorScheme.secondary,
                ),
                value: option.value,
                groupValue: value,
                onChanged: (value) => onChanged.call(value),
              ),
            );
          }).toList(),
    );
  }
}
