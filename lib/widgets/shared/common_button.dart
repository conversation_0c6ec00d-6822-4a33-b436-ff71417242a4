import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';

import '../../constants/constants.dart';

class CommonButton extends StatelessWidget {
  final BuildContext context;
  final VoidCallback? onPressed;
  final VoidCallback? onLongPress;
  final String title;
  final ButtonStyle? buttonStyle;
  final TextStyle? textStyle;
  final ButtonSize? buttonSize;
  final ButtonType _buttonType;
  final Widget? icon;
  final IconPosition? iconPosition;
  final bool? isFullWidth;
  final Color? color;
  final Color? disabledColor;

  CommonButton.primaryRoundedRectangle({
    required this.context,
    required this.title,
    required this.buttonSize,
    required this.onPressed,
    this.onLongPress,
    this.color,
    this.icon,
    this.isFullWidth = false,
    this.iconPosition = IconPosition.atLeft,
    super.key,
    this.disabledColor,
  }) : _buttonType = ButtonType.elevated,
       buttonStyle =
           buttonSize == ButtonSize.small
               ? null
               : context.theme.buttonStyle
                   .primaryRoundedRectangleButton(context)
                   .copyWith(
                     minimumSize: WidgetStatePropertyAll(
                       buttonSize == ButtonSize.medium
                           ? context.theme.d.buttonSizeMedium
                           : buttonSize == ButtonSize.large
                           ? context.theme.d.buttonSizeLarge
                           : buttonSize == ButtonSize.xLarge
                           ? context.theme.d.buttonSizeXLarge
                           : null,
                     ),
                   ),
       textStyle = context.textTheme.titleMedium?.copyWith(
         color: onPressed == null ? disabledColor : color ?? context.colorScheme.onPrimary,
       );

  CommonButton.textButton({
    required this.context,
    required this.title,
    this.buttonSize = ButtonSize.small,
    required this.onPressed,
    this.onLongPress,
    this.textStyle,
    this.color,
    this.icon,
    this.iconPosition = IconPosition.atLeft,
    this.isFullWidth = false,
    this.disabledColor,
    super.key,
  }) : _buttonType = ButtonType.text,
       buttonStyle =
           buttonSize == ButtonSize.small
               ? null
               : context.theme.buttonStyle
                   .primaryTextButton(context)
                   .copyWith(
                     minimumSize: WidgetStatePropertyAll(
                       buttonSize == ButtonSize.medium
                           ? context.theme.d.buttonSizeMedium
                           : buttonSize == ButtonSize.large
                           ? context.theme.d.buttonSizeLarge
                           : buttonSize == ButtonSize.xLarge
                           ? context.theme.d.buttonSizeXLarge
                           : null,
                     ),
                   );

  CommonButton.outlinedButton({
    required this.context,
    required this.title,
    this.buttonSize = ButtonSize.small,
    required this.onPressed,
    this.onLongPress,
    this.textStyle,
    this.color,
    this.icon,
    this.iconPosition = IconPosition.atLeft,
    this.isFullWidth = false,
    this.disabledColor,
    super.key,
  }) : _buttonType = ButtonType.outline,
       buttonStyle =
           buttonSize == ButtonSize.small
               ? null
               : context.theme.buttonStyle
                   .primaryOutlineButton(context)
                   .copyWith(
                     minimumSize: WidgetStatePropertyAll(
                       buttonSize == ButtonSize.medium
                           ? context.theme.d.buttonSizeMedium
                           : buttonSize == ButtonSize.large
                           ? context.theme.d.buttonSizeLarge
                           : buttonSize == ButtonSize.xLarge
                           ? context.theme.d.buttonSizeXLarge
                           : null,
                     ),
                   );

  @override
  Widget build(BuildContext context) {
    Widget button =
        _buttonType == ButtonType.elevated
            ? SizedBox(
              width: (isFullWidth == true) ? double.infinity : null,
              child: ElevatedButton(
                style: buttonStyle,
                onPressed: onPressed,
                onLongPress: onLongPress,
                child:
                    icon != null
                        ? IconButtonView(
                          title: title,
                          icon: icon!,
                          iconPosition: iconPosition,
                          textStyle: textStyle,
                        )
                        : Text(title, textAlign: TextAlign.center, style: textStyle),
              ),
            )
            : _buttonType == ButtonType.text
            ? SizedBox(
              width: (isFullWidth == true) ? double.infinity : null,
              child: TextButton(
                style: buttonStyle,
                onPressed: onPressed,
                onLongPress: onLongPress,
                child:
                    icon != null
                        ? IconButtonView(
                          title: title,
                          icon: icon!,
                          iconPosition: iconPosition,
                          textStyle:
                              textStyle ??
                              context.theme.textTheme.labelLarge?.copyWith(
                                color:
                                    onPressed != null
                                        ? context.theme.colorScheme.primary
                                        : context.theme.colorScheme.onSurface.withValues(
                                          alpha: 0.6,
                                        ),
                              ),
                        )
                        : Text(
                          title,
                          textAlign: TextAlign.center,
                          style:
                              textStyle ??
                              context.theme.textTheme.labelLarge?.copyWith(
                                color:
                                    onPressed != null
                                        ? context.theme.colorScheme.primary
                                        : context.theme.colorScheme.onSurface.withValues(
                                          alpha: 0.6,
                                        ),
                              ),
                        ),
              ),
            )
            : Container(
              width: (isFullWidth == true) ? double.infinity : null,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXLarge),
              ),
              child: OutlinedButton(
                style: buttonStyle,
                onPressed: onPressed,
                onLongPress: onLongPress,
                child:
                    icon != null
                        ? IconButtonView(
                          title: title,
                          icon: icon!,
                          iconPosition: iconPosition,
                          textStyle: textStyle,
                        )
                        : Text(title, textAlign: TextAlign.center, style: textStyle),
              ),
            );

    return button;
  }
}

class IconButtonView extends StatelessWidget {
  final IconPosition? iconPosition;
  final Widget icon;
  final String title;
  final TextStyle? textStyle;

  const IconButtonView({
    required this.icon,
    required this.title,
    this.iconPosition = IconPosition.atLeft,
    this.textStyle,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        (iconPosition == IconPosition.atLeft)
            ? icon
            : Text(title, textAlign: TextAlign.center, style: textStyle),
        SizedBox(width: context.theme.d.chipPadding),
        (iconPosition == IconPosition.atRight)
            ? icon
            : Text(title, textAlign: TextAlign.center, style: textStyle),
      ],
    );
  }
}
