import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';

import '../../constants/constants.dart';

class RoundedSelectOptionWidget extends StatelessWidget {
  final List<Option> options;
  final String? value;
  final void Function(String) onChanged;

  const RoundedSelectOptionWidget({
    super.key,
    required this.options,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children:
          options.map((option) {
            return Padding(
              padding:
                  options.indexOf(option) == 0
                      ? EdgeInsets.zero
                      : EdgeInsets.only(top: context.theme.d.paddingMedium),
              child: InkWell(
                onTap: () {
                  onChanged.call(option.value);
                },
                child: Container(
                  width: double.infinity,
                  height: context.theme.d.boxSizeSmall,
                  padding: EdgeInsets.symmetric(
                    horizontal: context.theme.d.paddingMedium,
                    vertical: context.theme.d.paddingXSmall,
                  ),
                  decoration: ShapeDecoration(
                    color: option.value == value ? context.colorScheme.primary : null,
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                        width: context.theme.d.borderWidthRegular,
                        color: context.colorScheme.primary,
                      ),
                      borderRadius: BorderRadius.circular(999),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      option.label,
                      style: context.textTheme.titleMedium?.copyWith(
                        color:
                            option.value == value
                                ? context.colorScheme.onPrimary
                                : context.colorScheme.primary,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
    );
  }
}
