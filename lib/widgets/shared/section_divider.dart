import 'package:flutter/material.dart';

import '../../../../services/extensions.dart';

class SectionDivider extends StatelessWidget {
  const SectionDivider({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: context.theme.d.paddingXxSmall),
      child: Divider(
        color: context.theme.colorScheme.outlineVariant,
        height: context.theme.d.zero,
        thickness: context.theme.d.borderWidthXSmall,
      ),
    );
  }
}
