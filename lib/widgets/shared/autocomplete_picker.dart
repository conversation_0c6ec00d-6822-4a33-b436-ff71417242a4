import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:textfield_tags/textfield_tags.dart';

import '../../../../services/extensions.dart';
import '../../constants/parts/micromentor_icons.dart';

class AutocompletePicker extends StatelessWidget {
  final StringTagController tagsController;
  final String? label;
  final String? hint;
  final bool singleSelect;
  final int? maxSelection;
  final List<String> options;
  final String Function(String)? optionsTranslations;
  final Set<String>? selectedOptions;
  final bool shouldScrollSelections;
  final String? requiredValueError;
  final Function()? onChange;

  const AutocompletePicker({
    super.key,
    this.label,
    this.hint,
    this.singleSelect = false,
    required this.tagsController,
    required this.options,
    this.optionsTranslations,
    this.maxSelection,
    this.selectedOptions,
    this.shouldScrollSelections = true,
    this.requiredValueError,
    this.onChange,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AutocompleteWidget(
                label: label,
                hint: hint,
                options: options.toList(),
                optionsTranslations: optionsTranslations ?? (s) => s,
                selectedOptions: selectedOptions?.toList() ?? [],
                singleSelect: (singleSelect || maxSelection == 1),
                tagController: tagsController,
                shouldScrollSelections: shouldScrollSelections,
                maxSelection: maxSelection,
                requiredValueError: requiredValueError,
                onChange: onChange,
              ),
              SizedBox(height: context.theme.d.paddingSmall),
            ],
          ),
        ),
      ],
    );
  }
}

class AutocompleteWidget extends StatelessWidget {
  final String? label;
  final String? hint;
  final List<String> options;
  final String Function(String) optionsTranslations;
  final List<String> selectedOptions;
  final bool singleSelect;
  final int? maxSelection;
  final StringTagController tagController;

  final bool shouldScrollSelections;
  final String? requiredValueError;
  final Function()? onChange;

  const AutocompleteWidget({
    super.key,
    this.label,
    this.hint,
    required this.options,
    required this.selectedOptions,
    this.singleSelect = false,
    required this.tagController,
    required this.optionsTranslations,
    this.maxSelection,
    this.shouldScrollSelections = true,
    this.requiredValueError,
    this.onChange,
  });

  @override
  Widget build(BuildContext context) {
    ScrollController scrollController = ScrollController();

    return LayoutBuilder(
      builder: (_, BoxConstraints constraints) {
        return Autocomplete<String>(
          optionsBuilder: (TextEditingValue textEditingValue) {
            var filteredOptions = options.where(
              (option) => !(tagController.getTags?.contains(option) ?? false),
            );

            if (textEditingValue.text.trim().isEmpty) {
              return filteredOptions;
            }
            return filteredOptions.where(
              (option) => optionsTranslations(
                option,
              ).toLowerCase().contains(textEditingValue.text.trim().toLowerCase()),
            );
          },
          displayStringForOption: optionsTranslations,
          onSelected: (String selectedTag) {
            if (singleSelect && tagController.getTags?.isNotEmpty == true) {
              tagController.clearTags();
            } else if (maxSelection != null && tagController.getTags?.length == maxSelection) {
              tagController.onTagRemoved(tagController.getTags!.last);
            }
            if (selectedTag.isNotEmpty == true) {
              tagController.onTagSubmitted(selectedTag);
            }
            if (singleSelect) {
              FocusManager.instance.primaryFocus?.unfocus();
            }

            onChange?.call();
          },
          optionsViewBuilder: (context, onSelected, optionsToShow) {
            return Align(
              alignment: Alignment.topLeft,
              child: Material(
                color: context.colorScheme.onPrimary,
                elevation: context.theme.d.elevationLevel2,
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: context.theme.d.dropdownHeight,
                    maxWidth: constraints.maxWidth,
                  ),
                  child: ScrollbarTheme(
                    data: ScrollbarThemeData(
                      thumbColor: WidgetStateProperty.all<Color>(context.colorScheme.scrim),
                    ),
                    child: Scrollbar(
                      controller: scrollController,
                      thumbVisibility: true,
                      child: ListView.builder(
                        padding: EdgeInsets.zero,
                        controller: scrollController,
                        shrinkWrap: true,
                        itemCount: optionsToShow.length,
                        itemBuilder: (BuildContext context, int index) {
                          final String option = optionsToShow.elementAt(index);
                          final String optionToDisplay = optionsTranslations(option);
                          return InkWell(
                            onTap: () => onSelected(option),
                            child: Builder(
                              builder: (BuildContext context) {
                                final bool highlight =
                                    AutocompleteHighlightedOption.of(context) == index;
                                if (highlight) {
                                  SchedulerBinding.instance.addPostFrameCallback((
                                    Duration timeStamp,
                                  ) {
                                    Scrollable.ensureVisible(context, alignment: 0.5);
                                  });
                                }
                                return Container(
                                  color: highlight ? Theme.of(context).focusColor : null,
                                  padding: EdgeInsets.all(context.theme.d.paddingMedium),
                                  child: Text(optionToDisplay),
                                );
                              },
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
          fieldViewBuilder: (context, textFieldController, textFieldFocusNode, onFieldSubmitted) {
            return TextFieldTags<String>(
              textEditingController: textFieldController,
              focusNode: textFieldFocusNode,
              textfieldTagsController: tagController,
              initialTags: selectedOptions,
              validator: (String tag) {
                if (tagController.getTags?.contains(tag) ?? false) {
                  return AppLocale.current.errorAlreadyEntered;
                }
                if (!options.contains(tag)) {
                  return AppLocale.current.errorInvalidChoice;
                }
                return null;
              },
              inputFieldBuilder: (context, inputFieldValues) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label ?? '',
                      style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
                    ),
                    SizedBox(height: context.theme.d.paddingSmall),
                    TextField(
                      onTapOutside: (_) => FocusManager.instance.primaryFocus?.unfocus(),
                      controller: inputFieldValues.textEditingController,
                      focusNode: inputFieldValues.focusNode,
                      readOnly: singleSelect && tagController.getTags?.isNotEmpty == true,
                      decoration: InputDecoration(
                        isDense: true,
                        floatingLabelBehavior: FloatingLabelBehavior.always,
                        hintText: inputFieldValues.tags.isNotEmpty != true ? hint : null,
                        hintStyle: context.theme.textTheme.bodyLarge?.copyWith(
                          color: context.theme.colorScheme.outline,
                        ),
                        border: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: context.theme.colorScheme.outline,
                            width: context.theme.d.borderWidthMedium,
                          ),
                          borderRadius: BorderRadius.circular(context.theme.d.textFieldRadius),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: context.theme.colorScheme.primary,
                            width: context.theme.d.borderWidthMedium,
                          ),
                          borderRadius: BorderRadius.circular(context.theme.d.textFieldRadius),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: context.theme.colorScheme.error),
                        ),
                        errorText:
                            inputFieldValues.error ??
                            ((inputFieldValues.tags.isEmpty &&
                                    inputFieldValues.textEditingController.text.isEmpty)
                                ? requiredValueError
                                : null),
                        suffixIcon:
                            ((inputFieldValues.error?.isNotEmpty == true) ||
                                    ((inputFieldValues.tags.isEmpty &&
                                        (inputFieldValues.textEditingController.text.isEmpty) &&
                                        requiredValueError?.isNotEmpty == true)))
                                ? Icon(
                                  MicromentorIcons.error,
                                  color: context.theme.colorScheme.error,
                                )
                                : inputFieldValues.tags.isNotEmpty != true
                                ? Icon(
                                  MicromentorIcons.keyboardArrowDown,
                                  color: context.theme.colorScheme.onSurfaceVariant,
                                )
                                : null,
                        prefixIcon:
                            inputFieldValues.tags.isNotEmpty != true
                                ? Icon(
                                  MicromentorIcons.search,
                                  color: context.theme.colorScheme.onSurface,
                                )
                                : Padding(
                                  padding: EdgeInsets.only(left: context.theme.d.paddingSmall),
                                  child:
                                      shouldScrollSelections
                                          ? SingleChildScrollView(
                                            controller: inputFieldValues.tagScrollController,
                                            scrollDirection: Axis.horizontal,
                                            child: Row(
                                              children: [
                                                Icon(
                                                  MicromentorIcons.search,
                                                  color: context.theme.colorScheme.onSurface,
                                                ),
                                                Row(
                                                  children:
                                                      inputFieldValues.tags.map((String tag) {
                                                        return Container(
                                                          decoration: BoxDecoration(
                                                            color:
                                                                context.theme.colorScheme.secondary,
                                                            borderRadius: BorderRadius.circular(
                                                              context
                                                                  .theme
                                                                  .d
                                                                  .roundedRectRadiusSmall,
                                                            ),
                                                          ),
                                                          margin: EdgeInsets.only(
                                                            right: context.theme.d.paddingSmall,
                                                          ),
                                                          padding: EdgeInsets.symmetric(
                                                            horizontal:
                                                                context.theme.d.paddingSmall,
                                                            vertical:
                                                                context.theme.d.paddingXxSmall,
                                                          ),
                                                          child: InkWell(
                                                            onTap: () {
                                                              inputFieldValues.onTagRemoved(tag);
                                                              onChange?.call();
                                                            },
                                                            child: Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment.spaceBetween,
                                                              children: [
                                                                Text(
                                                                  optionsTranslations.call(tag),
                                                                  style: TextStyle(
                                                                    color:
                                                                        context
                                                                            .theme
                                                                            .colorScheme
                                                                            .onPrimary,
                                                                  ),
                                                                ),
                                                                SizedBox(
                                                                  width:
                                                                      context
                                                                          .theme
                                                                          .d
                                                                          .paddingXxSmall,
                                                                ),
                                                                Icon(
                                                                  MicromentorIcons.close,
                                                                  size:
                                                                      context
                                                                          .theme
                                                                          .d
                                                                          .iconSizeMedium,
                                                                  color:
                                                                      context
                                                                          .theme
                                                                          .colorScheme
                                                                          .onPrimary,
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        );
                                                      }).toList(),
                                                ),
                                              ],
                                            ),
                                          )
                                          : Wrap(
                                            runAlignment: WrapAlignment.center,
                                            children:
                                                (inputFieldValues.tags).map((tag) {
                                                  return Container(
                                                    decoration: BoxDecoration(
                                                      color: context.theme.colorScheme.secondary,
                                                      borderRadius: BorderRadius.circular(
                                                        context.theme.d.roundedRectRadiusSmall,
                                                      ),
                                                    ),
                                                    margin: EdgeInsets.all(
                                                      context.theme.d.paddingXxSmall,
                                                    ),
                                                    padding: EdgeInsets.symmetric(
                                                      horizontal: context.theme.d.paddingSmall,
                                                      vertical: context.theme.d.paddingXxSmall,
                                                    ),
                                                    child: InkWell(
                                                      onTap: () {
                                                        inputFieldValues.onTagRemoved(tag);
                                                        onChange?.call();
                                                      },
                                                      child: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment.spaceBetween,
                                                        mainAxisSize: MainAxisSize.min,
                                                        children: [
                                                          Text(
                                                            optionsTranslations.call(tag),
                                                            style: TextStyle(
                                                              color:
                                                                  context
                                                                      .theme
                                                                      .colorScheme
                                                                      .onPrimary,
                                                              fontWeight: FontWeight.w500,
                                                            ),
                                                          ),
                                                          SizedBox(
                                                            width: context.theme.d.paddingXxSmall,
                                                          ),
                                                          Icon(
                                                            MicromentorIcons.close,
                                                            size: context.theme.d.fontSizeLarge,
                                                            color:
                                                                context.theme.colorScheme.onPrimary,
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  );
                                                }).toList(),
                                          ),
                                ),
                      ),
                      onChanged: (String str) {
                        inputFieldValues.onTagChanged.call(str);

                        //helps to refresh the screen UI
                        if (inputFieldValues.error?.isNotEmpty == true ||
                            (inputFieldValues.tags.isEmpty ||
                                inputFieldValues.textEditingController.text.trim().isNotEmpty)) {
                          onChange?.call();
                        }
                      },
                      onSubmitted: inputFieldValues.onTagSubmitted,
                      onEditingComplete: () {
                        FocusManager.instance.primaryFocus?.unfocus();
                      },
                    ),
                  ],
                );
              },
            );
          },
        );
      },
    );
  }
}
