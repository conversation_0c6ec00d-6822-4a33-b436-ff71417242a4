import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../constants/parts/micromentor_icons.dart';
import '../../models/locale_model.dart';

class TextFormFieldWidget extends StatefulWidget {
  final TextEditingController? textController;
  final Function(String)? onChanged;
  final Function(String)? onFieldSubmitted;
  final String? label;
  final TextStyle? labelStyle;
  final Iterable<String>? autofillHints;
  final String? hint;
  final TextStyle? hintStyle;
  final bool? isPassword;
  final int? maxLength;
  final int maxLines;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool enabled;
  final bool readOnly;
  final bool autofocus;
  final EdgeInsetsGeometry? contentPadding;
  final Color? focusedBorderColor;
  final TextCapitalization textCapitalization;
  final double? scrollableBottomPadding;
  final FocusNode? focusNode;

  const TextFormFieldWidget({
    super.key,
    this.textController,
    this.onChanged,
    this.onFieldSubmitted,
    this.label,
    this.labelStyle,
    this.autofillHints,
    this.hint,
    this.hintStyle,
    this.maxLength,
    this.maxLines = 1,
    this.validator,
    this.isPassword,
    this.keyboardType,
    this.inputFormatters,
    this.prefixIcon,
    this.suffixIcon,
    this.enabled = true,
    this.autofocus = false,
    this.readOnly = false,
    this.contentPadding,
    this.focusedBorderColor,
    this.scrollableBottomPadding,
    this.focusNode,
    this.textCapitalization = TextCapitalization.none,
  });

  @override
  State<TextFormFieldWidget> createState() => _TextFormFieldWidgetState();
}

class _TextFormFieldWidgetState extends State<TextFormFieldWidget> {
  late final LocaleModel _localeModel;
  bool? _passwordVisible;
  bool hasError = false;
  GlobalKey? _textKey;
  late final FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _textKey = GlobalKey();
    _focusNode = widget.focusNode ?? FocusNode();
    _passwordVisible = false;
    _localeModel = Provider.of<LocaleModel>(context, listen: false);

    // Given an increased zoom and a closed keyboard, the `ensureVisible` scroll always loses the race to the keyboard animation.
    _focusNode.addListener(_ensureFieldIsVisible);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_ensureFieldIsVisible);
    _focusNode.dispose();
    super.dispose();
  }

  void _ensureFieldIsVisible() {
    if (_focusNode.hasFocus && _textKey != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Future.delayed(
          Durations.medium1,
          () => Scrollable.ensureVisible(
            _textKey!.currentContext!,
            alignment: 0.1, // 0.1 is the bottom of the viewport
            duration: Durations.medium1,
          ),
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if ((widget.label ?? '').trim().isNotEmpty) ...[
          Text(
            widget.label ?? '',
            style:
                widget.labelStyle ??
                context.textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: context.colorScheme.onSecondaryContainer,
                ),
          ),
          SizedBox(height: context.theme.d.paddingXSmall),
        ],
        TextFormField(
          scrollPadding: EdgeInsets.only(
            bottom: widget.scrollableBottomPadding ?? context.theme.d.zero,
          ),
          focusNode: _focusNode,
          key: _textKey,
          maxLength: widget.maxLength,
          controller: widget.textController,
          autofocus: widget.autofocus,
          enabled: widget.enabled,
          autofillHints: widget.autofillHints,
          style: context.textTheme.bodyLarge?.copyWith(color: context.colorScheme.onSurface),
          textCapitalization: widget.textCapitalization,
          obscureText:
              widget.isPassword != null &&
              widget.isPassword == true &&
              _passwordVisible != null &&
              _passwordVisible == false,
          textAlign:
              _localeModel.isArabic(text: widget.textController?.text)
                  ? TextAlign.right
                  : TextAlign.left,
          textDirection:
              _localeModel.isArabic(text: widget.textController?.text)
                  ? TextDirection.rtl
                  : TextDirection.ltr,
          // validator: ((value) {
          //   if (value?.trim().isEmpty == true) {
          //     hasError = false;
          //     return null;
          //   }
          //   var result = widget.validator?.call(value);
          //   hasError = result != null;
          //   return result;
          // }),
          validator: (value) {
            final result = widget.validator?.call(value);
            // Only call setState if the error state has changed
            if ((result != null) != hasError) {
              setState(() {
                hasError = result != null;
              });
            }
            return result;
          },
          keyboardType: widget.keyboardType,
          inputFormatters: widget.inputFormatters,
          maxLines: widget.maxLines,
          readOnly: widget.readOnly,
          decoration: InputDecoration(
            errorMaxLines: 5,
            contentPadding: widget.contentPadding,
            floatingLabelBehavior: FloatingLabelBehavior.always,
            errorStyle: context.theme.textTheme.labelSmall?.copyWith(
              color: context.colorScheme.error,
              overflow: TextOverflow.clip,
            ),
            hintText: widget.hint,
            hintStyle:
                widget.hintStyle ??
                context.textTheme.bodyLarge?.copyWith(
                  color:
                      widget.enabled
                          ? context.colorScheme.outline
                          : context.colorScheme.outline.withValues(alpha: 0.3),
                ),
            prefixIcon: widget.prefixIcon,
            suffixIcon: _suffixIconWidget(),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: context.colorScheme.outline),
              borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
            ),
            disabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: context.colorScheme.outline),
              borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: widget.focusedBorderColor ?? context.colorScheme.primary,
                width: context.theme.d.borderWidthMedium,
              ),
              borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
            ),
            errorBorder: OutlineInputBorder(
              borderSide: BorderSide(color: context.colorScheme.error),
              borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderSide: BorderSide(color: context.colorScheme.error),
              borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
            ),
          ),
          onChanged: widget.onChanged,
          onFieldSubmitted: widget.onFieldSubmitted,
        ),
      ],
    );
  }

  _suffixIconWidget() {
    bool isPassword =
        widget.isPassword != null &&
        widget.isPassword == true &&
        (widget.textController?.text.isNotEmpty ?? false);

    List<Widget> widgets = [];
    if (hasError) widgets.add(_errorSuffixIcon());
    if (isPassword) widgets.add(_passwordSuffixIcon());
    if (widget.suffixIcon != null) widgets.add(widget.suffixIcon!);

    if (widgets.isEmpty) return null;
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (hasError) _errorSuffixIcon(),
        if (isPassword) _passwordSuffixIcon(),
        if (widget.suffixIcon != null) widget.suffixIcon!,
      ],
    );
  }

  _errorSuffixIcon() {
    return Icon(MicromentorIcons.error, color: context.colorScheme.error);
  }

  _passwordSuffixIcon() {
    return IconButton(
      icon: Icon(
        _passwordVisible!
            ? MicromentorIcons.visibilityOutlined
            : MicromentorIcons.visibilityOffOutlined,
        color: context.colorScheme.onSurface,
      ),
      onPressed: () {
        setState(() {
          _passwordVisible = !_passwordVisible!;
        });
      },
    );
  }
}
