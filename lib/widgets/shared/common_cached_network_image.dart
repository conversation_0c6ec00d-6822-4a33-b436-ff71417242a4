import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';

import '../../constants/constants.dart';

class CommonCachedNetworkImage extends StatelessWidget {
  final String? imageUrl;
  final double? size;

  const CommonCachedNetworkImage(this.imageUrl, this.size, {super.key});

  @override
  Widget build(BuildContext context) {
    return imageUrl == null
        ? Container(
          clipBehavior: Clip.hardEdge,
          decoration: const BoxDecoration(shape: BoxShape.circle),
          child: Image(
            height: size ?? context.theme.d.imageSizeXLarge,
            width: size ?? context.theme.d.imageSizeXLarge,
            image: const AssetImage(Assets.blankAvatar),
            fit: BoxFit.cover,
          ),
        )
        : CachedNetworkImage(
          height: size ?? context.theme.d.imageSizeXLarge,
          width: size ?? context.theme.d.imageSizeXLarge,
          imageUrl: imageUrl!,
          imageBuilder:
              (context, imageProvider) => Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  image: DecorationImage(image: imageProvider, fit: BoxFit.fill),
                ),
              ),
          placeholder: (context, url) => const CircularProgressIndicator(),
          errorWidget:
              (context, url, error) => Image(
                height: size ?? context.theme.d.imageSizeXLarge,
                width: size ?? context.theme.d.imageSizeXLarge,
                image: const AssetImage(Assets.blankAvatar),
                fit: BoxFit.cover,
              ),
        );
  }
}
