import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';

class RadioTileWidget<T> extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final TextStyle? titleStyle;
  final T value;
  final T? groupValue;
  final Function(T)? onChanged;
  final ListTileControlAffinity radioButtonPosition;

  const RadioTileWidget({
    super.key,
    this.title,
    this.subtitle,
    this.titleStyle,
    required this.value,
    required this.groupValue,
    this.onChanged,
    this.radioButtonPosition = ListTileControlAffinity.platform,
  });

  @override
  Widget build(BuildContext context) {
    return RadioListTile(
      title:
          title == null
              ? null
              : Text(
                title!,
                style:
                    titleStyle ??
                    context.theme.textTheme.bodyLarge?.copyWith(
                      color: context.colorScheme.onSecondaryContainer,
                      fontWeight: FontWeight.w600,
                    ),
              ),
      controlAffinity: radioButtonPosition,
      subtitle:
          subtitle == null
              ? null
              : Text(
                subtitle!,
                style: context.theme.textTheme.bodyLarge?.copyWith(
                  color: context.colorScheme.onSecondaryContainer,
                ),
              ),
      hoverColor: Colors.transparent,
      onChanged: (_) => onChanged?.call(value),
      value: value,
      groupValue: groupValue,
      contentPadding: EdgeInsets.zero,
      visualDensity: VisualDensity.standard,
      dense: true,
    );
  }
}
