import 'package:another_flushbar/flushbar.dart';
import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../../../services/extensions.dart';
import '../../__generated/schema/schema.graphql.dart';
import '../../services/graphql/providers/providers.dart';
import '../../utilities/debug_logger.dart';
import '../../utilities/loading/loading_provider.dart';
import 'shared.dart';

class ReportUserDialog extends StatefulWidget {
  final String userFullName;
  final String userId;

  const ReportUserDialog({super.key, required this.userId, required this.userFullName});

  @override
  State<ReportUserDialog> createState() => _ReportUserDialogState();
}

class _ReportUserDialogState extends State<ReportUserDialog> {
  Enum$ReportUserReasonTextId? _chosenReasonTextId;
  late final ContentProvider _contentProvider;
  final _formKey = GlobalKey<FormState>();
  late final UserProvider _userProvider;

  @override
  void initState() {
    super.initState();
    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
  }

  void _cancelDialog() {
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: DialogTemplate.withCustomContent(
        title: AppLocale.current.userReportTitle(widget.userFullName),
        content: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(AppLocale.current.userReportSubtitle),
              SizedBox(height: context.theme.d.paddingLarge),
              _listOfReportReasons(),
            ],
          ),
        ),
        actionButtonTitle: AppLocale.current.actionReport,
        showTitleInCenter: false,
        isFullWidthButtons: false,
        onAction:
            _chosenReasonTextId == null
                ? null
                : () async {
                  if (_formKey.currentState!.validate()) {
                    Loader.show(context);
                    var response = await _userProvider.reportUser(
                      userId: widget.userId,
                      reasonTextId: _chosenReasonTextId!,
                    );

                    if (context.mounted) {
                      Loader.hide(context);
                    }
                    if (response.gqlQueryResult.hasException) {
                      DebugLogger.error(
                        'Error occurred while reporting the user ${widget.userFullName}',
                      );
                      _showMsgOnSnackBar(AppLocale.current.userReportErrorMsg(widget.userFullName));
                    } else {
                      _cancelDialog();
                      _showMsgOnSnackBar(AppLocale.current.userReportSuccessMsg);
                    }
                  }
                },
      ),
    );
  }

  Widget _listOfReportReasons() {
    final reasons =
        (_contentProvider.reportUserReasonOptions ?? [])
            .where(
              (reason) =>
                  reason.textId != Enum$ReportUserReasonTextId.notSet.name &&
                  reason.textId != Enum$ReportUserReasonTextId.$unknown.name,
            )
            .toList();

    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children:
              reasons.map((reason) {
                return Column(
                  children: [
                    RadioTileWidget<Enum$ReportUserReasonTextId>(
                      title: reason.translatedValue,
                      value: Enum$ReportUserReasonTextId.values.byName(reason.textId),
                      groupValue: _chosenReasonTextId,
                      onChanged:
                          (value) => setState(() {
                            _chosenReasonTextId = value;
                          }),
                    ),
                  ],
                );
              }).toList(),
        ),
      ),
    );
  }

  void _showMsgOnSnackBar(String msg) {
    Flushbar(
      message: msg,
      messageColor: context.theme.colorScheme.surface,
      messageSize: context.theme.d.fontSizeMedium,
      duration: AppUtility.snackBarDuration,
    ).show(context);
  }
}

//TODO: Need in future
//This is "Other" reason's implementation for report user
/*
                    ListTile(
                      title: Text(l10n.userReportReasonOther),
                      trailing: Radio<String>(
                        value: l10n.userReportReasonOther,
                        groupValue: _reason,
                        onChanged: (String? value) {
                          setState(() {
                            _reason = value;
                          });
                        },
                      ),
                      contentPadding: EdgeInsets.zero,
                      visualDensity: VisualDensity.compact,
                      subtitle: TextFormField(
                          controller: _textEditingController,
                          decoration: InputDecoration(
                            filled: true,
                            fillColor:
                                Theme.of(context).colorScheme.surfaceVariant,
                            hintText: l10n.userReportReasonOtherSpecify,
                          ),
                          validator: (text) {
                            if (_reason == l10n.userReportReasonOther &&
                                (text?.isEmpty ?? true)) {
                              return l10n.errorEmptyField;
                            }
                            return null;
                          }),
                    ), */
