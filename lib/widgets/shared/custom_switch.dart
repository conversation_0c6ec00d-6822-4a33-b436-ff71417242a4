import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';

class CustomSwitch extends StatefulWidget {
  final Function(bool isActive)? onChanged;
  final bool initialValue;
  final bool showOnOff;

  const CustomSwitch({
    required this.onChanged,
    this.initialValue = false,
    this.showOnOff = true,
    super.key,
  });

  @override
  State<CustomSwitch> createState() => _CustomSwitchState();
}

class _CustomSwitchState extends State<CustomSwitch> {
  late bool _value;

  @override
  void initState() {
    super.initState();
    _value = widget.initialValue;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _value = !_value;
          widget.onChanged?.call(_value);
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingXxSmall),
        width:
            widget.showOnOff ? context.theme.d.boxSizeMediumLarge : context.theme.d.imageSizeMedium,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXLarge),
          color:
              _value ? context.colorScheme.tertiary : context.colorScheme.surfaceContainerHighest,
        ),
        child: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            (widget.showOnOff)
                ? Align(
                  alignment: _value ? const Alignment(-0.3, 0) : const Alignment(0.5, -1),
                  child: Text(
                    _value ? AppLocale.current.switchOnLabel : AppLocale.current.switchOffLabel,
                    style: context.theme.textTheme.bodyMedium?.copyWith(
                      color:
                          _value
                              ? context.colorScheme.onPrimary
                              : context.colorScheme.scrim.withValues(alpha: 0.6),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                )
                : SizedBox(
                  height: context.theme.d.iconSizeMedium,
                  width: context.theme.d.imageSizeSmall,
                ),
            AnimatedPositioned(
              duration: const Duration(milliseconds: 200),
              curve: Curves.ease,
              left: !_value ? context.theme.d.boxWidth : null,
              right: _value ? context.theme.d.boxWidth : null,
              child: Container(
                width: context.theme.d.fontSizeMediumLarge,
                height: context.theme.d.fontSizeMediumLarge,
                decoration: BoxDecoration(
                  color: context.colorScheme.onPrimary,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
