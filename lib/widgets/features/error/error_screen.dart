import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../../constants/constants.dart';
import '../../../generatedAppLocale/l10n.dart';
import '../../../models/models.dart';

class ErrorScreen extends StatelessWidget {
  const ErrorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: context.theme.colorScheme.secondaryContainer,
        centerTitle: false,
        shape: AppUtility.topDivider(context),
        leading: Center(
          child: InkWell(
            hoverColor: context.colorScheme.secondaryContainer,
            borderRadius: BorderRadius.circular(context.theme.d.paddingSmall),
            key: const Key('homeTab'),
            onTap: () {
              int index = Tabs.values.indexOf(Tabs.home);
              Provider.of<ScaffoldModel>(context, listen: false).setParams(index: index);
              Provider.of<LocalDataModel>(context, listen: false).lastSelectedTab = index;
              context.go(AppRoutes.home.path);
            },
            child: Image.asset(Assets.homeSelected, height: context.theme.d.iconSizeMedium),
          ),
        ),
      ),
      body: Stack(
        children: [
          SafeArea(
            child: Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingMedium),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Text(
                      AppLocale.current.routerErrorMessage,
                      style: TextStyle(
                        color: context.theme.colorScheme.secondary,
                        fontSize: context.theme.d.fontSizeLarge,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
