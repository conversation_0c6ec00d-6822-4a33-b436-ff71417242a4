import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';

import '../../../../constants/constants.dart';

class Splash extends StatelessWidget {
  const Splash({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: const Alignment(0.06, -1.00),
            end: const Alignment(-0.06, 1),
            colors: [context.colorScheme.primary, context.colorScheme.secondary],
          ),
        ),
        child: Center(
          child: SizedBox(
            height: context.theme.d.boxSizeXLarge,
            child: const Image(image: AssetImage(Assets.whiteMicromentorLogoWithName)),
          ),
        ),
      ),
    );
  }
}
