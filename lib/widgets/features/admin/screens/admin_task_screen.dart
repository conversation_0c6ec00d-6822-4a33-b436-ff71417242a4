import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';

import '../../../../utilities/navigation_mixin.dart';
import '../../../widgets.dart';

class AdminTaskScreen extends StatefulWidget {
  const AdminTaskScreen({super.key});

  @override
  State<AdminTaskScreen> createState() => _AdminTaskScreenState();
}

class _AdminTaskScreenState extends State<AdminTaskScreen> with NavigationMixin<AdminTaskScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        leading: backButton(
          context,
          onPressed: () {
            context.pop();
          },
        ),
        title: Text(AppLocale.current.runAdminTaskTitle),
        centerTitle: false,
        shape: AppUtility.topDivider(context),
      ),
      body: Safe<PERSON>rea(
        child: Padding(
          padding: EdgeInsetsDirectional.fromSTEB(
            context.theme.d.paddingMedium,
            context.theme.d.paddingXLarge,
            context.theme.d.paddingSmall,
            context.theme.d.zero,
          ),
          child: const AdminTaskForm(),
        ),
      ),
    );
  }
}
