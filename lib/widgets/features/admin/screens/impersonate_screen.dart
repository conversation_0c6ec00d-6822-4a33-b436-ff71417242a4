import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../../utilities/navigation_mixin.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class ImpersonateScreen extends StatefulWidget {
  final String userId;

  const ImpersonateScreen({super.key, required this.userId});

  @override
  State<ImpersonateScreen> createState() => _ImpersonateScreenState();
}

class _ImpersonateScreenState extends State<ImpersonateScreen>
    with NavigationMixin<ImpersonateScreen> {
  late final UserProvider _userProvider;
  Future<LoadObjectResult>? _loadObjectResult;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!isCurrentPageRoute()) return;
    _loadObjectResult = _userProvider.loadUser(userId: widget.userId);
  }

  @override
  Widget build(BuildContext context) {
    if (!isCurrentPageRoute()) return const SizedBox.shrink();

    doImpersonate(BuildContext context) async {
      await _userProvider.impersonateAnotherUser(widget.userId);
      if (context.mounted) {
        context.push(AppRoutes.home.path);
      }
    }

    return FutureBuilder<LoadObjectResult>(
      future: _loadObjectResult,
      builder: (BuildContext context, AsyncSnapshot<LoadObjectResult?> userSnapshot) {
        return AppUtility.widgetForAsyncSnapshot(
          snapshot: userSnapshot,
          onReady: () {
            final User? user = userSnapshot.hasData ? userSnapshot.data?.object : null;

            if (user == null) {
              return const LoadingScreen();
            }
            final profileUtils = ProfileUtility.fromFindId(user);

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              child: Scaffold(
                appBar: AppBar(
                  automaticallyImplyLeading: false,
                  backgroundColor: context.colorScheme.secondaryContainer,
                  title: Text(
                    '${AppLocale.current.profileViewHeaderImpersonate} ${AppUtility.getUserFullName(user.firstName, user.lastName)}',
                  ),
                  centerTitle: false,
                  shape: AppUtility.topDivider(context),
                  actions: <Widget>[
                    IconButton(
                      icon: const Icon(MicromentorIcons.close),
                      onPressed: () {
                        context.pop();
                      },
                    ),
                  ],
                ),
                body: SafeArea(
                  child: ListView(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: context.theme.d.paddingMedium,
                          vertical: context.theme.d.paddingXxSmall,
                        ),
                        child: RecommendedUserCard(
                          profileUtils: profileUtils,
                          userId: user.id,
                          cardWidth: context.theme.d.mobileUserCardWidth,
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CommonButton.textButton(
                            context: context,
                            title: AppLocale.current.actionCancel,
                            onPressed: () {
                              router.pop();
                            },
                          ),
                          SizedBox(width: context.theme.d.paddingMedium),
                          CommonButton.primaryRoundedRectangle(
                            context: context,
                            title: AppLocale.current.actionImpersonate,
                            buttonSize: ButtonSize.small,
                            onPressed: () {
                              doImpersonate(context);
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
