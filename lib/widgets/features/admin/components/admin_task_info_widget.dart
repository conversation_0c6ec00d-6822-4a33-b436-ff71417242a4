import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';

import '../../../../constants/constants.dart';
import '../../../../services/graphql/providers/admin_provider.dart';
import '../../../shared/common_button.dart';

class AdminTaskInfoWidget extends StatelessWidget {
  final AdminTask adminTask;
  final Function() onRefresh;

  const AdminTaskInfoWidget({super.key, required this.adminTask, required this.onRefresh});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: context.theme.d.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Task Info',
            style: context.theme.textTheme.titleLarge!.copyWith(
              color: context.theme.colorScheme.surfaceBright,
              fontWeight: FontWeight.w400,
            ),
          ),
          Row(
            children: [
              Text(
                'Task ID: ',
                style: context.theme.textTheme.bodyMedium!.copyWith(
                  color: context.theme.colorScheme.surfaceBright,
                  fontWeight: FontWeight.w400,
                ),
              ),
              Text(adminTask.id, style: context.theme.textTheme.bodyMedium),
            ],
          ),
          Row(
            children: [
              Text(
                'Result: ',
                style: context.theme.textTheme.bodyMedium!.copyWith(
                  color: context.theme.colorScheme.surfaceBright,
                  fontWeight: FontWeight.w400,
                ),
              ),
              Text(adminTask.result?.name ?? 'N/A', style: context.theme.textTheme.bodyMedium),
            ],
          ),
          Row(
            children: [
              Text(
                'Result info: ',
                style: context.theme.textTheme.bodyMedium!.copyWith(
                  color: context.theme.colorScheme.surfaceBright,
                  fontWeight: FontWeight.w400,
                ),
              ),
              Text(adminTask.resultMessage ?? '', style: context.theme.textTheme.bodyMedium),
            ],
          ),
          Row(
            children: [
              Text(
                'Error: ',
                style: context.theme.textTheme.bodyMedium!.copyWith(
                  color: context.theme.colorScheme.surfaceBright,
                  fontWeight: FontWeight.w400,
                ),
              ),
              Text(adminTask.error ?? '', style: context.theme.textTheme.bodyMedium),
            ],
          ),
          SizedBox(height: context.theme.d.paddingMedium),
          CommonButton.primaryRoundedRectangle(
            context: context,
            key: const Key('btnRefresh'),
            title: 'Refresh',
            onPressed: onRefresh,
            buttonSize: ButtonSize.medium,
          ),
        ],
      ),
    );
  }
}
