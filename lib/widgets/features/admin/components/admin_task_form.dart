import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';
import 'package:textfield_tags/textfield_tags.dart';

import '../../../../constants/constants.dart';
import '../../../../services/graphql/providers/admin_provider.dart';
import '../../../../services/graphql/providers/base/operation_result.dart';
import '../../../../utilities/navigation_mixin.dart';
import '../../../widgets.dart';

class AdminTaskForm extends StatefulWidget {
  const AdminTaskForm({super.key});

  @override
  State<StatefulWidget> createState() => _AdminTaskFormState();
}

class _AdminTaskFormState extends State<AdminTaskForm> with NavigationMixin<AdminTaskForm> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late final AdminProvider _adminProvider;
  Future<OperationResult<List<AdminTaskDef>>>? _loadAdminTaskDefsResult;
  AdminTaskDef? _selectedAdminTaskDef;
  List<String> _args = [];
  AdminTask? _adminTask;

  late StringTagController _controller;

  @override
  void initState() {
    super.initState();
    _adminProvider = Provider.of<AdminProvider>(context, listen: false);
    _controller = StringTagController();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!isCurrentPageRoute()) return;

    _loadAdminTaskDefsResult = _adminProvider.findAdminTaskDefs();
  }

  List<String> _getAdminTaskDefDropdownList(List<AdminTaskDef> adminTaskDefs) {
    return adminTaskDefs.map((e) => e.label).toList();
  }

  void _onSubmit() async {
    debugPrint('type=${_selectedAdminTaskDef?.adminTaskType}, args=$_args');
    final result = await _adminProvider.createAdminTask(
      adminTaskType: _selectedAdminTaskDef!.adminTaskType,
      args: _args,
    );
    _onRefreshAdminTaskInfo(result.response?.id);
  }

  void _onRefreshAdminTaskInfo(String? adminTaskId) async {
    if (adminTaskId?.isNotEmpty != true) {
      adminTaskId = _adminTask?.id;
    }
    if (adminTaskId?.isNotEmpty == true) {
      final LoadObjectResult<AdminTask> loadAdminTaskResult = await _adminProvider
          .findAdminTaskById(adminTaskId!);
      setState(() {
        _adminTask = loadAdminTaskResult.object;
      });
      debugPrint('Received admin task info: $_adminTask');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!isCurrentPageRoute()) return const SizedBox.shrink();

    return FutureBuilder<OperationResult<List<AdminTaskDef>>>(
      future: _loadAdminTaskDefsResult,
      builder: (
        BuildContext context,
        AsyncSnapshot<OperationResult<List<AdminTaskDef>>?> userSnapshot,
      ) {
        final List<AdminTaskDef>? adminTaskDefs =
            userSnapshot.hasData ? userSnapshot.data!.response : null;

        if (adminTaskDefs?.isNotEmpty != true) {
          return const LoadingScreen();
        }

        Widget renderArgs() {
          if (_selectedAdminTaskDef?.args == null ||
              (_selectedAdminTaskDef?.args?.length ?? 0) < 1) {
            return const SizedBox.shrink();
          }

          List<Widget> children =
              _selectedAdminTaskDef!.args!.asMap().entries.map((entry) {
                int argIndex = entry.key;
                AdminTaskArgDef argDef = entry.value;
                return Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(
                    context.theme.d.zero,
                    context.theme.d.paddingLarge,
                    context.theme.d.zero,
                    context.theme.d.zero,
                  ),
                  child: AdminTaskArgWidget(
                    argDef: argDef,
                    argIndex: argIndex,
                    onChanged: (String value) {
                      for (int i = 0; i <= argIndex; i++) {
                        if (_args.length <= argIndex) {
                          _args.add(i == argIndex ? value : '');
                        } else if (i == argIndex) {
                          _args[i] = value;
                        }
                      }
                    },
                  ),
                );
              }).toList();

          return Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: children,
          );
        }

        return Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              AutocompletePicker(
                label: 'Admin Task Type',
                hint: 'Admin Task Type',
                singleSelect: true,
                tagsController: _controller,
                options: _getAdminTaskDefDropdownList(adminTaskDefs!),
                shouldScrollSelections: false,
                selectedOptions:
                    _selectedAdminTaskDef == null ? null : {_selectedAdminTaskDef?.label ?? ''},
                onChange: () {
                  final selectedValue = _controller.getTags?.firstOrNull;
                  setState(() {
                    _selectedAdminTaskDef =
                        adminTaskDefs.where((e) => e.label == selectedValue).firstOrNull;
                    _args = [];
                  });
                },
              ),
              renderArgs(),
              SizedBox(height: context.theme.d.paddingMedium),
              Center(
                child: CommonButton.primaryRoundedRectangle(
                  context: context,
                  key: const Key('btnSubmit'),
                  title: 'Run Task',
                  onPressed: _onSubmit,
                  buttonSize: ButtonSize.medium,
                ),
              ),
              if (_adminTask != null) SizedBox(height: context.theme.d.paddingMedium),
              if (_adminTask != null)
                AdminTaskInfoWidget(
                  adminTask: _adminTask!,
                  onRefresh: () {
                    _onRefreshAdminTaskInfo(_adminTask?.id);
                  },
                ),
            ],
          ),
        );
      },
    );
  }
}
