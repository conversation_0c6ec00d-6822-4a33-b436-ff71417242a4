import 'package:flutter/material.dart';
import 'package:mm_flutter_app/widgets/shared/autocomplete_picker.dart';
import 'package:textfield_tags/textfield_tags.dart';

import '../../../../services/graphql/providers/admin_provider.dart';
import '../../../shared/text_form_field_widget.dart';

class AdminTaskArgWidget extends StatefulWidget {
  final AdminTaskArgDef argDef;
  final int argIndex;
  final Function(String) onChanged;

  const AdminTaskArgWidget({
    super.key,
    required this.argDef,
    required this.argIndex,
    required this.onChanged,
  });

  @override
  State<StatefulWidget> createState() => _AdminTaskArgWidgetState();
}

class _AdminTaskArgWidgetState extends State<AdminTaskArgWidget> {
  late final TextEditingController _valueController;
  String? _selectedChoice;
  late StringTagController _controller;

  @override
  void initState() {
    super.initState();
    _valueController = TextEditingController();
    _controller = StringTagController();
  }

  @override
  void dispose() {
    try {
      _valueController.dispose();
    } catch (_) {}
    super.dispose();
  }

  Widget _inputWidget() {
    String label =
        widget.argDef.optional == true
            ? '${widget.argDef.label ?? 'Arg'} (optional)'
            : widget.argDef.label ?? 'Arg';
    if (widget.argDef.choices?.isNotEmpty == true) {
      return AutocompletePicker(
        label: label,
        hint: label,
        singleSelect: true,
        tagsController: _controller,
        options: widget.argDef.choices ?? [],
        shouldScrollSelections: false,
        selectedOptions: _selectedChoice == null ? null : {_selectedChoice ?? ''},
        onChange: () {
          setState(() {
            _selectedChoice = _controller.getTags?.firstOrNull;
            if (_selectedChoice?.isNotEmpty == true) {
              widget.onChanged(_selectedChoice!);
            }
          });
        },
      );
    }

    return TextFormFieldWidget(
      label: label,
      hint: widget.argDef.description ?? '',
      textController: _valueController,
      onChanged: widget.onChanged,
    );
  }

  @override
  Widget build(BuildContext context) {
    return _inputWidget();
  }
}
