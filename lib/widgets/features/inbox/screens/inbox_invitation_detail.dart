import 'package:another_flushbar/flushbar.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/models/locale_model.dart';
import 'package:mm_flutter_app/models/scaffold_model.dart';
import 'package:mm_flutter_app/services/firebase/analytic_service.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/channels_provider.dart';
import '../../../../services/graphql/providers/invitations_provider.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../../utilities/loading/loading_provider.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class InboxInvitationDetailScreen extends StatefulWidget {
  final String channelInvitationId;
  final MessageDirection invitationDirection;
  final bool autoAccept;

  const InboxInvitationDetailScreen({
    super.key,
    required this.channelInvitationId,
    required this.invitationDirection,
    this.autoAccept = false,
  });

  @override
  State<InboxInvitationDetailScreen> createState() => _InboxInvitationDetailScreenState();
}

class _InboxInvitationDetailScreenState extends State<InboxInvitationDetailScreen> {
  late final ChannelsProvider _channelsProvider;
  late final InvitationsProvider _invitationsProvider;
  late final UserProvider _userProvider;
  ChannelInvitationById? invitationResult;
  // assume the recipient has used MM3
  bool _invitationRecipientHasUsedMm3 = true;
  bool _isMarkedAsRead = false;
  bool showVacationPromptDialog = false;
  final GlobalKey<State> _dialogKey = GlobalKey<State>();
  final GlobalKey<State> _declineInviteDialogKey = GlobalKey<State>();
  late ScaffoldModel _scaffoldModel;
  late final LocaleModel _localeModel;

  GoRouter get router => GoRouter.of(context);

  @override
  void initState() {
    super.initState();
    _channelsProvider = Provider.of<ChannelsProvider>(context, listen: false);
    _scaffoldModel = Provider.of<ScaffoldModel>(context, listen: false);
    _localeModel = Provider.of<LocaleModel>(context, listen: false);
    _invitationsProvider = Provider.of<InvitationsProvider>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
  }

  Widget _createSenderCard(ChannelInvitationById invitation) {
    return ProfileQuickViewCard(sender: invitation.sender);
  }

  Widget _createRecipientCard(ChannelInvitationById invitation) {
    return ProfileQuickViewCard(recipient: invitation.recipient);
  }

  Widget _createMessagePopup(String messageText) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: context.theme.d.paddingXLarge),
      padding: EdgeInsets.all(context.theme.d.paddingMedium),
      decoration: BoxDecoration(
        color: context.colorScheme.onPrimary,
        border: Border.all(color: context.colorScheme.outlineVariant),
        borderRadius: BorderRadius.circular(context.theme.d.paddingMedium),
      ),
      child: Text(
        messageText,
        style: context.textTheme.titleMedium?.copyWith(color: context.colorScheme.onSurface),
      ),
    );
  }

  Widget _createDeclineAcceptButtons(String createdBy, String? senderName) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingXLarge),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CommonButton.primaryRoundedRectangle(
            context: context,
            title: AppLocale.current.actionAccept,
            buttonSize: ButtonSize.large,
            isFullWidth: true,
            onPressed: _acceptAction,
          ),
          SizedBox(height: context.theme.d.paddingMedium),
          CommonButton.outlinedButton(
            context: context,
            title: AppLocale.current.actionDecline,
            buttonSize: ButtonSize.large,
            isFullWidth: true,
            onPressed: () async {
              await showDialog(
                context: context,
                builder: (BuildContext context) {
                  return DeclineChannelInvitationDialog(
                    key: _declineInviteDialogKey,
                    name: senderName,
                    continueAction: (
                      Enum$DeclineChannelInvitationReasonTextId selectedReason,
                    ) async {
                      if (selectedReason == Enum$DeclineChannelInvitationReasonTextId.tooBusy) {
                        setState(() {
                          showVacationPromptDialog = true;
                        });
                      }
                      await _invitationsProvider.declineChannelInvitation(
                        channelInvitationId: widget.channelInvitationId,
                        reasonTextId: selectedReason,
                      );
                      AnalyticService.invitationDeclined();
                      if (!showVacationPromptDialog) {
                        _scaffoldModel.setParams(index: Tabs.inbox.index);
                        router.replace(AppRoutes.inboxInvitesReceived.path);
                      }
                    },
                  );
                },
              );

              if (mounted) {
                if (showVacationPromptDialog && !(_userProvider.myUser?.isOnVacation ?? false)) {
                  await _showVacationModePromptDialog();
                  _scaffoldModel.setParams(index: Tabs.inbox.index);
                  router.replace(AppRoutes.inboxInvitesReceived.path);
                }
              }
            },
          ),
        ],
      ),
    );
  }

  _acceptAction() async {
    Loader.show(context);
    String createdBy = invitationResult?.sender?.id ?? '';
    await _invitationsProvider.acceptChannelInvitation(
      channelInvitationId: widget.channelInvitationId,
      senderUserId: createdBy,
    );
    if (mounted) {
      Loader.hide(context);
    }
    AnalyticService.invitationAccepted();
    final ChannelForUser? newChannel =
        _channelsProvider.channels
            .where((e) => e.participants.any((p) => p.user.id == createdBy))
            .firstOrNull;
    _scaffoldModel.setParams(index: Tabs.inbox.index);
    if (newChannel != null) {
      router.replace('${AppRoutes.inboxChats.path}/${newChannel.id}');
    } else {
      router.replace(AppRoutes.inboxChats.path);
    }
  }

  Widget _createWithdrawButton(ThemeData theme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          AppLocale.current.inboxInvitesSentFooter,
          style: context.theme.textTheme.titleMedium?.copyWith(
            color: context.theme.colorScheme.scrim,
          ),
        ),
        SizedBox(height: context.theme.d.paddingSmall),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingXLarge),
          child: CommonButton.primaryRoundedRectangle(
            isFullWidth: true,
            context: context,
            title: AppLocale.current.inboxInvitesActionWithdraw,
            onPressed: () async {
              await _invitationsProvider.withdrawChannelInvitation(
                channelInvitationId: widget.channelInvitationId,
              );
              AnalyticService.invitationWithdraw();
              if (_scaffoldModel.selectedTabIndex == Tabs.inbox.index) {
                router.pop();
              } else {
                _scaffoldModel.setParams(index: Tabs.inbox.index);
                router.replace(AppRoutes.inboxInvitesSent.path);
              }
            },
            buttonSize: ButtonSize.small,
          ),
        ),
      ],
    );
  }

  Future<void> _markReceivedInvitationAsRead() async {
    await _invitationsProvider.markChannelInvitationAsSeenByMe(
      channelInvitationId: widget.channelInvitationId,
    );
    _isMarkedAsRead = true;
  }

  @override
  Widget build(BuildContext context) {
    if (invitationResult != null) return _content();

    return FutureBuilder(
      future: _invitationsProvider.findChannelInvitationById(
        channelInvitationId: widget.channelInvitationId,
      ),
      builder: (context, snapshot) {
        return AppUtility.widgetForAsyncSnapshot(
          snapshot: snapshot,
          onReady: () {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (widget.autoAccept) _acceptAction();
            });

            invitationResult = snapshot.data!.response!;
            _invitationRecipientHasUsedMm3 = invitationResult?.recipient?.hasSignedInToMm3 == true;
            return _content();
          },
        );
      },
    );
  }

  _content() {
    Widget Function(ChannelInvitationById)? userCardCreateFunction;
    AppBar? appBar;
    switch (widget.invitationDirection) {
      case MessageDirection.received:
        userCardCreateFunction = _createSenderCard;
        appBar = InboxAppBarFactory.createInviteReceivedDetailAppBar(
          key: _dialogKey,
          context: context,
          userId: invitationResult?.sender?.id ?? '',
          userFullName:
              '${invitationResult?.sender?.firstName} ${invitationResult?.sender?.lastName}',
        );
        if (!_isMarkedAsRead && invitationResult?.readByRecipientAt == null) {
          _markReceivedInvitationAsRead();
        }
        break;
      case MessageDirection.sent:
        userCardCreateFunction = _createRecipientCard;
        appBar = InboxAppBarFactory.createInviteSentDetailAppBar(
          key: _dialogKey,
          context: context,
          recipientUserId: invitationResult?.recipient?.id ?? '',
          recipentFullName:
              '${invitationResult?.recipient?.firstName} ${invitationResult?.recipient?.lastName}',
        );
        break;
      default:
        break;
    }
    return Scaffold(
      appBar: appBar,
      body: PopScope(
        canPop: false,
        onPopInvokedWithResult: (bool didPop, Object? _) async {
          if (didPop) return;
          if (_dialogKey.currentState != null && Navigator.canPop(_dialogKey.currentContext!)) {
            Navigator.of(_dialogKey.currentContext!).pop();
          }
          context.pop();
        },
        child: Column(
          children: [
            Expanded(
              child: ListView(
                children: [
                  if (userCardCreateFunction != null && invitationResult != null)
                    userCardCreateFunction(invitationResult!),
                  SizedBox(height: context.theme.d.paddingMedium),
                  if (invitationResult?.createdAt != null)
                    Center(
                      child: Text(
                        AppUtility.simplePastDateFormat(
                          context,
                          invitationResult!.createdAt.toLocal(),
                          locale: _localeModel.getCurrentLanguageCode(),
                        ),
                        style: context.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w700),
                      ),
                    ),
                  SizedBox(height: context.theme.d.paddingMedium),
                  _createMessagePopup(invitationResult?.messageText ?? ''),
                  if (invitationResult?.createdAt != null)
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: context.theme.d.paddingXLarge,
                        vertical: context.theme.d.paddingSmall,
                      ),
                      child: Align(
                        alignment: Alignment.bottomRight,
                        child: Text(
                          DateFormat.jm(
                            _localeModel.getCurrentLanguageCode(),
                          ).format(invitationResult!.createdAt.toLocal()).toLowerCase(),
                          style: context.textTheme.titleSmall?.copyWith(
                            color: context.colorScheme.onSecondaryContainer,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            widget.invitationDirection == MessageDirection.received
                ? _createDeclineAcceptButtons(
                  invitationResult?.sender?.id ?? '',
                  '${invitationResult?.sender?.firstName} ${invitationResult?.sender?.lastName}',
                )
                : widget.invitationDirection == MessageDirection.sent &&
                    _invitationRecipientHasUsedMm3
                ? _createWithdrawButton(context.theme)
                : const SizedBox(),
          ],
        ),
      ),
    );
  }

  _showVacationModePromptDialog() async {
    await showDialog(
      context: context,
      builder: (dialogContext) {
        return DialogTemplate(
          title: AppLocale.current.vacationPromptDialogTitle,
          description: AppLocale.current.vacationPromptDialogMsg,
          onAction: () async {
            Loader.show(dialogContext);

            await _userProvider.updateUser(
              input: Input$UserInput(id: _userProvider.myUser?.id, isOnVacation: true),
            );

            if (dialogContext.mounted) {
              Loader.hide(dialogContext);

              Flushbar(
                message: AppLocale.current.vacationModeOn,
                messageColor: dialogContext.theme.colorScheme.surface,
                messageSize: dialogContext.theme.d.fontSizeMedium,
                duration: AppUtility.snackBarDuration,
              ).show(dialogContext);

              Navigator.pop(dialogContext);
            }
          },
          actionButtonTitle: AppLocale.current.vacationPromptDialogActionButtonTitle,
          cancelButtonTitle: AppLocale.current.vacationPromptDialogCancelButtonTitle,
        );
      },
    );
  }
}
