import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/invitations_provider.dart';
import '../../../../utilities/navigation_mixin.dart';
import '../../../../utilities/utility.dart';
import '../inbox.dart';

class InboxInvitesSentScreen extends StatefulWidget {
  final String? selectedInviteId;
  final Function(String?)? onSelect;
  final String? selectedInvitationId;
  const InboxInvitesSentScreen({
    super.key,
    this.selectedInviteId,
    this.onSelect,
    this.selectedInvitationId,
  });

  @override
  State<InboxInvitesSentScreen> createState() => _InboxInvitesSentScreenState();
}

class _InboxInvitesSentScreenState extends State<InboxInvitesSentScreen>
    with NavigationMixin<InboxInvitesSentScreen> {
  late final InvitationsProvider _invitationsProvider;
  static const int tabBarIndex = 1;

  @override
  void initState() {
    super.initState();

    _invitationsProvider = Provider.of<InvitationsProvider>(context, listen: false);
    if (widget.selectedInviteId == null) {
      //By default 1st invite will be selected
      if (_invitationsProvider.sentInvitations.isEmpty) return;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onSelect?.call(_invitationsProvider.sentInvitations.firstOrNull?.id);
      });
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!isCurrentPageRoute()) return;
    if (AppUtility.displayDesktopUI(context, isChatScreen: true)) return;
    _invitationsProvider.clearSentInvitation();
    _invitationsProvider.loadMyInvitations(sent: true);
  }

  void _refreshTabIndex(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      TabController? tabController = DefaultTabController.maybeOf(context);
      if (tabController != null && tabController.index != tabBarIndex) {
        tabController.animateTo(tabBarIndex);
      }
    });
  }

  InboxListTile _createTile(MyChannelInvitation invitation) {
    return InboxListTile(
      isSelected: widget.selectedInviteId == invitation.id,
      avatarUrl: invitation.recipient?.avatarUrl,
      channelName: '${invitation.recipient?.firstName} ${invitation.recipient?.lastName}',
      date: invitation.createdAt.toLocal(),
      message: invitation.messageText ?? '',
      highlightTileText: false,
      showArchiveOption: false,
      onPressed: () {
        (AppUtility.displayDesktopUI(context, isChatScreen: true))
            ? widget.onSelect?.call(invitation.id)
            : router.push('${AppRoutes.inboxInvitesSent.path}/${invitation.id}');
      },
    );
  }

  List<InboxListTile> _createTileList(List<MyChannelInvitation> invitations) {
    List<InboxListTile> tiles = [];
    for (MyChannelInvitation invitation in invitations) {
      tiles.add(_createTile(invitation));
    }
    return tiles;
  }

  List<Widget> _createContentList(List<InboxListTile> tiles) {
    if (tiles.isEmpty) {
      return [];
    }
    List<Widget> contentList = [tiles.first];
    for (int i = 1; i < tiles.length + 1; i++) {
      if (i == tiles.length) {
        contentList.add(_moreIcon());
      } else {
        contentList.addAll([
          Divider(
            indent: context.theme.d.paddingSmall,
            endIndent: context.theme.d.paddingMedium,
            color: context.colorScheme.primaryContainer,
            thickness: 1,
          ),
          tiles[i],
        ]);
      }
    }
    return contentList;
  }

  _moreIcon() {
    if (_invitationsProvider.endOfResultsSent) {
      return const SizedBox.shrink();
    }
    return AppUtility.seeMoreButton(
      context,
      onTap: () {
        _invitationsProvider.loadMyInvitations(sent: true);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    _refreshTabIndex(context);

    return Selector<InvitationsProvider, List<MyChannelInvitation>?>(
      selector: (_, invitationProvider) => invitationProvider.sentInvitations,
      shouldRebuild:
          (oldValue, newValue) =>
              !(const DeepCollectionEquality.unordered().equals(oldValue, newValue)) ||
              _invitationsProvider.asyncState != AsyncState.loading,
      builder: (_, invitations, __) {
        return AppUtility.widgetForAsyncState(
          state: _invitationsProvider.asyncState,
          onError: () {
            return EmptyStateMessage(
              iconPath: Assets.noInvites,
              text: AppLocale.current.errorMessageUnknownError,
              height: context.theme.d.boxSizeMedium,
            );
          },
          onReady: () {
            if (invitations?.isNotEmpty != true) {
              return EmptyStateMessage(
                iconPath: Assets.noInvites,
                text: AppLocale.current.inboxInvitesEmptyState,
                height: context.theme.d.boxSizeMedium,
              );
            }
            return ListView(
              padding: EdgeInsetsDirectional.only(
                start: context.theme.d.paddingSmall,
                end: context.theme.d.paddingSmall,
              ),
              children: _createContentList(_createTileList(invitations!)),
            );
          },
        );
      },
    );
  }
}
