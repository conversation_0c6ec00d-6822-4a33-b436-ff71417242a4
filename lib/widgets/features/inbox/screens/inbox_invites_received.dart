import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/invitations_provider.dart';
import '../../../../utilities/navigation_mixin.dart';
import '../../../../utilities/utility.dart';
import '../inbox.dart';

class InboxInvitesReceivedScreen extends StatefulWidget {
  final String? selectedInviteId;
  final Function(String?)? onSelect;
  const InboxInvitesReceivedScreen({super.key, this.selectedInviteId, this.onSelect});

  @override
  State<InboxInvitesReceivedScreen> createState() => _InboxInvitesReceivedScreenState();
}

class _InboxInvitesReceivedScreenState extends State<InboxInvitesReceivedScreen>
    with NavigationMixin<InboxInvitesReceivedScreen> {
  late final InvitationsProvider _invitationsProvider;
  static const int tabBarIndex = 0;

  @override
  void initState() {
    super.initState();

    _invitationsProvider = Provider.of<InvitationsProvider>(context, listen: false);
    if (widget.selectedInviteId == null) {
      //By default 1st invite will be selected
      if (_invitationsProvider.receivedInvitations.isEmpty) return;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onSelect?.call(_invitationsProvider.receivedInvitations.firstOrNull?.id);
      });
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!isCurrentPageRoute()) return;
    if (AppUtility.displayDesktopUI(context, isChatScreen: true)) return;
    _invitationsProvider.clearReceivedInvitation();
    _invitationsProvider.loadMyInvitations(sent: false);
  }

  void _refreshTabIndex(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      TabController? tabController = DefaultTabController.maybeOf(context);
      if (tabController != null && tabController.index != tabBarIndex) {
        tabController.animateTo(tabBarIndex);
      }
    });
  }

  InboxListTile _createTile(MyChannelInvitation invitation) {
    final bool isUnseenByMe = invitation.readByRecipientAt == null;
    return InboxListTile(
      isSelected: widget.selectedInviteId == invitation.id,
      avatarUrl: invitation.sender?.avatarUrl,
      channelName: '${invitation.sender?.firstName} ${invitation.sender?.lastName}',
      date: invitation.createdAt.toLocal(),
      message: invitation.messageText ?? AppLocale.current.inboxInvitesReceivedMessage,
      showPlainNotificationBubble: isUnseenByMe,
      highlightTileText: isUnseenByMe,
      showArchiveOption: false,
      onPressed: () {
        (AppUtility.displayDesktopUI(context, isChatScreen: true))
            ? widget.onSelect?.call(invitation.id)
            : router.push('${AppRoutes.inboxInvitesReceived.path}/${invitation.id}');
      },
    );
  }

  List<InboxListTile> _createTileList(List<MyChannelInvitation> invitations) {
    List<InboxListTile> tiles = [];
    for (MyChannelInvitation invitation in invitations) {
      tiles.add(_createTile(invitation));
    }
    return tiles;
  }

  List<Widget> _createContentList(List<InboxListTile> tiles) {
    if (tiles.isEmpty) {
      return [];
    }
    List<Widget> contentList = [tiles.first];
    for (int i = 1; i < tiles.length + 1; i++) {
      if (i == tiles.length) {
        contentList.add(_moreIcon());
      } else {
        contentList.addAll([
          Divider(height: context.theme.d.zero, indent: context.theme.d.paddingSmall),
          tiles[i],
        ]);
      }
    }
    return contentList;
  }

  _moreIcon() {
    if (_invitationsProvider.endOfResultsReceived) {
      return const SizedBox.shrink();
    }
    return AppUtility.seeMoreButton(
      context,
      onTap: () {
        _invitationsProvider.loadMyInvitations(sent: false);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    _refreshTabIndex(context);

    return Selector<InvitationsProvider, List<MyChannelInvitation>?>(
      selector: (_, invitationProvider) => invitationProvider.receivedInvitations,
      shouldRebuild:
          (oldValue, newValue) =>
              !(const DeepCollectionEquality.unordered().equals(oldValue, newValue)) ||
              _invitationsProvider.asyncState != AsyncState.loading,
      builder: (_, invitations, __) {
        return AppUtility.widgetForAsyncState(
          state: _invitationsProvider.asyncState,
          onError: () {
            return EmptyStateMessage(
              iconPath: Assets.noInvites,
              text: AppLocale.current.errorMessageUnknownError,
              height: context.theme.d.boxSizeMedium,
            );
          },
          onReady: () {
            if (invitations?.isNotEmpty != true) {
              return EmptyStateMessage(
                iconPath: Assets.noInvites,
                text: AppLocale.current.inboxInvitesEmptyState,
                height: context.theme.d.boxSizeMedium,
              );
            }
            return ListView(
              padding: EdgeInsetsDirectional.only(
                start: context.theme.d.paddingSmall,
                end: context.theme.d.paddingSmall,
              ),
              children: _createContentList(_createTileList(invitations!)),
            );
          },
        );
      },
    );
  }
}
