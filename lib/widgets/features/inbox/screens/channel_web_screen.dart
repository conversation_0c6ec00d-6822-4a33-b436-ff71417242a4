import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/utility.dart';
import '../../featured_widgets.dart';

class ChannelWebScreen extends StatelessWidget {
  final String channelId;
  final Channel? channel;
  final bool isArchivedForUser;
  final Function? onArchiveUnarchiveAction;
  final Function? onBlockUnblockAction;
  final String? myUserId;

  const ChannelWebScreen({
    super.key,
    required this.channelId,
    this.channel,
    required this.isArchivedForUser,
    this.onArchiveUnarchiveAction,
    this.onBlockUnblockAction,
    this.myUserId,
  });

  @override
  Widget build(BuildContext context) {
    return _content(context);
  }

  _content(BuildContext context) {
    if (channel == null) return const SizedBox.shrink();
    final participant =
        myUserId == null
            ? null
            : channel?.participants.firstWhere((item) => item.user.id != myUserId).user;
    if (participant == null) {
      return const SizedBox();
    }
    return Scaffold(
      appBar: InboxAppBarFactory.createChannelMessagesAppBar(
        context: context,
        showBackButton: false,
        userFullName: AppUtility.getUserFullName(participant.firstName, participant.lastName),
        channelId: channelId,
        isArchivedForUser: isArchivedForUser,
        userId: participant.id,
        avatarUrl: participant.avatarUrl,
        onArchiveUnarchiveAction: () {
          onArchiveUnarchiveAction?.call();
        },
        onBlockUnblockAction: () {
          onBlockUnblockAction?.call();
        },
      ),
      body: ChangeNotifierProvider(
        create: (context) => ChatModel(context: context, channelId: channel!.id),
        child: ChannelWidget(channel: channel!),
      ),
    );
  }
}
