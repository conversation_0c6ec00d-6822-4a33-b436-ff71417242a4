import 'package:another_flushbar/flushbar.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/models/locale_model.dart';
import 'package:mm_flutter_app/models/scaffold_model.dart';
import 'package:mm_flutter_app/services/firebase/analytic_service.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/channels_provider.dart';
import '../../../../services/graphql/providers/invitations_provider.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../../utilities/loading/loading_provider.dart';
import '../../../../utilities/navigation_mixin.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class InboxWebInvitationDetailScreen extends StatelessWidget {
  final String channelInvitationId;
  final MessageDirection invitationDirection;
  final ChannelInvitationById invitationResult;
  final bool invitationRecipientHasUsedMm3;
  final bool autoAccept;
  final Function(String?)? onAccept;
  final Function? onAction;

  const InboxWebInvitationDetailScreen({
    super.key,
    required this.channelInvitationId,
    required this.invitationDirection,
    required this.invitationResult,
    required this.invitationRecipientHasUsedMm3,
    this.autoAccept = false,
    this.onAccept,
    this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    final GlobalKey<State> dialogKey = GlobalKey<State>();
    return Scaffold(
      body: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: context.theme.d.paddingMedium,
              vertical: context.theme.d.paddingSmall,
            ),
            color: context.colorScheme.secondaryContainer,
            child: Row(
              children: [
                Flexible(
                  child: InkWell(
                    onTap: () {
                      GoRouter.of(context).pushNamed(
                        AppRoutes.profileId.name,
                        pathParameters: {
                          RouteParams.userId:
                              invitationDirection == MessageDirection.sent
                                  ? (invitationResult.recipient?.id ?? '')
                                  : (invitationResult.sender?.id ?? ''),
                        },
                      );
                    },
                    child: UserProfileBasicDetails(
                      recipient:
                          invitationDirection == MessageDirection.sent
                              ? invitationResult.recipient
                              : null,
                      sender:
                          invitationDirection == MessageDirection.received
                              ? invitationResult.sender
                              : null,
                      avatarSize: context.theme.d.imageSizeLarge,
                    ),
                  ),
                ),
                UserActionPopupMenu(
                  key: dialogKey,
                  userFullName:
                      invitationDirection == MessageDirection.sent
                          ? AppUtility.getUserFullName(
                            invitationResult.recipient?.firstName,
                            invitationResult.recipient?.lastName,
                          )
                          : AppUtility.getUserFullName(
                            invitationResult.sender?.firstName,
                            invitationResult.sender?.lastName,
                          ),
                  userId:
                      (invitationDirection == MessageDirection.sent
                          ? invitationResult.recipient?.id
                          : invitationResult.sender?.id) ??
                      '',
                  includeBlockUserOption: true,
                ),
              ],
            ),
          ),
          Expanded(
            child: WebInvitationDetails(
              channelInvitationId: channelInvitationId,
              invitationResult: invitationResult,
              invitationDirection: invitationDirection,
              invitationRecipientHasUsedMm3: invitationRecipientHasUsedMm3,
              autoAccept: autoAccept,
              onAccept: onAccept,
              onAction: onAction,
            ),
          ),
        ],
      ),
    );
  }
}

class WebInvitationDetails extends StatefulWidget {
  final String channelInvitationId;
  final MessageDirection invitationDirection;
  final ChannelInvitationById invitationResult;
  final bool invitationRecipientHasUsedMm3;
  final bool autoAccept;
  final Function(String?)? onAccept;
  final Function? onAction;

  const WebInvitationDetails({
    super.key,
    required this.channelInvitationId,
    required this.invitationDirection,
    required this.invitationResult,
    required this.invitationRecipientHasUsedMm3,
    this.autoAccept = false,
    this.onAccept,
    this.onAction,
  });

  @override
  State<WebInvitationDetails> createState() => _WebInvitationDetailsState();
}

class _WebInvitationDetailsState extends State<WebInvitationDetails>
    with NavigationMixin<WebInvitationDetails> {
  late final ChannelsProvider _channelsProvider;
  late final InvitationsProvider _invitationsProvider;
  late final UserProvider _userProvider;
  bool _isMarkedAsRead = false;
  bool showVacationPromptDialog = false;
  final GlobalKey<State> _declineInviteDialogKey = GlobalKey<State>();
  late ScaffoldModel _scaffoldModel;
  late final LocaleModel _localeModel;

  @override
  void initState() {
    super.initState();
    _channelsProvider = Provider.of<ChannelsProvider>(context, listen: false);
    _scaffoldModel = Provider.of<ScaffoldModel>(context, listen: false);
    _localeModel = Provider.of<LocaleModel>(context, listen: false);
    _invitationsProvider = Provider.of<InvitationsProvider>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.autoAccept) _acceptAction();
    });
  }

  Future<void> _markReceivedInvitationAsRead() async {
    await _invitationsProvider.markChannelInvitationAsSeenByMe(
      channelInvitationId: widget.channelInvitationId,
    );
    _isMarkedAsRead = true;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.invitationDirection == MessageDirection.received) {
      if (!_isMarkedAsRead && widget.invitationResult.readByRecipientAt == null) {
        _markReceivedInvitationAsRead();
      }
    }

    return Column(
      children: [
        Flexible(
          child: ListView(
            shrinkWrap: true,
            children: [
              SizedBox(height: context.theme.d.paddingMedium),
              Center(
                child: Text(
                  AppUtility.simplePastDateFormat(
                    context,
                    widget.invitationResult.createdAt.toLocal(),
                    locale: _localeModel.getCurrentLanguageCode(),
                  ),
                  style: context.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w700),
                ),
              ),
              SizedBox(height: context.theme.d.paddingMedium),
              _createMessagePopup(widget.invitationResult.messageText ?? ''),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: context.theme.d.paddingXLarge,
                  vertical: context.theme.d.paddingSmall,
                ),
                child: Align(
                  alignment: Alignment.bottomRight,
                  child: Text(
                    DateFormat.jm(
                      _localeModel.getCurrentLanguageCode(),
                    ).format(widget.invitationResult.createdAt.toLocal()).toLowerCase(),
                    style: context.textTheme.titleSmall?.copyWith(
                      color: context.colorScheme.onSecondaryContainer,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        _getButtons(),
      ],
    );
  }

  Widget _createMessagePopup(String? messageText) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: context.theme.d.paddingXLarge),
      padding: EdgeInsets.all(context.theme.d.paddingMedium),
      decoration: BoxDecoration(
        color: context.colorScheme.onPrimary,
        border: Border.all(color: context.colorScheme.outlineVariant),
        borderRadius: BorderRadius.circular(context.theme.d.paddingMedium),
      ),
      child: Text(
        messageText ?? '',
        style: context.textTheme.titleMedium?.copyWith(color: context.colorScheme.onSurface),
      ),
    );
  }

  Widget _createDeclineAcceptButtons(String createdBy, String? senderName) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingXLarge * 3),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _declineButton(senderName),
          SizedBox(width: context.theme.d.paddingMedium),
          _acceptButton(createdBy, senderName),
        ],
      ),
    );
  }

  Widget _createWithdrawButton(ThemeData theme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          AppLocale.current.inboxInvitesSentFooter,
          style: context.theme.textTheme.titleMedium?.copyWith(
            color: context.theme.colorScheme.scrim,
          ),
        ),
        SizedBox(height: context.theme.d.paddingSmall),
        CommonButton.primaryRoundedRectangle(
          context: context,
          title: AppLocale.current.inboxInvitesActionWithdraw,
          onPressed: () async {
            Loader.show(context);
            await _invitationsProvider.withdrawChannelInvitation(
              channelInvitationId: widget.channelInvitationId,
            );
            if (mounted) {
              Loader.hide(context);
            }
            AnalyticService.invitationWithdraw();
            widget.onAction?.call();
          },
          buttonSize: ButtonSize.medium,
        ),
      ],
    );
  }

  _showVacationModePromptDialog() async {
    await showDialog(
      context: context,
      builder: (dialogContext) {
        return DialogTemplate(
          title: AppLocale.current.vacationPromptDialogTitle,
          description: AppLocale.current.vacationPromptDialogMsg,
          isFullWidthButtons: false,
          showTitleInCenter: false,
          onAction: () async {
            Loader.show(context);

            await _userProvider.updateUser(
              input: Input$UserInput(id: _userProvider.myUser?.id, isOnVacation: true),
            );

            if (mounted) {
              Loader.hide(context);

              Flushbar(
                message: AppLocale.current.vacationModeOn,
                messageColor: context.theme.colorScheme.surface,
                messageSize: context.theme.d.fontSizeMedium,
                duration: AppUtility.snackBarDuration,
              ).show(context);

              if (dialogContext.mounted) {
                Navigator.of(dialogContext).pop();
              }
            }
          },
          actionButtonTitle: AppLocale.current.vacationPromptDialogActionButtonTitle,
          cancelButtonTitle: AppLocale.current.vacationPromptDialogCancelButtonTitle,
        );
      },
    );
  }

  Widget _getButtons() {
    switch (widget.invitationDirection) {
      case MessageDirection.received:
        return _createDeclineAcceptButtons(
          widget.invitationResult.sender?.id ?? '',
          '${widget.invitationResult.sender?.firstName} ${widget.invitationResult.sender?.lastName}',
        );
      case MessageDirection.sent:
        return widget.invitationRecipientHasUsedMm3
            ? _createWithdrawButton(context.theme)
            : const SizedBox();

      default:
        return const SizedBox();
    }
  }

  Widget _declineButton(String? senderName) {
    return Expanded(
      child: CommonButton.outlinedButton(
        context: context,
        title: AppLocale.current.actionDecline,
        isFullWidth: true,
        buttonSize: ButtonSize.medium,
        onPressed: () async {
          await showDialog(
            context: context,
            builder: (BuildContext context) {
              return DeclineChannelInvitationDialog(
                key: _declineInviteDialogKey,
                name: senderName,
                continueAction: (Enum$DeclineChannelInvitationReasonTextId selectedReason) async {
                  if (selectedReason == Enum$DeclineChannelInvitationReasonTextId.tooBusy) {
                    setState(() {
                      showVacationPromptDialog = true;
                    });
                  }
                  await _invitationsProvider.declineChannelInvitation(
                    channelInvitationId: widget.channelInvitationId,
                    reasonTextId: selectedReason,
                  );

                  AnalyticService.invitationDeclined();
                  if (!showVacationPromptDialog || _userProvider.myUser?.isOnVacation == true) {
                    widget.onAction?.call();
                  }
                },
              );
            },
          );

          if (mounted) {
            if (showVacationPromptDialog && !(_userProvider.myUser?.isOnVacation ?? false)) {
              await _showVacationModePromptDialog();
              widget.onAction?.call();
            }
          }
        },
      ),
    );
  }

  Widget _acceptButton(String createdBy, String? senderName) {
    return Expanded(
      child: CommonButton.primaryRoundedRectangle(
        context: context,
        title: AppLocale.current.actionAccept,
        buttonSize: ButtonSize.medium,
        isFullWidth: true,
        onPressed: _acceptAction,
      ),
    );
  }

  _acceptAction() async {
    final createdBy = widget.invitationResult.sender?.id ?? '';
    Loader.show(context);
    await _invitationsProvider.acceptChannelInvitation(
      channelInvitationId: widget.channelInvitationId,
      senderUserId: createdBy,
    );
    if (mounted) {
      Loader.hide(context);
    }
    AnalyticService.invitationAccepted();
    final ChannelForUser? newChannel =
        _channelsProvider.channels
            .where((e) => e.participants.any((p) => p.user.id == createdBy))
            .firstOrNull;
    _scaffoldModel.setParams(index: Tabs.inbox.index);
    widget.onAccept?.call(newChannel?.id);
  }
}
