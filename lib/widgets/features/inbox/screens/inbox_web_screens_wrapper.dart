import 'package:flutter/material.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/base/operation_result.dart';
import 'package:mm_flutter_app/services/graphql/providers/channels_provider.dart';
import 'package:mm_flutter_app/services/graphql/providers/invitations_provider.dart';
import 'package:mm_flutter_app/services/graphql/providers/user_provider.dart';
import 'package:mm_flutter_app/utilities/loading/loading_provider.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../../../constants/parts/micromentor_icons.dart';
import '../../../widgets.dart';

class InboxWebScreensWrapper extends StatefulWidget {
  final String? selectedInviteId;
  final InviteTab? inviteTab;
  final String? channelId;
  final bool autoAccept;
  final InboxDrawersMenu? selectedInboxMenu;
  const InboxWebScreensWrapper({
    super.key,
    this.selectedInboxMenu,
    this.selectedInviteId,
    this.inviteTab,
    this.channelId,
    this.autoAccept = false,
  });

  @override
  State<InboxWebScreensWrapper> createState() => _InboxWebScreensWrapperState();
}

class _InboxWebScreensWrapperState extends State<InboxWebScreensWrapper>
    with TickerProviderStateMixin {
  late final ChannelsProvider _channelsProvider;
  late final UserProvider _userProvider;
  late final InvitationsProvider _invitationsProvider;
  late Future<OperationResult<ChannelInvitationById>> _invitation;
  // assume the recipient has used MM3
  bool _invitationRecipientHasUsedMm3 = true;
  InboxDrawersMenu _selectedMenu = InboxDrawersMenu.chats;
  late InviteTab _inviteTab;
  String? selectedChannelId;
  bool showArchivedUnarchiveFooterMsg = false;
  String? latestArchivedChannelId;
  late TabController _controller;

  // Invite Section
  String? selectedInvitationId;
  bool isReceivedDataLoaded = false;
  bool isSentDataLoaded = false;

  @override
  void initState() {
    super.initState();
    _controller = TabController(length: 2, vsync: this);
    _inviteTab = InviteTab.received;
    _controller.index = 0;
    _channelsProvider = Provider.of<ChannelsProvider>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _invitationsProvider = Provider.of<InvitationsProvider>(context, listen: false);
    _invitationsProvider.clearReceivedInvitation();
    _invitationsProvider.clearSentInvitation();
    selectedChannelId = widget.channelId;

    if (widget.selectedInviteId != null) {
      selectedInvitationId = widget.selectedInviteId;
      _selectedMenu = InboxDrawersMenu.invites;
      _inviteTab = widget.inviteTab ?? InviteTab.received;
      _controller.index = _inviteTab == InviteTab.received ? 0 : 1;
    }

    if (widget.selectedInboxMenu != null) {
      _selectedMenu = widget.selectedInboxMenu!;
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomLeft,
      children: [
        Column(
          children: [
            const Divider(height: 1, thickness: 0.1),
            Expanded(
              child: Row(
                children: [
                  Expanded(flex: 2, child: _leftDrawerWidget()),
                  VerticalDivider(
                    thickness: 0.7,
                    width: 0,
                    color: context.colorScheme.onSurface.withValues(alpha: 0.3),
                  ),
                  Expanded(flex: 3, child: _mainWidget()),
                  VerticalDivider(
                    thickness: 0.7,
                    width: 0,
                    color: context.colorScheme.onSurface.withValues(alpha: 0.3),
                  ),
                  Expanded(flex: 7, child: _detailsWidget()),
                ],
              ),
            ),
          ],
        ),
        if (showArchivedUnarchiveFooterMsg) _chatArchivedUnarchiveFooter(),
      ],
    );
  }

  _leftDrawerWidget() {
    return InboxDrawerFactory.createInboxDrawer(
      Theme.of(context),
      onSelect: (menu) {
        setState(() {
          _selectedMenu = menu;
          _resetAll();
          _channelsProvider.clearChannels();
        });
      },
      selectedMenu: _selectedMenu,
    );
  }

  _mainWidget() {
    switch (_selectedMenu) {
      case InboxDrawersMenu.chats:
      case InboxDrawersMenu.archivedChats:
        return InboxChatListScreen(
          key: Key(
            _selectedMenu == InboxDrawersMenu.archivedChats ? 'channelListArchived' : 'channelList',
          ),
          isArchivedForUser: _selectedMenu == InboxDrawersMenu.archivedChats,
          selectedChannelId: selectedChannelId,
          onArchiveUnrchiveAction: (channelId) {
            setState(() {
              if (_selectedMenu == InboxDrawersMenu.chats) {
                showArchivedUnarchiveFooterMsg = true;
                latestArchivedChannelId = channelId;
                _dismissFooter();
              }
              if (channelId == selectedChannelId) {
                selectedChannelId = null;
              }
              _channelsProvider.clearChannels();
            });
          },
          onSelect: (channelId) async {
            if (channelId == null) return;
            setState(() {
              selectedChannelId = channelId;
            });
          },
          onLoadingComplete: () => setState(() {}), // Callback to set loading status
        );
      case InboxDrawersMenu.invites:
        return _inviteMainScreen();
    }
  }

  _detailsWidget() {
    if (_selectedMenu == InboxDrawersMenu.invites) return _inviteDetailScreen();

    if (!_channelsProvider.isDataLoaded) {
      return const SizedBox();
    }

    if (_channelsProvider.isDataLoaded &&
        ((_selectedMenu == InboxDrawersMenu.chats &&
                _channelsProvider.unarchivedChannels.isEmpty) ||
            (_selectedMenu == InboxDrawersMenu.archivedChats &&
                _channelsProvider.archivedChannels.isEmpty))) {
      return const SizedBox();
    }

    if (selectedChannelId == null) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(Assets.noChatImage, height: context.theme.d.boxSizeXxLarge),
          Text(
            AppLocale.current.inboxChatsNoChatSelectedMsg,
            style: context.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w700),
          ),
        ],
      );
    }

    return FutureBuilder(
      future: _channelsProvider.findChannelById(channelId: selectedChannelId!),
      builder: (context, snapshot) {
        return AppUtility.widgetForAsyncSnapshot(
          snapshot: snapshot,
          onReady: () {
            Channel channel = snapshot.data!.response!;
            return ChannelWebScreen(
              channelId: selectedChannelId!,
              channel: channel,
              isArchivedForUser: _selectedMenu == InboxDrawersMenu.archivedChats,
              myUserId: _userProvider.myUser?.id,
              onArchiveUnarchiveAction: () {
                setState(() {
                  if (_selectedMenu == InboxDrawersMenu.chats) {
                    showArchivedUnarchiveFooterMsg = true;
                    latestArchivedChannelId = selectedChannelId;
                    _dismissFooter();
                  }
                  selectedChannelId = null;
                  _channelsProvider.clearChannels();
                });
              },
              onBlockUnblockAction: () {
                setState(() {
                  selectedChannelId = null;
                  _channelsProvider.clearChannels();
                });
              },
            );
          },
        );
      },
    );
  }

  Widget _chatArchivedUnarchiveFooter() {
    return Container(
      width: context.theme.d.webArchiveMessageCardSize.width,
      height: context.theme.d.webArchiveMessageCardSize.height,
      padding: EdgeInsets.symmetric(
        vertical: context.theme.d.paddingXxSmall,
        horizontal: context.theme.d.paddingSmall,
      ),
      margin: EdgeInsets.only(
        left: context.theme.d.paddingXSmall * 2,
        bottom: context.theme.d.paddingMedium,
        right: context.theme.d.paddingXSmall * 2,
      ),
      decoration: BoxDecoration(
        color: context.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusMedium),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(width: context.theme.d.paddingXSmall),
          Expanded(
            child: Text(
              AppLocale.current.chatArchivedTitle,
              overflow: TextOverflow.ellipsis,
              style: context.theme.textTheme.titleSmall?.copyWith(
                color: context.theme.colorScheme.onSurface,
              ),
            ),
          ),
          CommonButton.textButton(
            context: context,
            title: AppLocale.current.undoTitle,
            isFullWidth: false,
            onPressed: () async {
              if (latestArchivedChannelId != null) {
                await _unarchiveAction();
              }
            },
          ),
          IconButton(
            onPressed: () {
              setState(() {
                showArchivedUnarchiveFooterMsg = false;
              });
            },
            icon: Icon(
              MicromentorIcons.close,
              size: context.theme.d.iconSizeSmall,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  _unarchiveAction() async {
    Loader.show(context);
    await _channelsProvider.unarchiveChannelForMe(channelId: latestArchivedChannelId!);
    if (mounted) {
      Loader.hide(context);
    }
    setState(() {
      showArchivedUnarchiveFooterMsg = false;
      if (latestArchivedChannelId == selectedChannelId) {
        selectedChannelId = null;
      }
      _channelsProvider.clearChannels();
    });
  }

  _dismissFooter() {
    Future.delayed(const Duration(seconds: 20), () {
      if (showArchivedUnarchiveFooterMsg) {
        setState(() {
          showArchivedUnarchiveFooterMsg = false;
        });
      }
    });
  }

  // Invite screen
  _inviteMainScreen() {
    bool isDataLoaded = _inviteTab == InviteTab.received ? isReceivedDataLoaded : isSentDataLoaded;

    final body =
        (_inviteTab == InviteTab.received)
            ? InboxInvitesReceivedScreen(
              selectedInviteId: selectedInvitationId,
              onSelect: (id) {
                setState(() {
                  selectedInvitationId = id;
                });
              },
            )
            : InboxInvitesSentScreen(
              selectedInviteId: selectedInvitationId,
              onSelect: (id) {
                setState(() {
                  selectedInvitationId = id;
                });
              },
            );

    return Scaffold(
      appBar: AppBar(
        flexibleSpace: InboxInvitationAppbar(
          controller: _controller,
          onTabChange: (index) {
            setState(() {
              _controller.index = index;
              _inviteTab = index == 0 ? InviteTab.received : InviteTab.sent;
              selectedInvitationId = null;
            });
          },
        ),
      ),
      body:
          isDataLoaded
              ? body
              : FutureBuilder<LoadObjectResult<List<ChannelInvitation>>>(
                initialData: null,
                future: _invitationsProvider.loadMyInvitations(sent: _inviteTab == InviteTab.sent),
                builder:
                    (context, snapshot) => AppUtility.widgetForAsyncSnapshot(
                      snapshot: snapshot,
                      onReady: () {
                        _inviteTab == InviteTab.received
                            ? isReceivedDataLoaded = true
                            : isSentDataLoaded = true;
                        return body;
                      },
                    ),
              ),
    );
  }

  _inviteDetailScreen() {
    if (selectedInvitationId == null) return const SizedBox();
    MessageDirection invitationDirection =
        _inviteTab == InviteTab.received ? MessageDirection.received : MessageDirection.sent;

    _invitation = _invitationsProvider.findChannelInvitationById(
      channelInvitationId: selectedInvitationId!,
    );
    _invitation.then(
      (value) =>
          _invitationRecipientHasUsedMm3 = value.response!.recipient?.hasSignedInToMm3 == true,
    );

    return FutureBuilder(
      future: _invitation,
      builder: (context, snapshot) {
        return AppUtility.widgetForAsyncSnapshot(
          snapshot: snapshot,
          onReady: () {
            final ChannelInvitationById invitationResult = snapshot.data!.response!;
            return InboxWebInvitationDetailScreen(
              channelInvitationId: selectedInvitationId!,
              invitationResult: invitationResult,
              invitationDirection: invitationDirection,
              invitationRecipientHasUsedMm3: _invitationRecipientHasUsedMm3,
              autoAccept: widget.autoAccept,
              onAccept: (newChannel) {
                _resetAll();
                setState(() {
                  _selectedMenu = InboxDrawersMenu.chats;
                  if (newChannel != null) selectedChannelId = newChannel;
                });
              },
              onAction: () {
                switch (invitationDirection) {
                  case MessageDirection.received:
                    setState(() {
                      selectedInvitationId = null;
                      _invitationsProvider.clearReceivedInvitation();
                      isReceivedDataLoaded = false;
                    });
                    break;
                  case MessageDirection.sent:
                    setState(() {
                      selectedInvitationId = null;
                      _invitationsProvider.clearSentInvitation();
                      isSentDataLoaded = false;
                    });
                    break;
                  default:
                }
              },
            );
          },
        );
      },
    );
  }

  _resetAll() {
    selectedChannelId = null;
    showArchivedUnarchiveFooterMsg = false;
    latestArchivedChannelId = null;
    selectedInvitationId = null;

    _inviteTab = InviteTab.received;
    _controller.index = 0;
    _invitationsProvider.clearReceivedInvitation();
    _invitationsProvider.clearSentInvitation();
    isReceivedDataLoaded = false;
    isSentDataLoaded = false;
  }
}
