import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:provider/provider.dart';

import '../../../../models/models.dart';
import '../../../../services/graphql/providers/base/operation_result.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/navigation_mixin.dart';
import '../../../../utilities/utility.dart';
import '../../featured_widgets.dart';

class ChannelScreen extends StatefulWidget {
  final String channelId;
  final bool isArchivedForUser;

  const ChannelScreen({super.key, required this.channelId, required this.isArchivedForUser});

  @override
  State<ChannelScreen> createState() => _ChannelScreenState();
}

class _ChannelScreenState extends State<ChannelScreen> with NavigationMixin<ChannelScreen> {
  late final ChannelsProvider _channelsProvider;
  late final UserProvider _userProvider;
  late Future<OperationResult<Channel>> _loadChannelResult;

  @override
  void initState() {
    super.initState();
    _channelsProvider = Provider.of<ChannelsProvider>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _loadChannelResult = _channelsProvider.findChannelById(channelId: widget.channelId);
  }

  @override
  void didUpdateWidget(covariant ChannelScreen oldWidget) {
    if (oldWidget.channelId != widget.channelId) {
      _loadChannelResult = _channelsProvider.findChannelById(channelId: widget.channelId);
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<OperationResult<Channel>>(
      future: _loadChannelResult,
      builder: (context, snapshot) {
        return AppUtility.widgetForAsyncSnapshot(
          snapshot: snapshot,
          onReady: () {
            Channel? channel = snapshot.data?.response;
            return _content(channel);
          },
        );
      },
    );
  }

  Widget _content(Channel? channel) {
    if (channel == null) return const SizedBox.shrink();

    final participant =
        _userProvider.myUser == null
            ? null
            : channel.participants
                .firstWhere((item) => item.user.id != _userProvider.myUser!.id)
                .user;
    if (participant == null) return const SizedBox.shrink();
    return Scaffold(
      appBar: InboxAppBarFactory.createChannelMessagesAppBar(
        context: context,
        userFullName: AppUtility.getUserFullName(participant.firstName, participant.lastName),
        channelId: widget.channelId,
        isArchivedForUser: widget.isArchivedForUser,
        userId: participant.id,
        avatarUrl: participant.avatarUrl,
        onBlockUnblockAction: () {
          _channelsProvider.clearChannels();
          context.go(AppRoutes.inboxChats.path);
        },
      ),
      body: ChangeNotifierProvider(
        create: (context) => ChatModel(context: context, channelId: channel.id),
        child: ChannelWidget(channel: channel),
      ),
    );
  }
}
