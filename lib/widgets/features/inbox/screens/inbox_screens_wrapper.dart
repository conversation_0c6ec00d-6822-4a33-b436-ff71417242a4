import 'package:flutter/material.dart';
import 'package:mm_flutter_app/utilities/navigation_mixin.dart';
import 'package:mm_flutter_app/utilities/utility.dart';

import '../../../../constants/constants.dart';
import '../../../../services/extensions.dart';
import '../../../widgets.dart';

class InboxScreensWrapper extends StatefulWidget {
  const InboxScreensWrapper({super.key});

  @override
  State<InboxScreensWrapper> createState() => _InboxScreensWrapperState();
}

class _InboxScreensWrapperState extends State<InboxScreensWrapper>
    with NavigationMixin<InboxScreensWrapper>, TickerProviderStateMixin {
  late TabController _controller;
  InboxDrawersMenu _selectedMenu = InboxDrawersMenu.chats;

  @override
  void initState() {
    _controller = TabController(length: 2, vsync: this);
    _controller.index = 0;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _mobileView();
  }

  Widget getContent() {
    switch (_selectedMenu) {
      case InboxDrawersMenu.chats:
        return const InboxChatListScreen(isArchivedForUser: false);
      case InboxDrawersMenu.invites:
        return (_controller.index == 0)
            ? const InboxInvitesReceivedScreen()
            : const InboxInvitesSentScreen();
      case InboxDrawersMenu.archivedChats:
        return const InboxChatListScreen(isArchivedForUser: true);
    }
  }

  Widget _mobileView() {
    return Scaffold(
      appBar: InboxAppBarFactory.createInboxAppBar(
        inboxDrawersMenu: _selectedMenu,
        theme: context.theme,
        bottom: bottomAppBar(),
      ),
      body: getContent(),
      drawer: InboxDrawerFactory.createInboxDrawer(
        Theme.of(context),
        onSelect: (menu) {
          setState(() {
            _controller.index = 0;
            _selectedMenu = menu;
          });
          if (AppUtility.displayDesktopUI(context)) Navigator.pop(context);
        },
        selectedMenu: _selectedMenu,
      ),
    );
  }

  PreferredSizeWidget? bottomAppBar() {
    if (_selectedMenu != InboxDrawersMenu.invites) return null;
    return InboxInvitationAppbar(
      controller: _controller,
      onTabChange: (index) {
        setState(() {
          _controller.index = index;
        });
      },
    );
  }
}
