import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_swipe_action_cell/core/cell.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/loading/loading_provider.dart';
import 'package:provider/provider.dart';
import 'package:tuple/tuple.dart';

import '../../../../__generated/schema/operations_user_inbox.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/navigation_mixin.dart';
import '../../../../utilities/utility.dart';
import '../inbox.dart';

class InboxChatListScreen extends StatefulWidget {
  final bool isArchivedForUser;
  final String? selectedChannelId;
  final Function(String channelId)? onArchiveUnrchiveAction;
  final Function(String?)? onSelect;
  final VoidCallback? onLoadingComplete; // Callback for when loading is complete

  const InboxChatListScreen({
    super.key,
    this.selectedChannelId,
    required this.isArchivedForUser,
    this.onArchiveUnrchiveAction,
    this.onSelect,
    this.onLoadingComplete,
  });

  @override
  State<InboxChatListScreen> createState() => _InboxChatListScreenState();
}

class _InboxChatListScreenState extends State<InboxChatListScreen>
    with NavigationMixin<InboxChatListScreen> {
  late final ChannelsProvider _channelsProvider;
  late final InboxProvider _inboxProvider;
  late final UserProvider _userProvider;
  late List<Widget> _tiles;
  late bool isDesktop;

  @override
  void initState() {
    super.initState();
    _channelsProvider = Provider.of<ChannelsProvider>(context, listen: false);
    _inboxProvider = Provider.of<InboxProvider>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    isDesktop = AppUtility.displayDesktopUI(context, isChatScreen: true);
    if (!isCurrentPageRoute()) {
      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_channelsProvider.isDataLoaded || _userProvider.myUser == null) {
      return _content();
    }
    _channelsProvider.clearChannels();
    return FutureBuilder<LoadObjectResult<List<ChannelForUser>>>(
      initialData: null,
      future: _channelsProvider.loadMyChannels(),
      builder:
          (context, snapshot) => AppUtility.widgetForAsyncSnapshot(
            snapshot: snapshot,
            onReady: () {
              if (isDesktop) {
                // required this call back in desktop view to reload the details widget
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  widget.onLoadingComplete?.call();
                });
              }
              return _content();
            },
          ),
    );
  }

  _getChannels() {
    return widget.isArchivedForUser
        ? _channelsProvider.archivedChannels
        : _channelsProvider.unarchivedChannels;
  }

  _content() {
    return Selector<
      InboxProvider,
      Tuple2<List<Query$MyInbox$myInbox$channels$unseenMessages>, List<InboxLatestMessage>>
    >(
      selector:
          (_, inboxProvider) => Tuple2(
            inboxProvider.inbox?.channels?.unseenMessages ?? [],
            inboxProvider.inbox?.channels?.latestMessages ?? [],
          ),
      builder: (_, __, ___) {
        return AppUtility.widgetForAsyncState(
          state: _channelsProvider.asyncState,
          onReady: () {
            _channelsProvider.isDataLoaded = true;

            if (_getChannels().isEmpty) {
              return EmptyStateMessage(
                iconPath: Assets.emptyInbox,
                text: AppLocale.current.inboxChatsEmptyState,
                height: context.theme.d.boxSizeXXLarge,
              );
            }
            return ListView(
              padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingSmall),
              children: _createContentList(),
            );
          },
        );
      },
    );
  }

  List<Widget> _createContentList() {
    User? user = _userProvider.myUser;

    if (user == null) return [];
    _tiles = [];
    List<ChannelForUser> channels = _getChannels();

    // Sort channels by latestMessage creation time, starting from most recent
    // TODO: in backend, we could add a separate query for messages where the latest message is not nullable,
    //  so we don't have to check for null in lib/services/graphql/providers/channels_provider.dart and here
    channels.sort((a, b) {
      if (a.latestMessage != null && b.latestMessage != null) {
        return b.latestMessage!.createdAt.toLocal().compareTo(a.latestMessage!.createdAt.toLocal());
      }
      return 0;
    });

    for (int i = 0; i < channels.length; i++) {
      final ChannelForUser channel = channels[i];
      ChannelForUserParticipant? otherParticipant = _getOtherParticipants(channel, user.id);
      // if there is no other participant in the channel, remove it from the list to be displayed
      if (otherParticipant == null) {
        channels.removeAt(i);
        i--;
        continue;
      }
      final String channelName = AppUtility.getUserFullName(
        otherParticipant.user.firstName,
        otherParticipant.user.lastName,
      );
      final String? channelAvatarUrl = otherParticipant.user.avatarUrl;
      final InboxLatestMessage? latestMessageFromInbox = _getLatestMessage(channel.id);

      if (latestMessageFromInbox == null ||
          latestMessageFromInbox.messageText == null ||
          latestMessageFromInbox.messageText!.isEmpty) {
        if (channel.latestMessage == null ||
            channel.latestMessage!.messageText == null ||
            channel.latestMessage!.messageText!.isEmpty) {
          channels.removeAt(i);
          i--;
          continue;
        }
      }

      _tiles.add(
        isDesktop
            ? _desktopInboxTile(channel, channelName, channelAvatarUrl, latestMessageFromInbox)
            : _mobileInboxTile(channel, channelName, channelAvatarUrl, latestMessageFromInbox),
      );
    }

    List<Widget> contentList = [_tiles.first];
    for (int i = 1; i < _tiles.length; i++) {
      contentList.addAll([
        Divider(
          indent: context.theme.d.paddingSmall,
          endIndent: context.theme.d.paddingMedium,
          color: context.colorScheme.primaryContainer,
          thickness: 1,
        ),
        _tiles[i],
      ]);
    }
    // TODO - temporary fix for see more channels
    // contentList.add(_moreIcon());
    contentList.add(_maximumViewableChatHistoryWidget());

    if (widget.selectedChannelId == null) {
      //By default 1st chat will be selected
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onSelect?.call(channels.first.id);
      });
    }
    return contentList;
  }

  /*   _moreIcon() {
    if (_channelsProvider.endOfResults) {
      return const SizedBox.shrink();
    }
    return AppUtility.seeMoreButton(context, onTap: () async {
      setState(() {});
    });
  } */

  Widget _maximumViewableChatHistoryWidget() {
    if (_channelsProvider.endOfResults) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
      child: Container(
        decoration: BoxDecoration(
          color: context.colorScheme.secondaryContainer,
          borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusMedium),
          boxShadow: [
            BoxShadow(
              color: context.colorScheme.shadow.withValues(alpha: 0.1),
              blurRadius: context.theme.d.roundedRectRadiusSmall,
              spreadRadius: 1,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        padding: EdgeInsets.all(context.theme.d.paddingMedium),
        child: Row(
          children: [
            Icon(
              MicromentorIcons.warningAmberRounded,
              color: context.colorScheme.primary.withValues(alpha: 0.8),
            ),
            SizedBox(width: context.theme.d.paddingMedium),
            Expanded(
              child: Text(
                AppLocale.current.maximumViewableChatHistoryMessage,
                style: context.textTheme.titleSmall?.copyWith(
                  color: context.colorScheme.primary.withValues(alpha: 0.8),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _dismissTile(String channelId) async {
    _tiles.removeWhere((tile) {
      return tile.key == ValueKey(channelId);
    });

    Loader.show(context);
    if (widget.isArchivedForUser) {
      await _channelsProvider.unarchiveChannelForMe(channelId: channelId);
    } else {
      await _channelsProvider.archiveChannelForMe(channelId: channelId);
    }
    if (mounted) {
      Loader.hide(context);
    }
    // TODO - to check the use of below code
    // Refresh Scaffold and channels only once the change is live.
    /*final bool updateCompleted = await CrashHandler.retryOnException<bool>(
      () async {
        final result =
            await _channelsProvider.findChannelById(channelId: channelId);
        final bool? isArchived = result.response?.isArchivedForMe;
        if (isArchived == null || isArchived == widget.isArchivedForUser) {
          throw RetryException(
            message: 'Waiting for isArchivedForMe to update...',
          );
        }
        return true;
      },
      onFailOperation: () => false,
      logFailures: false,
    );

    if (updateCompleted) {
      if (widget.isArchivedForUser) {
        await _channelsProvider.unarchiveChannelForMe(channelId: channelId);
      } else {
        await _channelsProvider.archiveChannelForMe(channelId: channelId);
      }
    }*/

    (isDesktop) ? widget.onArchiveUnrchiveAction?.call(channelId) : setState(() {});
  }

  Widget _inboxTile(
    ChannelForUser channel,
    String channelName,
    String? channelAvatarUrl,
    InboxLatestMessage? latestMessageFromInbox,
  ) {
    bool isSelected = widget.selectedChannelId == channel.id;
    return InboxChatListTile(
      isSelected: isSelected,
      channelId: channel.id,
      channelName: channelName,
      channelAvatarUrl: channelAvatarUrl,
      isArchivedForUser: widget.isArchivedForUser,
      onChatArchiveUnarchiveAction:
          AppUtility.displayDesktopUI(context)
              ? () {
                _dismissTile(channel.id);
              }
              : null,
      // latestMessage should always be non-null with the correct logic in the Provider,
      // but we still check for null to avoid null safety issues
      // TODO: don't fall back to DateTime.now(), latestMessage should not be nullable here
      latestMessageDate:
          latestMessageFromInbox?.createdAt.toLocal() ?? channel.latestMessage!.createdAt.toLocal(),
      latestMessageText: latestMessageFromInbox?.messageText ?? channel.latestMessage!.messageText!,
      unseenMessageCount: _inboxProvider.badge.messagesByChannel[channel.id] ?? 0,
      nextRoute:
          widget.isArchivedForUser
              ? '${AppRoutes.inboxArchived.path}/${channel.id}'
              : '${AppRoutes.inboxChats.path}/${channel.id}',
      onTap: () {
        widget.onSelect?.call(channel.id);
      },
    );
  }

  Widget _desktopInboxTile(
    ChannelForUser channel,
    String channelName,
    String? channelAvatarUrl,
    InboxLatestMessage? latestMessageFromInbox,
  ) {
    return Padding(
      key: ValueKey(channel.id),
      padding: EdgeInsetsDirectional.only(
        start: context.theme.d.paddingSmall,
        end: context.theme.d.paddingMedium,
      ),
      child: _inboxTile(channel, channelName, channelAvatarUrl, latestMessageFromInbox),
    );
  }

  Widget _mobileInboxTile(
    ChannelForUser channel,
    String channelName,
    String? channelAvatarUrl,
    InboxLatestMessage? latestMessageFromInbox,
  ) {
    return SwipeActionCell(
      key: ValueKey(channel.id),
      trailingActions: <SwipeAction>[
        SwipeAction(
          icon: Icon(
            widget.isArchivedForUser
                ? MicromentorIcons.unarchiveOutlined
                : MicromentorIcons.archiveOutlined,
            color: context.theme.colorScheme.primary,
          ),
          onTap: (CompletionHandler handler) async {
            await handler(true);
            _dismissTile(channel.id);
          },
          color: context.theme.colorScheme.primaryContainer,
        ),
      ],
      child: Padding(
        padding: EdgeInsetsDirectional.only(
          start: context.theme.d.paddingSmall,
          end: context.theme.d.paddingMedium,
        ),
        child: _inboxTile(channel, channelName, channelAvatarUrl, latestMessageFromInbox),
      ),
    );
  }

  ChannelForUserParticipant? _getOtherParticipants(ChannelForUser channel, String userId) {
    return channel.participants.firstWhereOrNull((item) => item.user.id != userId);
  }

  InboxLatestMessage? _getLatestMessage(channelId) {
    return _inboxProvider.inbox?.channels?.latestMessages?.firstWhereOrNull(
      (msg) => msg.channelId == channelId,
    );
  }
}
