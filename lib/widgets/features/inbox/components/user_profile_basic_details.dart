import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../profile/profile.dart';

class UserProfileBasicDetails extends StatelessWidget {
  final RecipientUser? recipient;
  final SenderUser? sender;
  final double? avatarSize;

  const UserProfileBasicDetails({super.key, this.recipient, this.sender, this.avatarSize});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        ProfileImageWidget(
          size: avatarSize,
          avatarUrl: recipient?.avatarUrl ?? sender?.avatarUrl,
          isMentor: isMentor(),
        ),
        <PERSON><PERSON><PERSON><PERSON>(width: context.theme.d.paddingMedium),
        _createNameAndBadges(context, context.theme),
      ],
    );
  }

  Widget _createNameAndBadges(BuildContext context, ThemeData theme) {
    List<Widget> widgets = [
      if ((countryOfResidence()?.isNotEmpty ?? false))
        Padding(
          padding: EdgeInsets.symmetric(vertical: theme.d.paddingXxSmall),
          child: Row(
            children: [
              Icon(
                MicromentorIcons.locationOnOutlined,
                size: theme.d.iconSizeSmall,
                color: theme.colorScheme.primary,
              ),
              Flexible(
                child: Text(
                  countryOfResidence() ?? '',
                  softWrap: true,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
        ),
      Text(
        fullName(),
        style: theme.textTheme.bodyLarge?.copyWith(
          color: theme.colorScheme.onSecondaryContainer,
          fontWeight: FontWeight.w700,
        ),
      ),
      Consumer<UserProvider>(
        builder: (context, newUserProvider, _) {
          if (AppUtility.isUserBlocked(newUserProvider.myUser, recipient?.id ?? sender?.id)) {
            return Text(
              AppLocale.current.blockedUsersIndicator,
              style: theme.textTheme.labelSmall?.copyWith(color: theme.colorScheme.onSurface),
            );
          }
          return const SizedBox();
        },
      ),
      if (!isMentor())
        Padding(
          padding: EdgeInsets.symmetric(vertical: theme.d.paddingXxSmall),
          child: Text(
            '${companyName()}',
            style: theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.onSurface),
          ),
        ),
      if (isMentor())
        if (companyDetails().isNotEmpty)
          Text(
            companyDetails(),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: theme.textTheme.bodyLarge!.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w400,
            ),
          ),
    ];

    return Expanded(child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: widgets));
  }

  bool isMentor() {
    return recipient != null ? recipient?.offersHelp == true : sender?.offersHelp == true;
  }

  String? countryOfResidence() {
    return (recipient != null
            ? recipient?.countryOfResidence?.translatedValue
            : sender?.countryOfResidence?.translatedValue) ??
        AppLocale.current.defaultValueLocation;
  }

  String fullName() {
    return recipient != null
        ? AppUtility.getUserFullName(recipient?.firstName, recipient?.lastName)
        : AppUtility.getUserFullName(sender?.firstName, sender?.lastName);
  }

  String? companyName() {
    return recipient != null
        ? recipient?.companies?.firstOrNull?.name
        : sender?.companies?.firstOrNull?.name;
  }

  String? companyRole() {
    return recipient != null
        ? recipient?.businessExperiences?.firstOrNull?.jobTitle
        : sender?.businessExperiences?.firstOrNull?.jobTitle;
  }

  String companyDetails() {
    return [companyRole(), companyName()].nonNulls.join(AppLocale.current.listSeparator);
  }
}
