import 'package:flutter/material.dart';

import '../../../../services/extensions.dart';

class TextDivider extends StatelessWidget {
  final String text;
  const TextDivider({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Expanded(child: Divider()),
        Sized<PERSON><PERSON>(width: context.theme.d.paddingSmall),
        Text(
          text,
          style: context.theme.textTheme.labelSmall?.copyWith(
            color: context.theme.colorScheme.outline,
          ),
        ),
        SizedBox(width: context.theme.d.paddingSmall),
        const Expanded(child: Divider()),
      ],
    );
  }
}
