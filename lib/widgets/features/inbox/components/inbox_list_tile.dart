import 'package:flutter/material.dart';
import 'package:mm_flutter_app/models/locale_model.dart';
import 'package:provider/provider.dart';

import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../services/extensions.dart';
import '../../../../utilities/utility.dart';
import '../../../shared/notification_bubble.dart';
import '../../profile/components/profile_image_widget.dart';

class InboxListTile extends StatelessWidget {
  final String? avatarUrl;
  final String channelName;
  final DateTime date;
  final String message;
  final int notifications;
  final bool showPlainNotificationBubble;
  final bool highlightTileText;
  final bool simplifyDate;
  final String? datePrefix;
  final VoidCallback onPressed;
  final bool isSelected;
  final bool isArchivedForUser;
  final bool showArchiveOption;
  final Function? onChatArchiveAction;

  const InboxListTile({
    super.key,
    this.avatarUrl,
    required this.channelName,
    required this.date,
    required this.message,
    this.notifications = 0,
    required this.onPressed,
    this.showPlainNotificationBubble = false,
    this.highlightTileText = false,
    this.simplifyDate = false,
    this.datePrefix,
    this.isSelected = false,
    this.onChatArchiveAction,
    this.isArchivedForUser = false,
    this.showArchiveOption = true,
  });

  String _getDate(BuildContext context, String locale) {
    if (!simplifyDate) {
      return AppUtility.timestampDateFormat(context, date, locale: locale);
    } else if (datePrefix == null) {
      return AppUtility.simplePastDateFormat(context, date, capitalize: true, locale: locale);
    } else {
      return '$datePrefix ${AppUtility.simplePastDateFormat(context, date, capitalize: false, locale: locale)}';
    }
  }

  @override
  Widget build(BuildContext context) {
    final LocaleModel localeModel = Provider.of<LocaleModel>(context, listen: false);
    bool isHovered = false;
    bool isDesktop = AppUtility.displayDesktopUI(context);

    return isDesktop && showArchiveOption
        ? StatefulBuilder(
          builder: (context, setState) {
            return MouseRegion(
              onEnter: (_) => setState(() => isHovered = true),
              onExit: (_) => setState(() => isHovered = false),
              child: _listTile(
                context,
                localeModel: localeModel,
                isHovered: isHovered,
                isDesktop: isDesktop,
              ),
            );
          },
        )
        : _listTile(context, localeModel: localeModel, isDesktop: isDesktop);
  }

  Widget _listTile(
    BuildContext context, {
    required LocaleModel localeModel,
    bool isHovered = false,
    required bool isDesktop,
  }) {
    return ListTile(
      selected: isSelected,
      selectedTileColor: context.colorScheme.secondaryContainer,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(context.theme.d.paddingXSmall * 2),
      ),
      hoverColor:
          isSelected
              ? context.colorScheme.secondaryContainer
              : context.colorScheme.primaryContainer,
      onTap: onPressed,
      contentPadding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall),
      leading: ProfileImageWidget(avatarUrl: avatarUrl, size: context.theme.d.imageSizeMedium),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              channelName,
              maxLines: 1,
              style: context.theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w700,
                color: context.theme.colorScheme.onSecondaryContainer,
              ),
              // TODO - fix ellipsis error
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: context.theme.d.paddingXSmall),
          Text(
            _getDate(context, localeModel.getCurrentLanguageCode()),
            maxLines: 1,
            textAlign: TextAlign.end,
            style: context.theme.textTheme.labelSmall?.copyWith(
              color: context.theme.colorScheme.scrim,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
      subtitle: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              message,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: context.theme.textTheme.bodyMedium?.copyWith(
                fontWeight: highlightTileText ? FontWeight.w600 : FontWeight.w400,
                color: context.theme.colorScheme.onSurface,
              ),
            ),
          ),
          if (isDesktop && showArchiveOption && (isHovered || isSelected))
            IconButton(
              icon: Icon(
                isArchivedForUser
                    ? MicromentorIcons.unarchiveOutlined
                    : MicromentorIcons.archiveOutlined,
                color: context.colorScheme.onSurface,
              ),
              padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingXxSmall),
              iconSize: context.theme.d.iconSizeMedium,
              constraints: BoxConstraints(
                minWidth: context.theme.d.iconSizeXLarge,
                minHeight: context.theme.d.iconSizeMedium,
              ),
              onPressed: () {
                onChatArchiveAction?.call();
              },
            ),
          if (showPlainNotificationBubble || notifications > 0)
            NotificationBubble(
              value: showPlainNotificationBubble ? null : notifications,
              showEmptyBubble: showPlainNotificationBubble,
            ),
        ],
      ),
    );
  }
}
