import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/utilities/utility.dart';

import 'inbox_list_tile.dart';

class InboxChatListTile extends StatelessWidget {
  final String channelId;
  final String? channelAvatarUrl;
  final String channelName;
  final DateTime latestMessageDate;
  final String latestMessageText;
  final int unseenMessageCount;
  final Function? onTap;
  final bool isSelected;
  final String? nextRoute;
  final bool isArchivedForUser;
  final Function? onChatArchiveUnarchiveAction;

  const InboxChatListTile({
    super.key,
    required this.channelId,
    this.channelAvatarUrl,
    required this.channelName,
    required this.unseenMessageCount,
    required this.latestMessageDate,
    required this.latestMessageText,
    required this.isArchivedForUser,
    this.isSelected = false,
    this.nextRoute,
    this.onTap,
    this.onChatArchiveUnarchiveAction,
  });

  @override
  Widget build(BuildContext context) {
    final bool isHighlighted = unseenMessageCount > 0;
    return InboxListTile(
      isSelected: isSelected,
      avatarUrl: channelAvatarUrl,
      channelName: channelName,
      date: latestMessageDate,
      message: latestMessageText,
      notifications: unseenMessageCount,
      highlightTileText: isHighlighted,
      onChatArchiveAction: onChatArchiveUnarchiveAction,
      isArchivedForUser: isArchivedForUser,
      onPressed: () {
        if (AppUtility.displayDesktopUI(context, isChatScreen: true)) {
          onTap?.call();
        } else {
          context.push(nextRoute ?? '');
        }
      },
    );
  }
}
