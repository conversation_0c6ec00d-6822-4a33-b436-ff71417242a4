import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';

import '../../../../constants/parts/micromentor_icons.dart';

class InboxInvitationAppbar extends StatelessWidget implements PreferredSizeWidget {
  final TabController controller;
  final Function(int)? onTabChange;

  const InboxInvitationAppbar({super.key, required this.controller, this.onTabChange});

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    bool isDesktop = AppUtility.displayDesktopUI(context, isChatScreen: true);
    return TabBar(
      controller: controller,
      indicatorSize: TabBarIndicatorSize.tab,
      dividerHeight: context.theme.d.zero,
      tabs: [_getTabWidget(context, false, isDesktop), _getTabWidget(context, true, isDesktop)],
      onTap: (index) {
        onTabChange?.call(index);
      },
    );
  }

  _getTabWidget(BuildContext context, bool sent, bool isDesktop) {
    return Tab(
      child: FittedBox(
        fit: BoxFit.fitWidth,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              size: isDesktop ? context.theme.d.iconSizeMedium : null,
              sent ? MicromentorIcons.sendRounded : MicromentorIcons.mailRounded,
              color: context.colorScheme.onSurface,
            ),
            SizedBox(width: context.theme.d.paddingSmall),
            Text(
              //this spacing is required for same font size while autoSizing
              sent
                  ? '${AppLocale.current.inboxInvitesSent}        '
                  : AppLocale.current.inboxInvitesReceived,
              textAlign: TextAlign.center,
              style: context.textTheme.titleMedium?.copyWith(
                color: context.colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
