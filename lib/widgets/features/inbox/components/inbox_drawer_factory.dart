import 'package:flutter/material.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';
import 'package:tuple/tuple.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../shared/notification_bubble.dart';

class InboxDrawerFactory {
  InboxDrawerFactory._private();

  static Drawer? createInboxDrawer(
    ThemeData theme, {
    Function(InboxDrawersMenu)? onSelect,
    required InboxDrawersMenu selectedMenu,
  }) {
    return Drawer(
      child: SafeArea(
        child: Builder(
          builder: (context) {
            return Selector<InboxProvider, Tuple3<int, int, int>>(
              selector:
                  (_, inboxProvider) => Tuple3(
                    inboxProvider.badge.channelMessages,
                    inboxProvider.badge.channelInvitations,
                    inboxProvider.badge.archivedChannelMessages,
                  ),
              builder: (_, tuple3, __) {
                final inboxChatNotifications = tuple3.item1;
                final inboxInvitesNotifications = tuple3.item2;
                final inboxArchivedNotifications = tuple3.item3;
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: context.theme.d.drawerHeaderHeight,
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                AppLocale.current.inboxTitle,
                                style: theme.textTheme.titleLarge?.copyWith(
                                  color: theme.colorScheme.onSecondaryContainer,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall),
                        children: [
                          InboxMenuItem(
                            inboxDrawersMenu: InboxDrawersMenu.chats,
                            label: AppLocale.current.inboxChatsTitle,
                            icon: MicromentorIcons.chatBubbleOutline,
                            theme: theme,
                            notificationBadge: inboxChatNotifications,
                            onSelect: onSelect,
                            isSelected: selectedMenu == InboxDrawersMenu.chats,
                          ),
                          InboxMenuItem(
                            inboxDrawersMenu: InboxDrawersMenu.invites,
                            label: AppLocale.current.inboxInvitesTitle,
                            icon: MicromentorIcons.personAddOutlined,
                            theme: theme,
                            notificationBadge: inboxInvitesNotifications,
                            onSelect: onSelect,
                            isSelected: selectedMenu == InboxDrawersMenu.invites,
                          ),
                          InboxMenuItem(
                            inboxDrawersMenu: InboxDrawersMenu.archivedChats,
                            label: AppLocale.current.inboxArchivedChatsTitle,
                            icon: MicromentorIcons.inventory2outlined,
                            theme: theme,
                            notificationBadge: inboxArchivedNotifications,
                            onSelect: onSelect,
                            isSelected: selectedMenu == InboxDrawersMenu.archivedChats,
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            );
          },
        ),
      ),
    );
  }
}

class InboxMenuItem extends StatelessWidget {
  final ThemeData theme;
  final InboxDrawersMenu inboxDrawersMenu;
  final Function(InboxDrawersMenu)? onSelect;
  final String label;
  final IconData icon;
  final int notificationBadge;
  final bool isSelected;
  const InboxMenuItem({
    super.key,
    required this.theme,
    required this.inboxDrawersMenu,
    required this.icon,
    required this.label,
    required this.notificationBadge,
    required this.onSelect,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: context.theme.d.highlightBorderWidth),
      child: ListTile(
        tileColor: isSelected ? context.colorScheme.secondaryContainer : null,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(context.theme.d.paddingMedium),
        ),
        leading: Icon(icon, color: theme.colorScheme.onSurface),
        title: Text(
          label,
          style: theme.textTheme.titleMedium?.copyWith(
            color: theme.colorScheme.onSurface,
            fontWeight: FontWeight.w700,
          ),
        ),
        trailing:
            notificationBadge > 0
                ? NotificationBubble(value: notificationBadge, enlarge: true)
                : null,
        onTap: () {
          if (!AppUtility.displayDesktopUI(context)) {
            Navigator.of(context).pop();
          }

          onSelect?.call(inboxDrawersMenu);
        },
      ),
    );
  }
}
