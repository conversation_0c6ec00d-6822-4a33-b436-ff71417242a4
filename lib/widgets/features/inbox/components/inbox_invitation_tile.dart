import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../models/locale_model.dart';
import '../../../../services/extensions.dart';
import '../../../widgets.dart';

class InboxInvitationTile extends StatelessWidget {
  final String userName;
  final String companyName;
  final String userJobTitle;
  final Enum$ChannelInvitationStatus invitationStatus;
  final String? avatarUrl;
  final VoidCallback buttonOnPressed;

  const InboxInvitationTile({
    super.key,
    required this.userName,
    // TODO: mentees do not always have a job title
    required this.userJobTitle,
    this.companyName = '',
    required this.invitationStatus,
    this.avatarUrl,
    required this.buttonOnPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(
        horizontal: context.theme.d.paddingMedium,
        vertical: context.theme.d.paddingXxSmall,
      ),
      shape: RoundedRectangleBorder(
        side: BorderSide(
          color: context.colorScheme.onTertiaryContainer,
          width: context.theme.d.borderWidthRegular,
        ),
        borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusSmall),
      ),
      elevation: context.theme.d.elevationLevel1,
      child: ListTile(
        onTap: buttonOnPressed,
        shape: RoundedRectangleBorder(
          side: BorderSide(
            color: context.colorScheme.onTertiaryContainer,
            width: context.theme.d.borderWidthRegular,
          ),
          borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusSmall),
        ),
        minVerticalPadding: context.theme.d.paddingXxSmall,
        contentPadding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall),
        leading: ProfileImageWidget(avatarUrl: avatarUrl, size: context.theme.d.imageSizeMedium),
        title: Text(userName, maxLines: 1, overflow: TextOverflow.ellipsis),
        titleTextStyle: context.theme.textTheme.labelLarge?.copyWith(
          color: context.theme.colorScheme.onSecondaryContainer,
          fontWeight: FontWeight.w600,
        ),
        subtitle:
            userJobTitle.isNotEmpty || companyName.isNotEmpty
                ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingXxSmall),
                      child: Text(
                        '$userJobTitle${userJobTitle.isNotEmpty && companyName.isNotEmpty ? ' - ' : ''}$companyName',
                        style: context.theme.textTheme.labelMedium?.copyWith(
                          color: context.theme.colorScheme.secondary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                )
                : null,
        trailing: Icon(
          context.read<LocaleModel>().isArabic()
              ? MicromentorIcons.arrowBackIosRounded
              : MicromentorIcons.arrowForwardIosRounded,
          color: context.colorScheme.onSurface,
          size: context.theme.d.iconSizeSmall,
        ),
      ),
    );
  }
}
