import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../services/extensions.dart';
import '../../../widgets.dart';

class InboxAppBarFactory {
  InboxAppBarFactory._private();

  static AppBar? createInboxAppBar({
    required InboxDrawersMenu inboxDrawersMenu,
    required ThemeData theme,
    PreferredSizeWidget? bottom,
  }) {
    return AppBar(
      leading: Stack(
        children: [
          Builder(
            builder: (context) {
              return Center(
                child: IconButton(
                  icon: Icon(MicromentorIcons.menuRounded, size: context.theme.d.iconSizeLarge),
                  onPressed: () => Scaffold.of(context).openDrawer(),
                ),
              );
            },
          ),
          Padding(
            padding: EdgeInsetsDirectional.only(
              top: theme.d.paddingXxSmall,
              end: theme.d.paddingSmall,
            ),
            child: const Align(
              alignment: AlignmentDirectional.topEnd,
              child: NotificationBubble(badgeType: BadgeType.all),
            ),
          ),
        ],
      ),
      title: Builder(
        builder: (context) {
          return _getInboxAppBarTitle(Theme.of(context), inboxDrawersMenu);
        },
      ),
      centerTitle: false,
      bottom: bottom,
    );
  }

  static AppBar createChannelMessagesAppBar({
    required BuildContext context,
    required String userFullName,
    required String channelId,
    required bool isArchivedForUser,
    required String userId,
    Function? onArchiveUnarchiveAction,
    Function? onBlockUnblockAction,
    String? avatarUrl,
    bool showBackButton = true,
  }) {
    final GoRouter router = GoRouter.of(context);

    return AppBar(
      centerTitle: false,
      backgroundColor: context.colorScheme.secondaryContainer,
      automaticallyImplyLeading: false,
      leading:
          showBackButton
              ? backButton(
                context,
                // This is added to naviagte user back to inbox chat screen if user comes here by accepting invitation through email with deep linking
                onPressed:
                    () => context.canPop() ? context.pop() : context.go(AppRoutes.inboxChats.path),
              )
              : null,
      title: InkWell(
        hoverColor: context.colorScheme.primaryFixedDim.withValues(alpha: 0.2),
        onTap: () {
          router.pushNamed(AppRoutes.profileId.name, pathParameters: {RouteParams.userId: userId});
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ProfileImageWidget(avatarUrl: avatarUrl, size: context.theme.d.boxSizeSmall),
            SizedBox(width: context.theme.d.paddingMedium),
            Expanded(
              flex: 4,
              child: Text(
                userFullName,
                style: context.theme.textTheme.titleMedium?.copyWith(
                  color: context.theme.colorScheme.onSecondaryContainer,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        Padding(
          padding: EdgeInsets.only(right: context.theme.d.paddingSmall),
          child: UserActionPopupMenu(
            includeArchiveOption: !isArchivedForUser,
            includeUnarchiveOption: isArchivedForUser,
            userFullName: userFullName,
            userId: userId,
            channelId: channelId,
            includeBlockUserOption: true,
            onArchiveUnarchiveAction: onArchiveUnarchiveAction,
            onBlockUnblockAction: onBlockUnblockAction,
          ),
        ),
      ],
    );
  }

  static AppBar createInviteReceivedDetailAppBar({
    required Key key,
    required BuildContext context,
    required String userFullName,
    required String userId,
  }) {
    final GoRouter router = GoRouter.of(context);

    return AppBar(
      leading: IconButton(
        icon: Icon(MicromentorIcons.arrowBackRounded, size: context.theme.d.iconSizeLarge),
        onPressed: () {
          router.pop();
        },
      ),
      title: Text(
        AppLocale.current.inboxInvitesReceivedTitle,
        style: context.theme.textTheme.titleLarge?.copyWith(
          color: context.theme.colorScheme.surfaceBright,
          fontWeight: FontWeight.w700,
        ),
      ),
      centerTitle: false,
      actions: [
        UserActionPopupMenu(
          key: key,
          userFullName: userFullName,
          userId: userId,
          includeBlockUserOption: true,
        ),
      ],
    );
  }

  static AppBar createInviteSentDetailAppBar({
    required BuildContext context,
    required Key key,
    required String recipentFullName,
    required String recipientUserId,
  }) {
    final GoRouter router = GoRouter.of(context);
    return AppBar(
      leading: backButton(
        context,
        onPressed: () => router.canPop() ? router.pop() : context.go(AppRoutes.inboxChats.path),
      ),
      automaticallyImplyLeading: false,
      title: Text(
        AppLocale.current.inboxInvitesSentTitle,
        style: context.theme.textTheme.titleLarge?.copyWith(
          color: context.theme.colorScheme.surfaceBright,
          fontWeight: FontWeight.w700,
        ),
      ),
      centerTitle: false,
      actions: [
        UserActionPopupMenu(
          key: key,
          userFullName: recipentFullName,
          userId: recipientUserId,
          includeBlockUserOption: true,
        ),
      ],
    );
  }

  static Text _getInboxAppBarTitle(ThemeData theme, InboxDrawersMenu inboxDrawersMenu) {
    final String title;
    switch (inboxDrawersMenu) {
      case InboxDrawersMenu.chats:
        title = AppLocale.current.inboxChatsTitle;
        break;
      case InboxDrawersMenu.invites:
        title = AppLocale.current.inboxInvitesTitle;
      case InboxDrawersMenu.archivedChats:
        title = AppLocale.current.inboxArchivedChatsTitle;
    }

    return Text(
      title,
      style: theme.textTheme.titleLarge?.copyWith(
        color: theme.colorScheme.onSecondaryContainer,
        fontWeight: FontWeight.w700,
      ),
    );
  }
}
