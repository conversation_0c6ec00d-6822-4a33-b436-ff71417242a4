import 'package:flutter/material.dart';

import '../../../../services/extensions.dart';

class EmptyStateMessage extends StatelessWidget {
  final String? text;
  final String? iconPath;
  final double? height;

  const EmptyStateMessage({super.key, required this.text, this.iconPath, this.height = 5.0});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (iconPath != null) SizedBox(height: height, child: Image.asset(iconPath!)),
          if (text != null)
            Padding(
              padding: EdgeInsets.only(top: context.theme.d.paddingMedium),
              child: Text(
                text!,
                textAlign: TextAlign.center,
                style: context.theme.textTheme.titleMedium?.copyWith(
                  color: context.colorScheme.onSecondaryContainer,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
