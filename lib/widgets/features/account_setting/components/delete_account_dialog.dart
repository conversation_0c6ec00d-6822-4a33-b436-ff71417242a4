import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../services/graphql/providers/providers.dart';
import '../../../widgets.dart';

class DeleteAccountDialog extends StatefulWidget {
  const DeleteAccountDialog({super.key});

  @override
  State<DeleteAccountDialog> createState() => _DeleteAccountDialogState();
}

class _DeleteAccountDialogState extends State<DeleteAccountDialog> {
  late final UserProvider _userProvider;
  late final TextEditingController _confirmTextController;
  late final ValueNotifier<bool> _isConfirmed;

  @override
  void initState() {
    super.initState();
    _confirmTextController = TextEditingController(text: '');
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _isConfirmed = ValueNotifier(false);

    _confirmTextController.addListener(_checkConfirmed);
  }

  void _checkConfirmed() {
    final confirmed =
        _confirmTextController.text.trim().toLowerCase() ==
        AppLocale.current.deleteAccountHeading.toLowerCase();
    if (_isConfirmed.value != confirmed) {
      _isConfirmed.value = confirmed;
    }
  }

  @override
  void dispose() {
    _confirmTextController.removeListener(_checkConfirmed);
    _confirmTextController.dispose();
    _isConfirmed.dispose();
    super.dispose();
  }

  void onDeleteAccount() async {
    await _userProvider.deleteUserAccount(context);
    // check this video : from flutter team https://www.youtube.com/watch?v=bzWaMpD1LHY
    if (!mounted) return;
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: _isConfirmed,
      builder: (context, isConfirmed, _) {
        return DialogTemplate.withCustomContent(
          title: AppLocale.current.deleteAccountDialogHeading,
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(AppLocale.current.deleteAccountDialogSubtitle, textAlign: TextAlign.start),
                SizedBox(height: context.theme.d.paddingSmall),
                Text(
                  AppLocale.current.deleteAccountDialogConfirmInstructions,
                  textAlign: TextAlign.start,
                ),
                SizedBox(height: context.theme.d.paddingSmall),
                TextFormFieldWidget(
                  label: AppLocale.current.deleteAccountDialogConfirmInputLabel,
                  textController: _confirmTextController,
                ),
              ],
            ),
          ),
          actionButtonTitle: AppLocale.current.delete,
          actionButtonTextStyle:
              isConfirmed
                  ? context.textTheme.bodyMedium?.copyWith(color: context.colorScheme.error)
                  : null,
          onAction: isConfirmed ? onDeleteAccount : null,
        );
      },
    );
  }
}
