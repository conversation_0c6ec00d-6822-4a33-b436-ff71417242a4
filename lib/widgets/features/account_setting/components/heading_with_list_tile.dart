import 'package:flutter/material.dart';

import '../../../../services/extensions.dart';
import 'list_tile_widget.dart';

class HeadingWithListTile extends StatelessWidget {
  final String? heading;
  final String? title;
  final String? subtitle;
  final Widget? leadingWidget;
  final Widget? extraContent;
  final bool? isLink;
  final bool hideTrailing;
  final Function()? onTap;

  const HeadingWithListTile({
    super.key,
    this.heading,
    this.title,
    this.subtitle,
    this.leadingWidget,
    this.extraContent,
    this.isLink = false,
    this.hideTrailing = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        top: context.theme.d.paddingSmall,
        left: context.theme.d.paddingMedium,
        right: context.theme.d.paddingMedium,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (heading?.isNotEmpty == true)
            Padding(
              padding: EdgeInsets.symmetric(vertical: context.theme.d.chipPadding),
              child: Text(
                '$heading',
                style: context.theme.textTheme.titleLarge?.copyWith(
                  fontSize: context.theme.d.fontSizeMediumLarge,
                  color: context.colorScheme.surfaceBright,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          buildSectionContent(),
        ],
      ),
    );
  }

  Widget buildSectionContent() {
    if ((title?.isNotEmpty == true) || (subtitle?.isNotEmpty == true)) {
      return ListTileWidget(
        title: title,
        subtitle: subtitle,
        leading: leadingWidget,
        extraContent: extraContent,
        isLink: isLink ?? false,
        hideTrailing: hideTrailing,
        onTap: onTap,
      );
    }
    return const SizedBox();
  }
}
