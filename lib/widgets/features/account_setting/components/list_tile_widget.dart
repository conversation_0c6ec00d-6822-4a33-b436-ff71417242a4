import 'package:flutter/material.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:mm_flutter_app/widgets/shared/common_button.dart';
import 'package:provider/provider.dart';

import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../models/locale_model.dart';
import '../../../../services/extensions.dart';

class ListTileWidget extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final Widget? leading;
  final Widget? extraContent;
  final Function()? onTap;
  final bool enabled;
  final bool isLink;
  final bool hideTrailing;

  const ListTileWidget({
    super.key,
    this.title,
    this.subtitle,
    this.leading,
    this.extraContent,
    this.onTap,
    this.isLink = false,
    this.hideTrailing = false,
  }) : enabled = onTap != null;

  @override
  Widget build(BuildContext context) {
    bool isDesktopView = AppUtility.displayDesktopUI(context);

    return ListTile(
      tileColor: context.colorScheme.onSurface.withValues(alpha: 0.07),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusLarge),
      ),
      onTap: isDesktopView && !hideTrailing ? null : onTap,
      contentPadding: EdgeInsets.symmetric(
        vertical:
            isOnlySubtitle() || isOnlyTitle() ? context.theme.d.paddingSmall : context.theme.d.zero,
        horizontal: context.theme.d.paddingMedium,
      ),
      dense: true,
      leading: leading,
      horizontalTitleGap: context.theme.d.paddingSmall,
      title: _titleWidget(context),
      subtitle: showSubtitle() ? _subtitleOrExtraContent(context) : null,
      trailing:
          hideTrailing || !enabled
              ? null
              : isDesktopView && !isLink
              ? CommonButton.outlinedButton(
                context: context,
                color: context.colorScheme.onPrimary,
                title: AppLocale.current.inboxMessagesActionEdit,
                onPressed: onTap,
                buttonSize: ButtonSize.small,
              )
              : IconButton(
                icon: Icon(
                  context.read<LocaleModel>().isArabic()
                      ? MicromentorIcons.chevronLeft
                      : MicromentorIcons.chevronRight,
                ),
                onPressed: onTap,
                color: context.theme.colorScheme.onSurface,
              ),
    );
  }

  isOnlySubtitle() {
    return ((title ?? '').isEmpty && ((subtitle ?? '').isNotEmpty || extraContent != null));
  }

  isOnlyTitle() {
    return ((((subtitle ?? '').isEmpty) || extraContent == null) && (title ?? '').isNotEmpty);
  }

  Widget _subtitleOrExtraContent(BuildContext context) {
    return extraContent == null ? _subtitleTextWidget(context) : _extraContent(context);
  }

  bool showSubtitle() {
    return (((title ?? '').isNotEmpty) && ((subtitle ?? '').isNotEmpty || (extraContent != null)));
  }

  _subtitleTextWidget(BuildContext context) {
    if (subtitle == null) return null;
    return Padding(
      padding: EdgeInsets.only(
        top:
            (title == null || title?.isEmpty == true)
                ? context.theme.d.zero
                : context.theme.d.paddingSmall,
      ),
      child: Text(
        subtitle ?? '',
        maxLines: 5,
        overflow: TextOverflow.ellipsis,
        style: context.theme.textTheme.bodyLarge?.copyWith(
          color: context.theme.colorScheme.onSurface,
        ),
      ),
    );
  }

  Widget _extraContent(BuildContext context) {
    return Column(
      children: [_subtitleTextWidget(context), if (extraContent != null) extraContent!],
    );
  }

  Widget _titleWidget(BuildContext context) {
    return ((title ?? '').isNotEmpty)
        ? Text(
          '$title',
          style: context.theme.textTheme.bodyLarge?.copyWith(
            color:
                enabled
                    ? isLink
                        ? context.colorScheme.primary
                        : context.colorScheme.surfaceBright
                    : context.colorScheme.surfaceBright.withValues(alpha: 0.5),
            fontWeight: FontWeight.w700,
          ),
        )
        : _subtitleOrExtraContent(context); //show subtitle to avoid expty extra spce above subtitle
  }
}
