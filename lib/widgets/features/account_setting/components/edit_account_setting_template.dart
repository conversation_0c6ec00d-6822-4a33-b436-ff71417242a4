import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/utilities/errors/error_handler.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../models/scaffold_model.dart';
import '../../../../services/extensions.dart';
import '../../../../utilities/loading/loading_provider.dart';
import '../../../widgets.dart';

class EditAccountSettingTemplate extends StatelessWidget {
  final String title;
  final String subtitle;
  final Widget body;
  final Future Function()? onSave;
  final Future Function()? navigateBackAction;
  final bool isBackEnabled;
  final String? actionButtonLabel;
  final bool showWebActionButtons;
  final bool showCancelButton;
  final bool shouldShowDiscardDialog;
  final ValueListenable<bool> hasUnsavedChanges;

  const EditAccountSettingTemplate({
    super.key,
    required this.title,
    required this.subtitle,
    required this.body,
    required this.hasUnsavedChanges,
    this.isBackEnabled = true,
    this.showWebActionButtons = true,
    this.showCancelButton = true,
    this.shouldShowDiscardDialog = true,
    this.navigateBackAction,
    this.actionButtonLabel,
    this.onSave,
  });

  @override
  Widget build(BuildContext context) {
    return AppUtility.displayDesktopUI(context)
        ? _desktopView(context)
        : _mobileView(context);
  }

  Widget _mobileView(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ScaffoldModel>().setParams(
        hideNavBar: true,
        index: Tabs.home.index,
      );
    });

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? _) async {
        if (!kIsWeb && didPop) return;
        _onBackAction(
          context,
          isWebDeviceBack: AppUtility.displayDesktopUI(context),
        );
      },
      child: Scaffold(
        appBar: AppBar(
          actionsPadding: EdgeInsets.symmetric(
            horizontal: context.theme.d.paddingSmall,
          ),
          automaticallyImplyLeading: false,
          centerTitle: false,
          leading: backButton(
            context,
            onPressed: () async => _onBackAction(context),
          ),
          shape: AppUtility.topDivider(context),
          actions: [
            ValueListenableBuilder<bool>(
              valueListenable: hasUnsavedChanges,
              builder: (context, isSaveEnabled, _) {
                return CommonButton.textButton(
                  context: context,
                  title: actionButtonLabel ?? AppLocale.current.saveAction,
                  textStyle: context.theme.textTheme.titleSmall?.copyWith(
                    color:
                        isSaveEnabled
                            ? context.theme.colorScheme.tertiary
                            : context.theme.colorScheme.scrim,
                  ),
                  onPressed:
                      isSaveEnabled ? () async => _handleSave(context) : null,
                );
              },
            ),
          ],
          title: Text(title),
        ),
        body: _bodyContent(context),
      ),
    );
  }

  Widget _desktopView(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? _) async {
        if (didPop) return;
        _onBackAction(context);
      },
      child: DesktopEditLayoutScreen(
        title: title,
        body: _bodyContent(context),
        actionButtonTitle: actionButtonLabel,
        showCancelButton: showCancelButton,
        showWebActionButtons: showWebActionButtons,
        hasUnsavedChanges: hasUnsavedChanges,
        onCancelTap: () async => _onBackAction(context),
        onSaveTap: () async => await _handleSave(context),
      ),
    );
  }

  Widget _bodyContent(BuildContext context) {
    return SafeArea(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (subtitle.isNotEmpty)
            Container(
              alignment: Alignment.centerLeft,
              color: context.colorScheme.surface,
              padding: EdgeInsets.only(
                left: context.theme.d.paddingLarge,
                right: context.theme.d.paddingLarge,
                bottom: context.theme.d.paddingSmall,
                top: context.theme.d.paddingXxSmall,
              ),
              child: Text(
                subtitle,
                style: context.theme.textTheme.bodyLarge?.copyWith(
                  color: context.colorScheme.onSurface,
                ),
              ),
            ),
          body,
        ],
      ),
    );
  }

  Future<void> _handleSave(BuildContext context) async {
    final router = GoRouter.of(context);

    if (onSave == null) {
      _popOrReplace(context, router);
      return;
    }

    final bool saveSucceeded = await _performSave(context);
    if (!saveSucceeded) return;

    if (context.mounted) _popOrReplace(context, router);
  }

  void _popOrReplace(BuildContext context, GoRouter router) {
    if (router.canPop()) {
      router.pop();
    } else if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else {
      router.pushReplacement(AppRoutes.accountSettings.path);
    }
  }

  Future<bool> _performSave(
    BuildContext context, {
    bool isWebOrDeviceBackButton = false,
  }) async {
    if (!isWebOrDeviceBackButton) Loader.show(context);
    final value = await onSave?.call();
    if (context.mounted) {
      if (!isWebOrDeviceBackButton) Loader.hide(context);

      if (value != null) {
        // If value is bool and false, stop and return false
        if (value is bool) {
          return value;
        }
        // If value is OperationException, show error and return false
        if (value is OperationException) {
          AppErrorHandler(context: context, exception: value);
          return false;
        }
      }
    }
    return true;
  }

  /*   _onBackAction(BuildContext context, {bool isWebDeviceBack = false}) async {
    //handle back action when, user has came from deeplink on the mobile
    if (!context.canPop()) {
      context.pushReplacement(AppRoutes.accountSettings.path);
    }

    bool isDesktop = AppUtility.displayDesktopUI(context);

    final GoRouter router = GoRouter.of(context);
    if (!isWebDeviceBack) Loader.show(context);
    if (navigateBackAction != null) await navigateBackAction?.call();
    if (context.mounted) {
      if (!isWebDeviceBack) Loader.hide(context);
    }

    if (!hasUnsavedChanges.value || onSave == null) {
      if (!context.mounted) return;
      isDesktop ? Navigator.pop(context) : router.pop();
      return;
    }

    if (isWebDeviceBack && navigateBackAction == null && context.mounted) {
      final bool saveSucceeded = await _performSave(
        context,
        isWebOrDeviceBackButton: isWebDeviceBack,
      );

      if (!saveSucceeded) return;
      return;
    }

    await Future.delayed(const Duration(milliseconds: 500));
    if (!context.mounted) return;

    if (shouldShowDiscardDialog) {
      showDialog(
        context: context,
        builder: (dialogContext) {
          return DialogTemplate(
            title: AppLocale.current.discardChangesTitle,
            description: '',
            actionButtonTitle: AppLocale.current.saveChangeDialogDiscardLabel,
            cancelButtonTitle: AppLocale.current.actionCancel,
            isFullWidthButtons: false,
            showTitleInCenter: false,
            onCancel: () => Navigator.pop(dialogContext),

            onAction: () async {
              Navigator.pop(dialogContext);
              isDesktop ? Navigator.pop(context) : router.pop();
            },
          );
        },
      );
    } else {
      Navigator.pop(context);
    }
  } */

  _onBackAction(BuildContext context, {bool isWebDeviceBack = false}) async {
    final router = GoRouter.of(context);
    final isDesktop = AppUtility.displayDesktopUI(context);

    // If user came from a deeplink and can't pop, go to account settings
    if (!context.canPop()) {
      context.pushReplacement(AppRoutes.accountSettings.path);
      return;
    }

    if (!isWebDeviceBack) Loader.show(context);
    if (navigateBackAction != null) await navigateBackAction?.call();
    if (context.mounted && !isWebDeviceBack) Loader.hide(context);

    // If no unsaved changes or no onSave, just pop
    if (!hasUnsavedChanges.value || onSave == null) {
      if (!context.mounted) return;
      isDesktop ? Navigator.pop(context) : router.pop();
      return;
    }

    // If back triggered by web/device back and no custom back action, try to save
    if (isWebDeviceBack && navigateBackAction == null && context.mounted) {
      final bool saveSucceeded = await _performSave(
        context,
        isWebOrDeviceBackButton: isWebDeviceBack,
      );
      if (!saveSucceeded) return;
      return;
    }

    await Future.delayed(const Duration(milliseconds: 500));
    if (!context.mounted) return;

    if (shouldShowDiscardDialog) {
      showDialog(
        context: context,
        builder:
            (dialogContext) => DialogTemplate(
              title: AppLocale.current.discardChangesTitle,
              description: '',
              actionButtonTitle: AppLocale.current.saveChangeDialogDiscardLabel,
              cancelButtonTitle: AppLocale.current.actionCancel,
              isFullWidthButtons: false,
              showTitleInCenter: false,
              onCancel: () => Navigator.pop(dialogContext),
              onAction: () async {
                Navigator.pop(dialogContext);
                isDesktop ? Navigator.pop(context) : router.pop();
              },
            ),
      );
    } else {
      Navigator.pop(context);
    }
  }
}
