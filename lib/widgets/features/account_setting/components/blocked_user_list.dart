/* import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/errors/error_handler.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class BlockedUserList extends StatefulWidget {
  final VoidCallback onChanged;
  const BlockedUserList({super.key, required this.onChanged});

  @override
  State<BlockedUserList> createState() => _BlockedUserListState();
}

class _BlockedUserListState extends State<BlockedUserList> {
  late final UserProvider _userProvider;
  Future<List<BlockedUser>>? _myBlockedUsersFuture;
  String? _processingUserId;
  late List<BlockedUser>? blockedUsers;

  @override
  void initState() {
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _processingUserId = null;
    blockedUsers = [];
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _myBlockedUsersFuture = _userProvider.findMyBlockedUsers();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<BlockedUser>>(
      future: _myBlockedUsersFuture,
      builder: (BuildContext context, AsyncSnapshot<List<BlockedUser>?> snapshot) {
        blockedUsers = snapshot.data;
        final haveListEntries = snapshot.data?.isNotEmpty == true;

        if (snapshot.connectionState != ConnectionState.done && !haveListEntries) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return const CustomErrorWidget();
        }

        if (snapshot.hasData) {
          if (!haveListEntries) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
                  child: Text(
                    AppLocale.current.accountSettingNoBlockedUserMsg,
                    style: context.theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w400,
                      color: context.colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            );
          }

          return __conetentWrapper();
        }

        // todo: Show skeleton, rather than progress indicator
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  Widget buildTrailing(String userId) {
    if (userId == _processingUserId) {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall),
        child: SizedBox(
          width: context.theme.d.iconSizeMedium,
          height: context.theme.d.iconSizeMedium,
          child: const CircularProgressIndicator(),
        ),
      );
    }

    return CommonButton.outlinedButton(
      context: context,
      title: AppLocale.current.actionUnblock,
      buttonSize: ButtonSize.small,
      onPressed: () async {
        setState(() {
          _processingUserId = userId;
        });
        final response = await _userProvider.unblockUser(userId: userId);

        if (response.gqlQueryResult.hasException && mounted) {
          AppErrorHandler(context: context, exception: response.gqlQueryResult.exception);
          setState(() {
            _processingUserId = null;
          });
          return;
        }

        setState(() {
          _processingUserId = null;
          _myBlockedUsersFuture = _userProvider.findMyBlockedUsers();
        });

        widget.onChanged();
      },
    );
  }

  _scrollableContent(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingMedium),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children:
              blockedUsers!.map((user) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ListTile(
                      leading: ProfileImageWidget(
                        avatarUrl: user.avatarUrl,
                        size: context.theme.d.textFieldRadius,
                      ),
                      title: Text(
                        AppUtility.getUserFullName(user.firstName, user.lastName),
                        style: context.theme.textTheme.bodyMedium?.copyWith(
                          color: context.colorScheme.onSecondaryContainer,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      trailing: buildTrailing(user.id),
                    ),
                    Divider(color: context.colorScheme.onTertiaryContainer, thickness: 1),
                  ],
                );
              }).toList(),
        ),
      ),
    );
  }

  Widget __conetentWrapper() {
    return _scrollableContent(context);
  }
}
 */

import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';
import '../providers/account_setting_provider.dart';

class BlockedUserList extends StatefulWidget {
  const BlockedUserList({super.key});

  @override
  State<BlockedUserList> createState() => _BlockedUserListState();
}

class _BlockedUserListState extends State<BlockedUserList> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AccountSettingsProvider>().loadBlockedUsers();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AccountSettingsProvider>(
      builder: (context, provider, _) {
        if (provider.isBlockedUsersLoading) {
          return const Center(child: CircularProgressIndicator());
        }
        if (provider.blockedUsersError != null) {
          return Center(child: Text('Error: ${provider.blockedUsersError}'));
        }
        final users = provider.blockedUsers;
        if (users == null || users.isEmpty) {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
            child: Text(
              AppLocale.current.accountSettingNoBlockedUserMsg,
              style: context.theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w400,
                color: context.colorScheme.onSurface,
              ),
            ),
          );
        }
        return SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingMedium),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  users.map((user) {
                    final isProcessing = provider.processingBlockedUserId == user.id;
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ListTile(
                          leading: ProfileImageWidget(
                            avatarUrl: user.avatarUrl,
                            size: context.theme.d.textFieldRadius,
                          ),
                          title: Text(
                            AppUtility.getUserFullName(user.firstName, user.lastName),
                            style: context.theme.textTheme.bodyMedium?.copyWith(
                              color: context.colorScheme.onSecondaryContainer,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          trailing:
                              isProcessing
                                  ? SizedBox(
                                    width: context.theme.d.iconSizeMedium,
                                    height: context.theme.d.iconSizeMedium,
                                    child: CircularProgressIndicator(
                                      strokeWidth: context.theme.d.highlightBorderWidth,
                                    ),
                                  )
                                  : CommonButton.outlinedButton(
                                    context: context,
                                    title: AppLocale.current.actionUnblock,
                                    buttonSize: ButtonSize.small,
                                    onPressed: () async {
                                      await provider.unblockUser(user.id, context);
                                      provider.setMustReloadUser(true);
                                    },
                                  ),
                        ),
                        Divider(color: context.colorScheme.onTertiaryContainer, thickness: 1),
                      ],
                    );
                  }).toList(),
            ),
          ),
        );
      },
    );
  }
}
