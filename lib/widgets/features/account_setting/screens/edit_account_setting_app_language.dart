import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../models/locale_model.dart';
import '../../../widgets.dart';
import '../providers/account_setting_provider.dart';

class EditAccountSettingAppLanguage extends StatefulWidget {
  const EditAccountSettingAppLanguage({super.key});

  @override
  State<EditAccountSettingAppLanguage> createState() => _EditAccountSettingAppLanguageState();
}

class _EditAccountSettingAppLanguageState extends State<EditAccountSettingAppLanguage> {
  Enum$UiLanguage? _selectedUiLanguage;
  Enum$UiLanguage? _oldValue;
  late final AccountSettingsProvider _provider;
  late final ValueNotifier<bool> _hasUnsavedChanges;
  late final LocaleModel _localeModel;

  @override
  void initState() {
    super.initState();
    _provider = context.read<AccountSettingsProvider>();
    _localeModel = context.read<LocaleModel>();

    _oldValue = _provider.user?.selectedUiLanguageTextId;
    _selectedUiLanguage = _provider.selectedUiLanguage ?? _oldValue;
    _hasUnsavedChanges = ValueNotifier(false);
  }

  @override
  void dispose() {
    _hasUnsavedChanges.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditAccountSettingTemplate(
      title: AppLocale.current.appLanguageHeading,
      subtitle: AppLocale.current.accountSettingEditAppLanguageMsg,
      hasUnsavedChanges: _hasUnsavedChanges,
      body: Flexible(child: SingleChildScrollView(child: _bodyContent(context))),
      onSave: () async {
        return await _provider.updateUserAppLanguage(_selectedUiLanguage, _localeModel);
      },
    );
  }

  Widget _bodyContent(BuildContext context) {
    final contentProvider = _provider.contentProvider;
    return Padding(
      padding: EdgeInsets.all(context.theme.d.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Selector<AccountSettingsProvider, Enum$UiLanguage?>(
            selector: (_, provider) => provider.selectedUiLanguage,
            builder: (context, selectedUiLanguage, _) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children:
                    (contentProvider.hardCodedUiLanguages(_localeModel) ?? []).map((option) {
                      Enum$UiLanguage value = Enum$UiLanguage.values.byName(option.textId);
                      return Padding(
                        padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingXxSmall),
                        child: RadioListTile<Enum$UiLanguage>(
                          tileColor: context.colorScheme.primaryContainer,
                          selected: _selectedUiLanguage == value,
                          selectedTileColor: context.colorScheme.surfaceContainerHighest,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              context.theme.d.roundedRectRadiusXxLarge,
                            ),
                          ),
                          onChanged: _onLanguageChanged,
                          title: Text(
                            contentProvider.getNativeLanguageTitle(value),
                            style: context.theme.textTheme.bodyMedium?.copyWith(
                              color: context.theme.colorScheme.onSurface,
                            ),
                          ),
                          value: value,
                          groupValue: _selectedUiLanguage,
                          controlAffinity: ListTileControlAffinity.trailing,
                        ),
                      );
                    }).toList(),
              );
            },
          ),
          Padding(
            padding: EdgeInsets.all(context.theme.d.paddingMedium),
            child: MarkdownBody(
              data: AppLocale.current.appLanguageMessage(
                AppRoutes.profileEditLanguagePreferred.path,
              ),
              styleSheet: MarkdownStyleSheet.fromTheme(context.theme).copyWith(
                p: context.textTheme.bodyLarge?.copyWith(
                  color: context.colorScheme.onSurface,
                  fontWeight: FontWeight.w500,
                ),
                a: context.textTheme.bodyLarge?.copyWith(
                  color: context.colorScheme.tertiary,
                  fontWeight: FontWeight.w500,
                ),
              ),
              onTapLink: (_, path, __) {
                if (AppUtility.displayDesktopUI(context)) {
                  Navigator.pop(context);
                  if (_provider.user == null) return;
                  showDialog(
                    context: context,
                    barrierDismissible: true,
                    builder: (context) {
                      return const EditPreferredLanguageScreen();
                    },
                  );
                  return;
                }
                if (path == null) return;
                context.push(path);

                ///TODO: commenting it , because I think it is not required anymore,
                ///but in future on more testing if we realize that this is required , then we can directly uncomment it
                //  context.read<ScaffoldModel>().setParams(index: Tabs.profile.index);
              },
            ),
          ),
        ],
      ),
    );
  }

  void _onLanguageChanged(Enum$UiLanguage? newAppLanguage) {
    if (newAppLanguage == _selectedUiLanguage) return;
    _selectedUiLanguage = newAppLanguage;
    _hasUnsavedChanges.value = newAppLanguage != _oldValue;
    _provider.setSelectedUiLanguage(newAppLanguage);
  }
}
