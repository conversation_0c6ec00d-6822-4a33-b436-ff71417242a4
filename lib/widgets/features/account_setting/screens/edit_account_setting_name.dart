import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../shared/text_form_field_widget.dart';
import '../components/edit_account_setting_template.dart';
import '../providers/account_setting_provider.dart';

class EditAccountSettingName extends StatefulWidget {
  const EditAccountSettingName({super.key});

  @override
  State<EditAccountSettingName> createState() => _EditAccountSettingNameState();
}

class _EditAccountSettingNameState extends State<EditAccountSettingName> {
  late final TextEditingController _firstNameController;
  late final TextEditingController _lastNameController;
  late final AccountSettingsProvider _provider;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late String? _oldFirstName;
  late String? _oldLastName;
  late final ValueNotifier<bool> _isFormValid;

  @override
  void initState() {
    super.initState();
    _provider = context.read<AccountSettingsProvider>();
    final user = _provider.user!;
    _oldFirstName = user.firstName;
    _oldLastName = user.lastName;
    _firstNameController = TextEditingController(text: _oldFirstName);
    _lastNameController = TextEditingController(text: _oldLastName);
    _isFormValid = ValueNotifier(_canEnableSave());

    _firstNameController.addListener(_onFormChanged);
    _lastNameController.addListener(_onFormChanged);
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _isFormValid.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditAccountSettingTemplate(
      title: AppLocale.current.name,
      subtitle: AppLocale.current.accountSettingEditNameMsg,
      body: Padding(
        padding: EdgeInsets.symmetric(
          vertical: context.theme.d.paddingMedium,
          horizontal: context.theme.d.paddingLarge,
        ),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormFieldWidget(
                label: AppLocale.current.accountSettingFirstNameInputLabel,
                hint: AppLocale.current.accountSettingFirstNameInputHint,
                textController: _firstNameController,
                contentPadding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
                autofocus: true,
                textCapitalization: TextCapitalization.sentences,
                focusedBorderColor: context.theme.colorScheme.primary,
                keyboardType: TextInputType.name,
                validator: (value) {
                  return context.theme.validator.validateName(
                    value,
                    errorMessage: AppLocale.current.validationFirstNameEmpty,
                  );
                },
              ),
              SizedBox(height: context.theme.d.paddingLarge),
              TextFormFieldWidget(
                label: AppLocale.current.accountSettingLastNameInputLabel,
                hint: AppLocale.current.accountSettingLastNameInputHint,
                textController: _lastNameController,
                contentPadding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
                autofocus: true,
                focusedBorderColor: context.theme.colorScheme.primary,
                keyboardType: TextInputType.name,
                textCapitalization: TextCapitalization.sentences,
                validator: (value) {
                  return context.theme.validator.validateName(
                    value,
                    errorMessage: AppLocale.current.validationLastNameEmpty,
                  );
                },
              ),
            ],
          ),
        ),
      ),
      hasUnsavedChanges: _isFormValid,
      onSave: () async {
        if (_formKey.currentState?.validate() ?? false) {
          return await _provider.updateUserName(
            _firstNameController.text.trim(),
            _lastNameController.text.trim(),
          );
        }
        return null;
      },
    );
  }

  bool _noChangeInValues() {
    return (_oldFirstName?.trim() == _firstNameController.text.trim()) &&
        (_oldLastName?.trim() == _lastNameController.text.trim());
  }

  void _onFormChanged() {
    _isFormValid.value = _canEnableSave();
  }

  bool _canEnableSave() {
    return _firstNameController.text.trim().isNotEmpty &&
        _lastNameController.text.trim().isNotEmpty &&
        !_noChangeInValues();
  }
}
