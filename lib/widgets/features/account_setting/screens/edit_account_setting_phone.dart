import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../services/graphql/providers/content_provider.dart';
import '../../../widgets.dart';
import '../providers/account_setting_provider.dart';

class EditAccountSettingPhone extends StatefulWidget {
  const EditAccountSettingPhone({super.key});

  @override
  State<EditAccountSettingPhone> createState() => _EditAccountSettingPhoneState();
}

class _EditAccountSettingPhoneState extends State<EditAccountSettingPhone> {
  PhoneNumberInputInfo? _phoneNumberInputInfo;
  late final ContentProvider _contentProvider;
  late final AccountSettingsProvider _provider;
  late final String? _oldPhoneNumber;
  late final ValueNotifier<bool> _isFormValid;

  @override
  void initState() {
    super.initState();

    _provider = context.read<AccountSettingsProvider>();
    _contentProvider = _provider.contentProvider;

    final user = _provider.user!;
    _oldPhoneNumber = user.phoneNumber;

    if (_oldPhoneNumber?.isNotEmpty == true &&
        _contentProvider.countryOptions?.isNotEmpty == true) {
      _phoneNumberInputInfo = PhoneNumberInputInfo.fromInternationalNumber(
        _oldPhoneNumber,
        _contentProvider.countryOptions!,
        true,
      );
    }
    _isFormValid = ValueNotifier(_canEnableSave());
  }

  @override
  void dispose() {
    _isFormValid.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditAccountSettingTemplate(
      title: AppLocale.current.phone,
      subtitle: AppLocale.current.accountSettingEditPhoneNumberMsg,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: context.theme.d.paddingLarge,
            vertical: context.theme.d.paddingMedium,
          ),
          child: PhoneNumberForm(
            value: _phoneNumberInputInfo,
            checkIfAvailable: true,
            onChanged: (PhoneNumberInputInfo value) {
              _phoneNumberInputInfo = value;
              _onFormChanged();
            },
          ),
        ),
      ),
      hasUnsavedChanges: _isFormValid,
      onSave: () async {
        return await _provider.updateUserPhone(_phoneNumberInputInfo?.internationalNumber ?? '');
      },
    );
  }

  bool _noChangeInValue() {
    return (_phoneNumberInputInfo?.internationalNumber == _oldPhoneNumber);
  }

  void _onFormChanged() {
    _isFormValid.value = _canEnableSave();
  }

  bool _canEnableSave() {
    return (_phoneNumberInputInfo?.isValid == true) &&
        (_phoneNumberInputInfo?.internationalNumber.isNotEmpty == true) &&
        !_noChangeInValue();
  }
}
