import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/widgets/features/account_setting/components/edit_account_setting_template.dart';
import 'package:provider/provider.dart';

import '../components/blocked_user_list.dart';
import '../providers/account_setting_provider.dart';

class EditAccountSettingViewBlockedUsers extends StatelessWidget {
  const EditAccountSettingViewBlockedUsers({super.key});

  @override
  Widget build(BuildContext context) {
    return EditAccountSettingTemplate(
      title: AppLocale.current.blockedUsersHeading,
      subtitle: AppLocale.current.accountSettingEditBlockUsersMsg,
      actionButtonLabel: AppLocale.current.doneTitle,
      showCancelButton: false,
      shouldShowDiscardDialog: false,
      hasUnsavedChanges: ValueNotifier(true),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            vertical: context.theme.d.paddingMedium,
            horizontal: context.theme.d.paddingLarge,
          ),
          child: const BlockedUserList(),
        ),
      ),
      onSave: _refreshUser(context),
      navigateBackAction: _refreshUser(context),
    );
  }

  Future Function()? _refreshUser(BuildContext context) {
    return () async {
      await context.read<AccountSettingsProvider>().reloadUser(context);
    };
  }
}
