import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../shared/text_form_field_widget.dart';
import '../components/edit_account_setting_template.dart';
import '../providers/account_setting_provider.dart';

class EditAccountSettingPassword extends StatefulWidget {
  const EditAccountSettingPassword({super.key});

  @override
  State<EditAccountSettingPassword> createState() => _EditAccountSettingPasswordState();
}

class _EditAccountSettingPasswordState extends State<EditAccountSettingPassword> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _currentPasswordController;
  late final TextEditingController _newPasswordController;
  late final TextEditingController _confirmPasswordController;
  late final ValueNotifier<bool> _hasUnsavedChanges;
  late final AccountSettingsProvider _provider;

  @override
  void initState() {
    super.initState();
    _provider = context.read<AccountSettingsProvider>();
    _currentPasswordController = TextEditingController();
    _newPasswordController = TextEditingController();
    _confirmPasswordController = TextEditingController();
    _hasUnsavedChanges = ValueNotifier(false);

    _currentPasswordController.addListener(_onFormChanged);
    _newPasswordController.addListener(_onFormChanged);
    _confirmPasswordController.addListener(_onFormChanged);
  }

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    _hasUnsavedChanges.dispose();
    super.dispose();
  }

  void _onFormChanged() {
    final current = _currentPasswordController.text.trim();
    final newPass = _newPasswordController.text.trim();
    final confirm = _confirmPasswordController.text.trim();
    final isValid =
        current.isNotEmpty &&
        newPass.isNotEmpty &&
        confirm.isNotEmpty &&
        newPass == confirm &&
        context.theme.validator.validatePassword(newPass) == null;
    _hasUnsavedChanges.value = isValid;
  }

  @override
  Widget build(BuildContext context) {
    return EditAccountSettingTemplate(
      title: AppLocale.current.passwordAndSecurityHeading,
      subtitle: AppLocale.current.accountSettingEditPasswordMsg,
      hasUnsavedChanges: _hasUnsavedChanges,
      body: Padding(
        padding: EdgeInsets.symmetric(
          vertical: context.theme.d.paddingMedium,
          horizontal: context.theme.d.paddingLarge,
        ),
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            children: [
              TextFormFieldWidget(
                label: AppLocale.current.accountSettingCurrentPasswordInputLabel,
                textController: _currentPasswordController,
                validator: context.theme.validator.validatePassword,
              ),
              SizedBox(height: context.theme.d.paddingLarge),
              TextFormFieldWidget(
                label: AppLocale.current.accountSettingNewPasswordInputLabel,
                textController: _newPasswordController,
                validator: context.theme.validator.validatePassword,
              ),
              SizedBox(height: context.theme.d.paddingLarge),
              TextFormFieldWidget(
                label: AppLocale.current.accountSettingConfirmNewPasswordInputLabel,
                textController: _confirmPasswordController,
                validator: (value) {
                  return context.theme.validator.validatePassword(
                    value,
                    matchValue: _newPasswordController.text,
                  );
                },
              ),
            ],
          ),
        ),
      ),
      onSave: () async {
        if (!(_formKey.currentState?.validate() ?? false)) {
          return false;
        }
        return await _provider.updateUserPassword(
          currentPassword: _currentPasswordController.text.trim(),
          newPassword: _newPasswordController.text.trim(),
        );
      },
    );
  }
}
