import 'package:flutter/material.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../components/edit_account_setting_template.dart';
import '../providers/account_setting_provider.dart';

class EditAccountSettingGender extends StatefulWidget {
  const EditAccountSettingGender({super.key});

  @override
  State<EditAccountSettingGender> createState() => _EditAccountSettingGenderState();
}

class _EditAccountSettingGenderState extends State<EditAccountSettingGender> {
  String? _selectedGenderTextId;
  String? _oldGenderTextId;
  late final AccountSettingsProvider _provider;
  late final ValueNotifier<bool> _hasUnsavedChanges;

  @override
  void initState() {
    super.initState();
    _provider = context.read<AccountSettingsProvider>();
    if (_provider.user!.genderTextId?.isNotEmpty ?? false) {
      _oldGenderTextId = _provider.user!.genderTextId;
    }
    _selectedGenderTextId = _oldGenderTextId;
    _hasUnsavedChanges = ValueNotifier(false);
  }

  @override
  void dispose() {
    _hasUnsavedChanges.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditAccountSettingTemplate(
      title: AppLocale.current.gender,
      subtitle: AppLocale.current.accountSettingEditGenderMsg,
      hasUnsavedChanges: _hasUnsavedChanges,
      // noChangeInData: !_hasUnsavedChanges,
      body: Flexible(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.only(
              top: context.theme.d.paddingXLarge,
              // vertical: context.theme.d.paddingXSmall,
              left: context.theme.d.paddingLarge,
              right: context.theme.d.paddingLarge,
            ),
            child: Selector<AccountSettingsProvider, String?>(
              selector: (_, provider) => provider.selectedGenderTextId,
              builder: (context, selectedGenderTextIdFromProvider, _) {
                return Column(
                  children:
                      _provider.genderOptions().map<Widget>((genderOption) {
                        return _genderChip(genderOption);
                      }).toList(),
                );
              },
            ),
          ),
        ),
      ),
      onSave: () async {
        return await _provider.updateUserGender(_selectedGenderTextId);
      },
    );
  }

  void _onGenderChanged(String? newGenderTextId) {
    if (newGenderTextId == _selectedGenderTextId) return;
    _selectedGenderTextId = newGenderTextId;
    _hasUnsavedChanges.value = newGenderTextId != _oldGenderTextId;
    _provider.setSelectedGenderTextId(newGenderTextId);
  }

  Widget _genderChip(Option genderOption) {
    final isSelected = _selectedGenderTextId == genderOption.value;
    return Padding(
      padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingXSmall),
      child: InkWell(
        onTap: () => _onGenderChanged(genderOption.value),
        borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingSmall),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: isSelected ? context.colorScheme.onSurfaceVariant : null,
            border: Border.all(
              color: context.colorScheme.primary,
              width: context.theme.d.borderWidthRegular,
            ),
            borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
          ),
          child: Text(
            genderOption.label,
            style: context.theme.textTheme.bodyMedium?.copyWith(
              color:
                  isSelected
                      ? context.theme.colorScheme.onPrimary
                      : context.theme.colorScheme.primary,
            ),
          ),
        ),
      ),
    );
  }
}
