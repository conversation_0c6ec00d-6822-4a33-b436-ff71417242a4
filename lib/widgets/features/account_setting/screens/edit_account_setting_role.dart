import 'package:flutter/material.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:mm_flutter_app/widgets/shared/common_button.dart';
import 'package:mm_flutter_app/widgets/shared/common_dialog_template.dart';
import 'package:provider/provider.dart';

import '../../../../services/graphql/providers/user_provider.dart';
import '../components/edit_account_setting_template.dart';

class EditAccountSettingRole extends StatefulWidget {
  const EditAccountSettingRole({super.key});

  @override
  State<EditAccountSettingRole> createState() => _EditAccountSettingRoleState();
}

class _EditAccountSettingRoleState extends State<EditAccountSettingRole> {
  late final UserProvider _userProvider;

  bool get isDesktop => AppUtility.displayDesktopUI(context);

  @override
  void initState() {
    super.initState();

    _userProvider = Provider.of<UserProvider>(context, listen: false);
  }

  @override
  Widget build(BuildContext context) {
    return EditAccountSettingTemplate(
      title: AppLocale.current.role,
      subtitle: '',
      body: _roleBody(),
      showWebActionButtons: false,
      hasUnsavedChanges: ValueNotifier(false),
    );
  }

  _showConfirmationPopUp() {
    showDialog(
      context: context,
      builder: (context) {
        return DialogTemplate(
          title: AppLocale.current.accountSettingEditRoleConfirmationTitle,
          description:
              _userProvider.myUser?.seeksHelp == true
                  ? AppLocale.current.accountSettingEditRoleToMentorConfirmMessage
                  : AppLocale.current.accountSettingEditRoleToMenteeConfirmMessage,
          actionButtonTitle: AppLocale.current.actionConfirm,
          cancelButtonTitle: AppLocale.current.actionCancel,
          isFullWidthButtons: !isDesktop,
          showTitleInCenter: false,
          onCancel: () {
            Navigator.pop(context);
          },
          onAction: () async {
            Navigator.pop(context);
          },
        );
      },
    );
  }

  Widget _roleBody() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: context.theme.d.paddingLarge,
        vertical: context.theme.d.paddingSmall,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _userProvider.myUser?.seeksHelp == true
                ? AppLocale.current.accountSettingEditRoleToMentorNote
                : AppLocale.current.accountSettingEditRoleToMenteeNote,
            style: context.theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: context.colorScheme.tertiaryContainer,
            ),
          ),
          SizedBox(height: context.theme.d.paddingMedium),
          Text(
            AppLocale.current.accountSettingEditRoleNote,
            style: context.theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w400,
              color: context.colorScheme.surfaceBright,
            ),
          ),
          SizedBox(height: context.theme.d.paddingLarge),
          _bottomButtons(),
        ],
      ),
    );
  }

  Widget _bottomButtons() {
    final actionButton = CommonButton.primaryRoundedRectangle(
      context: context,
      title:
          _userProvider.myUser?.seeksHelp == true
              ? AppLocale.current.accountSettingEditRoleToMentorButtonTitle
              : AppLocale.current.accountSettingEditRoleToMenteeButtonTitle,
      buttonSize: ButtonSize.small,
      isFullWidth: !isDesktop,
      onPressed: () async {
        if (isDesktop) Navigator.pop(context);
        _showConfirmationPopUp();
      },
    );

    if (!isDesktop) return actionButton;

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        const Spacer(),
        CommonButton.outlinedButton(
          context: context,
          title: AppLocale.current.actionCancel,
          buttonSize: ButtonSize.small,
          onPressed: () async {
            Navigator.pop(context);
          },
        ),
        SizedBox(width: context.theme.d.paddingSmall),
        actionButton,
      ],
    );
  }
}
