import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../models/models.dart';
import '../../../../services/extensions.dart';
import '../../../widgets.dart';
import '../providers/account_setting_provider.dart';

class AccountSetting extends StatefulWidget {
  final EditAccountSettingOptions? editScreenOption;
  const AccountSetting({this.editScreenOption, super.key});

  @override
  State<AccountSetting> createState() => _AccountSettingState();
}

class _AccountSettingState extends State<AccountSetting> {
  final GlobalKey<State> _dialogKey = GlobalKey<State>();

  bool get isDesktop => AppUtility.displayDesktopUI(context);
  late final LocaleModel _localeModel;

  late final AccountSettingsProvider _accountSettingsProvider;

  @override
  void initState() {
    super.initState();
    _localeModel = context.read<LocaleModel>();
    _accountSettingsProvider = context.read<AccountSettingsProvider>();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.editScreenOption != null) {
        _navigateToEditScreen(editOption: widget.editScreenOption!);
      }
      context.read<ScaffoldModel>().setParams(hideNavBar: true);
    });
  }

  Widget _buildDataView(AccountSettingsProvider provider, BuildContext context) {
    final user = provider.user;
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? _) async {
        if (didPop) return;
        if (_dialogKey.currentState != null && Navigator.canPop(_dialogKey.currentContext!)) {
          Navigator.of(_dialogKey.currentContext!).pop();
        }
        context.pop();
      },
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          centerTitle: false,
          leading:
              isDesktop
                  ? null
                  : backButton(
                    context,
                    onPressed:
                        () =>
                            context.canPop()
                                ? context.pop()
                                : context.pushReplacement(AppRoutes.home.path),
                  ),
          title: Text(
            AppLocale.current.accountHeader,
            style: TextStyle(color: context.colorScheme.surfaceBright),
          ),
          shape: AppUtility.topDivider(context),
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            key: const PageStorageKey('account-setting'),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                HeadingWithListTile(
                  heading: AppLocale.current.personalDetails,
                  title: AppLocale.current.name,
                  subtitle: '${user?.firstName ?? ''} ${user?.lastName ?? ''}',
                  onTap: () => _navigateToEditScreen(editOption: EditAccountSettingOptions.name),
                ),
                HeadingWithListTile(
                  title: AppLocale.current.signupCredentialsEmailInputLabel,
                  subtitle: user?.email ?? '',
                  onTap:
                      () =>
                          _navigateToEditScreen(editOption: EditAccountSettingOptions.emailAddress),
                ),
                HeadingWithListTile(
                  title: AppLocale.current.phoneNumberInputLabel,
                  subtitle: user?.phoneNumber ?? '',
                  onTap:
                      () =>
                          _navigateToEditScreen(editOption: EditAccountSettingOptions.phoneNumber),
                ),
                HeadingWithListTile(
                  title: AppLocale.current.signupBirthInputLabel,
                  subtitle: user?.birthYear == null ? '' : user!.birthYear.toString(),
                  onTap:
                      () => _navigateToEditScreen(editOption: EditAccountSettingOptions.birthYear),
                ),
                HeadingWithListTile(
                  title: AppLocale.current.gender,
                  subtitle: provider.getGender(),
                  onTap: () => _navigateToEditScreen(editOption: EditAccountSettingOptions.gender),
                ),
                if (context.theme.appFeatures.switchProfileRole) ...[
                  SizedBox(height: context.theme.d.paddingMedium),
                  HeadingWithListTile(
                    heading: AppLocale.current.role,
                    subtitle:
                        user?.seeksHelp == true
                            ? AppLocale.current.exploreAdvanceFilterUserTypeEntrepreneur
                            : AppLocale.current.exploreAdvanceFilterUserTypeMentor,
                    onTap:
                        () => _navigateToEditScreen(
                          editOption: EditAccountSettingOptions.profileRole,
                        ),
                  ),
                ],
                if (context.theme.appFeatures.appLanguage) ...[
                  SizedBox(height: context.theme.d.paddingMedium),
                  HeadingWithListTile(
                    heading: AppLocale.current.appLanguageHeading,
                    subtitle:
                        provider.getAppLanguage(_localeModel) ??
                        AppLocale.current.selectLanguageTitle,
                    onTap:
                        () => _navigateToEditScreen(
                          editOption: EditAccountSettingOptions.appLanguage,
                        ),
                  ),
                ],
                SizedBox(height: context.theme.d.paddingMedium),
                HeadingWithListTile(
                  heading: AppLocale.current.passwordAndSecurityHeading,
                  subtitle: '**********',
                  onTap:
                      () => _navigateToEditScreen(
                        editOption: EditAccountSettingOptions.passwordAndSecurity,
                      ),
                ),
                HeadingWithListTile(
                  title: AppLocale.current.blockedUsersHeading,
                  onTap:
                      (user?.userBlocks ?? []).isEmpty
                          ? null
                          : () => _navigateToEditScreen(
                            editOption: EditAccountSettingOptions.blockedUsers,
                          ),
                ),
                if (context.theme.appFeatures.notificationPermission)
                  HeadingWithListTile(
                    title: AppLocale.current.notificationsHeading,
                    onTap:
                        () => _navigateToEditScreen(
                          editOption: EditAccountSettingOptions.notifications,
                        ),
                  ),
                HeadingWithListTile(
                  title: AppLocale.current.deleteAccountHeading,
                  leadingWidget: Icon(
                    MicromentorIcons.deleteForeverOutline,
                    color: context.colorScheme.onSecondaryContainer,
                  ),
                  hideTrailing: true,
                  onTap: () {
                    showDialog(
                      context: context,
                      builder: (context) => DeleteAccountDialog(key: _dialogKey),
                    );
                  },
                ),
                SizedBox(height: context.theme.d.paddingMedium),
                // Terms of Use
                HeadingWithListTile(
                  heading: AppLocale.current.accountSettingLegalTitle,
                  title: AppLocale.current.accountSettingLegalTermsOfUse,
                  isLink: true,
                  onTap: () {
                    AppUtility.openLink(
                      getLocalizedUrl(
                        urlType: LegalDocumentType.termsOfUse,
                        localeModel: _localeModel,
                      ),
                    );
                  },
                ),
                HeadingWithListTile(
                  title: AppLocale.current.accountSettingLegalPrivacyPolicy,
                  isLink: true,
                  onTap: () {
                    AppUtility.openLink(
                      getLocalizedUrl(
                        urlType: LegalDocumentType.privacyPolicy,
                        localeModel: _localeModel,
                      ),
                    );
                  },
                ),
                SizedBox(height: context.theme.d.paddingXLarge),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AccountSettingsProvider>(
      builder: (context, provider, _) {
        final user = provider.user;
        if (user == null) {
          return const LoadingScreen();
        }
        return _buildDataView(provider, context);
      },
    );
  }

  _navigateToEditScreen({required EditAccountSettingOptions editOption}) async {
    Widget editScreen;
    String editScreenRoute;

    switch (editOption) {
      case EditAccountSettingOptions.name:
        editScreen = const EditAccountSettingName();
        editScreenRoute = AppRoutes.accountSettingName.path;
        break;
      case EditAccountSettingOptions.emailAddress:
        editScreen = const EditAccountSettingEmail();
        editScreenRoute = AppRoutes.accountSettingEmail.path;

        break;
      case EditAccountSettingOptions.birthYear:
        editScreen = const EditAccountSettingBirthYear();
        editScreenRoute = AppRoutes.accountSettingBirthYear.path;

        break;
      case EditAccountSettingOptions.phoneNumber:
        editScreen = const EditAccountSettingPhone();
        editScreenRoute = AppRoutes.accountSettingPhone.path;

        break;
      case EditAccountSettingOptions.gender:
        editScreen = const EditAccountSettingGender();
        editScreenRoute = AppRoutes.accountSettingGender.path;

        break;
      case EditAccountSettingOptions.profileRole:
        editScreen = const EditAccountSettingRole();
        editScreenRoute = AppRoutes.accountSettingRole.path;

        break;
      case EditAccountSettingOptions.appLanguage:
        editScreen = const EditAccountSettingAppLanguage();
        editScreenRoute = AppRoutes.accountSettingAppLanguage.path;

        break;
      case EditAccountSettingOptions.passwordAndSecurity:
        editScreen = const EditAccountSettingPassword();
        editScreenRoute = AppRoutes.accountSettingPassword.path;

        break;
      case EditAccountSettingOptions.blockedUsers:
        editScreen = const EditAccountSettingViewBlockedUsers();
        editScreenRoute = AppRoutes.accountSettingViewBlockedUsers.path;

        break;
      case EditAccountSettingOptions.notifications:
        editScreen = const EditAccountSettingViewNotificationPermission();
        editScreenRoute = AppRoutes.accountSettingViewNotification.path;

        break;
    }

    if (isDesktop) {
      //Handled the responsive UI here , if user is on any of the edit account setting screen
      if (_isOnEditPage()) {
        context.canPop() ? context.pop() : context.pushReplacement(AppRoutes.accountSettings.path);
      }

      await showDialog(
        context: context,
        barrierDismissible: true,
        builder: (dialogContext) {
          return ChangeNotifierProvider.value(value: _accountSettingsProvider, child: editScreen);
        },
      );
    } else {
      context.push(editScreenRoute);
    }
  }

  bool _isOnEditPage() {
    // TODO: Keep this list in sync with any new routes related to edit-account-settings added in the future.
    final path = GoRouterState.of(context).uri.path;
    return [
      AppRoutes.accountSettingName.path,
      AppRoutes.accountSettingEmail.path,
      AppRoutes.accountSettingBirthYear.path,
      AppRoutes.accountSettingPhone.path,
      AppRoutes.accountSettingGender.path,
      AppRoutes.accountSettingRole.path,
      AppRoutes.accountSettingAppLanguage.path,
      AppRoutes.accountSettingPassword.path,
      AppRoutes.accountSettingViewBlockedUsers.path,
      AppRoutes.accountSettingViewNotification.path,
    ].contains(path);
  }
}
