import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../shared/text_form_field_widget.dart';
import '../components/edit_account_setting_template.dart';
import '../providers/account_setting_provider.dart';

class EditAccountSettingEmail extends StatefulWidget {
  const EditAccountSettingEmail({super.key});

  @override
  State<EditAccountSettingEmail> createState() => _EditAccountSettingEmailState();
}

class _EditAccountSettingEmailState extends State<EditAccountSettingEmail> {
  late final TextEditingController _emailController;
  late final AccountSettingsProvider _provider;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late String? _oldEmail;
  late final ValueNotifier<bool> _isFormValid;

  @override
  void initState() {
    super.initState();
    _provider = context.read<AccountSettingsProvider>();
    final user = _provider.user!;
    _oldEmail = user.email;
    _emailController = TextEditingController(text: _oldEmail);
    _isFormValid = ValueNotifier(_canEnableSave());
  }

  @override
  void dispose() {
    _emailController.dispose();
    _isFormValid.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditAccountSettingTemplate(
      title: AppLocale.current.signupCredentialsEmailInputLabel,
      subtitle: AppLocale.current.accountSettingEditEmailMsg,
      body: Padding(
        padding: EdgeInsets.symmetric(
          vertical: context.theme.d.paddingMedium,
          horizontal: context.theme.d.paddingLarge,
        ),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Consumer<AccountSettingsProvider>(
                builder: (context, provider, _) {
                  return TextFormFieldWidget(
                    label: AppLocale.current.signupCredentialsEmailInputLabel,
                    hint: AppLocale.current.signupCredentialsEmailInputHint,
                    textController: _emailController,
                    contentPadding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
                    onChanged: (_) {
                      if (provider.isEmailAvailable != true) {
                        provider.resetEmailAvailability();
                      }
                      _onFormChanged();
                    },
                    autofocus: true,
                    focusedBorderColor: context.theme.colorScheme.primary,
                    keyboardType: TextInputType.emailAddress,
                    validator:
                        (value) => context.theme.validator.validateEmail(
                          value,
                          isAvailable: _provider.isEmailAvailable,
                        ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
      hasUnsavedChanges: _isFormValid,
      onSave: () async {
        await _provider.checkEmailAvailability(_emailController.text.trim());
        if (!(_formKey.currentState?.validate() ?? false)) {
          _onFormChanged();
          return false;
        }
        return await _provider.updateUserEmail(_emailController.text.trim());
      },
    );
  }

  bool _noChangeInValues() {
    return (_oldEmail?.trim() == _emailController.text.trim());
  }

  void _onFormChanged() {
    _isFormValid.value = _canEnableSave();
  }

  bool _canEnableSave() {
    return _emailController.text.trim().isNotEmpty &&
        (_formKey.currentState?.validate() ?? false) &&
        !_noChangeInValues();
  }
}
