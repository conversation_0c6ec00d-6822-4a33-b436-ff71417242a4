import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../components/edit_account_setting_template.dart';
import '../providers/account_setting_provider.dart';

class EditAccountSettingViewNotificationPermission extends StatefulWidget {
  const EditAccountSettingViewNotificationPermission({super.key});

  @override
  State<EditAccountSettingViewNotificationPermission> createState() =>
      _EditAccountSettingViewNotificationPermissionState();
}

class _EditAccountSettingViewNotificationPermissionState
    extends State<EditAccountSettingViewNotificationPermission> {
  late final AccountSettingsProvider _provider;
  late final ValueNotifier<bool> _hasUnsavedChanges;
  late final bool _initialEmail;
  late final bool _initialPush;

  @override
  void initState() {
    super.initState();
    _provider = context.read<AccountSettingsProvider>();
    _provider.initNotificationPrefs();
    _initialEmail = _provider.isEmailEnabledForMessages;
    _initialPush = _provider.isPushNotificationEnabledForMessages;
    _hasUnsavedChanges = ValueNotifier(false);
  }

  @override
  void dispose() {
    _hasUnsavedChanges.dispose();
    super.dispose();
  }

  void _onChanged() {
    _hasUnsavedChanges.value =
        _provider.isEmailEnabledForMessages != _initialEmail ||
        _provider.isPushNotificationEnabledForMessages != _initialPush;
  }

  @override
  Widget build(BuildContext context) {
    return EditAccountSettingTemplate(
      title: AppLocale.current.notificationHeading,
      subtitle: AppLocale.current.notificationScreenSubtitle,
      hasUnsavedChanges: _hasUnsavedChanges,
      body: Padding(
        padding: EdgeInsets.symmetric(
          vertical: context.theme.d.paddingMedium,
          horizontal: context.theme.d.paddingLarge,
        ),
        child: Consumer<AccountSettingsProvider>(
          builder: (context, provider, _) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocale.current.notificationPush,
                  style: context.theme.textTheme.bodyLarge!.copyWith(
                    fontWeight: FontWeight.w700,
                    color: context.colorScheme.onSecondaryContainer,
                  ),
                ),
                SizedBox(height: context.theme.d.chipPadding),
                AppUtility.switchWithTitle(
                  context: context,
                  title: AppLocale.current.notificationNewMsgInvite,
                  initialValue: provider.isPushNotificationEnabledForMessages,
                  onChanged: (isActive) {
                    provider.setPushNotificationEnabledForMessages(isActive);
                    _onChanged();
                  },
                ),
                if (!context.theme.appFeatures.showOnlyEmailPushNotificationPreference)
                  AppUtility.switchWithTitle(
                    context: context,
                    title: AppLocale.current.notificationRecommendTip,
                    onChanged: (isActive) {},
                  ),
                SizedBox(height: context.theme.d.chipPadding),
                Text(
                  AppLocale.current.notificationEmail,
                  style: context.theme.textTheme.bodyLarge!.copyWith(
                    fontWeight: FontWeight.w700,
                    color: context.colorScheme.onSecondaryContainer,
                  ),
                ),
                SizedBox(height: context.theme.d.chipPadding),
                AppUtility.switchWithTitle(
                  context: context,
                  title: AppLocale.current.notificationNewMsgInvite,
                  initialValue: provider.isEmailEnabledForMessages,
                  onChanged: (isActive) {
                    provider.setEmailEnabledForMessages(isActive);
                    _onChanged();
                  },
                ),
                if (!context.theme.appFeatures.showOnlyEmailPushNotificationPreference)
                  AppUtility.switchWithTitle(
                    context: context,
                    title: AppLocale.current.notificationRecommendation,
                    onChanged: (isActive) {},
                  ),
                if (!context.theme.appFeatures.showOnlyEmailPushNotificationPreference)
                  AppUtility.switchWithTitle(
                    context: context,
                    title: AppLocale.current.notificationTipAdviceUpdate,
                    onChanged: (isActive) {},
                  ),
              ],
            );
          },
        ),
      ),
      onSave: () async {
        return await _provider.updateNotificationPrefs();
      },
    );
  }
}
