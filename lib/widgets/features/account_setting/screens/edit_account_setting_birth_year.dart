import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../shared/text_form_field_widget.dart';
import '../components/edit_account_setting_template.dart';
import '../providers/account_setting_provider.dart';

class EditAccountSettingBirthYear extends StatefulWidget {
  const EditAccountSettingBirthYear({super.key});

  @override
  State<EditAccountSettingBirthYear> createState() => _EditAccountSettingBirthYearState();
}

class _EditAccountSettingBirthYearState extends State<EditAccountSettingBirthYear> {
  late final TextEditingController _yearController;
  late final AccountSettingsProvider _provider;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late final String? _oldBirthYear;
  late final ValueNotifier<bool> _isFormValid;

  @override
  void initState() {
    super.initState();
    _provider = context.read<AccountSettingsProvider>();
    final user = _provider.user!;
    _oldBirthYear = user.birthYear?.toString();
    _yearController = TextEditingController(text: _oldBirthYear ?? '');
    _isFormValid = ValueNotifier(_canEnableSave());
  }

  @override
  void dispose() {
    _yearController.dispose();
    _isFormValid.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditAccountSettingTemplate(
      title: AppLocale.current.signupBirthInputLabel,
      subtitle: AppLocale.current.accountSettingEditBirthYearMsg,
      body: Padding(
        padding: EdgeInsets.symmetric(
          vertical: context.theme.d.paddingMedium,
          horizontal: context.theme.d.paddingLarge,
        ),
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: TextFormFieldWidget(
            label: AppLocale.current.signupBirthInputLabel,
            hint: AppLocale.current.signupBirthInputHint,
            keyboardType: TextInputType.number,
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(4),
            ],
            textController: _yearController,
            onChanged: (_) => _onFormChanged(),
            validator: context.theme.validator.validateBirthYear,
          ),
        ),
      ),
      hasUnsavedChanges: _isFormValid,
      onSave: () async {
        return await _provider.updateUserBirthYear(int.parse(_yearController.text));
      },
    );
  }

  bool _noChangeInValue() {
    return (_oldBirthYear?.trim() == _yearController.text.trim());
  }

  void _onFormChanged() {
    _isFormValid.value = _canEnableSave();
  }

  bool _canEnableSave() {
    return _yearController.text.trim().isNotEmpty &&
        !_noChangeInValue() &&
        (_formKey.currentState?.validate() ?? false);
  }
}
