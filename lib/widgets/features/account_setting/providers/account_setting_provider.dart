import 'package:flutter/material.dart';
import 'package:graphql_flutter/graphql_flutter.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../models/locale_model.dart';
import '../../../../services/graphql/providers/content_provider.dart';
import '../../../../services/graphql/providers/user_provider.dart';

class AccountSettingsProvider extends ChangeNotifier with UserProviderListener {
  final UserProvider userProvider;
  final ContentProvider contentProvider;

  AccountSettingsProvider({required this.userProvider, required this.contentProvider});

  @override
  void dispose() {
    userProvider.removeUserProviderListener(userProviderListenerName);
    super.dispose();
  }

  void addUserProviderListener() {
    userProvider.addUserProviderListener(this);
  }

  User? get user => userProvider.myUser;

  List<Option> genderOptions() {
    return (contentProvider.genderOptions ?? [])
        .map(
          (gender) => Option(value: gender.textId, label: gender.translatedValue ?? gender.textId),
        )
        .toList();
  }

  String? getGender() {
    final String? oldGender = user?.genderTextId;
    final selectedGender =
        contentProvider.genderOptions?.where((e) => e.textId == oldGender).firstOrNull;
    return selectedGender?.translatedValue;
  }

  String? getAppLanguage(LocaleModel localeModel) {
    final languageName =
        user?.selectedUiLanguageTextId?.name ?? user?.fallbackUiLanguageTextId?.name;
    if (languageName == null || languageName.isEmpty) return null;
    return contentProvider
        .hardCodedUiLanguages(localeModel)
        ?.where((e) => e.textId == languageName)
        .firstOrNull
        ?.translatedValue;
  }

  // region: Name Settings
  Future<OperationException?> updateUserName(String firstName, String lastName) async {
    if (user == null) return null;

    final LoadObjectResult<User> result = await userProvider.updateUser(
      input: Input$UserInput(id: user?.id, firstName: firstName, lastName: lastName),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );
    if (result.hasError) {
      return result.operationException;
    }
    return null;
  }
  //endregion

  // region: Phone Settings
  Future<OperationException?> updateUserPhone(String phoneNumber) async {
    if (user == null) return null;

    final LoadObjectResult<User> result = await userProvider.updateUser(
      input: Input$UserInput(id: user?.id, phoneNumber: phoneNumber),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );
    if (result.hasError) {
      return result.operationException;
    }
    return null;
  }

  // region:  Email settings
  bool _isEmailAvailable = true;
  bool get isEmailAvailable => _isEmailAvailable;

  Future<void> checkEmailAvailability(String email) async {
    _isEmailAvailable = await userProvider.isUserIdentAvailable(
      text: email,
      identType: Enum$UserIdentType.email,
    );
    notifyListeners();
  }

  void resetEmailAvailability() {
    _isEmailAvailable = true;
    notifyListeners();
  }

  Future<OperationException?> updateUserEmail(String email) async {
    if (user == null) return null;

    final LoadObjectResult<User> result = await userProvider.updateUser(
      input: Input$UserInput(id: user?.id, email: email),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );
    if (result.hasError) {
      return result.operationException;
    }
    return null;
  }

  // region: birth year settings
  Future<OperationException?> updateUserBirthYear(int birthYear) async {
    if (user == null) return null;
    final LoadObjectResult<User> result = await userProvider.updateUser(
      input: Input$UserInput(id: user?.id, birthYear: birthYear),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );
    if (result.hasError) {
      return result.operationException;
    }
    return null;
  }

  // region: Gender settings
  String? _selectedGenderTextId;
  String? get selectedGenderTextId => _selectedGenderTextId;

  void setSelectedGenderTextId(String? value) {
    if (_selectedGenderTextId != value) {
      _selectedGenderTextId = value;
      notifyListeners();
    }
  }

  Future<dynamic> updateUserGender(String? genderTextId) async {
    if (user == null || genderTextId == null) return null;

    final LoadObjectResult<User> result = await userProvider.updateUser(
      input: Input$UserInput(id: user?.id, genderTextId: genderTextId),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );

    if (result.hasError) {
      return result.operationException;
    }
    return null;
  }

  // region: App Language settings
  Enum$UiLanguage? _selectedUiLanguage;
  Enum$UiLanguage? get selectedUiLanguage => _selectedUiLanguage;

  void setSelectedUiLanguage(Enum$UiLanguage? value) {
    if (_selectedUiLanguage != value) {
      _selectedUiLanguage = value;
      notifyListeners();
    }
  }

  Future<OperationException?> updateUserAppLanguage(
    Enum$UiLanguage? selectedUiLanguage,
    LocaleModel localeModel,
  ) async {
    if (user == null) return null;

    final LoadObjectResult<User> result = await userProvider.updateUser(
      input: Input$UserInput(id: user?.id, selectedUiLanguageTextId: selectedUiLanguage),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );

    // Refresh content provider options if needed
    await contentProvider.findAllOptionsByType(fetchPolicy: FetchPolicy.networkOnly);

    if (result.hasError) {
      return result.operationException;
    }

    // Persist locale value
    localeModel.setLanguage(selectedUiLanguage);

    return null;
  }

  // region: Password settings
  Future<OperationException?> updateUserPassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    if (user == null) return null;

    final LoadObjectResult<User> result = await userProvider.updateUser(
      input: Input$UserInput(
        id: user?.id,
        currentPassword: currentPassword,
        newPassword: newPassword,
      ),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );

    if (result.hasError) {
      return result.operationException;
    }
    return null;
  }

  bool _mustReloadUser = false;

  void setMustReloadUser(bool value) {
    if (_mustReloadUser != value) {
      _mustReloadUser = value;
      notifyListeners();
    }
  }

  // region: Unblock user settings
  List<BlockedUser>? _blockedUsers;
  bool _isBlockedUsersLoading = false;
  String? _processingBlockedUserId;
  String? _blockedUsersError;

  List<BlockedUser>? get blockedUsers => _blockedUsers;
  bool get isBlockedUsersLoading => _isBlockedUsersLoading;
  String? get processingBlockedUserId => _processingBlockedUserId;
  String? get blockedUsersError => _blockedUsersError;

  Future<void> loadBlockedUsers() async {
    _isBlockedUsersLoading = true;
    _blockedUsersError = null;
    notifyListeners();
    try {
      _blockedUsers = await userProvider.findMyBlockedUsers();
    } catch (e) {
      _blockedUsersError = e.toString();
    }
    _isBlockedUsersLoading = false;
    notifyListeners();
  }

  Future<void> unblockUser(String userId, BuildContext context) async {
    _processingBlockedUserId = userId;
    notifyListeners();
    final response = await userProvider.unblockUser(userId: userId);
    _processingBlockedUserId = null;
    if (response.gqlQueryResult.hasException) {
      _blockedUsersError = response.gqlQueryResult.exception.toString();
      notifyListeners();
      return;
    }
    await loadBlockedUsers();
    notifyListeners();
  }

  Future<void> reloadUser(BuildContext context) async {
    if (!_mustReloadUser) return;
    await userProvider.loadUser(
      userId: userProvider.myUser?.id,
      options: LoadObjectOptions<User>(
        pollingConfig: PollingConfig(prevUpdatedAt: userProvider.myUser?.updatedAt),
      ),
    );

    // TODO:don't know why we clearing channels here
    // if (context.mounted) {
    //   Provider.of<ChannelsProvider>(context, listen: false).clearChannels();
    // }
    setMustReloadUser(false);
  }

  // region: Notifications settings
  bool? _isEmailEnabledForMessages;
  bool? _isPushNotificationEnabledForMessages;

  bool get isEmailEnabledForMessages => _isEmailEnabledForMessages ?? false;
  bool get isPushNotificationEnabledForMessages => _isPushNotificationEnabledForMessages ?? false;
  final types = [
    Enum$NotificationType.channelInvitationAccepted,
    Enum$NotificationType.channelInvitationReceived,
    Enum$NotificationType.channelMessageReceived,
  ];
  void initNotificationPrefs() {
    _isEmailEnabledForMessages =
        user?.preferences?.notificationOptions?.any(
          (e) => types.contains(e.notificationType) && e.enableEmail == true,
        ) ??
        false;
    _isPushNotificationEnabledForMessages =
        user?.preferences?.notificationOptions?.any(
          (e) => types.contains(e.notificationType) && e.enablePushNotification == true,
        ) ??
        false;
  }

  void setEmailEnabledForMessages(bool value) {
    if (_isEmailEnabledForMessages != value) {
      _isEmailEnabledForMessages = value;
      notifyListeners();
    }
  }

  void setPushNotificationEnabledForMessages(bool value) {
    if (_isPushNotificationEnabledForMessages != value) {
      _isPushNotificationEnabledForMessages = value;
      notifyListeners();
    }
  }

  Future<OperationException?> updateNotificationPrefs() async {
    if (user == null) return null;

    final prefs =
        types
            .map(
              (type) => Input$NotificationOptionsInput(
                enableEmail: _isEmailEnabledForMessages,
                enablePushNotification: _isPushNotificationEnabledForMessages,
                notificationType: type,
              ),
            )
            .toList();

    final result = await userProvider.updateUser(
      input: Input$UserInput(
        id: user?.id,
        preferences: Input$UserPreferencesInput(notificationOptionsInput: prefs),
      ),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );
    if (result.hasError) {
      return result.operationException;
    }
    return null;
  }

  @override
  void onReceivedMyUser(User myUser) {
    notifyListeners();
  }

  @override
  String get userProviderListenerName => 'AccountSettingProvider';
}
