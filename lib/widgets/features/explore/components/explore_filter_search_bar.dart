import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:mm_flutter_app/widgets/features/explore/screens/explore_filters.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../models/explore_card_filters_model.dart';
import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/content_provider.dart';
import '../../profile/components/big_profile_chip.dart';

class ExploreFilterSearchBar extends StatelessWidget {
  final Enum$UserProfileRole userType;
  final Function onApplyAction;

  const ExploreFilterSearchBar({super.key, required this.userType, required this.onApplyAction});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: context.theme.d.imageSizeMedium,
      margin: EdgeInsets.symmetric(
        horizontal:
            AppUtility.displayDesktopUI(context)
                ? context.theme.d.paddingMedium
                : context.theme.d.paddingSmall,
      ),
      decoration: BoxDecoration(
        color: context.colorScheme.primaryContainer,
        border: Border.all(
          color: context.colorScheme.primaryContainer,
          width: context.theme.d.highlightBorderWidth,
        ),
        borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusSmall),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return InkWell(
            onTap: () => _openFilterScreen(context),
            child: Row(
              children: [
                _searchIcon(context),
                _filterChipsList(context, constraints),
                AppUtility.displayDesktopUI(context)
                    ? _desktopFilterWidget(context)
                    : IconButton(
                      onPressed: () => _openFilterScreen(context),
                      icon: Icon(MicromentorIcons.tune, color: context.theme.colorScheme.onSurface),
                    ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _desktopFilterWidget(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: context.theme.d.paddingXxSmall),
      padding: EdgeInsets.symmetric(
        horizontal: context.theme.d.paddingSmall,
        vertical: context.theme.d.paddingXSmall,
      ),
      decoration: BoxDecoration(
        color: context.colorScheme.onPrimary,
        border: Border.all(color: context.colorScheme.primary),
        borderRadius: BorderRadius.circular(context.theme.d.paddingLarge),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            alignment: AlignmentDirectional.topEnd,
            children: [
              Padding(
                padding: EdgeInsets.all(context.theme.d.paddingXxSmall),
                child: Image.asset(Assets.exploreFilterIcon, width: context.theme.d.iconSizeMedium),
              ),
              Container(
                width: context.theme.d.paddingSmall,
                height: context.theme.d.paddingSmall,
                decoration: BoxDecoration(
                  color: context.theme.colorScheme.error,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
          SizedBox(width: context.theme.d.chipPadding),
          Text(
            AppLocale.current.exploreSearchFilterLabel,
            style: context.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w500,
              color: context.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _createExpertisesChips(BuildContext context, Set<String>? expertises) {
    final contentProvider = Provider.of<ContentProvider>(context, listen: false);

    Set<String> expertisesText = {};

    if (expertises != null) {
      expertisesText.addAll(expertises.map((c) => contentProvider.translateExpertise(c) ?? c));
    }
    List<Widget> chips = [];

    chips.addAll(
      expertisesText.map(
        (expertise) => BigProfileChip(
          text: expertise,
          onDeleted: () {
            expertises?.removeWhere((e) => contentProvider.translateExpertise(e) == expertise);
            _refreshAfterDelete(context);
          },
        ),
      ),
    );
    return chips;
  }

  List<Widget> _createIndustriesChips(BuildContext context, Set<String>? industries) {
    final contentProvider = Provider.of<ContentProvider>(context, listen: false);

    Set<String> industriesText = {};

    if (industries != null) {
      industriesText.addAll(industries.map((c) => contentProvider.translateIndustry(c) ?? c));
    }
    List<Widget> chips = [];

    chips.addAll(
      industriesText.map(
        (expertise) => BigProfileChip(
          text: expertise,
          onDeleted: () {
            industries?.removeWhere((e) => contentProvider.translateIndustry(e) == expertise);
            _refreshAfterDelete(context);
          },
        ),
      ),
    );
    return chips;
  }

  List<Widget> _createLanguageLocationChips(
    BuildContext context,
    Set<String>? countries,
    Set<String>? languages,
  ) {
    Set<String> countriesText = {};
    Set<String> languagesText = {};
    //handling scenarios where countries and languages may be null
    final contentProvider = Provider.of<ContentProvider>(context, listen: false);
    if (countries != null) {
      countriesText.addAll(countries.map((c) => contentProvider.translateCountry(c) ?? c));
    }
    if (languages != null) {
      languagesText.addAll(languages.map((l) => contentProvider.translateLanguages(l) ?? l));
    }

    List<Widget> chips = [];

    chips.addAll(
      languagesText.map(
        (item) => BigProfileChip(
          text: item,
          icon: MicromentorIcons.language,
          onDeleted: () {
            languages?.removeWhere((e) => contentProvider.translateLanguages(e) == item);
            _refreshAfterDelete(context);
          },
        ),
      ),
    );
    chips.addAll(
      countriesText.map(
        (item) => BigProfileChip(
          text: item,
          icon: MicromentorIcons.locationOnOutlined,
          onDeleted: () {
            countries?.removeWhere((e) => contentProvider.translateCountry(e) == item);
            _refreshAfterDelete(context);
          },
        ),
      ),
    );

    return chips;
  }

  String? _helpText() {
    switch (userType) {
      case Enum$UserProfileRole.mentee:
        return AppLocale.current.exploreSearchExpertiseHintMentor;
      case Enum$UserProfileRole.mentor:
        return AppLocale.current.exploreSearchExpertiseHintEntrepreneur;
      default:
        return null;
    }
  }

  Text _createHelpTextHeader(BuildContext context) {
    return Text(_helpText()!, style: Theme.of(context).textTheme.labelMedium);
  }

  Text _createInsertLanguageLocationSubHeader(BuildContext context) {
    return Text(
      AppLocale.current.exploreSearchLanguageLocationHint,
      style: context.theme.textTheme.labelSmall?.copyWith(
        color: context.theme.colorScheme.secondary,
      ),
    );
  }

  _refreshAfterDelete(BuildContext context) {
    Provider.of<ExploreCardFiltersModel>(context, listen: false).clearSearchResult();
    onApplyAction.call();
  }

  Widget _searchIcon(BuildContext context) {
    return IconButton(
      onPressed: null,
      icon: Icon(MicromentorIcons.search, color: context.theme.colorScheme.onSurface),
    );
  }

  _filterChipsList(BuildContext context, BoxConstraints constraints) {
    return Expanded(
      child: Row(
        children: [
          Consumer<ExploreCardFiltersModel>(
            builder: (context, filters, _) {
              List<Widget> myWidgets = [];

              if (filters.userFiltersSelected) {
                if (filters.expertiseFilterSelected) {
                  myWidgets.addAll(_createExpertisesChips(context, filters.selectedExpertises));
                }

                if (filters.countryFilterSelected || filters.languageFilterSelected) {
                  myWidgets.addAll(
                    _createLanguageLocationChips(
                      context,
                      filters.selectedCountries,
                      filters.selectedLanguages,
                    ),
                  );
                }
                //out of ALL advance filter: industry filters are showing
                if (filters.industriesFilterSelected) {
                  myWidgets.addAll(_createIndustriesChips(context, filters.selectedIndustries));
                }

                return AppUtility.displayDesktopUI(context)
                    ? DynamicWrapWithCountDesktop(
                      maxWidth:
                          constraints.maxWidth -
                          (context.theme.d.iconSizeLarge +
                              context
                                  .theme
                                  .d
                                  .imageSizeXLarge), //substracting search icon and  filter icon size
                      widgets: myWidgets,
                      wrapSpacing: context.theme.d.paddingXSmall,
                      wrapRunSpacing: context.theme.d.paddingSmall,
                    )
                    : DynamicWrapWithCount(
                      maxWidth: constraints.maxWidth,
                      widgets: myWidgets,
                      wrapSpacing: context.theme.d.paddingXSmall,
                      wrapRunSpacing: context.theme.d.paddingSmall,
                    );
              }

              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (!filters.userFiltersSelected ||
                      (filters.userFiltersSelected && !filters.expertiseFilterSelected))
                    Row(children: [_createHelpTextHeader(context)]),
                  Row(
                    children: [
                      if (!filters.userFiltersSelected ||
                          (filters.userFiltersSelected &&
                              !filters.languageFilterSelected &&
                              !filters.countryFilterSelected))
                        _createInsertLanguageLocationSubHeader(context),
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  _openFilterScreen(BuildContext context) {
    if (AppUtility.displayDesktopUI(context)) {
      showDialog(
        context: context,
        barrierDismissible: true,
        useSafeArea: true,
        builder:
            (context) => Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: Container(
                    margin: EdgeInsets.symmetric(vertical: context.theme.d.paddingXLarge),
                    constraints: BoxConstraints(maxWidth: context.theme.d.maxDesktopAppWidth),
                    child: Material(
                      color: context.colorScheme.surface,
                      borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXLarge),
                      clipBehavior: Clip.hardEdge,
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          vertical: context.theme.d.paddingLarge,
                          horizontal: context.theme.d.paddingMedium,
                        ),
                        child: ExploreFiltersScreen(onApplyAction: onApplyAction),
                      ),
                    ),
                  ),
                ),
              ],
            ),
      );
    } else {
      showModalBottomSheet(
        elevation: context.theme.d.elevationLevel3,
        isScrollControlled: true,
        context: context,
        useSafeArea: true,
        backgroundColor: context.colorScheme.onPrimary,
        constraints: BoxConstraints(maxHeight: context.mediaQuerySize.height - 150),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadiusDirectional.vertical(
            top: Radius.circular(context.theme.d.sheetRadius),
          ),
        ),
        builder: (context) => ExploreFiltersScreen(onApplyAction: onApplyAction),
      );
    }
  }
}

class DynamicWrapWithCount extends StatelessWidget {
  final List<Widget> widgets;
  final double maxWidth;
  final double wrapSpacing;
  final double wrapRunSpacing;
  final double maxLine;

  static const _displayChipCount = 1;

  const DynamicWrapWithCount({
    super.key,
    required this.widgets,
    required this.maxWidth,
    this.wrapSpacing = 0,
    this.wrapRunSpacing = 0,
    this.maxLine = 2,
  });

  @override
  Widget build(BuildContext context) {
    final List<Widget> children = _buildChildren(_displayChipCount);

    return Wrap(spacing: wrapSpacing, runSpacing: wrapRunSpacing, children: children);
  }

  List<Widget> _buildChildren(int showUptoCount) {
    final List<Widget> children = [];
    children.addAll(widgets.sublist(0, showUptoCount));

    if (widgets.length > showUptoCount) {
      final int remainingCount = widgets.length - showUptoCount;
      children.add(BigProfileChip(text: '+$remainingCount'));
    }
    return children;
  }
}

class DynamicWrapWithCountDesktop extends StatelessWidget {
  final List<Widget> widgets;
  final double maxWidth;
  final double wrapSpacing;
  final double wrapRunSpacing;
  final double maxLine;

  static const countSpacethreshold = 100;

  const DynamicWrapWithCountDesktop({
    super.key,
    required this.widgets,
    required this.maxWidth,
    this.wrapSpacing = 0,
    this.wrapRunSpacing = 0,
    this.maxLine = 1,
  });

  @override
  Widget build(BuildContext context) {
    int displayWidgetCount = 0;
    double finalAvailableSpace = 0;
    for (int i = 1; i <= maxLine; ++i) {
      List<Widget> availableWidget = widgets.sublist(displayWidgetCount);
      var (maxCountPerRow, availableSpace) = _calculateMaxWidgetsPerLine(context, availableWidget);
      displayWidgetCount = displayWidgetCount + maxCountPerRow;
      finalAvailableSpace = availableSpace;
    }

    //if final available space is less than countSpacethreshold(60) and there are more widgets to show
    //we need to adjust spacing to show +count chip
    if (displayWidgetCount < widgets.length) {
      displayWidgetCount =
          finalAvailableSpace > countSpacethreshold ? displayWidgetCount : displayWidgetCount - 1;
    }
    final List<Widget> children = _buildChildren(displayWidgetCount);

    return Wrap(spacing: wrapSpacing, runSpacing: wrapRunSpacing, children: children);
  }

  (int, double) _calculateMaxWidgetsPerLine(BuildContext context, List<Widget> availableWidgets) {
    int maxWidgets = 0;
    double availableWidth = maxWidth;
    for (final widget in availableWidgets) {
      BigProfileChip chip = widget as BigProfileChip;

      //Calculating sized of BibProfileChip to adjust in wrap
      bool hasDeleteIcon = chip.onDeleted != null;
      final double chipWidth = calculateBigProfileChipWidth(
        chip.text,
        chip.icon,
        context,
        hasDeleteIcon: hasDeleteIcon,
      );

      if (chipWidth < availableWidth) {
        maxWidgets += 1;
        //substracting wrap horizontal spacing for accuraccy
        availableWidth -= (chipWidth + wrapSpacing);
      } else {
        //adding wrap horizontal spacing from last widget
        availableWidth += wrapSpacing;
        break;
      }
    }
    return (maxWidgets, availableWidth);
  }

  //Calculating bigChipWidth
  double calculateBigProfileChipWidth(
    String text,
    IconData? icon,
    BuildContext context, {
    bool hasDeleteIcon = false,
  }) {
    TextStyle? chipTextStyle = context.theme.textTheme.labelLarge;
    final labelWidth = TextPainter(
      text: TextSpan(text: text, style: chipTextStyle),
      maxLines: 1,
      textDirection: TextDirection.ltr,
    )..layout();

    double defaultChipLabelHorizontalPadding = context.theme.d.paddingSmall;

    final iconWidth = icon != null ? context.theme.d.iconSizeLarge : context.theme.d.paddingXxSmall;
    final deleteIconWidth =
        hasDeleteIcon ? context.theme.d.iconSizeLarge : context.theme.d.paddingXxSmall;
    final totalWidth =
        labelWidth.width +
        iconWidth +
        deleteIconWidth +
        (context.theme.d.paddingSmall * 2) +
        (defaultChipLabelHorizontalPadding * 2);
    return totalWidth;
  }

  List<Widget> _buildChildren(int showUptoCount) {
    final List<Widget> children = [];
    children.addAll(widgets.sublist(0, showUptoCount));

    if (widgets.length > showUptoCount) {
      final int remainingCount = widgets.length - showUptoCount;
      children.add(BigProfileChip(text: '+$remainingCount'));
    }
    return children;
  }
}
