import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../models/models.dart';
import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/navigation_mixin.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class ExploreScreen extends StatefulWidget {
  const ExploreScreen({super.key});

  @override
  State<ExploreScreen> createState() => _ExploreScreenState();
}

class _ExploreScreenState extends State<ExploreScreen> with NavigationMixin<ExploreScreen> {
  late final UserProvider _userProvider;
  late final InboxProvider _inboxProvider;

  late final ExploreCardFiltersModel filterModel;
  GlobalKey<RefreshIndicatorState> refreshKey = GlobalKey<RefreshIndicatorState>();
  late bool isDesktop;
  Future<bool> isDataAleadyLoaded = Future.value(false);

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    filterModel = Provider.of<ExploreCardFiltersModel>(context, listen: false);
    _inboxProvider = Provider.of<InboxProvider>(context, listen: false);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      filterModel.isSourceHome = false;
      _setInitialFiltersAndPageLimit();
    });
  }

  @override
  void didChangeDependencies() {
    isDesktop = AppUtility.displayDesktopUI(context);
    super.didChangeDependencies();
  }

  Future<bool> _fetchResults() async {
    return await filterModel.fetchSearchResults(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding:
              isDesktop
                  ? EdgeInsets.only(top: context.theme.d.paddingLarge)
                  : EdgeInsets.symmetric(
                    vertical: context.theme.d.paddingSmall,
                    horizontal: context.theme.d.paddingMedium,
                  ),
          child: RefreshIndicator(
            key: refreshKey,
            onRefresh: _fetchResults,
            child: FutureBuilder<bool>(
              future: isDataAleadyLoaded,
              builder:
                  (context, snapshot) => AppUtility.widgetForAsyncSnapshot(
                    snapshot: snapshot,
                    onReady: () {
                      return Consumer<ExploreCardFiltersModel>(
                        builder: (context, filterModel, child) {
                          return Column(
                            children: [
                              ExploreFilterSearchBar(
                                userType:
                                    _userProvider.myUser?.seeksHelp == true
                                        ? Enum$UserProfileRole.mentor
                                        : Enum$UserProfileRole.mentee,
                                onApplyAction: _fetchResults,
                              ),
                              SizedBox(height: context.theme.d.paddingMedium),
                              Expanded(child: filterModel.isLoading ? _loaderWidget() : _content()),
                            ],
                          );
                        },
                      );
                    },
                  ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _loaderWidget() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _content() {
    if (filterModel.searchResults.isEmpty) {
      return _noResultsView();
    }
    return isDesktop ? _gridView() : _listView();
  }

  Widget _gridView() {
    double cardWidth =
        context.theme.d.webUserCardSize.width + (context.theme.d.paddingSmallMedium * 2);
    int noOfCards = context.mediaQuerySize.width ~/ cardWidth;

    return ListView(
      padding: EdgeInsets.only(
        left: context.theme.d.paddingMedium,
        right: context.theme.d.paddingMedium,
        top: context.theme.d.paddingLarge,
      ),
      key: const PageStorageKey('PageStorageKeyExploreGrid'),
      children: [
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: filterModel.searchResults.length,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: noOfCards,
            crossAxisSpacing: context.theme.d.paddingSmallMedium,
            mainAxisSpacing: context.theme.d.paddingSmallMedium,
            mainAxisExtent: context.theme.d.webUserCardSize.height,
          ),
          itemBuilder: (BuildContext context, int index) {
            final user = filterModel.searchResults[index];
            return FutureBuilder(
              future: _inboxProvider.getOneOnOneChannelIdForUser(user.id),
              builder: (context, snap) {
                final channelId = snap.hasData ? snap.data?.$1 : null;
                final isArchievedChannel = snap.hasData ? snap.data?.$2 ?? false : false;
                return _userCard(channelId, isArchievedChannel, user);
              },
            );
          },
        ),
        _seeMoreIcon(true),
      ],
    );
  }

  Widget _listView() {
    return ListView.builder(
      key: const PageStorageKey('PageStorageKeyExploreList'),
      padding: EdgeInsets.symmetric(
        horizontal: context.theme.d.paddingSmall,
        vertical: context.theme.d.paddingSmall,
      ),
      shrinkWrap: true,
      itemCount: filterModel.searchResults.length + 1,
      itemBuilder: (context, i) {
        if (i == filterModel.searchResults.length) {
          return _seeMoreIcon(false);
        }
        final user = filterModel.searchResults[i];
        return FutureBuilder(
          future: _inboxProvider.getOneOnOneChannelIdForUser(user.id),
          builder: (context, snap) {
            final channelId = snap.hasData ? snap.data?.$1 : null;
            final isArchievedChannel = snap.hasData ? snap.data?.$2 ?? false : false;
            return _userCard(channelId, isArchievedChannel, user);
          },
        );
      },
    );
  }

  Widget _noResultsView() {
    return Column(
      children: [
        SizedBox(height: context.theme.d.paddingXLarge),
        Text(
          AppLocale.current.exploreSearchNoResultsTitle,
          style: context.theme.textTheme.bodyLarge?.copyWith(
            color: context.theme.colorScheme.onSurface,
          ),
        ),
        Text(
          AppLocale.current.exploreSearchNoResultsSubtitle,
          style: context.theme.textTheme.bodyMedium?.copyWith(
            color: context.theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _userCard(String? channelId, bool isArchivedChannel, UserBySearch user) {
    final profileUtils = ProfileUtility.fromSearch(user);
    String? sentInvitationId;
    String? receivedInvitationId;
    if (channelId == null) {
      sentInvitationId = _inboxProvider.getSentInvitationIdForUser(user.id);
      if (sentInvitationId == null) {
        receivedInvitationId = _inboxProvider.getReceivedInvitationIdForUser(user.id);
      }
    }
    return RecommendedUserCard(
      profileUtils: profileUtils,
      userId: user.id,
      channelId: channelId,
      isArchivedChannel: isArchivedChannel,
      sentInvitationId: sentInvitationId,
      receivedInvitationId: receivedInvitationId,
      onTap: () {
        GoRouter.of(
          context,
        ).pushNamed(AppRoutes.profileId.name, pathParameters: {RouteParams.userId: user.id});
      },
    );
  }

  Widget _seeMoreIcon(bool isDesktop) {
    if (filterModel.endOfResults) return const SizedBox.shrink();
    Widget seeMoreButton = AppUtility.seeMoreButton(context, onTap: _fetchResults);

    if (!isDesktop) return seeMoreButton;
    return Card(
      margin: EdgeInsets.only(bottom: context.theme.d.paddingXLarge),
      child: seeMoreButton,
    );
  }

  void _setInitialFiltersAndPageLimit() {
    _calculatePageLimit();

    (filterModel.searchId ?? '').isEmpty
        ? filterModel.initializeFilters(context)
        : filterModel.clearSearchResult(clearSearchId: false);
    isDataAleadyLoaded = _fetchResults();
  }

  _calculatePageLimit() {
    if (!isDesktop) {
      filterModel.webPageLimit = Limits.searchResultsPageSize;
      return;
    }
    double height =
        context.mediaQuerySize.height -
        (context.theme.d.paddingLarge + context.theme.d.imageSizeMedium) -
        context.theme.d.customToolbarHeight;

    double cardWidth =
        context.theme.d.webUserCardSize.width + (context.theme.d.paddingSmallMedium * 2);
    int noOfItemsInRow = context.mediaQuerySize.width ~/ cardWidth;

    double cardHeight = context.theme.d.webUserCardSize.height;
    int noOfItemsInColumn = height ~/ cardHeight;
    noOfItemsInColumn += 1;

    int totalItems = noOfItemsInRow * noOfItemsInColumn;
    filterModel.webPageLimit = totalItems;
  }
}
