import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/widgets/shared/text_form_field_widget.dart';
import 'package:textfield_tags/textfield_tags.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../models/explore_card_filters_model.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../shared/multi_select_dropdown.dart';

class ExploreAdvancedFiltersScreen extends StatelessWidget {
  final ContentProvider contentProvider;
  final ExploreCardFiltersModel filtersModel;
  final StringTagController ventureStageController;
  final StringTagController industriesController;
  final StringTagController userRoleController;
  final TextEditingController keywordController;
  final Enum$UserProfileRole? userProfileRole;
  final Function(Enum$UserProfileRole?) onUserProfileRoleChanged;
  final Function()? refreshScreen;

  const ExploreAdvancedFiltersScreen({
    super.key,
    required this.contentProvider,
    required this.filtersModel,
    required this.industriesController,
    required this.userRoleController,
    required this.ventureStageController,
    required this.keywordController,
    required this.userProfileRole,
    required this.onUserProfileRoleChanged,
    this.refreshScreen,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        MultiSelectDropdown(
          label: AppLocale.current.exploreSearchFilterIndustry,
          hint: AppLocale.current.exploreSearchFilterHintSelect1,
          tagsController: industriesController,
          maxSelection: 1,
          options: contentProvider.industryTextIds,
          optionsTranslations:
              (textId) =>
                  contentProvider.industryOptions!
                      .firstWhere((e) => e.textId == textId)
                      .translatedValue!,
          selectedOptions: filtersModel.selectedIndustries,
          onChange: () {
            refreshScreen?.call();
          },
        ),
        SizedBox(height: context.theme.d.paddingXLarge),
        MultiSelectDropdown(
          label: AppLocale.current.exploreSearchFilterUserType,
          hint: AppLocale.current.exploreSearchFilterHintSelect1,
          tagsController: userRoleController,
          maxSelection: 1,
          onChange: () {
            Enum$UserProfileRole? value =
                (userRoleController.getTags?.isNotEmpty == true &&
                        (userRoleController.getTags?.first ==
                            AppLocale.current.exploreAdvanceFilterUserTypeEntrepreneur))
                    ? Enum$UserProfileRole.mentee
                    : userRoleController.getTags?.isNotEmpty == true
                    ? Enum$UserProfileRole.mentor
                    : null;

            onUserProfileRoleChanged.call(value);
          },
          options: [
            AppLocale.current.exploreAdvanceFilterUserTypeEntrepreneur,
            AppLocale.current.exploreAdvanceFilterUserTypeMentor,
          ],
          selectedOptions:
              (userProfileRole != null &&
                      (userProfileRole == Enum$UserProfileRole.mentee ||
                          userProfileRole == Enum$UserProfileRole.mentor))
                  ? userProfileRole == Enum$UserProfileRole.mentee
                      ? {AppLocale.current.exploreAdvanceFilterUserTypeEntrepreneur}
                      : {AppLocale.current.exploreAdvanceFilterUserTypeMentor}
                  : null,
        ),
        SizedBox(height: context.theme.d.paddingLarge),
        if (userProfileRole == Enum$UserProfileRole.mentee) ...[
          MultiSelectDropdown(
            label: AppLocale.current.exploreSearchFilterVentureStage,
            tagsController: ventureStageController,
            options: contentProvider.companyStageTextIds,
            optionsTranslations:
                (textId) =>
                    contentProvider.companyStageOptions!
                        .firstWhere((e) => e.textId == textId)
                        .translatedValue!,
            selectedOptions: filtersModel.selectedVentureStages,
            onChange: () {
              refreshScreen?.call();
            },
          ),
          SizedBox(height: context.theme.d.paddingLarge),
        ],
        TextFormFieldWidget(
          label: AppLocale.current.exploreSearchFilterKeyword,
          labelStyle: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
          hint: AppLocale.current.exploreSearchFilterKeywordHint,
          textController: keywordController,
          onChanged: (value) {
            refreshScreen?.call();
          },
        ),
      ],
    );
  }
}
