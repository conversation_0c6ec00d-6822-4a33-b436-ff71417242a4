import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';
import 'package:textfield_tags/textfield_tags.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../widgets.dart';

class ExploreFiltersScreen extends StatefulWidget {
  final Function? onApplyAction;
  const ExploreFiltersScreen({super.key, this.onApplyAction});

  @override
  State<StatefulWidget> createState() => _RecommendedMentorsFilters();
}

class _RecommendedMentorsFilters extends State<ExploreFiltersScreen> {
  late final StringTagController _countriesController;
  late final StringTagController _languagesController;
  late final StringTagController _expertiseController;
  late final ExploreCardFiltersModel _filtersModel;
  late final ContentProvider _contentProvider;

  // Advanced filters
  late bool showAdvancedFilters;
  late final StringTagController _industriesController;
  late final StringTagController _ventureStageController;
  late final StringTagController _userRoleController;
  late final TextEditingController _keywordController;
  late Enum$UserProfileRole? _userProfileRole;
  final GlobalKey<State> _dialogKey = GlobalKey<State>();
  late bool isDesktopView;

  @override
  void initState() {
    super.initState();
    _countriesController = StringTagController();
    _languagesController = StringTagController();
    _expertiseController = StringTagController();
    _userRoleController = StringTagController();
    _ventureStageController = StringTagController();
    _industriesController = StringTagController();

    _filtersModel = Provider.of<ExploreCardFiltersModel>(context, listen: false);
    _contentProvider = Provider.of<ContentProvider>(context, listen: false);

    _keywordController = TextEditingController(text: _filtersModel.selectedKeyword);
    _userProfileRole = null;
  }

  @override
  void didChangeDependencies() {
    isDesktopView = AppUtility.displayDesktopUI(context);
    showAdvancedFilters = isDesktopView ? true : false;
    _setUserProfileRole();
    super.didChangeDependencies();
  }

  //Setting up userProfileRole
  _setUserProfileRole() {
    if (_filtersModel.selectedUserType == null ||
        _filtersModel.selectedUserType == Enum$UserProfileRole.none ||
        _filtersModel.selectedUserType == Enum$UserProfileRole.$unknown) {
      _userProfileRole = null;
    } else {
      _userProfileRole = _filtersModel.selectedUserType;

      if (_userProfileRole != null) {
        _userRoleController.addTag(
          (_userProfileRole == Enum$UserProfileRole.mentee ||
                  _userProfileRole == Enum$UserProfileRole.mentor)
              ? _userProfileRole == Enum$UserProfileRole.mentee
                  ? AppLocale.current.exploreAdvanceFilterUserTypeEntrepreneur
                  : AppLocale.current.exploreAdvanceFilterUserTypeMentor
              : '',
        );
      }
    }
  }

  @override
  void dispose() {
    try {
      _countriesController.dispose();
      _languagesController.dispose();
      _keywordController.dispose();
      _ventureStageController.dispose();
      _industriesController.dispose();
      _userRoleController.dispose();
      _expertiseController.dispose();
    } catch (_) {}
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    int filterCounts = _getAdvanceFilter();
    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          ListTile(
            leading: isDesktopView ? null : backButton(context, onPressed: () => context.pop()),
            title: Text(
              AppLocale.current.exploreSearchFilterTitle,
              style: context.theme.textTheme.titleLarge?.copyWith(
                color: context.colorScheme.onSecondaryContainer,
                fontWeight: FontWeight.w700,
              ),
            ),
            trailing:
                isDesktopView
                    ? IconButton(
                      icon: Icon(MicromentorIcons.close, size: context.theme.d.iconSizeLarge),
                      color: context.colorScheme.onSurface,
                      onPressed: () {
                        ExploreCardFiltersModel currentUnappliedFilters =
                            _getCurrentUnappliedFilters();

                        // Comparing objects of ExploreCardFiltersModel to check if there are any unapplied filter selections. If so, a confirmation dialog to discard changes will be shown.
                        if (_filtersModel != currentUnappliedFilters) {
                          _showAlertDialog(context);
                        } else {
                          context.pop();
                        }
                      },
                    )
                    : CommonButton.textButton(
                      context: context,
                      textStyle: context.theme.textTheme.labelLarge?.copyWith(
                        color: context.theme.colorScheme.tertiary,
                      ),
                      title: AppLocale.current.exploreSearchFilterReset,
                      onPressed: () {
                        //TODO: for now keeping mobile Reset all button functionality as it is
                        //i.e  Reset all button is clearing all applied filters
                        //but in future we have to change this
                        if (_isAnyFilterSelected()) {
                          _showClearAllAlertDialog(context);
                        } else {
                          context.pop();
                        }
                      },
                    ),
          ),
          Flexible(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.all(context.theme.d.paddingMedium),
                child: Column(
                  children: [
                    Column(
                      children: [
                        MultiSelectDropdown(
                          maxSelection: 3,
                          label: AppLocale.current.exploreSearchFilterExpertise,
                          hint: AppLocale.current.exploreSearchFilterHintSelectUpTo3,
                          tagsController: _expertiseController,
                          options: _contentProvider.expertiseTextIds,
                          optionsTranslations:
                              (id) => _contentProvider.translateExpertise(id) ?? id,
                          selectedOptions: _filtersModel.selectedExpertises,
                        ),
                        SizedBox(height: context.theme.d.paddingLarge),
                        MultiSelectDropdown(
                          label: AppLocale.current.exploreSearchFilterHeadingLanguage,
                          hint: AppLocale.current.exploreSearchFilterHintTypeToSelectUpTo3,
                          editable: true,
                          tagsController: _languagesController,
                          maxSelection: 3,
                          options: _contentProvider.languageTextIds,
                          notFoundMessage: AppLocale.current.multiSelectDropdownLanguageNotFoundMsg,
                          optionsTranslations:
                              (id) => _contentProvider.translateLanguages(id) ?? id,
                          selectedOptions: _filtersModel.selectedLanguages,
                        ),
                        SizedBox(height: context.theme.d.paddingMedium),
                        MultiSelectDropdown(
                          label: AppLocale.current.exploreSearchFilterHeadingCountries,
                          hint: AppLocale.current.exploreSearchFilterHintTypeToSelectUpTo3,
                          maxSelection: 3,
                          editable: true,
                          tagsController: _countriesController,
                          options: _contentProvider.countryTextIds,
                          optionsTranslations: (id) => _contentProvider.translateCountry(id) ?? id,
                          selectedOptions: _filtersModel.selectedCountries,
                        ),
                      ],
                    ),
                    SizedBox(height: context.theme.d.paddingMedium),
                    if (!isDesktopView)
                      Align(
                        alignment: Alignment.center,
                        child: CommonButton.textButton(
                          context: context,
                          icon: Icon(
                            showAdvancedFilters ? MicromentorIcons.remove : MicromentorIcons.add,
                            color: context.theme.colorScheme.tertiary,
                          ),
                          textStyle: context.theme.textTheme.labelLarge?.copyWith(
                            color: context.theme.colorScheme.tertiary,
                          ),
                          title:
                              filterCounts > 0
                                  ? showAdvancedFilters
                                      ? '${AppLocale.current.exploreSearchFilterHideFilterMsg} ($filterCounts)'
                                      : '${AppLocale.current.exploreSearchFilterShowFilterMsg} ($filterCounts)'
                                  : showAdvancedFilters
                                  ? AppLocale.current.exploreSearchFilterHideFilterMsg
                                  : AppLocale.current.exploreSearchFilterShowFilterMsg,
                          onPressed: () {
                            setState(() {
                              showAdvancedFilters = !showAdvancedFilters;
                              if (!showAdvancedFilters) {
                                _filtersModel.setAdvancedFilters(
                                  selectedIndustries: _industriesController.getTags?.toSet(),
                                  selectedVentureStages:
                                      _userProfileRole == Enum$UserProfileRole.mentee
                                          ? _ventureStageController.getTags?.toSet()
                                          : null,
                                );
                              }
                            });
                          },
                        ),
                      ),
                    if (showAdvancedFilters) ...[
                      SizedBox(height: context.theme.d.paddingMedium),
                      _advancedFilters(),
                    ],
                    SizedBox(height: context.theme.d.paddingLarge),
                  ],
                ),
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: context.theme.d.paddingMedium,
                vertical: context.theme.d.paddingXxSmall,
              ),
              child: CommonButton.primaryRoundedRectangle(
                context: context,
                isFullWidth: isDesktopView ? false : true,
                title: AppLocale.current.actionApply,
                buttonSize: isDesktopView ? ButtonSize.medium : ButtonSize.large,
                onPressed: () {
                  _filtersModel.setFilters(
                    selectedCountries: _countriesController.getTags?.toSet(),
                    selectedLanguages: _languagesController.getTags?.toSet(),
                    selectedExpertises: _expertiseController.getTags?.toSet(),
                  );

                  _filtersModel.setAdvancedFilters(
                    selectedIndustries: _industriesController.getTags?.toSet(),
                    selectedVentureStages:
                        _userProfileRole == Enum$UserProfileRole.mentee
                            ? _ventureStageController.getTags?.toSet()
                            : null,
                    selectedUserType: _userProfileRole,
                    selectedKeyword:
                        _keywordController.text.trim().isNotEmpty
                            ? _keywordController.text.trim()
                            : null,
                  );
                  widget.onApplyAction?.call();
                  context.pop();
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  _advancedFilters() {
    return ExploreAdvancedFiltersScreen(
      contentProvider: _contentProvider,
      filtersModel: _filtersModel,
      industriesController: _industriesController,
      ventureStageController: _ventureStageController,
      userRoleController: _userRoleController,
      keywordController: _keywordController,
      userProfileRole: _userProfileRole,
      refreshScreen: () => setState(() {}),
      onUserProfileRoleChanged: (Enum$UserProfileRole? newProfileRole) {
        setState(() {
          _userProfileRole = newProfileRole;
        });
      },
    );
  }

  int _getAdvanceFilter() {
    int count = 0;

    if (_industriesController.getTags?.isNotEmpty == true ||
        (_filtersModel.selectedIndustries.isNotEmpty)) {
      count += 1;
    }
    if (_userRoleController.getTags?.isNotEmpty == true) {
      count += 1;
    }
    if (_keywordController.text.trim().isNotEmpty || (_filtersModel.selectedKeyword != null)) {
      count += 1;
    }

    if (_ventureStageController.getTags?.isNotEmpty == true ||
        (_filtersModel.selectedVentureStages.isNotEmpty)) {
      count += 1;
    }
    return count;
  }

  ExploreCardFiltersModel _getCurrentUnappliedFilters() {
    ExploreCardFiltersModel currentUnappliedFilters = ExploreCardFiltersModel();

    currentUnappliedFilters.setFilters(
      selectedCountries: _countriesController.getTags?.toSet(),
      selectedLanguages: _languagesController.getTags?.toSet(),
      selectedExpertises: _expertiseController.getTags?.toSet(),
    );

    currentUnappliedFilters.setAdvancedFilters(
      selectedIndustries: _industriesController.getTags?.toSet(),
      selectedVentureStages:
          _userProfileRole == Enum$UserProfileRole.mentee
              ? _ventureStageController.getTags?.toSet()
              : null,
      selectedUserType: _userProfileRole,
      selectedKeyword:
          _keywordController.text.trim().isNotEmpty ? _keywordController.text.trim() : null,
    );

    return currentUnappliedFilters;
  }

  _showAlertDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) {
        return DialogTemplate(
          key: _dialogKey,
          title: AppLocale.current.exploreSearchFilterDiscardFiltersTitle,
          description: AppLocale.current.exploreSearchFilterDiscardFiltersMsg,
          showTitleInCenter: !isDesktopView,
          isFullWidthButtons: !isDesktopView,
          onAction: () {
            Navigator.pop(dialogContext);
            context.pop();
          },
        );
      },
    );
  }

  _showClearAllAlertDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) {
        return DialogTemplate(
          key: _dialogKey,
          title: AppLocale.current.exploreSearchFilterDiscardFiltersTitle,
          description: AppLocale.current.exploreSearchFilterDiscardFiltersMsg,
          showTitleInCenter: !isDesktopView,
          isFullWidthButtons: !isDesktopView,
          onAction: () {
            _clearFilterData();
            _expertiseController.clearTags();
            _languagesController.clearTags();
            _countriesController.clearTags();
            widget.onApplyAction?.call();

            Navigator.pop(dialogContext);
            context.pop();
          },
        );
      },
    );
  }

  _clearFilterData() {
    _filtersModel.clearData();
    if (_industriesController.getTags?.isNotEmpty == true) {
      _industriesController.clearTags();
    }
    _keywordController.clear();
    if (_userProfileRole == Enum$UserProfileRole.mentee &&
        _ventureStageController.getTags?.isNotEmpty == true) {
      _ventureStageController.clearTags();
    }
    _setUserProfileRole();
  }

  bool _isAnyFilterSelected() {
    return ((_expertiseController.getTags?.isNotEmpty == true ||
            _filtersModel.expertiseFilterSelected) ||
        (_languagesController.getTags?.isNotEmpty == true ||
            _filtersModel.languageFilterSelected) ||
        (_countriesController.getTags?.isNotEmpty == true || _filtersModel.countryFilterSelected) ||
        _getAdvanceFilter() > 0);
  }
}
