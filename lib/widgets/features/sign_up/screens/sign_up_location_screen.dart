import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../widgets.dart';

class SignupLocationScreen extends StatefulWidget {
  const SignupLocationScreen({super.key});

  @override
  State<SignupLocationScreen> createState() => _SignupLocationScreenState();
}

class _SignupLocationScreenState extends State<SignupLocationScreen> {
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  late final ContentProvider _contentProvider;
  late final TextEditingController _locationController;
  late final TextEditingController _governorateController;
  late final TextEditingController _districtController;
  late final TextEditingController _striveProvinceController;
  late final TextEditingController _striveDistrictController;
  Country? _selectedCountry;
  JordanianGovernorate? _selectedGovernorate;
  JordanianDistrict? _selectedDistrict;
  IndonesianCity? _selectedStriveDistrict;
  IndonesianProvince? _selectedStriveProvince;
  double _progress = 0;
  ValueNotifier<double> extraScrollingPadding = ValueNotifier(0);
  final _formKey = GlobalKey<FormState>();
  bool _processing = false;
  late FocusNode focusNode;
  double? _scrollablePadding;

  @override
  void initState() {
    super.initState();

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    // TODO - also set region and country

    _locationController = TextEditingController();
    _governorateController = TextEditingController();
    _districtController = TextEditingController();
    _striveProvinceController = TextEditingController();
    _striveDistrictController = TextEditingController();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (_contentProvider.indonesianProvincesOptions == null) {
        final localeProvider = Provider.of<LocaleModel>(context, listen: false);
        localeProvider.getCurrentLanguageCode();
        final selectedLanguage = Enum$UiLanguage.values.byName(
          localeProvider.getCurrentLanguageCode(),
        );
        await _contentProvider.findIndonesianCities(selectedLanguage);
        await _contentProvider.findIndonesianProvinces(selectedLanguage);
        setState(() {});
      }
      _setInitialValues();
    });

    focusNode = FocusNode();
  }

  _setInitialValues() {
    _selectedCountry =
        _contentProvider.countryOptions
            ?.toList()
            .where((element) => element.textId == _userProvider.myUser?.countryOfResidenceTextId)
            .firstOrNull;
    if (_onboardingModel.isIQLAAGroup) {
      _selectedCountry ??=
          _contentProvider.countryOptions
              ?.toList()
              .where((element) => element.textId == 'JO')
              .firstOrNull;
      _selectedGovernorate =
          _contentProvider.jordanianGovernorateOptions
              ?.toList()
              .where(
                (element) =>
                    element.translatedValue?.toLowerCase() ==
                    _userProvider.myUser?.regionOfResidence?.toLowerCase(),
              )
              .firstOrNull;
      _selectedDistrict =
          _contentProvider.jordanianDistrictOptions
              ?.toList()
              .where(
                (element) =>
                    element.translatedValue?.toLowerCase() ==
                    _userProvider.myUser?.cityOfResidence?.toLowerCase(),
              )
              .firstOrNull;
    }

    if (_onboardingModel.isStriveIndonesiaGroup) {
      _selectedCountry ??=
          _contentProvider.countryOptions
              ?.toList()
              .where((element) => element.textId == 'ID')
              .firstOrNull;

      _selectedStriveProvince =
          _contentProvider.indonesianProvincesOptions
              ?.toList()
              .where(
                (element) =>
                    element.translatedValue?.toLowerCase() ==
                    _userProvider.myUser?.regionOfResidence?.toLowerCase(),
              )
              .firstOrNull;
      _selectedStriveDistrict =
          _contentProvider.indonesianCitiesOptions
              ?.toList()
              .where(
                (element) =>
                    element.translatedValue?.toLowerCase() ==
                    _userProvider.myUser?.cityOfResidence?.toLowerCase(),
              )
              .firstOrNull;
    }

    _locationController.text = _selectedCountry?.translatedValue ?? '';
    _governorateController.text = _selectedGovernorate?.translatedValue ?? '';
    _districtController.text = _selectedDistrict?.translatedValue ?? '';
    _striveDistrictController.text = _selectedStriveDistrict?.translatedValue ?? '';
    _striveProvinceController.text = _selectedStriveProvince?.translatedValue ?? '';

    _localData.onboardingStage = OnboardingStage.location;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.location,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );
  }

  @override
  void dispose() {
    try {
      _locationController.dispose();
      _governorateController.dispose();
      _districtController.dispose();
      _striveProvinceController.dispose();
      _striveDistrictController.dispose();
    } catch (_) {}
    super.dispose();
  }

  bool _canGoNext() {
    if (_processing) {
      return false;
    }
    return (_onboardingModel.isIQLAAGroup)
        ? (_locationController.text.isNotEmpty &&
            _selectedCountry != null &&
            _selectedGovernorate != null &&
            _selectedDistrict != null)
        : (_onboardingModel.isStriveIndonesiaGroup)
        ? (_locationController.text.isNotEmpty &&
            _selectedStriveDistrict != null &&
            _selectedStriveProvince != null)
        : (_locationController.text.isNotEmpty && _selectedCountry != null);
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      title: AppLocale.current.signupLocationTitle,
      subtitle: AppLocale.current.signupLocationSubtitle,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      onboardingStage: OnboardingStage.location,
      onboardingModel: _onboardingModel,
      extraScrollingSpace:
          _scrollablePadding ??
          (context.theme.d.desktopOnboardingExtraScrollingSpace + context.theme.d.boxSizeSmall),
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      focusNode: focusNode,
      body: Form(
        key: _formKey,
        child:
            _onboardingModel.isIQLAAGroup
                ? Column(children: [_countryWidget(), _governorateWidget(), _districtWidget()])
                : _onboardingModel.isStriveIndonesiaGroup
                ? Column(
                  children: [_countryWidget(), _striveProvinceWidget(), _striveDistrictWidget()],
                )
                : _countryWidget(),
      ),
      canGoNext: _canGoNext(),
      onGoNext: () async {
        if (_formKey.currentState != null && !_formKey.currentState!.validate()) {
          return;
        }

        setState(() => _processing = true);
        // TODO - also set region and country
        OnboardingStage? nextStage = _onboardingModel.getNextStage(
          curStage: OnboardingStage.location,
          isMentee: _userProvider.myUser?.seeksHelp != false,
        );
        final localeModel = Provider.of<LocaleModel>(context, listen: false);

        final result = await _userProvider.updateUser(
          input: Input$UserInput(
            id: _localData.userId,
            countryOfResidenceTextId: _selectedCountry?.textId,
            regionOfResidence: _selectedGovernorate?.translatedValue,
            cityOfResidence: _selectedDistrict?.translatedValue,
            onboardingStage: nextStage?.name,
            preferredLanguageTextId:
                _onboardingModel.isStriveIndonesiaGroup
                    ? localeModel.getCurrentLanguageCode()
                    : null,
          ),
          options: UpdateObjectOptions<User>(
            loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
          ),
        );

        if (result.hasError) {
          _handleError(result);
          return;
        }

        // Navigate to next screen:
        setState(() => _processing = false);
        if (context.mounted && nextStage != null) {
          _localData.onboardingStage = nextStage;
          context.push(_onboardingModel.getRouteFromStage(nextStage).path);
        }
      },
    );
  }

  _countryWidget() {
    if (_onboardingModel.isIQLAAGroup || _onboardingModel.isStriveIndonesiaGroup) {
      return TextFormFieldWidget(
        label: AppLocale.current.signupLocationCountryInputLabel,
        keyboardType: TextInputType.number,
        textController: _locationController,
        enabled: false,
      );
    }
    return AutocompleteTextField<Country>(
      scrollableBottomPadding: _scrollablePadding,
      textEditingController: _locationController,
      label: AppLocale.current.signupLocationCountryInputLabel,
      hint: AppLocale.current.signupLocationInputHint,
      showClose: true,
      showPrefixSearchIcon: true,
      itemBuilder: (context, country) {
        return ListTile(title: Text(country.translatedValue ?? ''));
      },
      suggestionsCallback: (String search) {
        var suggestionList =
            _contentProvider.countryOptions
                ?.toList()
                .where(
                  (country) =>
                      ('${country.translatedValue} ${country.textId}').toLowerCase().contains(
                        search.trim().toLowerCase(),
                      ) ==
                      true,
                )
                .toList();
        _updateScrollableSpace(suggestionList?.length ?? 1);
        return suggestionList;
      },
      emptyBuilder: (_) {
        return ListTile(title: Text(AppLocale.current.emptyLocationMessage));
      },
      onClose: (controller) => controller.clear(),
      onChange: () {
        setState(() => _selectedCountry = null);
      },
      onSelected: (country) {
        _updateScrollableSpaceOnItemSelection();

        setState(() {
          _selectedCountry = country;
          _locationController.text = country.translatedValue ?? '';
          _selectedGovernorate = null;
          _governorateController.clear();
          _selectedDistrict = null;
          _districtController.clear();
        });
      },
    );
  }

  _governorateWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingMedium),
      child: AutocompleteTextField<JordanianGovernorate>(
        scrollableBottomPadding: _scrollablePadding,
        textEditingController: _governorateController,
        label: AppLocale.current.governorate,
        hint: AppLocale.current.governorateHint,
        showClose: true,
        showPrefixSearchIcon: true,
        itemBuilder: (context, governorate) {
          return ListTile(title: Text(governorate.translatedValue ?? ''));
        },
        suggestionsCallback: (String search) {
          var suggestionList =
              _contentProvider.jordanianGovernorateOptions
                  ?.toList()
                  .where(
                    (governorate) =>
                        ('${governorate.translatedValue} ${governorate.textId}')
                            .toLowerCase()
                            .contains(search.trim().toLowerCase()) ==
                        true,
                  )
                  .toList();
          _updateScrollableSpace(suggestionList?.length ?? 1);

          return suggestionList;
        },
        emptyBuilder: (_) {
          return ListTile(title: Text(AppLocale.current.emptyLocationMessage));
        },
        onClose: (controller) {
          _selectedDistrict = null;
          _districtController.clear();
          setState(() {
            _selectedGovernorate = null;
            controller.clear();
          });
        },
        onChange: () {
          setState(() {
            _selectedGovernorate = null;
            _selectedDistrict = null;
            _districtController.clear();
          });
        },
        onSelected: (governorate) {
          _updateScrollableSpaceOnItemSelection();

          setState(() {
            _selectedGovernorate = governorate;
            _governorateController.text = governorate.translatedValue ?? '';
            _selectedDistrict = null;
            // This line has added to refresh the district suggestion callback
            _districtController.text = governorate.translatedValue ?? '';
            _districtController.clear();
          });
        },
      ),
    );
  }

  _districtWidget() {
    return AutocompleteTextField<JordanianDistrict>(
      scrollableBottomPadding: _scrollablePadding,
      textEditingController: _districtController,
      label: AppLocale.current.district,
      hint: AppLocale.current.districtHint,
      showClose: true,
      showPrefixSearchIcon: true,
      itemBuilder: (context, district) {
        return ListTile(title: Text(district.translatedValue ?? ''));
      },
      suggestionsCallback: (String search) {
        var suggestionList =
            _contentProvider.jordanianDistrictOptions
                ?.toList()
                .where(
                  (district) =>
                      ('${district.translatedValue} ${district.textId}').toLowerCase().contains(
                            search.trim().toLowerCase(),
                          ) ==
                          true &&
                      district.parentTextId == _selectedGovernorate?.textId,
                )
                .toList();
        _updateScrollableSpace(suggestionList?.length ?? 1);

        return suggestionList;
      },
      emptyBuilder: (_) {
        return ListTile(title: Text(AppLocale.current.emptyLocationMessage));
      },
      onClose: (controller) {
        controller.clear();
        _selectedDistrict = null;
      },
      onChange: () {
        setState(() => _selectedDistrict = null);
      },
      onSelected: (district) {
        _updateScrollableSpaceOnItemSelection();

        setState(() {
          _selectedDistrict = district;
          _districtController.text = district.translatedValue ?? '';
        });
      },
    );
  }

  _striveProvinceWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingMedium),
      child: AutocompleteTextField<IndonesianProvince>(
        scrollableBottomPadding: _scrollablePadding,
        textEditingController: _striveProvinceController,
        label: AppLocale.current.striveIndonesiaProvince,
        hint: AppLocale.current.striveIndonesiaProvinceHint,
        showClose: true,
        showPrefixSearchIcon: true,
        itemBuilder: (context, province) {
          return ListTile(title: Text(province.translatedValue ?? ''));
        },
        suggestionsCallback: (String search) {
          var suggestionList =
              _contentProvider.indonesianProvincesOptions
                  ?.toList()
                  .where(
                    (province) =>
                        ('${province.translatedValue} ${province.textId}').toLowerCase().contains(
                          search.trim().toLowerCase(),
                        ) ==
                        true,
                  )
                  .toList();
          _updateScrollableSpace(suggestionList?.length ?? 1);

          return suggestionList;
        },
        emptyBuilder: (_) {
          return ListTile(title: Text(AppLocale.current.emptyLocationMessage));
        },
        onClose: (controller) {
          _selectedStriveDistrict = null;
          _striveDistrictController.clear();
          setState(() {
            _selectedStriveProvince = null;
            controller.clear();
          });
        },
        onChange: () {
          setState(() {
            _selectedStriveDistrict = null;
            _selectedStriveProvince = null;
            _striveDistrictController.clear();
          });
        },
        onSelected: (province) {
          _updateScrollableSpaceOnItemSelection();

          setState(() {
            _selectedStriveProvince = province;
            _striveProvinceController.text = province.translatedValue ?? '';
            _selectedStriveDistrict = null;
            // This line has added to refresh the district suggestion callback
            _striveDistrictController.text = province.translatedValue ?? '';
            _striveDistrictController.clear();
          });
        },
      ),
    );
  }

  _striveDistrictWidget() {
    return AutocompleteTextField<IndonesianCity>(
      scrollableBottomPadding: _scrollablePadding,
      textEditingController: _striveDistrictController,
      label: AppLocale.current.striveIndonesiaDistrict,
      hint: AppLocale.current.striveIndonesiaDistrictHint,
      showClose: true,
      showPrefixSearchIcon: true,
      itemBuilder: (context, district) {
        return ListTile(title: Text(district.translatedValue ?? ''));
      },
      suggestionsCallback: (String search) {
        var suggestionList =
            _contentProvider.indonesianCitiesOptions
                ?.toList()
                .where(
                  (district) =>
                      ('${district.translatedValue} ${district.textId}').toLowerCase().contains(
                            search.trim().toLowerCase(),
                          ) ==
                          true &&
                      district.parentTextId == _selectedStriveProvince?.textId,
                )
                .toList();
        _updateScrollableSpace(suggestionList?.length ?? 1);

        return suggestionList;
      },
      emptyBuilder: (_) {
        return ListTile(title: Text(AppLocale.current.emptyLocationMessage));
      },
      onClose: (controller) {
        controller.clear();
        _selectedStriveDistrict = null;
      },
      onChange: () {
        setState(() => _selectedStriveDistrict = null);
      },
      onSelected: (district) {
        _updateScrollableSpaceOnItemSelection();

        setState(() {
          _selectedStriveDistrict = district;
          _striveDistrictController.text = district.translatedValue ?? '';
        });
      },
    );
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(context: context, exception: result.operationException);
    WidgetsBinding.instance.addPostFrameCallback((_) => setState(() => _processing = false));
  }

  _updateScrollableSpace(int suggestionListLength) {
    suggestionListLength = (suggestionListLength == 0) ? 1 : suggestionListLength;

    setState(() {
      extraScrollingPadding.value =
          (suggestionListLength) < 4
              ? (suggestionListLength) * context.theme.d.boxSizeMedium
              : context.theme.d.dropdownHeight + context.theme.d.paddingMedium;
      _scrollablePadding =
          context.theme.d.desktopOnboardingExtraScrollingSpace + extraScrollingPadding.value;
    });
  }

  _updateScrollableSpaceOnItemSelection() {
    _scrollablePadding =
        context.theme.d.desktopOnboardingExtraScrollingSpace + context.theme.d.boxSizeSmall;
  }
}
