import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/base/operation_result.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/debug_logger.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../widgets.dart';

class SignUpCreateAccountScreen extends StatefulWidget {
  const SignUpCreateAccountScreen({super.key});

  @override
  State<SignUpCreateAccountScreen> createState() => _SignUpCreateAccountScreenState();
}

class _SignUpCreateAccountScreenState extends State<SignUpCreateAccountScreen> {
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  late final TextEditingController _firstNameController;
  late final TextEditingController _lastNameController;
  late final TextEditingController _fathersNameController;
  late final TextEditingController _emailController;
  late final TextEditingController _passwordController;
  late final TextEditingController _confirmPasswordController;
  late bool _optIntoNewsletterController;
  bool? _isEmailAvailable = true;
  final double _progress = 0.03;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _processing = false;
  late FocusNode focusNode;
  bool isIQLAASignUp = false;

  @override
  void initState() {
    super.initState();

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    isIQLAASignUp = _onboardingModel.isIQLAAGroup;

    _firstNameController = TextEditingController(text: _userProvider.myUser?.firstName);
    _lastNameController = TextEditingController(text: _userProvider.myUser?.lastName);
    _emailController = TextEditingController(text: _userProvider.myUser?.email);
    _passwordController = TextEditingController(text: '');
    _confirmPasswordController = TextEditingController(text: '');
    _optIntoNewsletterController = _userProvider.myUser?.optIntoNewsletter ?? false;

    if (isIQLAASignUp) {
      final iqlaaMembership = ProfileUtility.fromFindId(_userProvider.myUser).iqlaaGroupMembership;
      _fathersNameController = TextEditingController(text: iqlaaMembership?.fatherName);
    }

    _localData.onboardingStage = OnboardingStage.createAccount;
    focusNode = FocusNode();
  }

  @override
  void dispose() {
    try {
      _firstNameController.dispose();
      _lastNameController.dispose();
      _emailController.dispose();
      _passwordController.dispose();
      _confirmPasswordController.dispose();
    } catch (_) {}
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      title: AppLocale.current.signupCredentialsTitle,
      subtitle: AppLocale.current.signupCredentialsSubTitle,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      onboardingStage: OnboardingStage.createAccount,
      onboardingModel: _onboardingModel,
      focusNode: focusNode,
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: TextFormFieldWidget(
                    label: AppLocale.current.signupCredentialsNameFirstInputLabel,
                    hint: AppLocale.current.signupCredentialsNameFirstInputHint,
                    textController: _firstNameController,
                    contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
                    textCapitalization: TextCapitalization.sentences,
                    onChanged: (_) => setState(() {}),
                  ),
                ),
                SizedBox(width: context.theme.d.paddingMedium),
                Expanded(child: isIQLAASignUp ? _fathersNameWidget() : _lastNameWidget()),
              ],
            ),
            if (isIQLAASignUp) ...[
              SizedBox(height: context.theme.d.paddingMedium),
              _lastNameWidget(),
            ],
            if (_localData.userId.isEmpty) ...[
              SizedBox(height: context.theme.d.paddingMedium),
              TextFormFieldWidget(
                key: const Key('username'),
                label: AppLocale.current.signupCredentialsEmailInputLabel,
                autofillHints: const [AutofillHints.username],
                hint: AppLocale.current.signupCredentialsEmailInputHint,
                textController: _emailController,
                keyboardType: TextInputType.emailAddress,
                contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
                onChanged:
                    (_) => setState(() {
                      _formKey.currentState!.validate();
                      _isEmailAvailable = true;
                    }),
                validator:
                    (value) => context.theme.validator.validateEmail(
                      value,
                      isAvailable: _isEmailAvailable,
                    ),
              ),
              SizedBox(height: context.theme.d.paddingMedium),
              TextFormFieldWidget(
                key: const Key('password'),
                isPassword: true,
                validator: context.theme.validator.validatePassword,
                label: AppLocale.current.signupCredentialsPasswordInputLabel,
                autofillHints: const [AutofillHints.password],
                contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
                textController: _passwordController,
                onChanged:
                    (_) => setState(() {
                      _formKey.currentState!.validate();
                    }),
              ),
              SizedBox(height: context.theme.d.paddingMedium),
              TextFormFieldWidget(
                isPassword: true,
                scrollableBottomPadding: context.theme.d.boxSizeXLarge,
                label: AppLocale.current.signupCredentialsPasswordConfirmInputLabel,
                textController: _confirmPasswordController,
                contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
                onChanged:
                    (_) => setState(() {
                      _formKey.currentState!.validate();
                    }),
                validator:
                    (value) => context.theme.validator.validatePassword(
                      value,
                      matchValue: _passwordController.text,
                    ),
              ),
            ],
            SizedBox(height: context.theme.d.paddingMedium),
            CheckboxListTile(
              controlAffinity: ListTileControlAffinity.leading,
              value: _optIntoNewsletterController,
              onChanged: (value) => setState(() => _optIntoNewsletterController = value ?? false),
              title: Text(
                AppLocale.current.signupCredentialsNewsCheckboxLabel,
                style: context.theme.textTheme.bodyMedium?.copyWith(
                  color: context.theme.colorScheme.secondary,
                ),
              ),
            ),
          ],
        ),
      ),
      canGoNext: canGoNext(),
      onGoNext: () => _goNextAction(),
    );
  }

  _lastNameWidget() {
    return TextFormFieldWidget(
      label:
          isIQLAASignUp
              ? AppLocale.current.familyName
              : AppLocale.current.signupCredentialsNameLastInputLabel,
      hint: AppLocale.current.signupCredentialsNameLastInputHint,
      contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
      textCapitalization: TextCapitalization.sentences,
      textController: _lastNameController,
      onChanged: (_) => setState(() {}),
    );
  }

  _fathersNameWidget() {
    return TextFormFieldWidget(
      label: AppLocale.current.fathersName,
      hint: AppLocale.current.signupCredentialsNameLastInputHint,
      contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
      textCapitalization: TextCapitalization.sentences,
      textController: _fathersNameController,
      onChanged: (_) => setState(() {}),
    );
  }

  _goNextAction() async {
    setState(() {
      _processing = true;
    });

    bool isSuccess = _localData.userId.isEmpty ? await _signUpUser() : await _updateName();

    if (!isSuccess) return;

    if (!_onboardingModel.isRegularUser) {
      bool hasSuccess = await _addGroupData();
      _onboardingModel.userCms = await _userProvider.getUserCms();
      if (!hasSuccess) return;
    }

    OnboardingStage nextStage = _onboardingModel.firstStage();

    final findUserByIdResult = await _userProvider.findUserById(userId: _localData.userId);

    if (findUserByIdResult.gqlQueryResult.hasException) {
      _handleError(findUserByIdResult.gqlQueryResult.exception);
      return;
    }

    if (_onboardingModel.isStriveIndonesiaGroup && mounted) {
      final contentProvider = Provider.of<ContentProvider>(context, listen: false);
      final selectedLanguage =
          Provider.of<LocaleModel>(context, listen: false).getCurrentLanguageCode();

      await contentProvider.findIndonesianCities(Enum$UiLanguage.values.byName(selectedLanguage));
      await contentProvider.findIndonesianProvinces(
        Enum$UiLanguage.values.byName(selectedLanguage),
      );
    }

    setState(() {
      _processing = false;
    });

    if (mounted) {
      _localData.onboardingStage = nextStage;
      context.pushReplacement(_onboardingModel.getRouteFromStage(nextStage).path);
    }
  }

  bool canGoNext() {
    if (_localData.userId.isEmpty) {
      return _firstNameController.text.isNotEmpty &&
          _lastNameController.text.isNotEmpty &&
          _emailController.text.isNotEmpty &&
          _passwordController.text.isNotEmpty &&
          _confirmPasswordController.text.isNotEmpty &&
          _isEmailAvailable != false &&
          _formKey.currentState!.validate();
    }
    return _firstNameController.text.isNotEmpty && _lastNameController.text.isNotEmpty;
  }

  _handleError(OperationException? exception) {
    AppErrorHandler(context: context, exception: exception);
    WidgetsBinding.instance.addPostFrameCallback((_) => setState(() => _processing = false));
  }

  Future<bool> _signUpUser() async {
    final available = await _userProvider.isUserIdentAvailable(
      text: _emailController.text.trim(),
      identType: Enum$UserIdentType.email,
    );
    setState(() {
      _isEmailAvailable = available;
    });

    if (_formKey.currentState != null && !_formKey.currentState!.validate()) {
      setState(() {
        _processing = false;
      });
      return false;
    }

    final signUpResponse = await _userProvider.signUpUser(
      firstName: _firstNameController.text.trim(),
      lastName: _lastNameController.text.trim(),
      email: _emailController.text.trim(),
      password: _passwordController.text,
      optIntoNewsletter: _optIntoNewsletterController,
    );
    if (signUpResponse.gqlQueryResult.hasException) {
      DebugLogger.error('signUpUser returned an error: $signUpResponse');
      _handleError(signUpResponse.gqlQueryResult.exception);
      return false;
    }

    if (_localData.userId == '' || _localData.authToken == '') {
      DebugLogger.error('Onboarding: signed up user not saved correctly');
      setState(() {
        _processing = false;
      });
      return false;
    }

    return true;
  }

  Future<bool> _updateName() async {
    final result = await _userProvider.updateUser(
      input: Input$UserInput(
        id: _localData.userId,
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        optIntoNewsletter: _optIntoNewsletterController,
      ),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );

    if (result.hasError) {
      _handleError(result.operationException);
      return false;
    }
    return true;
  }

  Future<bool> _addGroupData() async {
    final response = await _userProvider.addUserToGroup(
      groupIdentName: _onboardingModel.groupIdentName,
      groupIdent: _onboardingModel.groupIdent,
      userId: _localData.userId,
    );
    if (response.hasError) {
      DebugLogger.error('Add user to IQLAA failed with an error: ${response.operationException}');
      _handleError(response.operationException);
      return false;
    }

    if (!_onboardingModel.isIQLAAGroup) return true;

    OperationResult<void> updateGroupMembershipResponse = await _userProvider
        .updateIqlaaGroupMembership(
          input: Input$IqlaaGroupMembershipInput(
            userId: _localData.userId,
            fatherName: _fathersNameController.text.trim(),
          ),
        );

    if (updateGroupMembershipResponse.gqlQueryResult.hasException) {
      DebugLogger.error('update father name failed: $updateGroupMembershipResponse');
      _handleError(updateGroupMembershipResponse.gqlQueryResult.exception);
      return false;
    }

    return true;
  }
}
