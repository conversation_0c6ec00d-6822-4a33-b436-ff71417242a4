import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../widgets.dart';

class SignupMentorInfoScreen extends StatefulWidget {
  const SignupMentorInfoScreen({super.key});

  @override
  State<SignupMentorInfoScreen> createState() => _SignupMentorInfoScreenState();
}

class _SignupMentorInfoScreenState extends State<SignupMentorInfoScreen> {
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  late String? _oldJobTitle;
  late String? _oldCompanyName;
  late final TextEditingController _jobTitleController;
  late final TextEditingController _companyNameController;
  double _progress = 0;
  final _formKey = GlobalKey<FormState>();
  bool _processing = false;
  late FocusNode focusNode;

  @override
  void initState() {
    super.initState();

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);

    List<BusinessExperience>? businessExperiences = _userProvider.myUser?.businessExperiences;
    BusinessExperience? firstBusinessExperience = businessExperiences?.firstOrNull;
    _oldCompanyName = firstBusinessExperience?.businessName;
    _oldJobTitle = firstBusinessExperience?.jobTitle;
    _jobTitleController = TextEditingController(text: _oldJobTitle);
    _companyNameController = TextEditingController(text: _oldCompanyName);

    _localData.onboardingStage = OnboardingStage.mentorInfo;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.mentorInfo,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );
    focusNode = FocusNode();
  }

  @override
  void dispose() {
    try {
      _jobTitleController.dispose();
      _companyNameController.dispose();
    } catch (_) {}
    super.dispose();
  }

  bool _canGoNext() {
    if (_processing) {
      return false;
    }
    if (_formKey.currentState != null && _formKey.currentState?.validate() != true) {
      return false;
    }
    return (_jobTitleController.text.trim().isNotEmpty || _oldJobTitle?.isNotEmpty == true) &&
        (_companyNameController.text.trim().isNotEmpty || _oldCompanyName?.isNotEmpty == true);
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      title: AppLocale.current.signupRoleTitle,
      subtitle: AppLocale.current.signupRoleSubtitle,
      isMentee: false,
      onboardingStage: OnboardingStage.mentorInfo,
      onboardingModel: _onboardingModel,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      focusNode: focusNode,
      body: Column(
        children: [
          Form(
            key: _formKey,
            child: TextFormFieldWidget(
              label: AppLocale.current.signupRoleJobTitleInputLabel,
              hint: AppLocale.current.signupRoleJobTitleInputHint,
              textController: _jobTitleController,
              textCapitalization: TextCapitalization.sentences,
              onChanged: (_) => setState(() {}),
            ),
          ),
          SizedBox(height: context.theme.d.paddingMedium),
          Form(
            child: TextFormFieldWidget(
              label: AppLocale.current.signupRoleCompanyInputLabel,
              hint: AppLocale.current.signupRoleCompanyInputHint,
              textController: _companyNameController,
              textCapitalization: TextCapitalization.sentences,
              onChanged: (_) => setState(() {}),
            ),
          ),
        ],
      ),
      canGoNext: _canGoNext(),
      onGoNext: () => _goNextAction(),
    );
  }

  _goNextAction() async {
    if (_formKey.currentState != null && !_formKey.currentState!.validate()) {
      return;
    }

    OnboardingStage? nextStage = _onboardingModel.getNextStage(
      curStage: OnboardingStage.mentorInfo,
      isMentee: false,
    );

    if (_isDirty()) {
      setState(() => _processing = true);

      final updateGroupMembershipResponse = await _userProvider.updateMentorsGroupMembership(
        input: Input$MentorsGroupMembershipInput(
          // Note: The server will find the membership using userId and groupIdent:
          userId: _localData.userId,
          groupIdent: GroupIdent.mentors.name,
        ),
      );

      if (updateGroupMembershipResponse.gqlQueryResult.hasException) {
        // todo: is that the correct way to handle a failure?
        WidgetsBinding.instance.addPostFrameCallback((_) => setState(() => _processing = false));
        return;
      }

      final result = await _userProvider.updateUser(
        input: Input$UserInput(
          id: _localData.userId,
          onboardingStage: nextStage?.name,
          businessExperiences:
              _jobTitleController.text.isNotEmpty
                  ? [
                    Input$BusinessExperienceInput(
                      jobTitle: _jobTitleController.text.trim(),
                      businessName: _companyNameController.text.trim(),
                    ),
                  ]
                  : null,
        ),
        options: UpdateObjectOptions<User>(
          loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
        ),
      );

      setState(() => _processing = false);

      if (result.hasError) {
        _handleError(result);
        return;
      }
    }

    // Navigate to next screen:
    if (mounted && nextStage != null) {
      _localData.onboardingStage = nextStage;
      context.push(_onboardingModel.getRouteFromStage(nextStage).path);
    }
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(context: context, exception: result.operationException);
  }

  bool _isDirty() {
    BusinessExperience? experience = _userProvider.myUser?.businessExperiences?.firstOrNull;

    return (experience?.jobTitle != _jobTitleController.text.trim()) ||
        (_companyNameController.text.trim() != experience?.businessName);
  }
}
