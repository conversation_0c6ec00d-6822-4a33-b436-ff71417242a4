import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../widgets.dart';

class SignUpProfileRoleScreen extends StatefulWidget {
  const SignUpProfileRoleScreen({super.key});

  @override
  State<SignUpProfileRoleScreen> createState() => _SignUpProfileRoleScreenState();
}

class _SignUpProfileRoleScreenState extends State<SignUpProfileRoleScreen> {
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  late int _selectedIndex;
  double _progress = 0;
  bool _processing = false;
  late FocusNode focusNode;

  @override
  void initState() {
    super.initState();

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    User? user = _userProvider.myUser;
    _selectedIndex = user?.offersHelp == true ? 1 : 0;
    _localData.onboardingStage = OnboardingStage.profileRole;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.profileRole,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );
    focusNode = FocusNode();
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      title: AppLocale.current.signupProfileRoleTitle,
      isMentee: _selectedIndex == 0,
      onboardingStage: OnboardingStage.profileRole,
      onboardingModel: _onboardingModel,
      extraScrollingSpace: context.theme.d.desktopOnboardingExtraScrollingSpace,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      focusNode: focusNode,
      body: RadioButtonCards(
        cards: [
          if (_onboardingModel.userCms?.groupCms?.onboarding?.allowProfileRoleOnSignUp == null ||
              _onboardingModel.userCms?.groupCms?.onboarding?.allowProfileRoleOnSignUp !=
                  Enum$UserProfileRole.mentor)
            RadioCard(
              title:
                  _onboardingModel.isStriveIndonesiaGroup
                      ? AppLocale.current.striveUserEntrepreneurTitle
                      : AppLocale.current.signupProfileRoleEntrepreneur,
              subtitle: AppLocale.current.signupProfileRoleEntrepreneurDescription,
              trailingImage: const Image(image: AssetImage(Assets.entrepreneurIcon)),
            ),
          if (_onboardingModel.userCms?.groupCms?.onboarding?.allowProfileRoleOnSignUp == null ||
              _onboardingModel.userCms?.groupCms?.onboarding?.allowProfileRoleOnSignUp !=
                  Enum$UserProfileRole.mentee)
            RadioCard(
              title: AppLocale.current.signupProfileRoleMentor,
              subtitle: AppLocale.current.signupProfileRoleMentorDescription,
              trailingImage: const Image(image: AssetImage(Assets.mentorIcon)),
            ),
        ],
        initialSelection: _selectedIndex,
        onSelectedCardChanged: (value) => setState(() => _selectedIndex = value),
      ),
      canGoNext: !_processing && _selectedIndex > -1,
      onGoNext: () async {
        final seeksHelp = _selectedIndex == 0;
        final offersHelp = _selectedIndex == 1;

        OnboardingStage? nextStage = _onboardingModel.getNextStage(
          curStage: OnboardingStage.profileRole,
          isMentee: seeksHelp,
        );

        bool isDirty = seeksHelp != _userProvider.myUser?.seeksHelp;

        if (isDirty) {
          setState(() => _processing = true);
          final result = await _userProvider.updateUser(
            input: Input$UserInput(
              id: _localData.userId,
              seeksHelp: seeksHelp,
              offersHelp: offersHelp,
              onboardingStage: nextStage?.name,
            ),
            options: UpdateObjectOptions<User>(
              loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
            ),
          );

          // Fetch banks for Entrepreneurs Only
          if (_onboardingModel.isMastercardGroup && seeksHelp && context.mounted) {
            final contentProvider = Provider.of<ContentProvider>(context, listen: false);
            final selectedLanguage =
                Provider.of<LocaleModel>(context, listen: false).getCurrentLanguageCode();
            await contentProvider.findMastercardBanks(
              Enum$UiLanguage.values.byName(selectedLanguage),
            );
          }

          setState(() => _processing = false);

          if (result.hasError) {
            _handleError(result);
            return;
          }
        }

        if (context.mounted && nextStage != null) {
          _localData.onboardingStage = nextStage;
          context.push(_onboardingModel.getRouteFromStage(nextStage).path);
        }
      },
    );
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(context: context, exception: result.operationException);
  }
}
