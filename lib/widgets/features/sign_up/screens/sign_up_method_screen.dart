import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/firebase/analytic_service.dart';
import 'package:mm_flutter_app/utilities/errors/error_handler.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class SignUpMethodScreen extends StatefulWidget {
  const SignUpMethodScreen({super.key});

  @override
  State<SignUpMethodScreen> createState() => _SignUpMethodScreenState();
}

class _SignUpMethodScreenState extends State<SignUpMethodScreen> {
  late final UserProvider _userProvider;
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final LocaleModel _localeModel;
  late bool userDeniedTrackingConsent = false;
  late FocusNode focusNode;

  @override
  void initState() {
    super.initState();

    // Check if the user has permitted tracking
    if (!kIsWeb && Platform.isIOS) {
      userDeniedTrackingConsent = true;
      Permission.appTrackingTransparency.status.then((value) {
        setState(() {
          userDeniedTrackingConsent = value.isDenied || value.isPermanentlyDenied;
          setState(() {});
        });
      });
    }

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _localeModel = Provider.of<LocaleModel>(context, listen: false);
    _localData.onboardingStage = OnboardingStage.authType;
    _localData.clear(removeTrackId: false);
    focusNode = FocusNode();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if ((_onboardingModel.belongsToEBRDGroup)) {
        final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
        themeProvider.setEBRDColorScheme();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      title: AppLocale.current.signupMethodTitle,
      progress: 0,
      isMentee: true,
      canGoNext: true,
      onboardingStage: OnboardingStage.authType,
      onboardingModel: _onboardingModel,
      focusNode: focusNode,
      showLanguageDropdown: true,
      body: _content(),
      showNavigationButtons: false,
    );
  }

  Widget _content() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.all(context.theme.d.paddingLarge),
          child: MarkdownBody(
            data: AppLocale.current.signupAgreement(
              getLocalizedUrl(urlType: LegalDocumentType.termsOfUse, localeModel: _localeModel),
            ),
            styleSheet: MarkdownStyleSheet.fromTheme(context.theme).copyWith(
              textAlign: WrapAlignment.center,
              p: context.textTheme.labelLarge?.copyWith(
                color: context.colorScheme.scrim,
                fontWeight: FontWeight.w500,
              ),
              a: context.textTheme.labelLarge?.copyWith(
                color: context.colorScheme.tertiary,
                fontWeight: FontWeight.w500,
              ),
            ),
            onTapLink: (_, url, __) => launchUrl(Uri.parse(url!)),
          ),
        ),
        SignInWithButtons(
          showLabel: true,
          showOwnIdentityProvider: true,
          showDisabledTrackingHelpText: userDeniedTrackingConsent,
          onSelectIdentityProvider: (provider) async {
            ScaffoldMessenger.of(context).clearSnackBars();
            final isSuccess = await _userProvider.startSignIn(provider, context);
            AnalyticService.userSignUp(provider.name);
            if (!isSuccess) {
              _localData.clear(removeTrackId: false);
              if (mounted) {
                // TODO - need to pass social signuo exception/error here
                AppErrorHandler(
                  context: context,
                  exception: null,
                  errorCode: Enum$ErrorCode.failedToCreateAccount,
                );
              }
            }
          },
        ),
        SizedBox(height: context.theme.d.paddingXLarge),
        Text(
          AppLocale.current.alreadyMember,
          style: context.theme.textTheme.titleMedium?.copyWith(
            color: context.theme.colorScheme.onSurface,
          ),
        ),
        CommonButton.textButton(
          context: context,
          title: AppLocale.current.login,
          onPressed: () {
            ScaffoldMessenger.of(context).clearSnackBars();
            context.pushReplacement(AppRoutes.signin.path);
          },
          textStyle: context.theme.textTheme.titleMedium?.copyWith(
            color: context.theme.colorScheme.primary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
