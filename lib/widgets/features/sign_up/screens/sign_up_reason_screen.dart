import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/base/operation_result.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../widgets.dart';

class SignUpReasonScreen extends StatefulWidget {
  const SignUpReasonScreen({super.key});

  @override
  State<SignUpReasonScreen> createState() => _SignUpReasonScreenState();
}

class _SignUpReasonScreenState extends State<SignUpReasonScreen> {
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  late final bool _isMentee;
  late final TextEditingController _reasonController;
  late final String? _oldReason;
  double _progress = 0;
  final _formKey = GlobalKey<FormState>();
  bool _processing = false;

  @override
  void initState() {
    super.initState();

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _isMentee = _userProvider.myUser?.offersHelp != true;

    if (_isMentee) {
      final MenteesGroupMembership? menteesGroupMembership =
          _userProvider.myUser?.groupMemberships
                  .where((element) => element.groupIdent == GroupIdent.mentees.name)
                  .firstOrNull
              as MenteesGroupMembership?;
      _oldReason = menteesGroupMembership?.reasonsForStartingBusiness;
    } else {
      final MentorsGroupMembership? mentorsGroupMembership =
          _userProvider.myUser?.groupMemberships
                  .where((element) => element.groupIdent == GroupIdent.mentors.name)
                  .firstOrNull
              as MentorsGroupMembership?;
      _oldReason = mentorsGroupMembership?.reasonsForMentoring;
    }
    _reasonController = TextEditingController(text: _oldReason);

    _localData.onboardingStage = OnboardingStage.reasonToJoin;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.reasonToJoin,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );
  }

  @override
  void dispose() {
    try {
      _reasonController.dispose();
      super.dispose();
    } catch (_) {}
  }

  bool _canGoNext() {
    if (_processing) {
      return false;
    }
    if (_formKey.currentState != null && _formKey.currentState?.validate() != true) {
      return false;
    }
    return _reasonController.text.trim().isNotEmpty || _oldReason?.isNotEmpty == true;
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      title:
          _isMentee
              ? _onboardingModel.isIQLAAGroup
                  ? AppLocale.current.iqlaaSignupReasonEntrepreneurTitle
                  : AppLocale.current.signupReasonEntrepreneurTitle
              : AppLocale.current.signupReasonMentorTitle,
      subtitle:
          _isMentee
              ? AppLocale.current.signupReasonEntrepreneurSubtitle
              : AppLocale.current.signupReasonMentorSubtitle,
      isMentee: _isMentee,
      onboardingStage: OnboardingStage.reasonToJoin,
      onboardingModel: _onboardingModel,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      body: Form(
        key: _formKey,
        child: TextFormFieldWidget(
          hint:
              _isMentee
                  ? AppLocale.current.signupReasonEntrepreneurInputHint
                  : AppLocale.current.signupReasonMentorInputHint,
          maxLength: 1000,
          maxLines: 6,
          textController: _reasonController,
          textCapitalization: TextCapitalization.sentences,
          onChanged: (_) => setState(() {}),
        ),
      ),
      canGoNext: _canGoNext(),
      onGoNext: () => _goNextAction(),
    );
  }

  _goNextAction() async {
    if (_formKey.currentState != null && !_formKey.currentState!.validate()) {
      return;
    }

    OnboardingStage? nextStage = _onboardingModel.getNextStage(
      curStage: OnboardingStage.reasonToJoin,
      isMentee: _isMentee,
    );

    if (_isDirty()) {
      setState(() => _processing = true);
      OperationResult<void> updateGroupMembershipResponse;
      if (_isMentee) {
        updateGroupMembershipResponse = await _userProvider.updateMenteesGroupMembership(
          input: Input$MenteesGroupMembershipInput(
            userId: _localData.userId,
            reasonsForStartingBusiness: _reasonController.text.trim(),
          ),
        );
      } else {
        updateGroupMembershipResponse = await _userProvider.updateMentorsGroupMembership(
          input: Input$MentorsGroupMembershipInput(
            userId: _localData.userId,
            reasonsForMentoring: _reasonController.text.trim(),
          ),
        );
      }

      if (updateGroupMembershipResponse.gqlQueryResult.hasException) {
        // todo: is that the correct way to handle a failure?
        // todo: show error?
        WidgetsBinding.instance.addPostFrameCallback((_) => setState(() => _processing = false));
        return;
      }

      final result = await _userProvider.updateUser(
        input: Input$UserInput(id: _localData.userId, onboardingStage: nextStage?.name),
        options: UpdateObjectOptions<User>(
          loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
        ),
      );

      setState(() => _processing = false);
      if (result.hasError) {
        _handleError(result);
        return;
      }
    }

    if (mounted && nextStage != null) {
      _localData.onboardingStage = nextStage;
      context.push(_onboardingModel.getRouteFromStage(nextStage).path);
    }
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(context: context, exception: result.operationException);
  }

  bool _isDirty() {
    String? oldReason;
    if (_isMentee) {
      final MenteesGroupMembership? menteesGroupMembership =
          _userProvider.myUser?.groupMemberships
                  .where((element) => element.groupIdent == GroupIdent.mentees.name)
                  .firstOrNull
              as MenteesGroupMembership?;
      oldReason = menteesGroupMembership?.reasonsForStartingBusiness;
    } else {
      final MentorsGroupMembership? mentorsGroupMembership =
          _userProvider.myUser?.groupMemberships
                  .where((element) => element.groupIdent == GroupIdent.mentors.name)
                  .firstOrNull
              as MentorsGroupMembership?;
      oldReason = mentorsGroupMembership?.reasonsForMentoring;
    }

    return _reasonController.text.trim() != oldReason;
  }
}
