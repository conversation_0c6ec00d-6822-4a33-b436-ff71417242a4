import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';
import 'package:textfield_tags/textfield_tags.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../widgets.dart';

class SignupLanguageScreen extends StatefulWidget {
  const SignupLanguageScreen({super.key});

  @override
  State<SignupLanguageScreen> createState() => _SignupLanguageScreenState();
}

class _SignupLanguageScreenState extends State<SignupLanguageScreen> {
  final StringTagController _preferredLanguagesController = StringTagController();
  late final ContentProvider _contentProvider;
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  late final String? _oldPreferredLanguageTextId;
  final Set<String> _initialSelection = {};
  bool _isPreferredLanguageSelected = false;
  double _progress = 0;
  double extraScrollingSpace = 0;
  final _formKey = GlobalKey<FormState>();
  bool _processing = false;
  late FocusNode focusNode;

  @override
  void initState() {
    super.initState();

    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    focusNode = FocusNode();

    _oldPreferredLanguageTextId = _userProvider.myUser?.preferredLanguageTextId;
    if (_oldPreferredLanguageTextId?.isNotEmpty ?? false) {
      _initialSelection.add(
        _contentProvider.languageOptions
                ?.firstWhere((e) => e.textId == _userProvider.myUser?.preferredLanguageTextId)
                .translatedValue ??
            '',
      );
      _isPreferredLanguageSelected = true;
    }
    // todo: The method TextfieldTagsController.init does not longer exist in textfield_tags >3.0

    _preferredLanguagesController.addListener(() {
      setState(() {
        _isPreferredLanguageSelected = _preferredLanguagesController.getTags?.isNotEmpty == true;
      });
    });

    _localData.onboardingStage = OnboardingStage.language;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.language,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );
  }

  @override
  void dispose() {
    try {
      _preferredLanguagesController.dispose();
    } catch (_) {}
    super.dispose();
  }

  bool _canGoNext() {
    if (_processing) {
      return false;
    }
    if (_formKey.currentState != null && _formKey.currentState?.validate() != true) {
      return false;
    }
    return _isPreferredLanguageSelected;
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      title: AppLocale.current.signupLanguageTitle,
      subtitle: AppLocale.current.signupLanguageSubtitle,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      onboardingStage: OnboardingStage.language,
      onboardingModel: _onboardingModel,
      extraScrollingSpace:
          context.theme.d.desktopOnboardingExtraScrollingSpace + extraScrollingSpace,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      focusNode: focusNode,
      body: Form(
        key: _formKey,
        child: MultiSelectDropdown(
          singleSelect: true,
          editable: true,
          label: AppLocale.current.signupLanguageInputLabel,
          hint: AppLocale.current.signupLanguageInputHint,
          tagsController: _preferredLanguagesController,
          selectedOptions: _initialSelection,
          notFoundMessage: AppLocale.current.multiSelectDropdownLanguageNotFoundMsg,
          options:
              _contentProvider.languageOptions?.map((e) => e.translatedValue).nonNulls.toList() ??
              [],
          onDropdownSizeChanged: (double height) {
            setState(() {
              extraScrollingSpace = height;
            });
          },
        ),
      ),
      canGoNext: _canGoNext(),
      onGoNext: () async {
        if (_formKey.currentState != null && !_formKey.currentState!.validate()) {
          return;
        }

        OnboardingStage? nextStage = _onboardingModel.getNextStage(
          curStage: OnboardingStage.language,
          isMentee: _userProvider.myUser?.seeksHelp != false,
        );

        String? selectedLanguageTextId =
            _preferredLanguagesController.getTags
                ?.map(
                  (t) =>
                      _contentProvider.languageOptions
                          ?.firstWhere((o) => o.translatedValue! == t)
                          .textId,
                )
                .toList()
                .firstOrNull;

        if (isDirty(selectedLanguageTextId)) {
          setState(() => _processing = true);

          final result = await _userProvider.updateUser(
            input: Input$UserInput(
              id: _localData.userId,
              preferredLanguageTextId: selectedLanguageTextId,
              // For all the communities where entrepreneur role is mandatory
              seeksHelp:
                  _onboardingModel.userCms?.groupCms?.onboarding?.allowProfileRoleOnSignUp == null
                      ? null
                      : _onboardingModel.userCms?.groupCms?.onboarding?.allowProfileRoleOnSignUp ==
                          Enum$UserProfileRole.mentee
                      ? true
                      : false,
              onboardingStage: nextStage?.name,
            ),
            options: UpdateObjectOptions<User>(
              loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
            ),
          );

          setState(() => _processing = false);

          if (result.hasError) {
            _handleError(result);
            return;
          }
        }

        // Navigate to next screen:
        if (context.mounted && nextStage != null) {
          _localData.onboardingStage = nextStage;
          context.push(_onboardingModel.getRouteFromStage(nextStage).path);
        }
      },
    );
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(context: context, exception: result.operationException);
    WidgetsBinding.instance.addPostFrameCallback((_) => setState(() => _processing = false));
  }

  bool isDirty(String? selectedLanguageTextId) {
    return (selectedLanguageTextId != _userProvider.myUser?.preferredLanguageTextId);
  }
}
