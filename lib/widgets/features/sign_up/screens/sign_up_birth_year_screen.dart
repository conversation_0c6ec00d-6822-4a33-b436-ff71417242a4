import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/base/operation_result.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../widgets.dart';

class SignUpBirthYearScreen extends StatefulWidget {
  final int minAge;
  const SignUpBirthYearScreen({super.key, this.minAge = 16});

  @override
  State<SignUpBirthYearScreen> createState() => _SignUpBirthYearScreenState();
}

class _SignUpBirthYearScreenState extends State<SignUpBirthYearScreen> {
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  late final TextEditingController _yearController;
  late final TextEditingController _monthController;
  late final TextEditingController _dateController;
  late final String? _oldBirthYear;
  double _progress = 0;
  final _formKey = GlobalKey<FormState>();
  bool _processing = false;
  late FocusNode focusNode;

  @override
  void initState() {
    super.initState();

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);

    _oldBirthYear = _userProvider.myUser?.birthYear?.toString();
    _yearController = TextEditingController(text: _oldBirthYear ?? '');

    if (_onboardingModel.isIQLAAGroup) {
      final iqlaaMembership = ProfileUtility.fromFindId(_userProvider.myUser).iqlaaGroupMembership;
      DateTime? birthdate = iqlaaMembership?.birthDate?.toLocal();
      String? date =
          birthdate?.day == null
              ? ''
              : (birthdate?.day ?? 0) < 10
              ? '0${birthdate?.day.toString()}'
              : birthdate?.day.toString();
      String? month =
          birthdate?.day == null
              ? ''
              : (birthdate?.month ?? 0) < 10
              ? '0${birthdate?.month.toString()}'
              : birthdate?.month.toString();
      _monthController = TextEditingController(text: month);
      _dateController = TextEditingController(text: date);
      _yearController.text = birthdate?.year.toString() ?? '';
    }

    _localData.onboardingStage = OnboardingStage.birthYear;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.birthYear,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );
    focusNode = FocusNode();
  }

  @override
  void dispose() {
    try {
      _yearController.dispose();
    } catch (_) {}
    super.dispose();
  }

  bool _canGoNext() {
    if (_processing) {
      return false;
    }
    if (_formKey.currentState != null && _formKey.currentState?.validate() != true) {
      return false;
    }
    return _yearController.text.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      title: AppLocale.current.signupBirthTitle,
      subtitle: AppLocale.current.signupBirthSubtitle,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      onboardingStage: OnboardingStage.birthYear,
      onboardingModel: _onboardingModel,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      focusNode: focusNode,
      body: Form(
        key: _formKey,
        child:
            _onboardingModel.isIQLAAGroup
                ? Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    _birthDateAndMonthWidget(),
                    SizedBox(height: context.theme.d.paddingMedium),
                    _birthYearWidget(),
                  ],
                )
                : _birthYearWidget(),
      ),
      canGoNext: _canGoNext(),
      onGoNext: () => _goNextAction(),
    );
  }

  _birthDateAndMonthWidget() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: TextFormFieldWidget(
            label: AppLocale.current.date,
            hint: AppLocale.current.dateHint,
            keyboardType: TextInputType.number,
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(2),
            ],
            textController: _dateController,
            onChanged: (_) => setState(() {}),
            validator: context.theme.validator.validateBirthDate,
          ),
        ),
        SizedBox(width: context.theme.d.paddingMedium),
        Expanded(
          child: TextFormFieldWidget(
            label: AppLocale.current.month,
            hint: AppLocale.current.monthHint,
            keyboardType: TextInputType.number,
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(2),
            ],
            textController: _monthController,
            onChanged: (_) => setState(() {}),
            validator: context.theme.validator.validateBirthMonth,
          ),
        ),
      ],
    );
  }

  _birthYearWidget() {
    return TextFormFieldWidget(
      label: AppLocale.current.signupBirthInputLabel,
      hint: AppLocale.current.signupBirthInputHint,
      keyboardType: TextInputType.number,
      inputFormatters: <TextInputFormatter>[
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(4),
      ],
      textController: _yearController,
      onChanged: (_) => setState(() {}),
      validator: context.theme.validator.validateBirthYear,
    );
  }

  _goNextAction() async {
    if (_formKey.currentState != null && !_formKey.currentState!.validate()) {
      return;
    }

    OnboardingStage? nextStage = _onboardingModel.getNextStage(
      curStage: OnboardingStage.birthYear,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );

    if (_isDirty()) {
      setState(() => _processing = true);

      _onboardingModel.isIQLAAGroup
          ? _iqlaaUpdateBirthdate(nextStage)
          : _regularUserUpdateYear(nextStage);

      setState(() => _processing = false);
    }

    // Navigate to next screen:
    if (mounted && nextStage != null) {
      _localData.onboardingStage = nextStage;
      context.push(_onboardingModel.getRouteFromStage(nextStage).path);
    }
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(context: context, exception: result.operationException);
  }

  bool _isDirty() {
    String? oldBirthYear = _userProvider.myUser?.birthYear?.toString();
    return oldBirthYear == null || (_yearController.text.trim() != oldBirthYear);
  }

  _regularUserUpdateYear(OnboardingStage? nextStage) async {
    final result = await _userProvider.updateUser(
      input: Input$UserInput(
        id: _localData.userId,
        birthYear: int.parse(_yearController.text),
        onboardingStage: nextStage?.name,
      ),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );
    if (result.hasError) {
      _handleError(result);
    }
  }

  _iqlaaUpdateBirthdate(OnboardingStage? nextStage) async {
    OperationResult<void> updateGroupMembershipResponse = await _userProvider
        .updateIqlaaGroupMembership(
          input: Input$IqlaaGroupMembershipInput(
            userId: _localData.userId,
            birthDate:
                DateTime(
                  int.parse(_yearController.text),
                  int.parse(_monthController.text),
                  int.parse(_dateController.text),
                ).toUtc(),
          ),
        );
    if (updateGroupMembershipResponse.gqlQueryResult.hasException) {
      _handleError(LoadObjectResult(object: updateGroupMembershipResponse.gqlQueryResult));
    }

    await _userProvider.updateUser(
      input: Input$UserInput(id: _localData.userId, onboardingStage: nextStage?.name),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );
  }
}
