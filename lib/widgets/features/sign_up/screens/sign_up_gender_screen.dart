import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../shared/rounded_select_option_widget.dart';
import '../components/sign_up_template.dart';

class SignUpGenderScreen extends StatefulWidget {
  const SignUpGenderScreen({super.key});

  @override
  State<SignUpGenderScreen> createState() => _SignUpGenderScreenState();
}

class _SignUpGenderScreenState extends State<SignUpGenderScreen> {
  late final ContentProvider _contentProvider;
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  List<Option> genderOptions = [];
  late final String? _oldGender;
  String? _genderTextId;
  double _progress = 0;
  bool _processing = false;
  late FocusNode focusNode;

  @override
  void initState() {
    super.initState();

    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);

    focusNode = FocusNode();
    _oldGender = _userProvider.myUser?.genderTextId;
    if (_oldGender?.isNotEmpty == true) {
      _genderTextId = _oldGender;
    }

    _localData.onboardingStage = OnboardingStage.gender;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.gender,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );

    genderOptions =
        _contentProvider.genderOptions
            ?.map(
              (gender) =>
                  Option(value: gender.textId, label: gender.translatedValue ?? gender.textId),
            )
            .toList() ??
        [];
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      focusNode: focusNode,
      title: AppLocale.current.signupGenderTitle,
      subtitle: AppLocale.current.signupGenderSubtitle,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      onboardingStage: OnboardingStage.gender,
      onboardingModel: _onboardingModel,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      body: RoundedSelectOptionWidget(
        options: genderOptions,
        value: _genderTextId,
        onChanged: (String genderTextId) {
          setState(() {
            _genderTextId = genderTextId;
          });
        },
      ),
      canGoNext:
          !_processing && (_genderTextId?.isNotEmpty == true || _oldGender?.isNotEmpty == true),
      onGoNext: () async {
        OnboardingStage? nextStage = _onboardingModel.getNextStage(
          curStage: OnboardingStage.gender,
          isMentee: _userProvider.myUser?.seeksHelp != false,
        );

        if (_isDirty()) {
          setState(() => _processing = true);

          final result = await _userProvider.updateUser(
            input: Input$UserInput(
              id: _localData.userId,
              genderTextId: _genderTextId,
              onboardingStage: nextStage?.name,
            ),
            options: UpdateObjectOptions<User>(
              loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
            ),
          );

          setState(() => _processing = false);

          if (result.hasError) {
            _handleError(result);
            return;
          }
        }

        // Navigate to next screen:
        if (context.mounted && nextStage != null) {
          _localData.onboardingStage = nextStage;
          context.push(_onboardingModel.getRouteFromStage(nextStage).path);
        }
      },
    );
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(context: context, exception: result.operationException);
  }

  bool _isDirty() {
    String? oldGender = _userProvider.myUser?.genderTextId;
    return (oldGender == null || (_genderTextId != oldGender));
  }
}
