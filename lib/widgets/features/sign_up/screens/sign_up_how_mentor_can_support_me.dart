import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/base/operation_result.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../widgets.dart';

class SignUpHowCanMentorSupportMeScreen extends StatefulWidget {
  const SignUpHowCanMentorSupportMeScreen({super.key});

  @override
  State<SignUpHowCanMentorSupportMeScreen> createState() =>
      _SignUpHowCanMentorSupportMeScreenState();
}

class _SignUpHowCanMentorSupportMeScreenState extends State<SignUpHowCanMentorSupportMeScreen> {
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  late final TextEditingController _textEditingController;
  late final String? _oldValue;
  double _progress = 0;
  final _formKey = GlobalKey<FormState>();
  bool _processing = false;

  @override
  void initState() {
    super.initState();

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);

    final MenteesGroupMembership? menteesGroupMembership = _userProvider.myUser?.groupMemberships
        .whereType<MenteesGroupMembership>()
        .firstWhereOrNull((element) => element.groupIdent == GroupIdent.mentees.name);

    _oldValue = menteesGroupMembership?.howCanMentorSupportMe;
    _textEditingController = TextEditingController(text: _oldValue);

    _localData.onboardingStage = OnboardingStage.howCanMentorSupportMe;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.howCanMentorSupportMe,
      isMentee: true,
    );
  }

  @override
  void dispose() {
    try {
      _textEditingController.dispose();
      super.dispose();
    } catch (_) {}
  }

  bool _canGoNext() {
    if (_processing) {
      return false;
    }
    if (_formKey.currentState != null && _formKey.currentState?.validate() != true) {
      return false;
    }
    return _textEditingController.text.trim().isNotEmpty || _oldValue?.isNotEmpty == true;
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      title: AppLocale.current.howCanMentorSupportMeTitle,
      subtitle: AppLocale.current.howCanMentorSupportMeSubtitle,
      isMentee: true,
      onboardingStage: OnboardingStage.howCanMentorSupportMe,
      onboardingModel: _onboardingModel,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      body: Form(
        key: _formKey,
        child: TextFormFieldWidget(
          hint: AppLocale.current.howCanMentorSupportMeInputHint,
          maxLength: 1000,
          maxLines: 6,
          textController: _textEditingController,
          textCapitalization: TextCapitalization.sentences,
          onChanged: (_) => setState(() {}),
        ),
      ),
      canGoNext: _canGoNext(),
      onGoNext: () => _goNextAction(),
    );
  }

  _goNextAction() async {
    if (_formKey.currentState != null && !_formKey.currentState!.validate()) {
      return;
    }

    OnboardingStage? nextStage = _onboardingModel.getNextStage(
      curStage: OnboardingStage.howCanMentorSupportMe,
      isMentee: true,
    );

    if (_isDirty()) {
      setState(() => _processing = true);
      OperationResult<void> updateGroupMembershipResponse;
      updateGroupMembershipResponse = await _userProvider.updateMenteesGroupMembership(
        input: Input$MenteesGroupMembershipInput(
          userId: _localData.userId,
          howCanMentorSupportMe: _textEditingController.text.trim(),
        ),
      );

      if (updateGroupMembershipResponse.gqlQueryResult.hasException) {
        // todo: is that the correct way to handle a failure?
        // todo: show error?
        WidgetsBinding.instance.addPostFrameCallback((_) => setState(() => _processing = false));
        return;
      }

      final result = await _userProvider.updateUser(
        input: Input$UserInput(id: _localData.userId, onboardingStage: nextStage?.name),
        options: UpdateObjectOptions<User>(
          loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
        ),
      );

      setState(() => _processing = false);
      if (result.hasError) {
        _handleError(result);
        return;
      }
    }

    if (mounted && nextStage != null) {
      _localData.onboardingStage = nextStage;
      context.push(_onboardingModel.getRouteFromStage(nextStage).path);
    }
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(context: context, exception: result.operationException);
  }

  bool _isDirty() {
    final MenteesGroupMembership? menteesGroupMembership = _userProvider.myUser?.groupMemberships
        .whereType<MenteesGroupMembership>()
        .firstWhereOrNull((element) => element.groupIdent == GroupIdent.mentees.name);
    final oldValue = menteesGroupMembership?.howCanMentorSupportMe;

    return _textEditingController.text.trim() != oldValue;
  }
}
