import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/main/app.dart' as app;
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/firebase/analytic_service.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../widgets.dart';

class SignUpAcceptTermsScreen extends StatefulWidget {
  const SignUpAcceptTermsScreen({super.key});

  @override
  State<SignUpAcceptTermsScreen> createState() => _SignUpAcceptTermsScreenState();
}

class _SignUpAcceptTermsScreenState extends State<SignUpAcceptTermsScreen> {
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  late final ScaffoldModel _scaffoldModel;
  late final LocaleModel _localeModel;
  late final InboxProvider _inboxProvider;
  late final bool _isMentee;
  bool _acceptedTerms = false;
  bool _processing = false;
  late final User? user;

  @override
  void initState() {
    super.initState();

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _inboxProvider = Provider.of<InboxProvider>(context, listen: false);
    _scaffoldModel = Provider.of<ScaffoldModel>(context, listen: false);
    _localeModel = Provider.of<LocaleModel>(context, listen: false);
    user = _userProvider.myUser;
    _isMentee = user?.offersHelp != true;
    _localData.onboardingStage = OnboardingStage.acceptTerms;
  }

  onGoBack() {
    if (app.navigatorKey.currentState!.canPop()) {
      context.pop();
      return;
    }

    OnboardingStage? stage = _localData.onboardingStage;
    if (stage == null) {
      return;
    }
    final prevPath = _onboardingModel.getPreviousRouteFromStage(stage, isMentee: _isMentee)?.path;
    if (prevPath == null) {
      return;
    }
    context.pushReplacement(prevPath);
  }

  onGoNext() async {
    final GoRouter router = GoRouter.of(context);

    if (!_acceptedTerms) {
      return;
    }
    setState(() => _processing = true);

    var selectedUiLanguageTextId =
        Enum$UiLanguage.values
            .where((element) => element.name == _localeModel.getCurrentLanguageCode())
            .firstOrNull;

    final result = await _userProvider.updateUser(
      input: Input$UserInput(
        id: _localData.userId,
        termsAndConditionsAcceptedAt: DateTime.now().toUtc(),
        onboardingStage: OnboardingStage.finished.name,
        fallbackUiLanguageTextId: selectedUiLanguageTextId,
        selectedUiLanguageTextId: selectedUiLanguageTextId,
      ),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );

    if (result.hasError) {
      _handleError(result);
      return;
    }

    await _inboxProvider.start();

    bool hasWelcomeMessage = false;
    // Check for welcome messages with polling
    if (_onboardingModel.groupIdentName != null ||
        (_onboardingModel.groupIdent != GroupIdent.mentors &&
            _onboardingModel.groupIdent != GroupIdent.mentees)) {
      hasWelcomeMessage = await _userProvider.pollForWelcomeMessage();
    }

    // Success. We're done with onboarding!
    setState(() => _processing = false);

    _localData.onboardingStage = OnboardingStage.finished;
    _scaffoldModel.setParams(index: Tabs.home.index);
    _localData.lastSelectedTab = Tabs.home.index;
    AnalyticService.onBoardingCompleted();

    AppUtility.checkForNonMigratedGroups(_userProvider.myUser);

    if (hasWelcomeMessage) {
      if (mounted && !AppUtility.displayDesktopUI(context)) {
        final channelsProvider = Provider.of<ChannelsProvider>(context, listen: false);
        await channelsProvider.loadMyChannels();
        final welcomeChannel = channelsProvider.unarchivedChannels.firstOrNull;
        if (welcomeChannel != null) {
          router.pushNamed(
            AppRoutes.root.name,
            queryParameters: {
              RouteParams.nextRouteName: AppRoutes.inboxChatsChannelId.name,
              RouteParams.channelId: welcomeChannel.id,
            },
          );
          return;
        }
      }

      router.pushNamed(
        AppRoutes.root.name,
        queryParameters: {RouteParams.nextRouteName: AppRoutes.inboxChats.name},
      );
      return;
    }

    final nextRouteName =
        _onboardingModel.isStriveIndonesiaGroup && _userProvider.myUser?.hasTrainings == true
            ? AppRoutes.trainings.name
            : AppRoutes.home.name;
    router.pushNamed(
      AppRoutes.root.name,
      queryParameters: {RouteParams.nextRouteName: nextRouteName},
    );
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(context: context, exception: result.operationException);
    WidgetsBinding.instance.addPostFrameCallback((_) => setState(() => _processing = false));
  }

  @override
  Widget build(BuildContext context) {
    final TextStyle? contentTextStyle = context.theme.textTheme.bodyLarge?.copyWith(
      color: context.theme.colorScheme.onSurface,
      fontWeight: FontWeight.w400,
    );
    return AppOnboardingTemplate(
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(context.theme.d.paddingXLarge),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(
                        height: context.theme.d.boxSizeXXLarge,
                        child: const Image(image: AssetImage(Assets.successful)),
                      ),
                      SizedBox(height: context.theme.d.paddingMedium),
                      Text(
                        AppLocale.current.signupCompletedTitle,
                        style: context.theme.textTheme.headlineLarge?.copyWith(
                          color: context.theme.colorScheme.secondary,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      SizedBox(height: context.theme.d.paddingLarge),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(AppLocale.current.signupCompletedContent1, style: contentTextStyle),
                          SizedBox(height: context.theme.d.paddingMedium),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppLocale.current.signupCompletedContent2Icon,
                                style: contentTextStyle,
                              ),
                              SizedBox(width: context.theme.d.paddingMedium),
                              Flexible(
                                child: Text(
                                  AppLocale.current.signupCompletedContent2,
                                  style: contentTextStyle,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: context.theme.d.paddingXxSmall),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppLocale.current.signupCompletedContent3Icon,
                                style: contentTextStyle,
                              ),
                              SizedBox(width: context.theme.d.paddingMedium),
                              Flexible(
                                child: Text(
                                  AppLocale.current.signupCompletedContent3,
                                  style: contentTextStyle,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: context.theme.d.paddingXxSmall),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppLocale.current.signupCompletedContent4Icon,
                                style: contentTextStyle,
                              ),
                              SizedBox(width: context.theme.d.paddingMedium),
                              Flexible(
                                child: Text(
                                  AppLocale.current.signupCompletedContent4,
                                  style: contentTextStyle,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      SizedBox(height: context.theme.d.paddingLarge),
                      CheckboxListTile(
                        value: _acceptedTerms,
                        controlAffinity: ListTileControlAffinity.leading,
                        onChanged: (value) => setState(() => _acceptedTerms = value ?? false),
                        title: MarkdownBody(
                          data: AppLocale.current.signupMethodSubtitle(
                            user?.firstName ?? '',
                            user?.lastName ?? '',
                            getLocalizedUrl(
                              urlType: LegalDocumentType.codeOfConduct,
                              localeModel: _localeModel,
                            ),
                          ),
                          styleSheet: MarkdownStyleSheet.fromTheme(context.theme).copyWith(
                            p: context.textTheme.labelLarge?.copyWith(
                              color: context.colorScheme.scrim,
                              fontWeight: FontWeight.w500,
                            ),
                            a: context.textTheme.labelLarge?.copyWith(
                              color: context.colorScheme.tertiary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          onTapLink: (_, url, __) => launchUrl(Uri.parse(url!)),
                        ),
                      ),
                      SizedBox(height: context.theme.d.paddingXLarge),
                    ],
                  ),
                ),
              ),
              const Divider(),
              _processing
                  ? const CircularProgressIndicator()
                  : SignUpBottomButtons(
                    goBackButtonText: AppLocale.current.actionPrevious,
                    onGoBack: onGoBack,
                    goNextButtonText:
                        _isMentee
                            ? _onboardingModel.isStriveIndonesiaGroup
                                ? AppLocale.current.findMyResources
                                : AppLocale.current.signupCompletedEntrepreneurAction
                            : AppLocale.current.signupCompletedMentorAction,
                    onGoNext: _acceptedTerms ? onGoNext : null,
                  ),
            ],
          ),
        ),
      ),
    );
  }
}
