import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../shared/text_form_field_widget.dart';
import '../components/sign_up_template.dart';

class SignUpVentureNameScreen extends StatefulWidget {
  const SignUpVentureNameScreen({super.key});

  @override
  State<SignUpVentureNameScreen> createState() => _SignUpVentureNameScreenState();
}

class _SignUpVentureNameScreenState extends State<SignUpVentureNameScreen> {
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  late final TextEditingController _ventureNameController;
  late final String? _oldVentureName;
  double _progress = 0;
  final _formKey = GlobalKey<FormState>();
  bool _processing = false;
  late FocusNode focusNode;

  @override
  void initState() {
    super.initState();

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    final company = _userProvider.myUser?.companies?.firstOrNull;
    _oldVentureName = company?.name;
    _ventureNameController = TextEditingController(text: _oldVentureName);

    _localData.onboardingStage = OnboardingStage.ventureName;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.ventureName,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );
    focusNode = FocusNode();
  }

  @override
  void dispose() {
    try {
      _ventureNameController.dispose();
      super.dispose();
    } catch (_) {}
  }

  bool _canGoNext() {
    if (_processing) {
      return false;
    }
    if (_formKey.currentState != null && _formKey.currentState?.validate() != true) {
      return false;
    }
    return _ventureNameController.text.isNotEmpty || _oldVentureName != null;
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      title: AppLocale.current.signupBusinessNameTitle,
      subtitle: AppLocale.current.signupBusinessNameSubtitle,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      onboardingStage: OnboardingStage.ventureName,
      onboardingModel: _onboardingModel,
      focusNode: focusNode,
      body: Form(
        key: _formKey,
        child: TextFormFieldWidget(
          label: AppLocale.current.signupBusinessNameInputLabel,
          maxLength: 50,
          hint: AppLocale.current.signupBusinessNameInputHint,
          textController: _ventureNameController,
          textCapitalization: TextCapitalization.sentences,
          onChanged: (_) => setState(() {}),
        ),
      ),
      canGoNext: _canGoNext(),
      onGoNext: () => _goNextAction(),
    );
  }

  _goNextAction() async {
    if (_formKey.currentState != null && !_formKey.currentState!.validate()) {
      return;
    }

    OnboardingStage? nextStage = _onboardingModel.getNextStage(
      curStage: OnboardingStage.ventureName,
      isMentee: true,
    );

    final oldCompany = _userProvider.myUser?.companies?.firstOrNull;
    bool isDirty = oldCompany?.name != _ventureNameController.text.trim();

    if (isDirty) {
      setState(() => _processing = true);
      final result = await _userProvider.updateUser(
        input: Input$UserInput(
          id: _localData.userId,
          company: Input$CompanyInput(id: oldCompany?.id, name: _ventureNameController.text.trim()),
          onboardingStage: nextStage?.name,
        ),
        options: UpdateObjectOptions<User>(
          loadObjectOptions: LoadObjectOptions<User>(
            pollingConfig: PollingConfig<User>(
              isUpdatedFunc:
                  (User user) =>
                      user.companies?.firstOrNull?.name.trim() ==
                      _ventureNameController.text.trim(),
            ),
          ),
        ),
      );

      setState(() => _processing = false);

      if (result.hasError) {
        _handleError(result);
        return;
      }
    }

    if (mounted && nextStage != null) {
      _localData.onboardingStage = nextStage;
      context.push(_onboardingModel.getRouteFromStage(nextStage).path);
    }
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(context: context, exception: result.operationException);
  }
}
