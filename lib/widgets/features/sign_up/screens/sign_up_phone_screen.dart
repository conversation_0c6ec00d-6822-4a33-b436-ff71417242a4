import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class SignUpPhoneScreen extends StatefulWidget {
  const SignUpPhoneScreen({super.key});

  @override
  State<SignUpPhoneScreen> createState() => _SignUpPhoneScreenState();
}

class _SignUpPhoneScreenState extends State<SignUpPhoneScreen> {
  late final ContentProvider _contentProvider;
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  double _progress = 0;
  final _formKey = GlobalKey<FormState>();
  bool _processing = false;
  bool? _requireUniquePhoneNumbers;
  PhoneNumberInputInfo? _phoneNumberInputInfo;
  PhoneNumberInputInfo? _alternatePhoneNumberInputInfo;
  late FocusNode focusNode;

  @override
  void initState() {
    super.initState();

    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);

    _phoneNumberInputInfo = null;
    _alternatePhoneNumberInputInfo = null;
    _requireUniquePhoneNumbers = null;

    if (_userProvider.myUser?.phoneNumber?.isNotEmpty == true &&
        _contentProvider.countryOptions?.isNotEmpty == true) {
      _phoneNumberInputInfo = PhoneNumberInputInfo.fromInternationalNumber(
        _userProvider.myUser?.phoneNumber,
        _contentProvider.countryOptions ?? [],
        true,
      );
    }

    _localData.onboardingStage = OnboardingStage.phoneNumber;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.phoneNumber,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );
    focusNode = FocusNode();
  }

  @override
  Widget build(BuildContext context) {
    _requireUniquePhoneNumbers = context.theme.validator.requireUniquePhoneNumbers;

    return SignUpTemplate(
      progress: _progress,
      title: AppLocale.current.signupPhoneTitle,
      subtitle: AppLocale.current.signupPhoneSubtitle,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      onboardingStage: OnboardingStage.phoneNumber,
      onboardingModel: _onboardingModel,
      focusNode: focusNode,
      body: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PhoneNumberForm(
            value: _phoneNumberInputInfo,
            checkIfAvailable: _requireUniquePhoneNumbers == true,
            onChanged: (PhoneNumberInputInfo value) {
              setState(() {
                _phoneNumberInputInfo = value;
              });
            },
          ),
          // TODO - Alternate Phone numbers
          // if (_onboardingModel.isIQLAASignUp()) ...[
          //   SizedBox(
          //     height: context.theme.d.paddingMedium,
          //   ),
          //   Text(
          //     AppLocale.current.alternatePhoneNumber,
          //     style: TextStyle(
          //       color: context.theme.colorScheme.onSurface,
          //       fontSize: context.theme.d.paddingMedium,
          //       fontWeight: FontWeight.w400,
          //     ),
          //   ),
          //   SizedBox(
          //     height: context.theme.d.paddingLarge,
          //   ),
          //   PhoneNumberForm(
          //     value: _phoneNumberInputInfo,
          //     isOptional: true,
          //     checkIfAvailable: _requireUniquePhoneNumbers == true,
          //     onChanged: (PhoneNumberInputInfo value) {
          //       setState(() {
          //         _alternatePhoneNumberInputInfo = value;
          //       });
          //     },
          //   ),
          // ]
        ],
      ),
      canGoNext:
          !_processing &&
          _phoneNumberInputInfo?.isNotEmpty == true &&
          _phoneNumberInputInfo?.isValid == true,
      onGoNext: () => _goNextAction(),
    );
  }

  _goNextAction() async {
    if (_processing ||
        _phoneNumberInputInfo?.isNotEmpty != true ||
        _formKey.currentState?.validate() == false) {
      return;
    }
    //TODO - pass _alternatePhoneNumberInputInfo to backend for IQLAA signup
    debugPrint(_alternatePhoneNumberInputInfo?.phoneNumber);

    final String formattedPhoneNumber = AppUtility.formatPhoneNumber(
      _phoneNumberInputInfo?.country?.phoneCode,
      _phoneNumberInputInfo?.phoneNumber,
    );

    if (formattedPhoneNumber.isNotEmpty != true) {
      return;
    }

    setState(() {
      _processing = true;
    });

    OnboardingStage? nextStage = _onboardingModel.getNextStage(
      curStage: OnboardingStage.phoneNumber,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );

    final result = await _userProvider.updateUser(
      input: Input$UserInput(
        id: _localData.userId,
        phoneNumber: _phoneNumberInputInfo!.internationalNumber,
        onboardingStage: nextStage?.name,
      ),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );

    if (result.hasError) {
      _handleError(result);
      return;
    }

    // Navigate to next screen:
    setState(() => _processing = false);
    if (mounted && nextStage != null) {
      _localData.onboardingStage = nextStage;
      context.push(_onboardingModel.getRouteFromStage(nextStage).path);
    }
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(context: context, exception: result.operationException);
    WidgetsBinding.instance.addPostFrameCallback((_) => setState(() => _processing = false));
  }
}
