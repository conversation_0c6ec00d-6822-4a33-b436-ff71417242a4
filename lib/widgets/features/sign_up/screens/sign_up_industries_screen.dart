import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/graphql/providers/base/operation_result.dart';
import 'package:mm_flutter_app/services/graphql/providers/providers.dart';
import 'package:mm_flutter_app/utilities/errors/error_handler.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../models/local_data_model.dart';
import '../../../../models/onboarding_model.dart';
import '../../../widgets.dart';

class SignUpIndustriesScreen extends StatefulWidget {
  const SignUpIndustriesScreen({super.key});

  @override
  State<SignUpIndustriesScreen> createState() => _SignUpIndustriesScreenState();
}

class _SignUpIndustriesScreenState extends State<SignUpIndustriesScreen> {
  late final ContentProvider _contentProvider;
  late final LocalDataModel _localData;
  late final UserProvider _userProvider;
  late final OnboardingModel _onboardingModel;
  late FocusNode focusNode;
  late double _progress;
  bool _processing = false;
  late final bool _isMentee;

  late final List<SelectableChip> _industryChips;
  List<SelectableChip> _initialValues = [];
  List<SelectableChip> _selectedChips = [];

  @override
  void initState() {
    super.initState();

    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _isMentee = _userProvider.myUser?.offersHelp != true;

    _localData.onboardingStage = OnboardingStage.industries;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.industries,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );

    focusNode = FocusNode();

    _setupContent();
  }

  _setupContent() {
    _industryChips =
        _contentProvider.industryOptions
            ?.map((e) => SelectableChip(chipContent: e.translatedValue ?? '', textId: e.textId))
            .toList() ??
        [];
    _industryChips.sort((a, b) => a.chipContent.compareTo(b.chipContent));

    if (_isMentee) {
      final MenteesGroupMembership? menteesGroupMembership =
          _userProvider.myUser?.groupMemberships
                  .where((element) => element.groupIdent == GroupIdent.mentees.name)
                  .firstOrNull
              as MenteesGroupMembership?;

      _initialValues =
          (menteesGroupMembership == null || menteesGroupMembership.industry == null)
              ? []
              : [
                SelectableChip(
                  chipContent: menteesGroupMembership.industry!.translatedValue!,
                  textId: menteesGroupMembership.industry!.textId,
                ),
              ];
    } else {
      final mentorGroupMembership =
          _userProvider.myUser?.groupMemberships
                  .where((g) => g.groupIdent == GroupIdent.mentors.name)
                  .firstOrNull
              as MentorsGroupMembership?;

      _initialValues =
          mentorGroupMembership == null
              ? []
              : mentorGroupMembership.industries
                  .map((e) => SelectableChip(chipContent: e.translatedValue!, textId: e.textId))
                  .toList();
    }
    _selectedChips = _initialValues;
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      focusNode: focusNode,
      title: AppLocale.current.signUpBusinessSectorTitle,
      subtitle:
          _isMentee
              ? AppLocale.current.profileEditSectionBusinessIndustrySubtitle
              : AppLocale.current.profileEditSectionMentorIndustrySubtitle,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      onboardingStage: OnboardingStage.industries,
      onboardingModel: _onboardingModel,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      canGoNext: _enableNext(),
      onGoNext: _goNextAction,
      body:
          _isMentee
              ? SelectableListTile.radio(
                chips: _industryChips,
                onSelect: (chips) {
                  setState(() => _selectedChips = chips);
                },
                initialSelection: _initialValues,
              )
              : SelectableListTile.checkbox(
                chips: _industryChips,
                maxSelection: Limits.profileMentorIndustryMaxSize,
                onSelect: (chips) {
                  setState(() => _selectedChips = chips);
                },
                initialSelection: _initialValues,
              ),
    );
  }

  _enableNext() {
    return _selectedChips.isNotEmpty;
  }

  _goNextAction() async {
    OnboardingStage? nextStage = _onboardingModel.getNextStage(
      curStage: OnboardingStage.industries,
      isMentee: _isMentee,
    );

    setState(() => _processing = true);
    OperationResult<void>? result =
        _isMentee
            ? await _userProvider.updateMenteesGroupMembership(
              input: Input$MenteesGroupMembershipInput(
                userId: _localData.userId,
                industryTextId: _selectedChips.firstOrNull?.textId,
              ),
            )
            : await _userProvider.updateMentorsGroupMembership(
              input: Input$MentorsGroupMembershipInput(
                userId: _localData.userId,
                industriesTextIds: _selectedChips.map((e) => e.textId).toList(),
              ),
            );

    await _userProvider.updateUser(
      input: Input$UserInput(id: _localData.userId, onboardingStage: nextStage?.name),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );
    setState(() => _processing = false);

    if (result.gqlQueryResult.hasException) {
      _handleError(result.gqlQueryResult.exception);
      return;
    }

    if (mounted && nextStage != null) {
      _localData.onboardingStage = nextStage;
      context.push(_onboardingModel.getRouteFromStage(nextStage).path);
    }
  }

  _handleError(OperationException? exception) {
    AppErrorHandler(context: context, exception: exception);
  }
}
