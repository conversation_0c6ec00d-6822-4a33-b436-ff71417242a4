import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/errors/error_handler.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../shared/radio_button_cards.dart';
import '../components/sign_up_template.dart';

class SignUpVentureStageScreen extends StatefulWidget {
  const SignUpVentureStageScreen({super.key});

  @override
  State<SignUpVentureStageScreen> createState() => _SignUpVentureStageScreenState();
}

class _SignUpVentureStageScreenState extends State<SignUpVentureStageScreen> {
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  final List<String> _companyStages = [
    CompanyStageTextId.idea.name,
    CompanyStageTextId.operational.name,
    CompanyStageTextId.earning.name,
    CompanyStageTextId.profitable.name,
  ];
  late int _selectedStageIndex = -1;
  late final String? _oldVentureStage;
  double _progress = 0;
  bool _processing = false;
  late FocusNode focusNode;

  @override
  void initState() {
    super.initState();

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    final company = _userProvider.myUser?.companies?.firstOrNull;
    _oldVentureStage = company?.companyStageTextId;
    if (_oldVentureStage?.isNotEmpty ?? false) {
      final indexOfVentureStage = _companyStages.indexOf(_oldVentureStage!);
      if (indexOfVentureStage > -1) {
        _selectedStageIndex = indexOfVentureStage;
      }
    }

    _localData.onboardingStage = OnboardingStage.ventureStage;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.ventureStage,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );
    focusNode = FocusNode();
  }

  @override
  Widget build(BuildContext context) {
    final iconSize = context.theme.d.iconSizeXSmall + context.theme.d.iconSizeXLarge;
    return SignUpTemplate(
      progress: _progress,
      title: AppLocale.current.signupBusinessStageTitle,
      subtitle: AppLocale.current.signupBusinessStageSubtitle,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      onboardingStage: OnboardingStage.ventureStage,
      onboardingModel: _onboardingModel,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      focusNode: focusNode,
      body: RadioButtonCards(
        cards: [
          RadioCard(
            title: AppLocale.current.signupBusinessStageCard1Title,
            subtitle: AppLocale.current.signupBusinessStageCard1Description,
            leadingImage: Image(image: const AssetImage(Assets.ideaStage), height: iconSize),
          ),
          RadioCard(
            title: AppLocale.current.signupBusinessStageCard2Title,
            subtitle: AppLocale.current.signupBusinessStageCard2Description,
            leadingImage: Image(image: const AssetImage(Assets.operationalStage), height: iconSize),
          ),
          RadioCard(
            title: AppLocale.current.signupBusinessStageCard3Title,
            subtitle: AppLocale.current.signupBusinessStageCard3Description,
            leadingImage: Image(image: const AssetImage(Assets.revenueStage), height: iconSize),
          ),
          RadioCard(
            title: AppLocale.current.signupBusinessStageCard4Title,
            subtitle: AppLocale.current.signupBusinessStageCard4Description,
            leadingImage: Image(image: const AssetImage(Assets.profitableStage), height: iconSize),
          ),
        ],
        onSelectedCardChanged:
            (index) => setState(() {
              _selectedStageIndex = index;
            }),
        initialSelection: _selectedStageIndex,
      ),
      canGoNext: !_processing && (_selectedStageIndex > -1 || _oldVentureStage?.isNotEmpty == true),
      onGoNext: () async {
        OnboardingStage? nextStage = _onboardingModel.getNextStage(
          curStage: OnboardingStage.ventureStage,
          isMentee: true,
        );
        final User? user = _userProvider.myUser;

        if (user == null ||
            user.companies == null ||
            user.companies!.isEmpty ||
            user.companies?.first.id == '') {
          // For some reason the user or company could not be loaded.
          // todo: show error
          WidgetsBinding.instance.addPostFrameCallback((_) => setState(() => _processing = false));
          return;
        }

        String? oldStageTextId = _userProvider.myUser?.companies?.firstOrNull?.companyStageTextId;
        final newStageTextId = _companyStages[_selectedStageIndex];
        bool isDirty = oldStageTextId != newStageTextId;

        if (isDirty) {
          setState(() => _processing = true);

          final result = await _userProvider.updateUser(
            input: Input$UserInput(
              id: _localData.userId,
              company: Input$CompanyInput(
                id: user.companies?.first.id,
                companyStageTextId: newStageTextId,
              ),
              onboardingStage: nextStage?.name,
            ),
            options: UpdateObjectOptions<User>(
              loadObjectOptions: LoadObjectOptions<User>(
                pollingConfig: PollingConfig<User>(
                  isUpdatedFunc:
                      (User user) =>
                          user.companies?.firstOrNull?.companyStageTextId == newStageTextId,
                ),
              ),
            ),
          );
          setState(() => _processing = false);

          if (result.hasError) {
            _handleError(result);
            return;
          }
        }

        // Navigate to next screen-
        if (context.mounted && nextStage != null) {
          _localData.onboardingStage = nextStage;
          context.push(_onboardingModel.getRouteFromStage(nextStage).path);
        }
      },
    );
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(context: context, exception: result.operationException);
  }
}
