import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../components/sign_up_template.dart';

class SignUpNotificationScreen extends StatefulWidget {
  const SignUpNotificationScreen({super.key});

  @override
  State<SignUpNotificationScreen> createState() => _SignUpNotificationScreenState();
}

class _SignUpNotificationScreenState extends State<SignUpNotificationScreen> {
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  double _progress = 0;
  final _processing = false;
  late FocusNode focusNode;

  @override
  void initState() {
    super.initState();

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);

    _localData.onboardingStage = OnboardingStage.gender;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.notifications,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );
    focusNode = FocusNode();
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      title: AppLocale.current.notificationsHeading,
      subtitle: AppLocale.current.notificationScreenSubtitle,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      onboardingStage: OnboardingStage.notifications,
      onboardingModel: _onboardingModel,
      extraScrollingSpace: context.theme.d.boxSizeMedium,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      body: _content(),
      canGoNext: !_processing,
      focusNode: focusNode,
      onGoNext: () async {
        OnboardingStage? nextStage = _onboardingModel.getNextStage(
          curStage: OnboardingStage.notifications,
          isMentee: _userProvider.myUser?.seeksHelp != false,
        );

        // TODO- update here

        if (context.mounted && nextStage != null) {
          _localData.onboardingStage = nextStage;
          context.push(_onboardingModel.getRouteFromStage(nextStage).path);
        }
      },
    );
  }

  _content() {
    return Column(
      children: [
        AppUtility.switchWithTitle(
          context: context,
          title: AppLocale.current.notificationNewMsgInvite,
          onChanged: (bool isActive) {},
        ),
        AppUtility.switchWithTitle(
          context: context,
          title: AppLocale.current.notificationRecommendTip,
          onChanged: (bool isActive) {},
        ),
      ],
    );
  }
}
