import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/base/operation_result.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/debug_logger.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../widgets.dart';

class SignUpExpertisesScreen extends StatefulWidget {
  const SignUpExpertisesScreen({super.key});

  @override
  State<SignUpExpertisesScreen> createState() => _SignUpExpertisesScreenState();
}

class _SignUpExpertisesScreenState extends State<SignUpExpertisesScreen> {
  late final ContentProvider _contentProvider;
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  late final List<SelectableChip> _expertiseChips;
  late final bool _isMentee;
  final int maxSelections = 3;
  double _progress = 0;
  bool _processing = false;

  late List<SelectableChip> _selectedChips;
  late final List<SelectableChip> _initialSelection;
  late FocusNode focusNode;

  @override
  void initState() {
    super.initState();

    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _isMentee = _userProvider.myUser?.offersHelp != true;
    _expertiseChips =
        _contentProvider.expertiseOptions
            ?.map((e) => SelectableChip(chipContent: e.translatedValue ?? '', textId: e.textId))
            .toList() ??
        [];
    _expertiseChips.sort((a, b) => a.chipContent.compareTo(b.chipContent));

    if (_isMentee) {
      final MenteesGroupMembership? menteesGroupMembership =
          _userProvider.myUser?.groupMemberships
                  .where((element) => element.groupIdent == GroupIdent.mentees.name)
                  .firstOrNull
              as MenteesGroupMembership?;

      if (menteesGroupMembership == null) {
        DebugLogger.error('SignUpExpertiseScreen.initState: menteesGroupMembership not found.');
      }

      _initialSelection =
          menteesGroupMembership?.soughtExpertisesTextIds
              ?.map((e) => _expertiseChips.where((c) => c.textId == e).firstOrNull)
              .nonNulls
              .toList() ??
          [];
    } else {
      final MentorsGroupMembership? mentorsGroupMembership =
          _userProvider.myUser?.groupMemberships
                  .where((element) => element.groupIdent == GroupIdent.mentors.name)
                  .firstOrNull
              as MentorsGroupMembership?;

      if (mentorsGroupMembership == null) {
        DebugLogger.error('SignUpExpertiseScreen.initState: mentorsGroupMembership not found.');
      }

      _initialSelection =
          mentorsGroupMembership?.expertisesTextIds
              ?.map((e) => _expertiseChips.where((c) => c.textId == e).firstOrNull)
              .nonNulls
              .toList() ??
          [];
    }
    _selectedChips = _initialSelection;
    _localData.onboardingStage = OnboardingStage.expertises;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.expertises,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );
    focusNode = FocusNode();
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      title:
          _isMentee
              ? AppLocale.current.signupExpertisesEntrepreneurTitle
              : AppLocale.current.signupExpertisesMentorTitle,
      subtitle: _isMentee ? AppLocale.current.signupExpertisesEntrepreneurSubtitle : null,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      onboardingStage: OnboardingStage.expertises,
      onboardingModel: _onboardingModel,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      extraScrollingSpace: context.theme.d.boxSizeLarge,
      focusNode: focusNode,
      body: SelectableListTile.checkbox(
        chips: _expertiseChips,
        maxSelection: maxSelections,
        initialSelection: _initialSelection,
        onSelect: (chips) => setState(() => _selectedChips = chips),
      ),
      canGoNext: !_processing && _selectedChips.isNotEmpty,
      onGoNext: () async {
        setState(() => _processing = true);
        OnboardingStage? nextStage = _onboardingModel.getNextStage(
          curStage: OnboardingStage.expertises,
          isMentee: _isMentee,
        );

        OperationResult<void> updateGroupMembershipResponse;
        if (_isMentee) {
          updateGroupMembershipResponse = await _userProvider.updateMenteesGroupMembership(
            input: Input$MenteesGroupMembershipInput(
              userId: _localData.userId,
              soughtExpertisesTextIds: _selectedChips.map((e) => e.textId).toList(),
            ),
          );
        } else {
          updateGroupMembershipResponse = await _userProvider.updateMentorsGroupMembership(
            input: Input$MentorsGroupMembershipInput(
              userId: _localData.userId,
              expertisesTextIds: _selectedChips.map((e) => e.textId).toList(),
            ),
          );
        }

        if (updateGroupMembershipResponse.gqlQueryResult.hasException) {
          _handleError(
            LoadObjectResult(
              operationException: updateGroupMembershipResponse.gqlQueryResult.exception,
            ),
          );
          return;
        }

        final result = await _userProvider.updateUser(
          input: Input$UserInput(id: _localData.userId, onboardingStage: nextStage?.name),
          options: UpdateObjectOptions<User>(
            loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
          ),
        );

        if (result.hasError) {
          _handleError(result);
          return;
        }

        setState(() => _processing = false);
        if (context.mounted && nextStage != null) {
          _localData.onboardingStage = nextStage;
          context.push(_onboardingModel.getRouteFromStage(nextStage).path);
        }
      },
    );
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(context: context, exception: result.operationException);
    WidgetsBinding.instance.addPostFrameCallback((_) => setState(() => _processing = false));
  }
}
