import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../widgets.dart';

class StriveIndonesiaSignupVentureStartDateScreen extends StatefulWidget {
  const StriveIndonesiaSignupVentureStartDateScreen({super.key});

  @override
  State<StriveIndonesiaSignupVentureStartDateScreen> createState() =>
      _StriveIndonesiaSignupVentureStartDateScreen();
}

class _StriveIndonesiaSignupVentureStartDateScreen
    extends State<StriveIndonesiaSignupVentureStartDateScreen> {
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  late final TextEditingController _foundedInYearController;
  late final TextEditingController _foundedInMonthController;
  late final String? _oldFoundedInYear;
  late final String? _oldFoundedInMonth;
  double _progress = 0;
  final _formKey = GlobalKey<FormState>();
  bool _processing = false;
  late FocusNode focusNode;

  @override
  void initState() {
    super.initState();

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);

    final company = _userProvider.myUser?.companies?.firstOrNull;
    _oldFoundedInYear = company?.foundedAt?.year.toString();
    _oldFoundedInMonth = company?.foundedAt?.month.toString();
    _foundedInYearController = TextEditingController(text: _oldFoundedInYear ?? '');
    _foundedInMonthController = TextEditingController(text: _oldFoundedInMonth ?? '');
    // Below line is needed if user refresh this page to save groupIdent
    _onboardingModel.setGroupIdentName(GroupIdent.striveIndonesia.name);

    _localData.onboardingStage = OnboardingStage.striveIndonesiaVentureStartDate;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.striveIndonesiaVentureStartDate,
      isMentee: true,
    );
    focusNode = FocusNode();
  }

  @override
  void dispose() {
    try {
      _foundedInYearController.dispose();
    } catch (_) {}
    super.dispose();
  }

  bool _canGoNext() {
    if (_processing) {
      return false;
    }
    if (_formKey.currentState != null && _formKey.currentState?.validate() != true) {
      return false;
    }
    return _foundedInYearController.text.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      title: AppLocale.current.signupBusinessStartYearTitle,
      isMentee: true,
      onboardingStage: OnboardingStage.striveIndonesiaVentureStartDate,
      onboardingModel: _onboardingModel,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      focusNode: focusNode,
      body: Form(
        key: _formKey,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(child: _startMonthWidget()),
            SizedBox(width: context.theme.d.paddingMedium),
            Expanded(child: _startYearWidget()),
          ],
        ),
      ),
      canGoNext: _canGoNext(),
      onGoNext: () => _goNextAction(),
    );
  }

  _startYearWidget() {
    return TextFormFieldWidget(
      label: AppLocale.current.signupYearTitle,
      hint: AppLocale.current.signupBirthInputHint,
      keyboardType: TextInputType.number,
      inputFormatters: <TextInputFormatter>[
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(4),
      ],
      textController: _foundedInYearController,
      onChanged: (_) => setState(() {}),
      validator: context.theme.validator.validateYear,
    );
  }

  _startMonthWidget() {
    return TextFormFieldWidget(
      label: AppLocale.current.month,
      hint: AppLocale.current.monthHint,
      keyboardType: TextInputType.number,
      inputFormatters: <TextInputFormatter>[
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(2),
      ],
      textController: _foundedInMonthController,
      onChanged: (_) => setState(() {}),
      validator: (value) {
        final ventureStartDate = getStartDateTime();
        final errorMessage = context.theme.validator.validateBirthMonth(value);
        if (ventureStartDate == null || errorMessage != null) {
          return errorMessage;
        }
        return context.theme.validator.validateDate(ventureStartDate);
      },
    );
  }

  DateTime? getStartDateTime() {
    if (_foundedInYearController.text.isEmpty || _foundedInMonthController.text.isEmpty) {
      return null;
    }
    return DateTime(
      int.parse(_foundedInYearController.text.trim()),
      int.parse(_foundedInMonthController.text.trim()),
    );
  }

  _goNextAction() async {
    if (_formKey.currentState != null && !_formKey.currentState!.validate()) {
      return;
    }

    OnboardingStage? nextStage = _onboardingModel.getNextStage(
      curStage: OnboardingStage.striveIndonesiaVentureStartDate,
      isMentee: true,
    );

    final oldCompany = _userProvider.myUser?.companies?.firstOrNull;
    bool isDirty = oldCompany?.foundedAt?.year.toString() != _foundedInYearController.text.trim();
    final ventureStartDate = getStartDateTime();

    if (isDirty && ventureStartDate != null) {
      setState(() => _processing = true);

      final result = await _userProvider.updateUser(
        input: Input$UserInput(
          id: _localData.userId,
          // Set role as Entrepreneur for StriveIndonesia user
          seeksHelp: true,
          company: Input$CompanyInput(
            id: oldCompany?.id,
            // The following line is using the year from the user's input and the month, day
            // defaults to 1. Since we don't ask for the month and day, we just set it to
            // January 1st of the year.
            foundedAt: ventureStartDate.toUtc(),
          ),
          onboardingStage: nextStage?.name,
        ),
        options: UpdateObjectOptions<User>(
          loadObjectOptions: LoadObjectOptions<User>(
            pollingConfig: PollingConfig<User>(
              isUpdatedFunc:
                  (User user) =>
                      user.companies?.firstOrNull?.foundedAt?.year.toString() ==
                      _foundedInYearController.text.trim(),
            ),
          ),
        ),
      );

      if (result.hasError) {
        _handleError(result);
        return;
      }
      setState(() => _processing = false);
    }

    // Navigate to next screen:
    if (mounted && nextStage != null) {
      _localData.onboardingStage = nextStage;
      context.push(_onboardingModel.getRouteFromStage(nextStage).path);
    }
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(context: context, exception: result.operationException);
  }
}
