import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/utilities/errors/error_handler.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../models/local_data_model.dart';
import '../../../../models/onboarding_model.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../widgets.dart';

class IqlaaEmployeeNumberScreen extends StatefulWidget {
  const IqlaaEmployeeNumberScreen({super.key});

  @override
  State<IqlaaEmployeeNumberScreen> createState() => _IqlaaEmployeeNumberScreenState();
}

class _IqlaaEmployeeNumberScreenState extends State<IqlaaEmployeeNumberScreen> {
  late final LocalDataModel _localData;
  late final UserProvider _userProvider;
  late final OnboardingModel _onboardingModel;
  late final TextEditingController _numberOfEmployesController;
  late FocusNode focusNode;
  late double _progress;
  final _formKey = GlobalKey<FormState>();
  bool _processing = false;

  @override
  void initState() {
    super.initState();

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);

    int? employeeCount = _userProvider.myUser?.companies?.firstOrNull?.employeeCount;
    _numberOfEmployesController = TextEditingController(
      text: employeeCount == null ? '' : employeeCount.toString(),
    );

    _localData.onboardingStage = OnboardingStage.iqlaaEmployeeNumber;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.iqlaaEmployeeNumber,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );

    focusNode = FocusNode();
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      focusNode: focusNode,
      title: AppLocale.current.iqlaaEmployeeNumberTitle,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      onboardingStage: OnboardingStage.iqlaaEmployeeNumber,
      onboardingModel: _onboardingModel,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      canGoNext: _enableNext(),
      onGoNext: _goNextAction,
      body: Form(
        key: _formKey,
        child: TextFormFieldWidget(
          label: AppLocale.current.iqlaaEmployeeNumberTextTieldLabel,
          hint: AppLocale.current.iqlaaEmployeeNumberTextTieldHint,
          maxLines: 1,
          textController: _numberOfEmployesController,
          textCapitalization: TextCapitalization.sentences,
          inputFormatters: <TextInputFormatter>[FilteringTextInputFormatter.digitsOnly],
          onChanged: (_) => setState(() {}),
        ),
      ),
    );
  }

  _enableNext() {
    return _numberOfEmployesController.text.trim().isNotEmpty;
  }

  _goNextAction() async {
    OnboardingStage? nextStage = _onboardingModel.getNextStage(
      curStage: OnboardingStage.iqlaaEmployeeNumber,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );

    final oldCompany = _userProvider.myUser?.companies?.firstOrNull;
    final employeeCount = int.parse(_numberOfEmployesController.text.trim());
    setState(() => _processing = true);
    final result = await _userProvider.updateUser(
      input: Input$UserInput(
        id: _localData.userId,
        company: Input$CompanyInput(id: oldCompany?.id, employeeCount: employeeCount),
        onboardingStage: nextStage?.name,
      ),
      options: UpdateObjectOptions<User>(
        loadObjectOptions: LoadObjectOptions<User>(
          pollingConfig: PollingConfig<User>(
            isUpdatedFunc:
                (User user) => user.companies?.firstOrNull?.employeeCount == employeeCount,
          ),
        ),
      ),
    );
    setState(() => _processing = false);

    if (result.hasError) {
      _handleError(result.operationException);
      return;
    }

    if (mounted && nextStage != null) {
      _localData.onboardingStage = nextStage;
      context.push(_onboardingModel.getRouteFromStage(nextStage).path);
    }
  }

  _handleError(OperationException? exception) {
    AppErrorHandler(context: context, exception: exception);
  }
}
