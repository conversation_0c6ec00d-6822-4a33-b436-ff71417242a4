import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/base/operation_result.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../widgets.dart';

class IqlaaBusinessRegistrationScreen extends StatefulWidget {
  const IqlaaBusinessRegistrationScreen({super.key});

  @override
  State<IqlaaBusinessRegistrationScreen> createState() => _IqlaaBusinessRegistrationScreenState();
}

class _IqlaaBusinessRegistrationScreenState extends State<IqlaaBusinessRegistrationScreen> {
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  late final TextEditingController _registrationNumberController;

  double _progress = 0;
  late bool _processing;
  late FocusNode focusNode;
  late FocusNode registrationNoFocusNode;
  bool? isRegistered;

  @override
  void initState() {
    super.initState();

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _localData.onboardingStage = OnboardingStage.iqlaaBusinessRegistration;

    final iqlaaMembership = ProfileUtility.fromFindId(_userProvider.myUser).iqlaaGroupMembership;
    isRegistered = iqlaaMembership?.isBusinessRegisteredWithCCD;
    _registrationNumberController = TextEditingController(
      text: iqlaaMembership?.businessRegistrationNumber,
    );

    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.iqlaaBusinessRegistration,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );
    _processing = false;
    focusNode = FocusNode();
    registrationNoFocusNode = FocusNode();
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      title: AppLocale.current.iqlaaBusinessRegistrationTitle,
      subtitle: AppLocale.current.iqlaaBusinessRegistrationSubtitle,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      onboardingStage: OnboardingStage.iqlaaBusinessRegistration,
      onboardingModel: _onboardingModel,
      extraScrollingSpace: context.theme.d.desktopOnboardingExtraScrollingSpace,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      focusNode: focusNode,
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          RadioTileWidget<bool>(
            title: AppLocale.current.yes,
            value: true,
            groupValue: isRegistered,
            onChanged: (value) {
              onChangedRegistrationOption(value);
              WidgetsBinding.instance.addPostFrameCallback((_) {
                registrationNoFocusNode.requestFocus();
              });
            },
          ),
          Row(
            children: [
              SizedBox(width: context.theme.d.buttonSizeSmall.height),
              Expanded(
                child: TextFormFieldWidget(
                  enabled: isRegistered ?? false,
                  autofocus: true,
                  label: AppLocale.current.iqlaaBusinessRegistrationOptionYesDisclamer,
                  labelStyle: context.theme.textTheme.bodyLarge?.copyWith(
                    color: context.colorScheme.onSurface,
                  ),
                  keyboardType: TextInputType.number,
                  //according to Katy: input limit upto 20 and only letters and numbers are allowed
                  inputFormatters: <TextInputFormatter>[
                    FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]')),
                    LengthLimitingTextInputFormatter(20),
                  ],
                  textController: _registrationNumberController,
                  onChanged: (_) => setState(() {}),
                  validator: context.theme.validator.validateBirthDate,
                  focusNode: registrationNoFocusNode,
                ),
              ),
            ],
          ),
          SizedBox(height: context.theme.d.paddingSmall),
          RadioTileWidget<bool>(
            title: AppLocale.current.no,
            value: false,
            groupValue: isRegistered,
            onChanged: (value) => onChangedRegistrationOption(value),
          ),
        ],
      ),
      canGoNext: _enableNext(),
      onGoNext: () => _goNextAction(),
    );
  }

  _goNextAction() async {
    setState(() => _processing = true);
    OnboardingStage? nextStage = _onboardingModel.getNextStage(
      curStage: OnboardingStage.iqlaaBusinessRegistration,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );

    OperationResult<void> updateGroupMembershipResponse = await _userProvider
        .updateIqlaaGroupMembership(
          input: Input$IqlaaGroupMembershipInput(
            userId: _localData.userId,
            isBusinessRegisteredWithCCD: isRegistered,
            businessRegistrationNumber:
                isRegistered == true ? _registrationNumberController.text.trim() : null,
          ),
        );

    if (updateGroupMembershipResponse.gqlQueryResult.hasException) {
      _handleError(
        LoadObjectResult(
          operationException: updateGroupMembershipResponse.gqlQueryResult.exception,
        ),
      );
      return;
    }

    final result = await _userProvider.updateUser(
      input: Input$UserInput(id: _localData.userId, onboardingStage: nextStage?.name),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );

    if (result.hasError) {
      _handleError(result);
      return;
    }

    setState(() => _processing = false);
    if (mounted && nextStage != null) {
      _localData.onboardingStage = nextStage;
      context.push(_onboardingModel.getRouteFromStage(nextStage).path);
    }
  }

  void onChangedRegistrationOption(bool? value) {
    setState(() {
      isRegistered = value;
    });
  }

  _enableNext() {
    if (isRegistered == null ||
        (isRegistered == true && _registrationNumberController.text.trim().isEmpty)) {
      return false;
    }
    return true;
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(context: context, exception: result.operationException);
    WidgetsBinding.instance.addPostFrameCallback((_) => setState(() => _processing = false));
  }
}
