import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/models/local_data_model.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/errors/error_handler.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../models/onboarding_model.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../widgets.dart';

class IqlaaConsentScreen extends StatefulWidget {
  const IqlaaConsentScreen({super.key});

  @override
  State<IqlaaConsentScreen> createState() => _IqlaaConsentScreenState();
}

class _IqlaaConsentScreenState extends State<IqlaaConsentScreen> {
  late final ScrollController _scrollController;
  late final UserProvider _userProvider;
  late final OnboardingModel _onboardingModel;
  late final LocalDataModel _localData;

  bool? dataUseConsent;
  late FocusNode focusNode;
  late double _progress;
  late bool allcheckboxesChecked;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _localData = Provider.of<LocalDataModel>(context, listen: false);

    allcheckboxesChecked = false;
    _scrollController = ScrollController();
    focusNode = FocusNode();
    _localData.onboardingStage = OnboardingStage.iqlaaConsent;

    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.iqlaaConsent,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void onChangedDataUseConsent(bool? value) {
    setState(() {
      dataUseConsent = value;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      focusNode: focusNode,
      title: AppLocale.current.iqlaaConsentTitle,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      onboardingStage: OnboardingStage.iqlaaConsent,
      onboardingModel: _onboardingModel,
      canGoNext: _enableNext(),
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              CheckboxTileWidget(
                content: [
                  AppLocale.current.iqlaaSignupConsentOption1,
                  AppLocale.current.iqlaaSignupConsentOption2,
                  AppLocale.current.iqlaaSignupConsentOption3,
                ],
                onSelection: (totalSelection) {
                  setState(() {
                    allcheckboxesChecked = totalSelection == 3;
                  });
                },
              ),
              Text(
                AppLocale.current.iqlaaSignupDataConsentConfirmationMessage,
                style: context.textTheme.bodyLarge,
              ),
              SizedBox(height: context.theme.d.paddingXSmall),
              RadioTheme(
                data: RadioThemeData(
                  fillColor: WidgetStateProperty.resolveWith<Color>((states) {
                    if (states.contains(WidgetState.selected)) {
                      return context.colorScheme.primary;
                    }
                    return context.colorScheme.outline;
                  }),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    RadioTileWidget<bool>(
                      title: AppLocale.current.yes,
                      value: true,
                      groupValue: dataUseConsent,
                      onChanged: (value) => onChangedDataUseConsent(value),
                    ),
                    RadioTileWidget<bool>(
                      title: AppLocale.current.no,
                      value: false,
                      groupValue: dataUseConsent,
                      onChanged: (value) => onChangedDataUseConsent(value),
                    ),
                    SizedBox(height: context.theme.d.paddingSmall),
                    Visibility(
                      visible: dataUseConsent == false,
                      child: Text(
                        AppLocale.current.iqlaaSignupDataConsentDeclinationMsg,
                        style: context.textTheme.bodyLarge?.copyWith(
                          color: context.colorScheme.error,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                    Visibility(
                      visible: (dataUseConsent == true) && (!allcheckboxesChecked),
                      child: Text(
                        AppLocale.current.iqlaaSignupDataConsentMustAgreeWarning,
                        style: context.textTheme.bodyLarge?.copyWith(
                          color: context.colorScheme.error,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      onGoNext: () => _goNextAction(),
    );
  }

  _enableNext() {
    if (dataUseConsent == null) return false;
    return ((dataUseConsent! && allcheckboxesChecked) || (!dataUseConsent!));
  }

  _goNextAction() async {
    OnboardingStage? nextStage = _onboardingModel.getNextStage(
      curStage: OnboardingStage.iqlaaConsent,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );

    if (dataUseConsent == false) {
      _onboardingModel.groupIdent = GroupIdent.mentees;
      final response = await _userProvider.removeUserFromGroup(
        groupIdent: GroupIdent.iqlaa,
        userId: _localData.userId,
      );
      if (!mounted) return;
      if (response.gqlQueryResult.hasException) {
        AppErrorHandler(context: context, exception: response.gqlQueryResult.exception);
      }
    }

    if (mounted && nextStage != null) {
      _localData.onboardingStage = nextStage;
      context.push(_onboardingModel.getRouteFromStage(nextStage).path);
    }
  }
}

class CheckboxTileWidget extends StatefulWidget {
  final List<String> content;
  final Function(int totalSelection)? onSelection;

  const CheckboxTileWidget({super.key, required this.content, this.onSelection});

  @override
  State<CheckboxTileWidget> createState() => _CheckboxTileWidgetState();
}

class _CheckboxTileWidgetState extends State<CheckboxTileWidget> {
  final Set<int> selectedOptions = {};

  void _handleSelection(int chipId) {
    setState(() {
      selectedOptions.contains(chipId)
          ? selectedOptions.remove(chipId)
          : selectedOptions.add(chipId);
    });
    if (widget.onSelection != null) {
      widget.onSelection?.call(selectedOptions.length);
    }
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> chipListWithPadding = [];

    for (int i = 0; i < widget.content.length; i++) {
      final bool isChecked = selectedOptions.contains(i);
      var chip = Padding(
        padding: EdgeInsets.only(bottom: context.theme.d.paddingXSmall),
        child: ListTile(
          selected: isChecked,
          titleAlignment: ListTileTitleAlignment.top,
          dense: true,
          contentPadding: EdgeInsets.symmetric(horizontal: context.theme.d.zero),
          hoverColor: Colors.transparent,
          onTap: () => _handleSelection(i),
          title: Text(widget.content[i], style: context.textTheme.bodyLarge),
          titleTextStyle: context.textTheme.bodyLarge,
          leading: Checkbox(
            value: isChecked,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXSmall),
            ),
            onChanged: (_) => _handleSelection(i),
          ),
        ),
      );
      chipListWithPadding.add(chip);
    }

    return Column(children: chipListWithPadding);
  }
}
