import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/models/local_data_model.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/base/operation_result.dart';
import 'package:mm_flutter_app/utilities/errors/error_handler.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../models/onboarding_model.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../widgets.dart';

class IqlaaNationalityScreen extends StatefulWidget {
  const IqlaaNationalityScreen({super.key});

  @override
  State<IqlaaNationalityScreen> createState() => _IqlaaNationalityScreenState();
}

class _IqlaaNationalityScreenState extends State<IqlaaNationalityScreen> {
  late final UserProvider _userProvider;
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;

  IQLAASignUpNationality _selectedNationalityType = IQLAASignUpNationality.jordanian;
  late FocusNode focusNode;
  late double _progress;
  bool _processing = false;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _localData = Provider.of<LocalDataModel>(context, listen: false);
    focusNode = FocusNode();
    _localData.onboardingStage = OnboardingStage.iqlaaNationality;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.iqlaaNationality,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );

    final iqlaaMembership = ProfileUtility.fromFindId(_userProvider.myUser).iqlaaGroupMembership;
    if (iqlaaMembership != null && iqlaaMembership.isJordanNational != null) {
      _selectedNationalityType =
          iqlaaMembership.isJordanNational!
              ? IQLAASignUpNationality.jordanian
              : IQLAASignUpNationality.other;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      focusNode: focusNode,
      title: AppLocale.current.signupIqlaaNationalityTitle,
      subtitle: AppLocale.current.signupIqlaaNationalitySubtitle,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      onboardingStage: OnboardingStage.iqlaaNationality,
      onboardingModel: _onboardingModel,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      canGoNext: true,
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _radioButton(IQLAASignUpNationality.jordanian),
          _radioButton(IQLAASignUpNationality.other),
        ],
      ),
      onGoNext: () => _goNextAction(),
    );
  }

  _radioButton(IQLAASignUpNationality nationalityType) {
    return RadioTileWidget<IQLAASignUpNationality>(
      title: nationalityType.name.capitalize(),
      value: nationalityType,
      groupValue: _selectedNationalityType,
      onChanged: (value) {
        setState(() {
          _selectedNationalityType = value;
        });
      },
    );
  }

  _goNextAction() async {
    OnboardingStage? nextStage = _onboardingModel.getNextStage(
      curStage: OnboardingStage.iqlaaNationality,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );

    setState(() => _processing = true);
    OperationResult<void> updateGroupMembershipResponse = await _userProvider
        .updateIqlaaGroupMembership(
          input: Input$IqlaaGroupMembershipInput(
            userId: _localData.userId,
            isJordanNational: _selectedNationalityType == IQLAASignUpNationality.jordanian,
          ),
        );

    await _userProvider.updateUser(
      input: Input$UserInput(id: _localData.userId, onboardingStage: nextStage?.name),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );

    setState(() => _processing = false);

    if (updateGroupMembershipResponse.gqlQueryResult.hasException) {
      _handleError(updateGroupMembershipResponse.gqlQueryResult.exception);
      return;
    }

    if (mounted && nextStage != null) {
      _localData.onboardingStage = nextStage;
      context.push(_onboardingModel.getRouteFromStage(nextStage).path);
    }
  }

  _handleError(OperationException? exception) {
    AppErrorHandler(context: context, exception: exception);
  }
}
