import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/base/operation_result.dart';
import 'package:mm_flutter_app/utilities/errors/error_handler.dart';
import 'package:provider/provider.dart';

import '../../../../generatedAppLocale/l10n.dart';
import '../../../../models/local_data_model.dart';
import '../../../../models/onboarding_model.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../widgets.dart';

class IqlaaHomeBaseBusinessScreen extends StatefulWidget {
  const IqlaaHomeBaseBusinessScreen({super.key});

  @override
  State<IqlaaHomeBaseBusinessScreen> createState() => _IqlaaHomeBaseBusinessScreenState();
}

class _IqlaaHomeBaseBusinessScreenState extends State<IqlaaHomeBaseBusinessScreen> {
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  double _progress = 0;
  late bool _processing;
  late FocusNode focusNode;
  bool? isHomeBasedBusiness;

  @override
  void initState() {
    super.initState();

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _localData.onboardingStage = OnboardingStage.iqlaaHomeBasedBusiness;
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.iqlaaHomeBasedBusiness,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );
    _processing = false;
    focusNode = FocusNode();

    final iqlaaMembership = ProfileUtility.fromFindId(_userProvider.myUser).iqlaaGroupMembership;
    isHomeBasedBusiness = iqlaaMembership?.isBusinessHomeBased;
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      title: AppLocale.current.iqlaaSignupHomeBasedBusinessTitle,
      subtitle: AppLocale.current.iqlaaHomeBasedBusinessDisclamer,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      onboardingStage: OnboardingStage.iqlaaHomeBasedBusiness,
      onboardingModel: _onboardingModel,
      extraScrollingSpace: context.theme.d.desktopOnboardingExtraScrollingSpace,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      focusNode: focusNode,
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          RadioTileWidget<bool>(
            title: AppLocale.current.yes,
            value: true,
            groupValue: isHomeBasedBusiness,
            onChanged: (value) => onChangedHomeBasedBusinessOption(value),
          ),
          RadioTileWidget<bool>(
            title: AppLocale.current.no,
            value: false,
            groupValue: isHomeBasedBusiness,
            onChanged: (value) => onChangedHomeBasedBusinessOption(value),
          ),
        ],
      ),
      canGoNext: _enableNext(),
      onGoNext: () => _goNextAction(),
    );
  }

  _goNextAction() async {
    OnboardingStage? nextStage = _onboardingModel.getNextStage(
      curStage: OnboardingStage.iqlaaHomeBasedBusiness,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );

    setState(() => _processing = true);
    OperationResult<void> updateGroupMembershipResponse = await _userProvider
        .updateIqlaaGroupMembership(
          input: Input$IqlaaGroupMembershipInput(
            userId: _localData.userId,
            isBusinessHomeBased: isHomeBasedBusiness,
          ),
        );

    await _userProvider.updateUser(
      input: Input$UserInput(id: _localData.userId, onboardingStage: nextStage?.name),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );

    setState(() => _processing = false);

    if (updateGroupMembershipResponse.gqlQueryResult.hasException) {
      _handleError(updateGroupMembershipResponse.gqlQueryResult.exception);
      return;
    }

    if (mounted && nextStage != null) {
      _localData.onboardingStage = nextStage;
      context.push(_onboardingModel.getRouteFromStage(nextStage).path);
    }
  }

  void onChangedHomeBasedBusinessOption(bool? value) {
    setState(() {
      isHomeBasedBusiness = value;
    });
  }

  _enableNext() {
    return isHomeBasedBusiness != null;
  }

  _handleError(OperationException? exception) {
    AppErrorHandler(context: context, exception: exception);
  }
}
