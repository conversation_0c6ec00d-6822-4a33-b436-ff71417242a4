import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/base/operation_result.dart';
import 'package:provider/provider.dart';
import 'package:textfield_tags/textfield_tags.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../widgets.dart';
import 'bank_card_types_drop_down.dart';

class SignUpBankDetailsScreen extends StatefulWidget {
  const SignUpBankDetailsScreen({super.key});

  @override
  State<SignUpBankDetailsScreen> createState() => _SignUpBankDetailsScreenState();
}

class _SignUpBankDetailsScreenState extends State<SignUpBankDetailsScreen> {
  late final ContentProvider _contentProvider;
  late final LocalDataModel _localData;
  late final OnboardingModel _onboardingModel;
  late final UserProvider _userProvider;
  double _progress = 0;
  bool _processing = false;
  late FocusNode focusNode;

  final _banksController = StringTagController();
  final _businessCardController = StringTagController();
  final _personalCardController = StringTagController();
  double extraScrollingSpace = 0;
  final ScrollController _scrollController = ScrollController();

  final Set<String> _initialValue = {};
  final Set<String> _initialBusinessCardTypes = {};
  final Set<String> _initialPersonalCardTypes = {};

  @override
  void initState() {
    super.initState();

    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _localData.onboardingStage = OnboardingStage.mastercardBankDetails;
    // Below line is needed if user refresh this page to save groupIdent
    _onboardingModel.setGroupIdentName(GroupIdent.mastercard.name);

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (_contentProvider.mastercardBanks == null) {
        Provider.of<LocaleModel>(context, listen: false).getCurrentLanguageCode();
        final selectedLanguage =
            Provider.of<LocaleModel>(context, listen: false).getCurrentLanguageCode();
        await _contentProvider.findMastercardBanks(Enum$UiLanguage.values.byName(selectedLanguage));
        setState(() {});
      }
      _setInitialValues();
    });

    focusNode = FocusNode();
    _progress = _onboardingModel.getProgress(
      curStage: OnboardingStage.mastercardBankDetails,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );
  }

  _setInitialValues() {
    final mastercardMembership =
        ProfileUtility.fromFindId(_userProvider.myUser).mastercardGroupMembership;
    _initialValue.addAll(
      mastercardMembership?.bankTextIds
              ?.map(
                (bankId) =>
                    _contentProvider.mastercardBanks
                        ?.firstWhere((e) => (e.textId == bankId))
                        .displayName,
              )
              .nonNulls
              .toList() ??
          [],
    );

    _initialBusinessCardTypes.addAll(
      mastercardMembership?.smallBusinessCardTypes?.map((e) => e.title).toSet() ?? {},
    );

    _initialPersonalCardTypes.addAll(
      mastercardMembership?.personalCardTypes?.map((e) => e.title).toSet() ?? {},
    );
  }

  @override
  void dispose() {
    try {
      _banksController.dispose();
      _businessCardController.dispose();
      _personalCardController.dispose();
    } catch (_) {}
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SignUpTemplate(
      progress: _progress,
      title: AppLocale.current.mastercardBankDetailsTitle,
      subtitle: AppLocale.current.mastercardBankDetailsSubtitle,
      isMentee: _userProvider.myUser?.seeksHelp != false,
      processingState: _processing ? AsyncState.loading : AsyncState.ready,
      onboardingStage: OnboardingStage.phoneNumber,
      onboardingModel: _onboardingModel,
      extraScrollingSpace:
          context.theme.d.desktopOnboardingExtraScrollingSpace + extraScrollingSpace,
      focusNode: focusNode,
      body:
          _contentProvider.mastercardBanks == null
              ? const Loading()
              : Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _bankWidget(),
                  SizedBox(height: context.theme.d.paddingSmall),
                  _businessCardWidget(),
                  SizedBox(height: context.theme.d.paddingSmall),
                  _personalCardWidget(),
                  SizedBox(height: context.theme.d.paddingSmall),
                ],
              ),
      canGoNext: _canGoNext(),
      onGoNext: () => _goNextAction(),
      scrollController: _scrollController,
    );
  }

  _bankWidget() {
    return MultiSelectDropdown(
      editable: true,
      tagsController: _banksController,
      selectedOptions: _initialValue,
      options: _contentProvider.mastercardBanks?.map((e) => e.displayName).nonNulls.toList() ?? [],
      label: AppLocale.current.mastercardSelectBankTitle,
      notFoundMessage: AppLocale.current.multiSelectDropdownLanguageNotFoundMsg,
      onChange: () => setState(() {}),
      onDropdownSizeChanged: (double height) {
        setState(() {
          extraScrollingSpace = height;
        });
      },
    );
  }

  _businessCardWidget() {
    return BankCardTypesDropDown(
      tagsController: _businessCardController,
      selectedOptions: _initialBusinessCardTypes,
      label: AppLocale.current.mastercardBusinessCardTitle,
      onChange: () => setState(() {}),
      onDropdownSizeChanged: (double height) {
        setState(() {
          extraScrollingSpace = height;
        });
      },
    );
  }

  _personalCardWidget() {
    return BankCardTypesDropDown(
      tagsController: _personalCardController,
      label: AppLocale.current.mastercardPersonalCardTitle,
      selectedOptions: _initialPersonalCardTypes,
      onChange: () => setState(() {}),
      onDropdownSizeChanged: (double height) async {
        setState(() {
          extraScrollingSpace = height;
        });

        await Future.delayed(const Duration(milliseconds: 2));
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
        );
      },
    );
  }

  _goNextAction() async {
    setState(() => _processing = true);
    OnboardingStage? nextStage = _onboardingModel.getNextStage(
      curStage: OnboardingStage.mastercardBankDetails,
      isMentee: _userProvider.myUser?.seeksHelp != false,
    );

    final banksTextIds =
        _banksController.getTags
            ?.map(
              (t) => _contentProvider.mastercardBanks?.firstWhere((o) => o.displayName == t).textId,
            )
            .nonNulls
            .toList() ??
        [];

    final bankNames =
        banksTextIds
            .map(
              (textId) =>
                  _contentProvider.mastercardBanks
                      ?.firstWhere((bank) => bank.textId == textId)
                      .name,
            )
            .nonNulls
            .toList();

    final businessCardTypes =
        _businessCardController.getTags
            ?.map((t) => Enum$MastercardCardType.values.firstWhere((o) => o.title == t))
            .nonNulls
            .toList();

    final personalCardTypes =
        _personalCardController.getTags
            ?.map((t) => Enum$MastercardCardType.values.firstWhere((o) => o.title == t))
            .nonNulls
            .toList();

    OperationResult<void> updateGroupMembershipResponse = await _userProvider
        .updateMastercardGroupMembership(
          input: Input$MastercardGroupMembershipInput(
            userId: _localData.userId,
            bankNames: bankNames,
            bankTextIds: banksTextIds,
            smallBusinessCardTypes: businessCardTypes,
            personalCardTypes: personalCardTypes,
          ),
        );

    if (updateGroupMembershipResponse.gqlQueryResult.hasException) {
      _handleError(
        LoadObjectResult(
          operationException: updateGroupMembershipResponse.gqlQueryResult.exception,
        ),
      );
      return;
    }

    final result = await _userProvider.updateUser(
      input: Input$UserInput(id: _localData.userId, onboardingStage: nextStage?.name),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );

    if (result.hasError) {
      _handleError(result);
      return;
    }

    setState(() => _processing = false);
    if (mounted && nextStage != null) {
      _localData.onboardingStage = nextStage;
      context.push(_onboardingModel.getRouteFromStage(nextStage).path);
    }
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(context: context, exception: result.operationException);
    WidgetsBinding.instance.addPostFrameCallback((_) => setState(() => _processing = false));
  }

  _canGoNext() {
    return !_processing &&
        _banksController.getTags?.isEmpty == false &&
        _businessCardController.getTags?.isEmpty == false &&
        _personalCardController.getTags?.isEmpty == false;
  }
}
