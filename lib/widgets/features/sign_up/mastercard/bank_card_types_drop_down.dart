import 'package:flutter/material.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:textfield_tags/textfield_tags.dart';

import '../../../../constants/parts/micromentor_icons.dart';

class BankCardTypesDropDown extends StatefulWidget {
  final StringTagController tagsController;
  final Set<String>? selectedOptions;
  final String? label;
  final String? hint;
  final Function(double)? onDropdownSizeChanged;
  final Function? onChange;

  const BankCardTypesDropDown({
    super.key,
    required this.tagsController,
    this.selectedOptions,
    this.label,
    this.hint,
    this.onDropdownSizeChanged,
    this.onChange,
  });

  @override
  State<BankCardTypesDropDown> createState() => _BankCardTypesDropDownState();
}

class _BankCardTypesDropDownState extends State<BankCardTypesDropDown> {
  late LayerLink _layerLink;
  late bool _isDropdownVisible;
  late double _dropdownMaxWidth;
  late TextEditingController _textEditingController;
  late FocusNode _textFieldFocusNode;
  late ValueNotifier<List<String>> _filteredOptionNotifier;
  OverlayEntry? _dropdownOverlay;
  GlobalKey dropdownKey = GlobalKey();
  late ScrollController _scrollController;
  late List<String> options;

  @override
  void initState() {
    _layerLink = LayerLink();
    _isDropdownVisible = false;
    _textEditingController = TextEditingController();
    _textFieldFocusNode = FocusNode();
    _scrollController = ScrollController();
    options = _mastercardTypes();
    _filteredOptionNotifier = ValueNotifier<List<String>>(options);

    super.initState();
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    _dropdownOverlay?.dispose();
    _filteredOptionNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        _dropdownMaxWidth = constraints.maxWidth;
        return CompositedTransformTarget(
          link: _layerLink,
          child: TextFieldTags<String>(
            textEditingController: _textEditingController,
            focusNode: _textFieldFocusNode,
            textfieldTagsController: widget.tagsController,
            initialTags: widget.selectedOptions?.toList() ?? [],
            inputFieldBuilder: (context, inputFieldValues) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.label ?? '',
                    style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
                  ),
                  SizedBox(height: context.theme.d.paddingSmall),
                  TextField(
                    focusNode: inputFieldValues.focusNode,
                    scrollPadding: EdgeInsets.only(bottom: context.theme.d.dropdownHeight * 1.2),
                    controller: inputFieldValues.textEditingController,
                    textCapitalization: TextCapitalization.sentences,
                    onTap: () => _showDropdown(inputFieldValues, context),
                    readOnly: true,
                    decoration: InputDecoration(
                      isDense: true,
                      floatingLabelBehavior: FloatingLabelBehavior.always,
                      floatingLabelAlignment: FloatingLabelAlignment.start,
                      hintTextDirection: TextDirection.ltr,
                      hintText: inputFieldValues.tags.isEmpty == true ? widget.hint : null,
                      hintStyle: context.theme.textTheme.bodyLarge?.copyWith(
                        color: context.theme.colorScheme.outline,
                      ),
                      border: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: context.theme.colorScheme.outline,
                          width: context.theme.d.borderWidthMedium,
                        ),
                        borderRadius: BorderRadius.circular(
                          context.theme.d.profilePhotoRadiusMedium,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: context.theme.colorScheme.primary,
                          width: context.theme.d.borderWidthMedium,
                        ),
                        borderRadius: BorderRadius.circular(
                          context.theme.d.profilePhotoRadiusMedium,
                        ),
                      ),
                      suffixIcon:
                          inputFieldValues.error?.isNotEmpty == true
                              ? Icon(MicromentorIcons.error, color: context.theme.colorScheme.error)
                              : Icon(
                                MicromentorIcons.keyboardArrowDown,
                                color: context.theme.colorScheme.onSurface,
                              ),
                      errorText: inputFieldValues.error,
                      prefixIcon:
                          inputFieldValues.tags.isEmpty
                              ? Icon(
                                MicromentorIcons.search,
                                color: context.theme.colorScheme.onSurface,
                              )
                              : Padding(
                                padding: EdgeInsets.symmetric(
                                  vertical: context.theme.d.paddingXSmall,
                                  horizontal: context.theme.d.paddingSmall,
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      MicromentorIcons.search,
                                      color: context.theme.colorScheme.onSurface,
                                    ),
                                    Expanded(
                                      child: Wrap(
                                        runAlignment: WrapAlignment.center,
                                        children:
                                            (inputFieldValues.tags).map((tag) {
                                              return Container(
                                                decoration: BoxDecoration(
                                                  color: context.theme.colorScheme.secondary,
                                                  borderRadius: BorderRadius.circular(
                                                    context.theme.d.roundedRectRadiusSmall,
                                                  ),
                                                ),
                                                margin: EdgeInsets.all(
                                                  context.theme.d.paddingXxSmall,
                                                ),
                                                padding: EdgeInsets.symmetric(
                                                  horizontal: context.theme.d.paddingSmall,
                                                  vertical: context.theme.d.paddingXxSmall,
                                                ),
                                                child: InkWell(
                                                  onTap: () {
                                                    setState(() {
                                                      _onUnselectOption(tag);
                                                      _hideDropdown();
                                                      widget.onChange?.call();
                                                    });
                                                  },
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.spaceBetween,
                                                    mainAxisSize: MainAxisSize.min,
                                                    children: [
                                                      Text(
                                                        //widget.optionsTranslations.call(tag),
                                                        tag,
                                                        style: TextStyle(
                                                          color:
                                                              context.theme.colorScheme.onPrimary,
                                                          fontWeight: FontWeight.w500,
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: context.theme.d.paddingXxSmall,
                                                      ),
                                                      Icon(
                                                        MicromentorIcons.close,
                                                        size: context.theme.d.fontSizeLarge,
                                                        color: context.theme.colorScheme.onPrimary,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              );
                                            }).toList(),
                                      ),
                                    ),
                                    SizedBox(width: context.theme.d.fontSizeMediumLarge),
                                  ],
                                ),
                              ),
                    ),
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  List<String> _mastercardTypes() {
    return Enum$MastercardCardType.values
        .where((cardType) => cardType != Enum$MastercardCardType.$unknown)
        .map((e) => e.title)
        .nonNulls
        .toList();
  }

  void _showDropdown(InputFieldValues<String> inputFieldValues, BuildContext context) {
    _dropdownOverlay = OverlayEntry(
      maintainState: false,
      builder:
          (context) => GestureDetector(
            onTap: _hideDropdown,
            child: Container(
              color: Colors.transparent,
              child: CompositedTransformFollower(
                link: _layerLink,
                showWhenUnlinked: false,
                targetAnchor: Alignment.bottomCenter,
                followerAnchor: Alignment.topCenter,
                child: Align(
                  alignment: Alignment.topCenter,
                  child: Material(
                    elevation: context.theme.d.elevationLevel2,
                    color: context.colorScheme.onPrimary,
                    child: StatefulBuilder(
                      builder: (context, setState1) {
                        return Container(
                          decoration: BoxDecoration(
                            color: context.colorScheme.surfaceTint.withValues(alpha: 0.08),
                            borderRadius: BorderRadius.circular(
                              context.theme.d.roundedRectRadiusLarge,
                            ),
                          ),
                          constraints: BoxConstraints(
                            maxHeight: context.theme.d.dropdownHeight,
                            maxWidth: _dropdownMaxWidth,
                          ),
                          child: ValueListenableBuilder<List<String>>(
                            valueListenable: _filteredOptionNotifier,
                            key: dropdownKey,
                            builder: (context, value, child) {
                              return ScrollbarTheme(
                                data: ScrollbarThemeData(
                                  thumbColor: WidgetStateProperty.all<Color>(
                                    context.colorScheme.scrim,
                                  ),
                                ),
                                child: Scrollbar(
                                  controller: _scrollController,
                                  thumbVisibility: true,
                                  child: ListView.builder(
                                    controller: _scrollController,
                                    padding: EdgeInsets.zero,
                                    shrinkWrap: true,
                                    itemCount: value.length,
                                    itemBuilder: (context, i) {
                                      return InkWell(
                                        onTap: () {
                                          if (!_isEnabled(value[i])) return;
                                          setState(() {
                                            setState1(() {
                                              inputFieldValues.tags.contains(value[i])
                                                  ? _onUnselectOption(value[i])
                                                  : _onSelectOption(value[i], inputFieldValues);
                                            });
                                          });
                                          widget.onChange?.call();
                                        },
                                        child: ListTile(
                                          selected: inputFieldValues.tags.contains(value[i]),
                                          selectedColor: context.colorScheme.secondary,
                                          leading: Checkbox(
                                            value: inputFieldValues.tags.contains(value[i]),
                                            onChanged: (bool? isSelected) {
                                              if (!_isEnabled(value[i])) return;
                                              setState(() {
                                                setState1(() {
                                                  (isSelected ?? false)
                                                      ? _onSelectOption(value[i], inputFieldValues)
                                                      : _onUnselectOption(value[i]);
                                                });
                                              });
                                              widget.onChange?.call();
                                            },
                                          ),
                                          title: Text(
                                            value[i],
                                            style: TextStyle(
                                              color:
                                                  _isEnabled(value[i])
                                                      ? context.colorScheme.shadow
                                                      : context.colorScheme.outline,
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          ),
    );
    _isDropdownVisible = true;
    _dropdownOverlay?.addListener(() {
      _updateDropdownHeight(
        height: _isDropdownVisible ? context.theme.d.dropdownHeight : context.theme.d.zero,
      );
    });
    return Overlay.of(context, rootOverlay: true).insert(_dropdownOverlay!);
  }

  _onSelectOption(String selectedTag, InputFieldValues<String> inputFieldValues) {
    _textEditingController.clear();
    _updateNotifyFilter(List.of(options));

    if (!hasCardSelected() && !isCurrentTagCard(selectedTag)) {
      widget.tagsController.clearTags();
    }

    if (selectedTag.isNotEmpty) {
      widget.tagsController.onTagSubmitted(selectedTag);
    }

    if (!hasCardSelected() && !isCurrentTagCard(selectedTag)) {
      _hideDropdown();
    }
  }

  bool _isEnabled(String selectedTag) {
    if (widget.tagsController.getTags == null || widget.tagsController.getTags!.isEmpty) {
      return true;
    }
    return hasCardSelected() && isCurrentTagCard(selectedTag) ||
        !hasCardSelected() && !isCurrentTagCard(selectedTag);
  }

  bool hasCardSelected() {
    if (widget.tagsController.getTags == null || widget.tagsController.getTags!.isEmpty) {
      return false;
    }
    return (widget.tagsController.getTags?.contains(Enum$MastercardCardType.credit.title) == true ||
        widget.tagsController.getTags?.contains(Enum$MastercardCardType.debit.title) == true ||
        widget.tagsController.getTags?.contains(Enum$MastercardCardType.prepaid.title) == true);
  }

  isCurrentTagCard(String selectedTag) {
    return selectedTag == Enum$MastercardCardType.credit.title ||
        selectedTag == Enum$MastercardCardType.debit.title ||
        selectedTag == Enum$MastercardCardType.prepaid.title;
  }

  _onUnselectOption(String tag) {
    widget.tagsController.onTagRemoved(tag);
  }

  void _updateDropdownHeight({double? height}) {
    if (widget.onDropdownSizeChanged != null) {
      if (height == null) {
        if (dropdownKey.currentContext != null) {
          final RenderBox overlayBox = dropdownKey.currentContext!.findRenderObject() as RenderBox;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            widget.onDropdownSizeChanged?.call(overlayBox.size.height);
          });
        }
      } else {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          widget.onDropdownSizeChanged?.call(height);
        });
      }
    }
  }

  void _hideDropdown() {
    if (_isDropdownVisible) {
      _dropdownOverlay?.remove();
      setState(() {
        _isDropdownVisible = false;
      });
      _textFieldFocusNode.unfocus();
    }
  }

  _updateNotifyFilter(List<String> options) {
    _filteredOptionNotifier.value = options;
  }
}
