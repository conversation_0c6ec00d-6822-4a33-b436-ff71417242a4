import 'package:flutter/material.dart';

import '../../../../services/extensions.dart';

class SignUpIconFooter extends StatelessWidget {
  final IconData icon;
  final String text;

  const SignUpIconFooter({super.key, required this.icon, required this.text});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(icon, color: context.theme.colorScheme.outline),
        SizedBox(width: context.theme.d.paddingSmall),
        Expanded(
          child: Text(
            text,
            softWrap: true,
            style: context.theme.textTheme.bodySmall?.copyWith(
              color: context.theme.colorScheme.outline,
            ),
          ),
        ),
      ],
    );
  }
}
