import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/main/app.dart' as app;
import 'package:mm_flutter_app/router/app_router.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../models/onboarding_model.dart';
import '../../../../services/extensions.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class SignUpTemplate extends StatelessWidget {
  final double progress;
  final String title;
  final String? subtitle;
  final String? errorMessage;
  final Widget body;
  final bool showNavigationButtons;
  final bool canGoNext;
  final VoidCallback? onGoNext;
  final AsyncState processingState;
  final bool isMentee;
  final OnboardingStage onboardingStage;
  final OnboardingModel onboardingModel;
  final double? extraScrollingSpace;
  final FocusNode? focusNode;
  final bool showLanguageDropdown;
  final ScrollController? scrollController;

  const SignUpTemplate({
    super.key,
    required this.progress,
    required this.title,
    required this.isMentee,
    this.subtitle,
    this.errorMessage,
    required this.body,
    this.showNavigationButtons = true,
    required this.canGoNext,
    this.onGoNext,
    this.focusNode,
    this.extraScrollingSpace = 120,
    this.processingState = AsyncState.ready,
    required this.onboardingStage,
    required this.onboardingModel,
    this.showLanguageDropdown = false,
    this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    canGoBack() {
      final OnboardingStage? prevStage = onboardingModel.getPreviousStage(
        curStage: onboardingStage,
        isMentee: isMentee,
      );

      return prevStage != null && onboardingModel.getRouteFromStage(prevStage).path.isNotEmpty;
    }

    onGoBack() {
      FocusManager.instance.primaryFocus?.unfocus();

      final lastPath = AppRouter.getSecondLastSubroutePath(context) ?? '';
      if (onboardingStage == OnboardingStage.location || lastPath.contains(AppRoutes.signin.path)) {
        return AppUtility.onboardingExitDialog(context);
      }

      final OnboardingStage? prevStage = onboardingModel.getPreviousStage(
        curStage: onboardingStage,
        isMentee: isMentee,
      );

      if (prevStage == null) {
        return;
      }

      if (app.navigatorKey.currentState?.canPop() == true || context.canPop()) {
        context.pop();
        return;
      }

      final String path = onboardingModel.getRouteFromStage(prevStage).path;

      if (path.isNotEmpty != true) {
        return;
      }

      context.pushReplacement(path);
    }

    if (!(focusNode?.hasFocus ?? false)) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        focusNode?.requestFocus();
      });
    }

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? _) async {
        if (kIsWeb || didPop) return;
        if (onboardingStage == OnboardingStage.location) {
          return AppUtility.onboardingExitDialog(context);
        }
      },
      child: GestureDetector(
        onTap: () {
          FocusManager.instance.primaryFocus?.unfocus();
          focusNode?.requestFocus();
        },
        child: KeyboardListener(
          focusNode: focusNode ?? FocusNode(),
          onKeyEvent: focusNode != null ? _handleEnterKeyEvent : null,
          child: AppOnboardingTemplate(
            resizeToAvoidBottomInset: true,
            body: Stack(
              children: [
                SafeArea(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (canGoBack())
                        Container(
                          color: context.colorScheme.surface,
                          height: context.theme.d.radioButtonPadding,
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: backButton(
                              context,
                              onPressed: onGoBack,
                              color: context.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      wrapper(context, _content(context)),
                    ],
                  ),
                ),
                if (showNavigationButtons)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      color: context.colorScheme.surface,
                      padding: EdgeInsets.fromLTRB(
                        context.theme.d.paddingMedium,
                        context.theme.d.paddingMedium,
                        context.theme.d.paddingMedium,
                        context.theme.d.paddingXLarge,
                      ),
                      child: Container(
                        child: AppUtility.widgetForAsyncState(
                          state: processingState,
                          onReady:
                              () => CommonButton.primaryRoundedRectangle(
                                context: context,
                                title: AppLocale.current.actionNext,
                                buttonSize: ButtonSize.medium,
                                disabledColor: context.colorScheme.scrim,
                                onPressed:
                                    canGoNext
                                        ? () {
                                          //Hide keyboard if visible while moving o next screen
                                          FocusManager.instance.primaryFocus?.unfocus();
                                          onGoNext?.call();
                                        }
                                        : null,
                                isFullWidth: true,
                              ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _content(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingXLarge),
          color: context.colorScheme.surface,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (progress > 0)
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: context.colorScheme.outlineVariant.withValues(alpha: 0.3),
                  minHeight: context.theme.d.paddingSmall,
                  borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusSmall),
                ),
              SizedBox(height: context.theme.d.paddingMedium),
            ],
          ),
        ),
        wrapper(context, _scrollableContent(context)),
      ],
    );
  }

  Widget _scrollableContent(BuildContext context) {
    return SingleChildScrollView(
      controller: scrollController,
      padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingXLarge),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  title,
                  softWrap: true,
                  style: context.theme.textTheme.headlineLarge?.copyWith(
                    color: context.theme.colorScheme.secondary,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              SizedBox(width: context.theme.d.paddingMedium),
              if (showLanguageDropdown) const LanguageDropdown(),
            ],
          ),
          SizedBox(
            height: subtitle == null ? context.theme.d.paddingMedium : context.theme.d.paddingSmall,
          ),
          if (subtitle != null) ...[
            Text(
              subtitle!,
              style: context.theme.textTheme.bodyLarge?.copyWith(
                color: context.theme.colorScheme.onSurface,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(height: context.theme.d.paddingMedium),
          ],
          body,
          if (errorMessage != null) ...[
            SizedBox(height: context.theme.d.paddingMedium),
            Text(
              errorMessage!,
              style: context.theme.textTheme.bodyMedium?.copyWith(
                color: context.theme.colorScheme.error,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          SizedBox(height: extraScrollingSpace),
        ],
      ),
    );
  }

  Widget wrapper(BuildContext context, Widget content) {
    return AppUtility.displayDesktopUI(context)
        ? Flexible(child: content)
        : Expanded(child: content);
  }

  void _handleEnterKeyEvent(KeyEvent event) {
    if (!canGoNext) return;
    if (event is KeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.enter) {
        onGoNext?.call();
      }
    }
  }
}
