import 'package:flutter/material.dart';

import '../../../../constants/constants.dart';
import '../../../../services/extensions.dart';
import '../../../shared/common_button.dart';

class SignUpBottomButtons extends StatelessWidget {
  final String? goBackButtonText;
  final String goNextButtonText;
  final Function()? onGoBack;
  final Function()? onGoNext;

  const SignUpBottomButtons({
    super.key,
    this.goBackButtonText,
    required this.goNextButtonText,
    this.onGoBack,
    this.onGoNext,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        CommonButton.primaryRoundedRectangle(
          context: context,
          title: goNextButtonText,
          buttonSize: ButtonSize.medium,
          onPressed: onGoNext,
          isFullWidth: true,
        ),
        if (goBackButtonText != null) SizedBox(height: context.theme.d.paddingSmall),
        if (goBackButtonText != null)
          CommonButton.outlinedButton(
            context: context,
            buttonSize: ButtonSize.medium,
            title: '$goBackButtonText',
            onPressed: onGoBack,
            isFullWidth: true,
          ),
      ],
    );
  }
}
