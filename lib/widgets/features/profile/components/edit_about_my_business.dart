import 'package:flutter/material.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/extensions.dart';
import '../../../widgets.dart';

class _PhotoWidget extends StatelessWidget {
  final String? imageUrl;
  final double size;

  const _PhotoWidget({this.imageUrl, required this.size});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(context.theme.d.paddingXxSmall),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusSmall),
        child: Container(
          width: size,
          height: size,
          color: context.theme.colorScheme.secondaryContainer,
          child:
              (imageUrl == null)
                  ? Align(
                    alignment: Alignment.center,
                    child: Icon(
                      MicromentorIcons.addCircleOutlined,
                      color: context.theme.colorScheme.onSecondaryContainer,
                    ),
                  )
                  : Image(image: NetworkImage(imageUrl!), height: size, fit: BoxFit.cover),
        ),
      ),
    );
  }
}

class EditProfileAboutMyBusiness extends StatelessWidget {
  final User myUser;
  final Function(EditProfileOptions)? onTapAction;

  const EditProfileAboutMyBusiness({super.key, required this.myUser, this.onTapAction});

  Widget _createPhotosSection(BuildContext context, String title, List<String> imageUrls) {
    return ListTile(
      title: Text(
        title,
        style: context.theme.textTheme.titleMedium!.copyWith(
          color: context.theme.colorScheme.primary,
        ),
      ),
      subtitle: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          _PhotoWidget(
            size: context.theme.d.dropdownHeight,
            imageUrl: (imageUrls.isNotEmpty) ? imageUrls[0] : null,
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _PhotoWidget(
                size: context.theme.d.imageSizeXLarge,
                imageUrl: (imageUrls.length > 1) ? imageUrls[1] : null,
              ),
              _PhotoWidget(
                size: context.theme.d.imageSizeXLarge,
                imageUrl: (imageUrls.length > 2) ? imageUrls[2] : null,
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final profileUtils = ProfileUtility.fromFindId(myUser);
    final List<String> expertises = profileUtils.expertises;
    expertises.sort();

    final topThreeTopics = expertises.take(Limits.profileExpertiseMaxSize);
    final additionalTopics =
        (expertises.length > Limits.profileExpertiseMaxSize)
            ? expertises.sublist(Limits.profileExpertiseMaxSize)
            : [];

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        HeadingWithListTile(
          heading: AppLocale.current.profileEditMainBusinessHeader,
          title: AppLocale.current.profileEditMainBusinessNameSection,
          subtitle: profileUtils.companyName,
          onTap: () => onTapAction?.call(EditProfileOptions.companyName),
        ),
        HeadingWithListTile(
          title: AppLocale.current.profileEditMainBusinessWebsiteSection,
          subtitle: profileUtils.companyWebsite,
          onTap: () => onTapAction?.call(EditProfileOptions.companyWebsite),
        ),
        HeadingWithListTile(
          title: AppLocale.current.profileEditMainBusinessLocationSection,
          subtitle: profileUtils.companyLocation,
          onTap: () => onTapAction?.call(EditProfileOptions.companyLocation),
        ),
        HeadingWithListTile(
          title: AppLocale.current.profileEditMainBusinessIndustrySection,
          subtitle: profileUtils.industryName,
          onTap: () => onTapAction?.call(EditProfileOptions.industryName),
        ),
        HeadingWithListTile(
          title: AppLocale.current.profileEditMainBusinessStageSection,
          subtitle: profileUtils.companyStage,
          onTap: () => onTapAction?.call(EditProfileOptions.companyStage),
        ),
        ChipsListTileWidget(
          title: AppLocale.current.profileEditMainBusinessTopicsSection,
          chipsList: topThreeTopics.toList(),
          isTernaryBgColor: true,
          onTap: () => onTapAction?.call(EditProfileOptions.expertiseTop),
        ),
        if (StaticAppFeatures.additionalExpertises)
          ChipsListTileWidget(
            title: AppLocale.current.profileEditMainBusinessTopicsAdditionalHint,
            chipsList: additionalTopics.map((e) => e.toString()).toList(),
            onTap: () => onTapAction?.call(EditProfileOptions.expertiseAdditional),
          ),
        if (StaticAppFeatures.businessImages) const SectionDivider(),
        if (StaticAppFeatures.businessImages)
          _createPhotosSection(
            context,
            AppLocale.current.profileEditMainBusinessPhotosSection,
            profileUtils.imageUrls,
          ),
        HeadingWithListTile(
          title: AppLocale.current.profileEditMainBusinessMotivationSection,
          subtitle: profileUtils.reasonForBusiness,
          onTap: () => onTapAction?.call(EditProfileOptions.menteeBusinessReason),
        ),
        HeadingWithListTile(
          title: AppLocale.current.businessChallengeTitle,
          subtitle: profileUtils.businessChallenges,
          onTap: () => onTapAction?.call(EditProfileOptions.businessChallenge),
        ),
        HeadingWithListTile(
          title: AppLocale.current.howCanMentorSupportMeTitle,
          subtitle: profileUtils.howCanMentorSupportMe,
          onTap: () => onTapAction?.call(EditProfileOptions.howCanMentorSupportMe),
        ),
      ],
    );
  }
}
