import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../../../../constants/constants.dart';
import '../../../../services/extensions.dart';

class ProfileChip extends StatelessWidget {
  final String text;
  final IconData? icon;
  final ChipBackgroundColor chipBackgroundColor;

  const ProfileChip({
    super.key,
    required this.text,
    this.icon,
    this.chipBackgroundColor = ChipBackgroundColor.secondary,
  });

  @override
  Widget build(BuildContext context) {
    var (bgColor, textIconColor) = _getChipBackgroundColor(context, this.chipBackgroundColor);
    LinearGradient chipBackgroundColor = bgColor;
    Color iconColor = textIconColor;

    TextStyle? chipTextStyle = context.theme.textTheme.labelMedium?.copyWith(color: textIconColor);

    double iconSize = context.theme.d.paddingMedium;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: context.theme.d.paddingSmall,
        vertical: context.theme.d.paddingXxSmall,
      ),
      decoration: BoxDecoration(
        gradient: chipBackgroundColor,
        borderRadius: BorderRadius.circular(context.theme.d.fontSizeMediumLarge),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (icon != null) SizedBox(child: Icon(icon, color: iconColor, size: iconSize)),
          SizedBox(width: context.theme.d.boxWidth),
          Text(text, style: chipTextStyle, overflow: TextOverflow.ellipsis),
        ],
      ),
    );
  }

  (LinearGradient bgColor, Color textIconColor) _getChipBackgroundColor(
    BuildContext context,
    ChipBackgroundColor chipBackgroundColor,
  ) {
    switch (chipBackgroundColor) {
      case ChipBackgroundColor.primary:
        return (
          LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.bottomRight,
            colors: [
              context.theme.colorScheme.onTertiary,
              context.theme.colorScheme.inversePrimary,
            ],
          ),
          context.theme.colorScheme.surface,
        );
      case ChipBackgroundColor.secondary:
        return (
          LinearGradient(
            colors: [
              context.theme.colorScheme.secondaryContainer,
              context.theme.colorScheme.tertiaryContainer,
            ],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          context.theme.colorScheme.onSecondaryContainer,
        );
      case ChipBackgroundColor.tertiary:
        return (
          LinearGradient(
            colors: [
              context.theme.colorScheme.tertiaryContainer,
              context.theme.colorScheme.tertiaryContainer,
            ],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          context.theme.colorScheme.onTertiaryContainer,
        );
    }
  }
}
