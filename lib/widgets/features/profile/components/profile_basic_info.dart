import 'package:flutter/material.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';

import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../widgets.dart';

class ProfileBasicInfo extends StatelessWidget {
  final User user;
  final bool isMyProfile;
  final MessageDirection? invitationDirection;
  final Widget? button;

  const ProfileBasicInfo({
    super.key,
    required this.user,
    required this.isMyProfile,
    required this.invitationDirection,
    this.button,
  });

  @override
  Widget build(BuildContext context) {
    final profileUtils = ProfileUtility.fromFindId(user);
    bool isDesktop = AppUtility.displayDesktopUI(context);

    return Column(
      children: [
        Container(
          margin:
              isDesktop
                  ? EdgeInsets.only(
                    right: context.theme.d.paddingSmall,
                    left: context.theme.d.paddingMedium,
                  )
                  : EdgeInsets.symmetric(
                    horizontal: context.theme.d.paddingMedium,
                    vertical: context.theme.d.paddingSmall,
                  ),
          decoration: BoxDecoration(
            color: context.colorScheme.onPrimary,
            border: Border.all(
              width: context.theme.d.borderWidthRegular,
              color: context.colorScheme.outline,
            ),
            borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: context.theme.d.paddingMedium,
                  vertical: context.theme.d.paddingSmall,
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Column(
                      children: [
                        ProfileImageWidget(
                          avatarUrl: user.avatarUrl,
                          isMentor: profileUtils.isMentor,
                        ),
                        if (user.pronounsDisplay.isNotEmpty)
                          Padding(
                            padding: EdgeInsets.only(top: context.theme.d.paddingXxSmall),
                            child: Text(
                              user.pronounsDisplay,
                              style: context.theme.textTheme.labelSmall?.copyWith(
                                color: context.theme.colorScheme.onSurface,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                      ],
                    ),
                    SizedBox(width: context.theme.d.paddingMedium),
                    _createNameAndBadges(context, profileUtils, isDesktop),
                  ],
                ),
              ),
              if (isDesktop && button != null)
                Padding(
                  padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingMedium),
                  child: button!,
                ),
            ],
          ),
        ),
        SizedBox(height: context.theme.d.paddingXxSmall),
        if (user.isOnVacation == true)
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _createVacationBanner(context.theme),
              SizedBox(height: context.theme.d.paddingSmall),
            ],
          ),
      ],
    );
  }

  Widget _createNameAndBadges(BuildContext context, ProfileUtility profileUtils, bool isDesktop) {
    return Expanded(
      child: Stack(
        children: [
          if (!isMyProfile)
            Align(alignment: Alignment.topRight, child: _popupMenu(context, isDesktop)),
          profileUtils.profileInfoSection(context, isMyProfile),
        ],
      ),
    );
  }

  _popupMenu(BuildContext context, bool isDesktop) {
    if (invitationDirection == MessageDirection.received || !isDesktop) {
      return const SizedBox();
    }

    return UserActionPopupMenu(
      userFullName: '${user.firstName}',
      userId: user.id,
      includeBlockUserOption: true,
    );
  }

  Widget _createVacationBanner(ThemeData theme) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: theme.d.paddingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.secondaryContainer,
        borderRadius: BorderRadius.circular(theme.d.roundedRectRadiusSmall),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: theme.d.paddingMedium,
        vertical: theme.d.paddingSmall,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(
            MicromentorIcons.flightTakeoffRounded,
            size: theme.d.iconSizeLarge,
            color: theme.colorScheme.onSecondaryContainer,
          ),
          SizedBox(width: theme.d.paddingSmall),
          Flexible(
            child: Text(
              AppLocale.current.profileViewVacationBanner,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSecondaryContainer,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
