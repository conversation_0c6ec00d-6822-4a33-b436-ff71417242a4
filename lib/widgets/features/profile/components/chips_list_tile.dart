import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:mm_flutter_app/widgets/shared/common_button.dart';

import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../services/extensions.dart';

class ChipsListTileWidget extends StatelessWidget {
  final String? heading;
  final String? title;
  final String? subtitle;
  final List<String> chipsList;
  final bool isTernaryBgColor;
  final IconData? icon;
  final Function()? onTap;

  const ChipsListTileWidget({
    super.key,
    this.heading,
    this.title,
    this.subtitle,
    this.chipsList = const [],
    this.onTap,
    this.isTernaryBgColor = false,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    bool isDesktopView = AppUtility.displayDesktopUI(context);

    return Padding(
      padding: EdgeInsets.only(
        top: context.theme.d.paddingSmall,
        left: context.theme.d.paddingMedium,
        right: context.theme.d.paddingMedium,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          if (heading?.isNotEmpty == true)
            Padding(
              padding: EdgeInsets.symmetric(vertical: context.theme.d.chipPadding),
              child: Text(
                heading ?? '',
                style: context.theme.textTheme.titleLarge?.copyWith(
                  fontSize: context.theme.d.fontSizeMediumLarge,
                  color: context.colorScheme.surfaceBright,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ListTile(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusLarge),
            ),
            tileColor: context.colorScheme.onSurface.withValues(alpha: 0.07),
            contentPadding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
            horizontalTitleGap: context.theme.d.paddingSmall,
            dense: true,
            title: _titleWidget(context),
            trailing:
                isDesktopView
                    ? CommonButton.outlinedButton(
                      context: context,
                      color: context.colorScheme.onPrimary,
                      title: AppLocale.current.inboxMessagesActionEdit,
                      onPressed: onTap,
                    )
                    : IconButton(
                      icon: const Icon(MicromentorIcons.chevronRight),
                      onPressed: onTap,
                      color: context.theme.colorScheme.onSurface,
                    ),
            subtitle: _chipsContainer(context, ternaryBgColor: isTernaryBgColor, icon: icon),
            onTap: isDesktopView ? null : onTap,
          ),
        ],
      ),
    );
  }

  _titleWidget(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title?.isNotEmpty ?? false)
          Text(
            title ?? '',
            style: context.theme.textTheme.bodyLarge?.copyWith(
              color: context.theme.colorScheme.surfaceBright,
              fontWeight: FontWeight.w700,
            ),
          ),
        if (subtitle?.isNotEmpty ?? false)
          Padding(
            padding: EdgeInsets.only(bottom: context.theme.d.paddingXSmall),
            child: Text(
              subtitle ?? '',
              style: context.theme.textTheme.bodyMedium?.copyWith(
                color: context.theme.colorScheme.secondary,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
      ],
    );
  }

  Widget _chipsContainer(BuildContext context, {bool ternaryBgColor = false, IconData? icon}) {
    if (chipsList.isEmpty) return const SizedBox();
    final chips =
        chipsList
            .map(
              (e) => Text(
                e,
                style: context.theme.textTheme.bodyLarge?.copyWith(
                  color: context.theme.colorScheme.onSurface,
                ),
              ),
            )
            .toList();
    return Padding(
      padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingSmall),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children:
            chips.map((chip) {
              return chip;
            }).toList(),
      ),
    );
  }
}
