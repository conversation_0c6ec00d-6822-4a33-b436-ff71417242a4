import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../services/extensions.dart';
import '../../../widgets.dart';

class HowCanIHelpSection extends StatelessWidget {
  final List<String> expertises;
  final List<String> industries;
  final List<String> mentoringPreferences;
  final String? reasonsForMentoring;
  final String? howICanHelpMentees;

  const HowCanIHelpSection({
    super.key,
    this.expertises = const [],
    this.industries = const [],
    this.mentoringPreferences = const [],
    this.reasonsForMentoring,
    this.howICanHelpMentees,
  });

  Widget _createChipsSection(BuildContext context, String title, List<BigProfileChip> chips) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: context.theme.textTheme.titleMedium?.copyWith(
            color: context.theme.colorScheme.surfaceBright,
            fontWeight: FontWeight.w700,
          ),
        ),
        SizedBox(height: context.theme.d.paddingXxSmall),
        Wrap(
          spacing: context.theme.d.paddingXSmall,
          runSpacing: context.theme.d.paddingXSmall,
          children: chips.map((chip) => chip).toList(),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: context.theme.d.paddingMedium,
            vertical: context.theme.d.paddingSmall,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppLocale.current.profileViewHowCanIHelp,
                style: context.theme.textTheme.titleMedium!.copyWith(
                  color: context.theme.colorScheme.surfaceBright,
                  fontWeight: FontWeight.w700,
                ),
              ),
              SizedBox(height: context.theme.d.paddingSmall),
              if (expertises.isNotEmpty) ...[
                _createChipsSection(
                  context,
                  AppLocale.current.profileViewHowCanIHelpExpertises,
                  expertises
                      .take(Limits.expertisesProfileViewMaxChips)
                      .map((e) => BigProfileChip(text: e))
                      .toList(),
                ),
                SizedBox(height: context.theme.d.paddingSmall),
              ],
              if (industries.isNotEmpty) ...[
                _createChipsSection(
                  context,
                  AppLocale.current.profileViewHowCanIHelpIndustries,
                  industries
                      .map(
                        (e) =>
                            BigProfileChip(text: e, icon: MicromentorIcons.businessCenterOutlined),
                      )
                      .toList(),
                ),
                SizedBox(height: context.theme.d.paddingSmall),
              ],
              if (mentoringPreferences.isNotEmpty) ...[
                _createChipsSection(
                  context,
                  AppLocale.current.profileViewHowCanIHelpPreferences,
                  mentoringPreferences.map((e) => BigProfileChip(text: e)).toList(),
                ),
                SizedBox(height: context.theme.d.paddingSmall),
              ],
              if (reasonsForMentoring != null)
                ExpandableContainer(
                  title: AppLocale.current.profileViewHowCanIHelpWhyIMentor,
                  description: reasonsForMentoring ?? '',
                ),
              if (howICanHelpMentees?.isNotEmpty == true)
                ExpandableContainer(
                  title: AppLocale.current.howYouCanHelpTitle,
                  description: howICanHelpMentees ?? '',
                ),
            ],
          ),
        ),
      ],
    );
  }
}
