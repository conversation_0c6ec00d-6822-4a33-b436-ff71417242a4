import 'package:flutter/material.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/widgets/features/profile/components/user_role_label.dart';

import '../../../../services/extensions.dart';

class ProfileImageWidget extends StatelessWidget {
  final String? avatarUrl;
  final double? size;
  final bool? isMentor;

  const ProfileImageWidget({super.key, this.avatarUrl, this.size, this.isMentor});

  @override
  Widget build(BuildContext context) {
    final Widget profilePhoto = Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Container(
          margin: EdgeInsets.only(bottom: context.theme.d.paddingXxSmall),
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: context.colorScheme.outline),
          ),
          child:
              avatarUrl != null && avatarUrl!.isNotEmpty
                  ? _networkImage(context)
                  : _blankAvatar(context),
        ),
        if (isMentor != null)
          UserRoleGradientWidget(
            label:
                isMentor == true
                    ? AppLocale.current.profileViewUserTypeMentor
                    : AppLocale.current.profileViewUserTypeEntrepreneur,
            isMentor: isMentor!,
          ),
      ],
    );

    return profilePhoto;
  }

  _networkImage(BuildContext context) {
    return Image.network(
      avatarUrl!,
      height: size ?? context.theme.d.imageSizeXLarge,
      width: size ?? context.theme.d.imageSizeXLarge,
      loadingBuilder: (BuildContext context, Widget child, ImageChunkEvent? loadingProgress) {
        if (loadingProgress == null) return child;

        return Stack(
          alignment: AlignmentDirectional.center,
          children: [_blankAvatar(context), const CircularProgressIndicator()],
        );
      },
      errorBuilder: (context, error, stackTrace) {
        return _blankAvatar(context);
      },
      fit: BoxFit.cover,
    );
  }

  Widget _blankAvatar(BuildContext context) {
    return Image(
      height: size ?? context.theme.d.imageSizeXLarge,
      width: size ?? context.theme.d.imageSizeXLarge,
      image: const AssetImage(Assets.blankAvatar),
      fit: BoxFit.cover,
    );
  }
}
