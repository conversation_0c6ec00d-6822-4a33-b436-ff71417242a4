import 'package:flutter/material.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/extensions.dart';
import '../../../widgets.dart';

class EditProfileAboutMe extends StatelessWidget {
  final User myUser;
  final Function(EditProfileOptions)? onTapAction;

  const EditProfileAboutMe({super.key, required this.myUser, this.onTapAction});

  @override
  Widget build(BuildContext context) {
    final profileUtils = ProfileUtility.fromFindId(myUser);

    return Column(
      children: [
        HeadingWithListTile(
          heading: AppLocale.current.profileEditMainAboutHeader,
          title: AppLocale.current.profileEditMainAboutPronounsSection,
          subtitle: myUser.pronounsDisplay,
          onTap: () => onTapAction?.call(EditProfileOptions.pronouns),
        ),
        HeadingWithListTile(
          title:
              profileUtils.linkedInUrl.isNotEmpty
                  ? AppLocale.current.profileEditMainAboutLinkedInConfirmationSection
                  : AppLocale.current.profileEditMainAboutLinkedInPromptSection,
          leadingWidget:
              profileUtils.linkedInUrl.isNotEmpty
                  ? Icon(
                    MicromentorIcons.checkCircleOutline,
                    color: context.theme.colorScheme.primary,
                    size: context.theme.d.iconSizeLarge,
                  )
                  : null,
          onTap: () => onTapAction?.call(EditProfileOptions.linkedin),
        ),
        if (context.theme.appFeatures.countryOfResidence)
          HeadingWithListTile(
            title: AppLocale.current.profileEditMainAboutCurrentCountrySection,
            subtitle: profileUtils.livesInLocation(context),
            onTap: () => onTapAction?.call(EditProfileOptions.country),
          ),
        if (context.theme.appFeatures.cityOfOrigin)
          HeadingWithListTile(
            title: AppLocale.current.profileEditMainAboutCurrentLocationSection,
            subtitle: profileUtils.livesInLocation(context),
            onTap: () => onTapAction?.call(EditProfileOptions.country),
          ),
        if (context.theme.appFeatures.regionOfOrigin)
          HeadingWithListTile(
            title: AppLocale.current.profileEditMainAboutOriginLocationSection,
            subtitle: profileUtils.fromLocation(context),
            onTap: () => onTapAction?.call(EditProfileOptions.originLocation),
          ),
        HeadingWithListTile(
          title: AppLocale.current.profileEditMainAboutLanguagePreferredSection,
          subtitle: profileUtils.preferredLanguage,
          onTap: () => onTapAction?.call(EditProfileOptions.prefferedLanguages),
        ),
        HeadingWithListTile(
          title: AppLocale.current.profileEditMainAboutLanguageOthersSection,
          subtitle: profileUtils.spokenLanguages(),
          onTap: () => onTapAction?.call(EditProfileOptions.otherLanguages),
        ),
      ],
    );
  }
}
