import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../services/extensions.dart';

class ExperienceAndEducation extends StatelessWidget {
  final List<ExperienceInput> experience;
  final List<EducationInput> education;
  final ThemeData theme;

  const ExperienceAndEducation({
    super.key,
    this.experience = const [],
    this.education = const [],
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(context.theme.d.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (experience.isNotEmpty) _Experience(experience: experience),
          if (education.isNotEmpty) SizedBox(height: context.theme.d.paddingSmall),
          if (education.isNotEmpty) _Education(education: education),
        ],
      ),
    );
  }
}

class _Experience extends StatelessWidget {
  final List<ExperienceInput> experience;
  const _Experience({required this.experience});

  @override
  Widget build(BuildContext context) {
    final bodySmallOnSurface = context.theme.textTheme.bodyMedium?.copyWith(
      color: context.theme.colorScheme.onSurface,
      fontWeight: FontWeight.w500,
    );
    final bodySmallSecondary = context.theme.textTheme.bodyMedium?.copyWith(
      color: context.theme.colorScheme.onSurface,
      fontWeight: FontWeight.w400,
    );
    final items =
        experience
            .map((exp) {
              final location = [
                exp.city,
                exp.state,
                exp.country,
              ].nonNulls.join(AppLocale.current.listSeparator);
              return [
                Text(
                  exp.position,
                  style: bodySmallOnSurface,
                  maxLines: 2,
                  softWrap: true,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  exp.companyName,
                  style: bodySmallOnSurface?.copyWith(fontWeight: FontWeight.bold),
                ),
                if (exp.dateRange(AppLocale.current.datePresent).isNotEmpty)
                  Text(
                    exp.timeRange.isNotEmpty
                        ? '(${exp.dateRange(AppLocale.current.datePresent)}) · ${exp.timeRange}'
                        : exp.dateRange(AppLocale.current.datePresent),
                    style: bodySmallSecondary,
                    maxLines: 2,
                    softWrap: true,
                    overflow: TextOverflow.ellipsis,
                  ),
                if (location.isNotEmpty)
                  Text(
                    location,
                    style: bodySmallSecondary,
                    maxLines: 2,
                    softWrap: true,
                    overflow: TextOverflow.ellipsis,
                  ),
                if (exp.companyUrl?.isNotEmpty == true)
                  InkWell(
                    onTap: () => launchUrl(Uri.parse(exp.companyUrl!)),
                    child: Text(
                      exp.companyUrl!,
                      style: bodySmallOnSurface?.copyWith(
                        decoration: TextDecoration.underline,
                        color: context.theme.colorScheme.primary,
                      ),
                      maxLines: 2,
                      softWrap: true,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                if (experience.indexOf(exp) != (experience.length - 1))
                  SizedBox(height: context.theme.d.paddingMedium),
              ];
            })
            .expand((i) => i)
            .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocale.current.profileViewExperience,
          style: context.theme.textTheme.titleMedium?.copyWith(
            color: context.colorScheme.surfaceBright,
            fontWeight: FontWeight.w700,
          ),
        ),
        SizedBox(height: context.theme.d.paddingSmall),
        ...items,
      ],
    );
  }
}

class _Education extends StatelessWidget {
  final List<EducationInput> education;

  const _Education({required this.education});

  @override
  Widget build(BuildContext context) {
    final bodyMediumOnSurface = context.theme.textTheme.bodyMedium?.copyWith(
      color: context.theme.colorScheme.onSurface,
      fontWeight: FontWeight.w400,
    );
    final items =
        education
            .map(
              (edu) => [
                Row(
                  children: [
                    Text(
                      edu.schoolName,
                      style: bodyMediumOnSurface?.copyWith(fontWeight: FontWeight.bold),
                      softWrap: true,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const Text(' '),
                    Text(
                      edu.dateRange(AppLocale.current.datePresent),
                      style: bodyMediumOnSurface,
                      softWrap: true,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
                Text(
                  [edu.title, edu.major].nonNulls.join(AppLocale.current.listSeparator),
                  style: bodyMediumOnSurface,
                  maxLines: 2,
                  softWrap: true,
                  overflow: TextOverflow.ellipsis,
                ),
                if (education.indexOf(edu) != (education.length - 1))
                  SizedBox(height: context.theme.d.paddingMedium),
              ],
            )
            .expand((i) => i)
            .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocale.current.profileViewEducation,
          style: context.theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w700,
            color: context.colorScheme.surfaceBright,
          ),
        ),
        SizedBox(height: context.theme.d.paddingMedium),
        ...items,
      ],
    );
  }
}

class ExperienceInput {
  final String position;
  final String companyName;
  final DateTime? start;
  final DateTime? end;
  final String? city;
  final String? state;
  final String? country;
  final String? companyUrl;

  const ExperienceInput({
    required this.position,
    required this.companyName,
    this.start,
    this.end,
    this.city,
    this.state,
    this.country,
    this.companyUrl,
  });

  String get timeRange {
    if (start == null) return '';
    final timeDiffDays = (end ?? DateTime.now()).difference(start!).inDays;
    final timeDiffYears = (timeDiffDays / 365).floor();
    final timeDiffMonths = ((timeDiffDays - timeDiffYears * 365) / 30).floor();

    final timeDiffYearsStr = stringifyDuration(timeDiffYears, 'year');
    final timeDiffMonthsStr = stringifyDuration(timeDiffMonths, 'month');

    return '$timeDiffYearsStr $timeDiffMonthsStr';
  }

  String dateRange(String locPresent) =>
      start?.year != null ? '${start?.year} - ${end?.year ?? locPresent}' : '';

  String stringifyDuration(int duration, String name) {
    switch (duration) {
      case 0:
        return '';
      case 1:
        return '1 $name';
      default:
        return '$duration ${name}s';
    }
  }
}

class EducationInput {
  final String schoolName;
  final DateTime? start;
  final DateTime? end;
  final String? title;
  final String? major;

  const EducationInput({
    required this.schoolName,
    required this.start,
    required this.end,
    required this.title,
    this.major,
  });

  String dateRange(String locPresent) =>
      start?.year != null ? '${start?.year} - ${end?.year ?? locPresent}' : '';
}
