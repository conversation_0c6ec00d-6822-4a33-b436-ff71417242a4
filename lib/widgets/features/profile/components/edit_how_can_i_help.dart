import 'package:flutter/material.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../featured_widgets.dart';

class EditProfileHowCanIHelp extends StatelessWidget {
  final User myUser;
  final Function(EditProfileOptions)? onTapAction;

  const EditProfileHowCanIHelp({super.key, required this.myUser, this.onTapAction});

  @override
  Widget build(BuildContext context) {
    final profileUtils = ProfileUtility.fromFindId(myUser);
    final List<String> expertises = profileUtils.expertises;
    expertises.sort();

    final topThreeExpertises = expertises.take(Limits.profileExpertiseMaxSize);
    final additionalExpertises =
        (expertises.length > Limits.profileExpertiseMaxSize)
            ? expertises.sublist(Limits.profileExpertiseMaxSize)
            : [];

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ChipsListTileWidget(
          heading: AppLocale.current.profileEditMainMentorHeader,
          title: AppLocale.current.profileEditMainMentorExpertisesSection,
          chipsList: topThreeExpertises.map((e) => e.toString()).toList(),
          isTernaryBgColor: true,
          onTap: () => onTapAction?.call(EditProfileOptions.expertiseTop),
        ),
        if (StaticAppFeatures.additionalExpertises)
          ChipsListTileWidget(
            title: AppLocale.current.profileEditMainMentorExpertisesAdditionalHint,
            chipsList: additionalExpertises.map((e) => e.toString()).toList(),
            onTap: () => onTapAction?.call(EditProfileOptions.expertiseAdditional),
          ),
        ChipsListTileWidget(
          title: AppLocale.current.profileEditMainMentorIndustriesSection,
          chipsList: profileUtils.mentorIndustries,
          icon: MicromentorIcons.businessCenterOutlined,
          onTap: () => onTapAction?.call(EditProfileOptions.industries),
        ),
        if (StaticAppFeatures.mentoringPreferences)
          ChipsListTileWidget(
            title: AppLocale.current.profileEditMainMentorPreferencesSection,
            chipsList: profileUtils.mentoringPreferences,
            onTap: () => onTapAction?.call(EditProfileOptions.mentoringPreference),
          ),
        HeadingWithListTile(
          title: AppLocale.current.profileEditMainMentorWhyIMentor,
          subtitle: profileUtils.reasonsForMentoring,
          onTap: () => onTapAction?.call(EditProfileOptions.mentoringReason),
        ),
        HeadingWithListTile(
          title: AppLocale.current.howYouCanHelpTitle,
          subtitle: profileUtils.howICanHelpMentees,
          onTap: () => onTapAction?.call(EditProfileOptions.howICanHelpMentees),
        ),
      ],
    );
  }
}
