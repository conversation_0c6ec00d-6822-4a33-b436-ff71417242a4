import 'package:flutter/material.dart';

import '../../../../services/extensions.dart';
import 'profile_chip.dart';

class ProfileChipPadded extends ProfileChip {
  const ProfileChipPadded({super.key, required super.text, super.icon});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(right: context.theme.d.paddingXSmall),
      child: super.build(context),
    );
  }
}
