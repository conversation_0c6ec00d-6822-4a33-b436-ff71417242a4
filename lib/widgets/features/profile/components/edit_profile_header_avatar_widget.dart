import 'package:flutter/material.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';

import '../../../widgets.dart';

class EditProfileHeaderAvatarWidget extends StatelessWidget {
  final User myUser;

  const EditProfileHeaderAvatarWidget({super.key, required this.myUser});

  @override
  Widget build(BuildContext context) {
    return AppUtility.displayDesktopUI(context) ? _desktopView(context) : _mobileView(context);
  }

  openFilterScreen(BuildContext context) {
    !AppUtility.displayDesktopUI(context)
        ? showModalBottomSheet(
          elevation: context.theme.d.elevationLevel3,
          isScrollControlled: true,
          context: context,
          useSafeArea: true,
          backgroundColor: context.colorScheme.onPrimary,
          constraints: BoxConstraints(maxHeight: context.mediaQuerySize.height - 150),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadiusDirectional.vertical(
              top: Radius.circular(context.theme.d.sheetRadius),
            ),
          ),
          builder: (context) => const UpdateAvatarScreen.mobile(),
        )
        : showDialog(
          context: context,
          builder: (dialogContext) {
            return DialogTemplate.withCustomContent(
              showTitleInCenter: false,
              title: AppLocale.current.editPicture,
              showButtons: false,
              content: const UpdateAvatarScreen.desktop(),
            );
          },
        );
  }

  Widget _desktopView(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(
        context.theme.d.paddingLarge,
        context.theme.d.paddingSmall,
        context.theme.d.paddingSmall,
        context.theme.d.paddingMedium,
      ),
      padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingXLarge),
      decoration: BoxDecoration(
        border: Border.all(
          width: context.theme.d.borderWidthRegular,
          color: context.colorScheme.outline,
        ),
        borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXLarge),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            AppUtility.getUserFullName(myUser.firstName, myUser.lastName),
            style: context.textTheme.titleLarge?.copyWith(
              fontSize: context.theme.d.fontSizeMedium18,
              color: context.colorScheme.onSecondaryContainer,
              fontWeight: FontWeight.w700,
            ),
          ),
          SizedBox(height: context.theme.d.paddingMedium),
          _profilePictureWidget(context),
        ],
      ),
    );
  }

  Widget _mobileView(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingMedium),
          child: _profilePictureWidget(context),
        ),
      ],
    );
  }

  Widget _profilePictureWidget(BuildContext context) {
    return InkWell(
      onTap: () => openFilterScreen(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          ProfileImageWidget(avatarUrl: myUser.avatarUrl, isMentor: myUser.offersHelp == true),
          SizedBox(height: context.theme.d.paddingSmall),
          Text(
            AppLocale.current.editPicture,
            maxLines: 2,
            textAlign: TextAlign.center,
            style: context.theme.textTheme.bodyLarge!.copyWith(
              color: context.theme.colorScheme.surfaceBright,
              fontWeight: FontWeight.w400,
              decoration: TextDecoration.underline,
              decorationColor: context.theme.colorScheme.surfaceBright,
            ),
          ),
        ],
      ),
    );
  }
}
