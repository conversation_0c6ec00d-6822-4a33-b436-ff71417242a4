/* import 'package:flutter/material.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/widgets/shared/common_button.dart';

class DesktopEditLayoutScreen extends StatelessWidget {
  final String title;
  final Widget body;
  final String? actionButtonTitle;
  final bool showWebActionButtons;
  final bool showCancelButton;
  final Function()? onCancelTap;
  final Function()? onSaveTap;

  const DesktopEditLayoutScreen({
    super.key,
    required this.body,
    required this.title,
    this.actionButtonTitle,
    this.showWebActionButtons = true,
    this.showCancelButton = true,
    this.onCancelTap,
    this.onSaveTap,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: context.theme.d.maxDesktopAppWidth,
                maxHeight: context.mediaQuerySize.height - context.theme.d.paddingXLarge,
              ),
              child: Material(
                color: context.colorScheme.surface,
                borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXLarge),
                clipBehavior: Clip.hardEdge,
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        color: context.colorScheme.surface,
                        padding: EdgeInsets.only(
                          left: context.theme.d.paddingLarge,
                          right: context.theme.d.paddingLarge,
                          top: context.theme.d.paddingLarge,
                          bottom: context.theme.d.paddingMedium,
                        ),
                        child: Row(
                          children: [
                            Text(
                              title,
                              style: context.theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.w700,
                                color: context.colorScheme.surfaceBright,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Flexible(child: body),
                      SizedBox(height: context.theme.d.paddingMedium),
                      if (showWebActionButtons)
                        Container(
                          padding: EdgeInsets.only(
                            bottom: context.theme.d.paddingLarge,
                            top: context.theme.d.paddingMedium,
                          ),
                          color: context.colorScheme.surface,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Spacer(),
                              if (showCancelButton)
                                CommonButton.outlinedButton(
                                  context: context,
                                  title: AppLocale.current.actionCancel,
                                  buttonSize: ButtonSize.small,
                                  onPressed: onCancelTap,
                                ),
                              Padding(
                                padding: EdgeInsets.only(
                                  left: context.theme.d.paddingSmall,
                                  right: context.theme.d.paddingLarge,
                                ),
                                child: CommonButton.primaryRoundedRectangle(
                                  context: context,
                                  title: actionButtonTitle ?? AppLocale.current.saveAction,
                                  buttonSize: ButtonSize.small,
                                  onPressed: onSaveTap,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
 */

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/widgets/shared/common_button.dart';

class DesktopEditLayoutScreen extends StatelessWidget {
  final String title;
  final Widget body;
  final String? actionButtonTitle;
  final bool showWebActionButtons;
  final bool showCancelButton;
  final Function()? onCancelTap;
  final Function()? onSaveTap;
  final ValueListenable<bool>? hasUnsavedChanges; // <-- Added

  const DesktopEditLayoutScreen({
    super.key,
    required this.body,
    required this.title,
    this.actionButtonTitle,
    this.showWebActionButtons = true,
    this.showCancelButton = true,
    this.onCancelTap,
    this.onSaveTap,
    this.hasUnsavedChanges, // <-- Added
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: context.theme.d.maxDesktopAppWidth,
                maxHeight: context.mediaQuerySize.height - context.theme.d.paddingXLarge,
              ),
              child: Material(
                color: context.colorScheme.surface,
                borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXLarge),
                clipBehavior: Clip.hardEdge,
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        color: context.colorScheme.surface,
                        padding: EdgeInsets.only(
                          left: context.theme.d.paddingLarge,
                          right: context.theme.d.paddingLarge,
                          top: context.theme.d.paddingLarge,
                          bottom: context.theme.d.paddingMedium,
                        ),
                        child: Row(
                          children: [
                            Text(
                              title,
                              style: context.theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.w700,
                                color: context.colorScheme.surfaceBright,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Flexible(child: body),
                      SizedBox(height: context.theme.d.paddingMedium),
                      if (showWebActionButtons)
                        Container(
                          padding: EdgeInsets.only(
                            bottom: context.theme.d.paddingLarge,
                            top: context.theme.d.paddingMedium,
                          ),
                          color: context.colorScheme.surface,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Spacer(),
                              if (showCancelButton)
                                CommonButton.outlinedButton(
                                  context: context,
                                  title: AppLocale.current.actionCancel,
                                  buttonSize: ButtonSize.small,
                                  onPressed: onCancelTap,
                                ),
                              Padding(
                                padding: EdgeInsets.only(
                                  left: context.theme.d.paddingSmall,
                                  right: context.theme.d.paddingLarge,
                                ),
                                child:
                                    hasUnsavedChanges != null
                                        ? ValueListenableBuilder<bool>(
                                          valueListenable: hasUnsavedChanges!,
                                          builder: (context, isEnabled, child) {
                                            return CommonButton.primaryRoundedRectangle(
                                              context: context,
                                              title:
                                                  actionButtonTitle ?? AppLocale.current.saveAction,
                                              buttonSize: ButtonSize.small,
                                              onPressed: isEnabled ? onSaveTap : null,
                                            );
                                          },
                                        )
                                        : CommonButton.primaryRoundedRectangle(
                                          context: context,
                                          title: actionButtonTitle ?? AppLocale.current.saveAction,
                                          buttonSize: ButtonSize.small,
                                          onPressed: onSaveTap,
                                        ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
