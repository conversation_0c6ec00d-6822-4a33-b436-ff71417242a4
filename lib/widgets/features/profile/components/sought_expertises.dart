import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';

import '../../../../services/extensions.dart';
import 'profile_chip_padded.dart';

class SoughtExpertises extends StatelessWidget {
  final List<String> expertiseLabels;

  const SoughtExpertises({super.key, required this.expertiseLabels});

  @override
  Widget build(BuildContext context) {
    final textContentTheme = context.theme.textTheme.labelSmall?.copyWith(
      color: context.theme.colorScheme.secondary,
    );

    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: context.theme.d.paddingMedium,
            vertical: context.theme.d.paddingSmall,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: context.theme.d.paddingSmall),
              if (expertiseLabels.isNotEmpty) ...[
                Text(AppLocale.current.profileViewHelpWith, style: textContentTheme),
                Wrap(
                  children: expertiseLabels.map((label) => ProfileChipPadded(text: label)).toList(),
                ),
                <PERSON><PERSON><PERSON><PERSON>(height: context.theme.d.paddingSmall),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
