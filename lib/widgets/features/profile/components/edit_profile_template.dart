import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/utilities/errors/error_handler.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../../../models/models.dart';
import '../../../../services/extensions.dart';
import '../../../../utilities/loading/loading_provider.dart';
import '../../../widgets.dart';

class EditProfileTemplate extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget body;
  final List<Widget>? actions;
  final Future Function()? editUserProfile;
  final bool isBackEnabled;
  final double? extraScrollingSpace;
  final bool noChangeInData;
  final String? actionButtonTitle;
  final bool showWebActionButtons;
  final bool showActionButton;
  final bool showCancelButton;
  final bool showAccountSettingsLink;

  const EditProfileTemplate({
    super.key,
    required this.title,
    this.subtitle,
    required this.body,
    this.actions,
    this.editUserProfile,
    this.isBackEnabled = true,
    this.noChangeInData = false,
    this.extraScrollingSpace,
    this.actionButtonTitle,
    this.showWebActionButtons = true,
    this.showActionButton = true,
    this.showCancelButton = true,
    this.showAccountSettingsLink = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppUtility.displayDesktopUI(context) ? _desktopView(context) : _mobileView(context);
  }

  _desktopView(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? _) async {
        if (didPop) return;
        _onBackAction(context);
      },
      child: DesktopEditLayoutScreen(
        title: title,
        body: _bodyContent(context),
        actionButtonTitle: actionButtonTitle,
        showCancelButton: showActionButton,
        showWebActionButtons: showWebActionButtons,
        onCancelTap: () async => _onBackAction(context),
        onSaveTap:
            noChangeInData || !isBackEnabled
                ? null
                : () async {
                  if (noChangeInData || editUserProfile == null) {
                    Navigator.pop(context);
                    return;
                  }
                  await _performSaveDataAction(context);

                  if (context.mounted) {
                    Navigator.pop(context);
                  }
                },
      ),
    );
  }

  _mobileView(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ScaffoldModel>().setParams(hideNavBar: true);
    });
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? _) async {
        if (!kIsWeb && didPop) return;
        _onBackAction(context, isWebDeviceBack: AppUtility.displayDesktopUI(context));
      },
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          centerTitle: false,
          leading: backButton(context, onPressed: () async => _onBackAction(context)),
          shape: AppUtility.topDivider(context),
          actions: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall),
              child: CommonButton.textButton(
                context: context,
                title: AppLocale.current.saveAction,
                textStyle: context.theme.textTheme.titleSmall?.copyWith(
                  color:
                      !isBackEnabled
                          ? context.theme.colorScheme.scrim
                          : context.theme.colorScheme.tertiary,
                ),
                onPressed:
                    !isBackEnabled
                        ? null
                        : () async {
                          final GoRouter router = GoRouter.of(context);

                          if (noChangeInData || editUserProfile == null) {
                            router.pop();
                            return;
                          }
                          await _performSaveDataAction(context);
                          router.pop();
                        },
              ),
            ),
            if (actions?.isNotEmpty == true) ...actions!,
          ],
          title: Text(
            title,
            style: context.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
              color: context.colorScheme.secondary,
            ),
          ),
        ),
        body: _bodyContent(context),
      ),
    );
  }

  Widget _bodyContent(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingXLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (subtitle != null) ...[
              Text(
                subtitle!,
                style: context.theme.textTheme.bodyLarge?.copyWith(
                  color: context.theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w400,
                ),
              ),
              SizedBox(height: context.theme.d.paddingLarge),
            ],
            body,
            SizedBox(height: extraScrollingSpace),
            if (showAccountSettingsLink) ...[
              SizedBox(height: context.theme.d.paddingMedium),
              _accountSettingsLink(context),
            ],
          ],
        ),
      ),
    );
  }

  _onBackAction(BuildContext context, {bool isWebDeviceBack = false}) async {
    if (!context.canPop()) {
      context.pushReplacement(AppRoutes.profileEdit.path);
    }
    bool isDesktop = AppUtility.displayDesktopUI(context);

    final router = GoRouter.of(context);
    if (!isBackEnabled) return;
    if (noChangeInData || editUserProfile == null) {
      isDesktop ? Navigator.pop(context) : router.pop();
      return;
    }

    if (isWebDeviceBack) {
      await _performSaveDataAction(context, isWebDeviceBack: isWebDeviceBack);
      return;
    }
    await Future.delayed(const Duration(milliseconds: 500));
    if (!context.mounted) return;

    showDialog(
      context: context,
      builder: (dialogContext) {
        return DialogTemplate(
          title: AppLocale.current.discardChangesTitle,
          description: '',
          actionButtonTitle: AppLocale.current.saveChangeDialogDiscardLabel,
          cancelButtonTitle: AppLocale.current.actionCancel,
          isFullWidthButtons: false,
          showTitleInCenter: false,
          onCancel: () {
            Navigator.pop(dialogContext);
          },
          onAction: () async {
            Navigator.pop(dialogContext);
            isDesktop ? Navigator.pop(context) : router.pop();
          },
        );
      },
    );
  }

  _performSaveDataAction(BuildContext context, {bool isWebDeviceBack = false}) async {
    if (editUserProfile != null) {
      if (!isWebDeviceBack) Loader.show(context);
      await editUserProfile!().then((exception) {
        if (context.mounted) {
          if (!isWebDeviceBack) Loader.hide(context);
          if (exception != null) {
            AppErrorHandler(context: context, exception: exception);
            return;
          }
        }
      });
      if (context.mounted) {
        Provider.of<ExploreCardFiltersModel>(context, listen: false).clearSearchResult();
      }
    }
  }

  _accountSettingsLink(BuildContext context) {
    return MarkdownBody(
      data: AppLocale.current.appLanguageAccountSettingMessage(
        AppRoutes.accountSettingAppLanguage.path,
      ),
      styleSheet: MarkdownStyleSheet.fromTheme(context.theme).copyWith(
        p: context.textTheme.bodyMedium?.copyWith(
          color: context.colorScheme.onSurface,
          fontWeight: FontWeight.w400,
        ),
        a: context.textTheme.bodyMedium?.copyWith(
          color: context.colorScheme.tertiary,
          fontWeight: FontWeight.w400,
        ),
      ),
      onTapLink: (_, path, __) {
        if (AppUtility.displayDesktopUI(context)) {
          Navigator.pop(context);
          showDialog(
            context: context,
            barrierDismissible: true,
            builder: (context) {
              return const EditAccountSettingAppLanguage();
            },
          );
          return;
        }
        context.push(path!);
      },
    );
  }
}
