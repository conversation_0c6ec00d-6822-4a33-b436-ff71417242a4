import 'package:flutter/material.dart';
import 'package:mm_flutter_app/__generated/schema/operations_user.graphql.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/user_provider.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:mm_flutter_app/widgets/features/profile/components/big_profile_chip.dart';
import 'package:mm_flutter_app/widgets/features/profile/components/profile_chip.dart';
import 'package:provider/provider.dart';

import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../services/graphql/providers/providers.dart';

class ProfileUtility {
  dynamic user;

  ProfileUtility.fromFindId(User? userByFindId) {
    user = userByFindId;
  }

  ProfileUtility.fromSearch(UserBySearch? userBySearch) {
    user = userBySearch;
  }

  Enum$UserProfileRole get userProfileRole {
    return user.offersHelp == true ? Enum$UserProfileRole.mentor : Enum$UserProfileRole.mentee;
  }

  bool get isMentor {
    return user.offersHelp == true;
  }

  bool get isMentee {
    return user.seeksHelp == true;
  }

  String? get avatarUrl {
    return user.avatarUrl;
  }

  String livesInLocation(BuildContext context) {
    return [
      regionOfResidence(context),
      cityOfResidence(context),
      countryOfResidence(context),
    ].nonNulls.toList().join(AppLocale.current.listSeparator);
  }

  String fromLocation(BuildContext context) {
    return [
      regionFrom(context),
      cityFrom(context),
      countryFrom(context),
    ].nonNulls.join(AppLocale.current.listSeparator);
  }

  String? regionOfResidence(BuildContext context) {
    return context.theme.appFeatures.regionOfResidence ? user.regionOfResidence : null;
  }

  String? cityOfResidence(BuildContext context) {
    return context.theme.appFeatures.cityOfResidence ? user.cityOfResidence : null;
  }

  String? countryOfResidence(BuildContext context) {
    return context.theme.appFeatures.countryOfResidence
        ? user.countryOfResidence?.translatedValue
        : null;
  }

  String? regionFrom(BuildContext context) {
    return context.theme.appFeatures.regionOfOrigin ? user.regionOfOrigin : null;
  }

  String? cityFrom(BuildContext context) {
    return context.theme.appFeatures.cityOfOrigin ? user.cityOfOrigin : null;
  }

  String? countryFrom(BuildContext context) {
    return context.theme.appFeatures.countryOfOrigin ? user.countryOfOrigin?.translatedValue : null;
  }

  String get fullName {
    return '${user.firstName} ${user.lastName}';
  }

  String get companyName {
    return (user.offersHelp == true
            ? headerBusinessExperience?.businessName
            : (user is UserBySearch)
            ? menteeCompanyBySearch?.name
            : menteeCompany?.name) ??
        '';
  }

  Query$FindUserById$findUserById$companies? get menteeCompany {
    return (user as User).companies?.firstOrNull;
  }

  Query$FindUserSearchResults$findUserSearchResults$companies? get menteeCompanyBySearch {
    return (user as UserBySearch).companies.firstOrNull;
  }

  String companyNameRole() {
    return [
      headerBusinessExperience?.jobTitle,
      companyName,
    ].nonNulls.join(AppLocale.current.listSeparator).toString();
  }

  dynamic get headerBusinessExperience {
    return (user is User)
        ? (user as User).businessExperiences?.firstOrNull
        : (user as UserBySearch).businessExperiences?.firstOrNull;
  }

  int get endorsement {
    return ((user is UserBySearch)
            ? mentorGroupMembershipBySearch?.endorsements
            : mentorGroupMembership?.endorsements) ??
        0;
  }

  List<String>? get affiliations {
    return null;
  }

  String get companyWebsite {
    return ((user is UserBySearch)
            ? menteeCompanyBySearch?.websites?.first.value
            : menteeCompany?.websites?.first.value) ??
        '';
  }

  String get companyDescription {
    return ((user is UserBySearch)
            ? menteeCompanyBySearch?.description
            : menteeCompany?.description) ??
        '';
  }

  String get companyLocation {
    return ((user is UserBySearch) ? menteeCompanyBySearch?.location : menteeCompany?.location) ??
        '';
  }

  String get companyStage {
    return ((user is UserBySearch)
            ? menteeCompanyBySearch?.companyStage?.translatedValue
            : menteeCompany?.companyStage?.translatedValue) ??
        '';
  }

  Query$FindUserById$findUserById$groupMemberships$$MenteesGroupMembership?
  get menteeGroupMembership {
    return (user as User).groupMemberships
        .where((g) => g.groupIdent == GroupIdent.mentees.name)
        .firstOrNull
        ?.maybeWhen(menteesGroupMembership: (g) => g, orElse: () => null);
  }

  Query$FindUserById$findUserById$groupMemberships$$MentorsGroupMembership?
  get mentorGroupMembership {
    return (user as User).groupMemberships
        .where((g) => g.groupIdent == GroupIdent.mentors.name)
        .firstOrNull
        ?.maybeWhen(mentorsGroupMembership: (g) => g, orElse: () => null);
  }

  Query$FindUserById$findUserById$groupMembers$$IqlaaGroupMembership? get iqlaaGroupMembership {
    if (user == null) return null;
    return (user as User).groupMembers
        .where((g) => g.groupIdent == GroupIdent.iqlaa.name)
        .firstOrNull
        ?.maybeWhen(iqlaaGroupMembership: (g) => g, orElse: () => null);
  }

  Query$FindUserById$findUserById$groupMembers$$MastercardGroupMembership?
  get mastercardGroupMembership {
    if (user == null) return null;
    return (user as User).groupMembers
        .where((g) => g.groupIdent == GroupIdent.mastercard.name)
        .firstOrNull
        ?.maybeWhen(mastercardGroupMembership: (g) => g, orElse: () => null);
  }

  Query$FindUserSearchResults$findUserSearchResults$groupMemberships$$MenteesGroupMembership?
  get menteeGroupMembershipBySearch {
    return (user as UserBySearch).groupMemberships
        .where((g) => g.groupIdent == GroupIdent.mentees.name)
        .firstOrNull
        ?.maybeWhen(menteesGroupMembership: (g) => g, orElse: () => null);
  }

  Query$FindUserSearchResults$findUserSearchResults$groupMemberships$$MentorsGroupMembership?
  get mentorGroupMembershipBySearch {
    return (user as UserBySearch).groupMemberships
        .where((g) => g.groupIdent == GroupIdent.mentors.name)
        .firstOrNull
        ?.maybeWhen(mentorsGroupMembership: (g) => g, orElse: () => null);
  }

  String get reasonsForMentoring {
    return ((user is UserBySearch)
            ? mentorGroupMembershipBySearch?.reasonsForMentoring
            : mentorGroupMembership?.reasonsForMentoring) ??
        '';
  }

  String get howICanHelpMentees {
    return ((user is UserBySearch)
            ? mentorGroupMembershipBySearch?.howICanHelpMentees
            : mentorGroupMembership?.howICanHelpMentees) ??
        '';
  }

  List<String> get expertises {
    if (user is UserBySearch) {
      return (isMentee
              ? menteeGroupMembershipBySearch?.soughtExpertises
                  .map((e) => e.translatedValue!)
                  .toList()
              : mentorGroupMembershipBySearch?.expertises
                  .map((e) => e.translatedValue!)
                  .toList()) ??
          [];
    }
    return (isMentee
            ? menteeGroupMembership?.soughtExpertises.map((e) => e.translatedValue!).toList()
            : mentorGroupMembership?.expertises.map((e) => e.translatedValue!).toList()) ??
        [];
  }

  String get industryName {
    return ((user is UserBySearch)
            ? menteeGroupMembershipBySearch?.industry?.translatedValue
            : menteeGroupMembership?.industry?.translatedValue) ??
        '';
  }

  List<String> get mentorIndustries {
    return ((user is UserBySearch)
        ? mentorGroupMembershipBySearch?.industries.map((e) => e.translatedValue!).toList() ?? []
        : mentorGroupMembership?.industries.map((e) => e.translatedValue!).toList() ?? []);
  }

  String get reasonForBusiness {
    return ((user is UserBySearch)
            ? menteeGroupMembershipBySearch?.reasonsForStartingBusiness
            : menteeGroupMembership?.reasonsForStartingBusiness) ??
        '';
  }

  String get businessChallenges {
    return ((user is UserBySearch)
            ? menteeGroupMembershipBySearch?.currentChallenges
            : menteeGroupMembership?.currentChallenges) ??
        '';
  }

  String get howCanMentorSupportMe {
    return ((user is UserBySearch)
            ? menteeGroupMembershipBySearch?.howCanMentorSupportMe
            : menteeGroupMembership?.howCanMentorSupportMe) ??
        '';
  }

  List<String> get imageUrls {
    return [];
  }

  List<String> get mentoringPreferences {
    return [];
  }

  List<String> get menteeExpertisesSought {
    return ((user is UserBySearch)
            ? menteeGroupMembershipBySearch?.soughtExpertises
                .take(Limits.expertisesProfileViewMaxChips)
                .map((e) => e.translatedValue ?? '')
                .toList()
            : menteeGroupMembership?.soughtExpertises
                .take(Limits.expertisesProfileViewMaxChips)
                .map((e) => e.translatedValue ?? '')
                .toList()) ??
        [];
  }

  List<String> get mentorExpertises {
    return ((user is UserBySearch)
            ? mentorGroupMembershipBySearch?.expertises
                .take(Limits.expertisesProfileViewMaxChips)
                .map((e) => e.translatedValue!)
                .toList()
            : mentorGroupMembership?.expertises
                .take(Limits.expertisesProfileViewMaxChips)
                .map((e) => e.translatedValue!)
                .toList()) ??
        [];
  }

  Set<String> languages() {
    if (user is UserBySearch) return {};
    return {
      '${(user as User).preferredLanguage?.translatedValue ?? ''} (${AppLocale.current.profileViewPreferredLanguage})',
      ...(user as User).spokenLanguages.nonNulls.map((e) => e.translatedValue!),
    };
  }

  String? get preferredLanguage {
    if (user is UserBySearch) return null;
    return (user as User).preferredLanguage?.translatedValue;
  }

  String spokenLanguages() {
    if (user is UserBySearch) return '';
    return (user as User).spokenLanguages
        .map((e) => e.translatedValue)
        .nonNulls
        .toList()
        .join(AppLocale.current.listSeparator);
  }

  String get linkedInUrl {
    if (user is UserBySearch) return '';
    return (user as User).websites
            ?.where((e) => e.label == WebsiteLabels.linkedIn.name)
            .firstOrNull
            ?.value ??
        '';
  }

  String get groupIdent {
    if (user is User) {
      final otherGoups =
          (user as User).groups
              .where(
                (element) =>
                    // TODO: where all is this logic used? should we actually filter based on badgeName?
                    element.badgeName?.isNotEmpty == true &&
                    element.ident.toLowerCase() != GroupIdent.mentors.name.toLowerCase() &&
                    element.ident.toLowerCase() != GroupIdent.mentees.name.toLowerCase(),
              )
              .toList();
      return otherGoups.firstOrNull?.ident.toUpperCase() ?? '';
    }
    if (user is UserSearchResult) {
      final otherGoups =
          (user as UserSearchResult).groups
              .where(
                (element) =>
                    element.badgeName?.isNotEmpty == true &&
                    element.ident.toLowerCase() != GroupIdent.mentors.name.toLowerCase() &&
                    element.ident.toLowerCase() != GroupIdent.mentees.name.toLowerCase(),
              )
              .toList();
      return otherGoups.firstOrNull?.ident.toUpperCase() ?? '';
    }
    return '';
  }

  String get groupName {
    if (user is User) {
      final otherGoups =
          (user as User).groups
              .where(
                (element) =>
                    element.badgeName?.isNotEmpty == true &&
                    element.ident.toLowerCase() != GroupIdent.mentors.name.toLowerCase() &&
                    element.ident.toLowerCase() != GroupIdent.mentees.name.toLowerCase(),
              )
              .toList();
      return otherGoups.firstOrNull?.name.toUpperCase() ?? '';
    }
    if (user is UserSearchResult) {
      final otherGoups =
          (user as UserSearchResult).groups
              .where(
                (element) =>
                    element.badgeName?.isNotEmpty == true &&
                    element.ident.toLowerCase() != GroupIdent.mentors.name.toLowerCase() &&
                    element.ident.toLowerCase() != GroupIdent.mentees.name.toLowerCase(),
              )
              .toList();
      return otherGoups.firstOrNull?.name.toUpperCase() ?? '';
    }
    return '';
  }

  Widget createMenteeChipsSection(BuildContext context, String title) {
    final chips =
        menteeExpertisesSought
            .map((e) => BigProfileChip(text: e, chipBackgroundColor: ChipBackgroundColor.secondary))
            .toList();
    return Padding(
      padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingSmall),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: context.theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: context.colorScheme.surfaceBright,
            ),
          ),
          SizedBox(height: context.theme.d.paddingXSmall),
          Wrap(
            spacing: context.theme.d.paddingXSmall,
            runSpacing: context.theme.d.paddingXSmall,
            children: chips.map((chip) => chip).toList(),
          ),
        ],
      ),
    );
  }

  Widget profileInfoSection(BuildContext context, bool isMyProfile, {int? fullNameMaxLine}) {
    final theme = context.theme;
    final livesInLocations = livesInLocation(context);
    final varFromLocation = fromLocation(context);

    List<Widget> widgets = [
      if ((livesInLocations.isNotEmpty) || (varFromLocation.isNotEmpty))
        Padding(
          padding: EdgeInsets.symmetric(vertical: theme.d.paddingXxSmall),
          child: Row(
            children: [
              Icon(
                MicromentorIcons.locationOnOutlined,
                size: theme.d.iconSizeSmall,
                color: theme.colorScheme.primary,
              ),
              Flexible(
                child: Text(
                  livesInLocations.isNotEmpty == true
                      ? livesInLocations
                      : AppLocale.current.profileViewAboutFrom(varFromLocation),
                  softWrap: true,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
        ),
      Text(
        fullName,
        maxLines: fullNameMaxLine,
        overflow: fullNameMaxLine != null ? TextOverflow.ellipsis : null,
        style: theme.textTheme.bodyLarge?.copyWith(
          color: theme.colorScheme.onSecondaryContainer,
          fontWeight: FontWeight.w700,
        ),
      ),
      if (!isMyProfile)
        Consumer<UserProvider>(
          builder: (context, newUserProvider, _) {
            if (AppUtility.isUserBlocked(newUserProvider.myUser, user.id)) {
              return Text(
                AppLocale.current.blockedUsersIndicator,
                style: theme.textTheme.labelSmall?.copyWith(color: theme.colorScheme.onSurface),
              );
            }
            return const SizedBox();
          },
        ),
      if (isMentee && companyName.isNotEmpty == true)
        Padding(
          padding: EdgeInsets.symmetric(vertical: theme.d.paddingXxSmall),
          child: Text(
            companyName,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: theme.textTheme.titleMedium?.copyWith(color: theme.colorScheme.onSurface),
          ),
        ),
      if (isMentor)
        if (companyNameRole().isNotEmpty)
          Text(
            companyNameRole(),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: theme.textTheme.bodyLarge!.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w400,
            ),
          ),
      if (groupName.isNotEmpty)
        Container(
          padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall),
          decoration: BoxDecoration(
            color: context.theme.colorScheme.error,
            borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusSmall),
          ),
          child: Text(
            groupName,
            style: context.theme.textTheme.bodyMedium?.copyWith(
              color: context.theme.colorScheme.surface,
            ),
          ),
        ),
      if (endorsement > 0)
        Padding(
          padding: EdgeInsets.symmetric(vertical: theme.d.paddingXxSmall),
          child: Row(
            children: [
              Icon(MicromentorIcons.thumbUpOffAltOutlined, size: theme.d.iconSizeMedium),
              SizedBox(width: theme.d.paddingSmall),
              Flexible(
                child: Text(
                  '$endorsement endorsement',
                  softWrap: true,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
        ),
    ];

    if (affiliations != null) {
      for (String affiliation in affiliations ?? []) {
        widgets.add(
          ProfileChip(text: affiliation, chipBackgroundColor: ChipBackgroundColor.primary),
        );
      }
    }

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: widgets);
  }
}
