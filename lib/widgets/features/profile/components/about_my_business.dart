import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../services/extensions.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class AboutMyBusiness extends StatelessWidget {
  final User user;

  const AboutMyBusiness({super.key, required this.user});

  @override
  Widget build(BuildContext context) {
    final profileUtils = ProfileUtility.fromFindId(user);
    final stageId = profileUtils.menteeCompany?.companyStage?.textId ?? '';

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: context.theme.d.paddingMedium,
        vertical: context.theme.d.paddingSmall,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocale.current.profileViewAboutBusiness,
            style: context.theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: context.colorScheme.surfaceBright,
            ),
          ),
          SizedBox(height: context.theme.d.paddingSmall),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (profileUtils.companyName.isNotEmpty)
                Text(profileUtils.companyName, style: context.theme.textTheme.titleMedium),
              if (profileUtils.companyWebsite.isNotEmpty)
                InkWell(
                  onTap: () async {
                    final uri = Uri.parse(AppUtility.addSchemeToUrl(profileUtils.companyWebsite));
                    if (await canLaunchUrl(uri)) {
                      await launchUrl(uri);
                      return;
                    }
                    if (context.mounted) _showDialog(context);
                  },
                  child: Text(
                    AppUtility.hideSchemeToUrl(profileUtils.companyWebsite),
                    style: context.theme.textTheme.titleMedium?.copyWith(
                      color: context.theme.colorScheme.tertiary,
                    ),
                  ),
                ),
            ],
          ),
          if (StaticAppFeatures.businessMission && profileUtils.companyDescription.isNotEmpty) ...[
            SizedBox(height: context.theme.d.paddingMedium),
            ExpandableContainer(
              title: AppLocale.current.profileViewMission,
              description: profileUtils.companyDescription,
            ),
          ],
          if (profileUtils.companyLocation.isNotEmpty ||
              profileUtils.companyStage.isNotEmpty ||
              profileUtils.industryName.isNotEmpty) ...[
            SizedBox(height: context.theme.d.paddingMedium),
            Wrap(
              spacing: context.theme.d.paddingXSmall,
              runSpacing: context.theme.d.paddingXSmall,
              children: [
                if (profileUtils.companyLocation.isNotEmpty)
                  BigProfileChip(
                    text: profileUtils.companyLocation,
                    chipBackgroundColor: ChipBackgroundColor.secondary,
                    icon: MicromentorIcons.locationOnOutlined,
                  ),
                if (profileUtils.industryName.isNotEmpty)
                  BigProfileChip(
                    text: profileUtils.industryName,
                    chipBackgroundColor: ChipBackgroundColor.secondary,
                  ),
                if (profileUtils.companyStage.isNotEmpty)
                  BigProfileChip(
                    text: profileUtils.companyStage,
                    chipBackgroundColor: ChipBackgroundColor.secondary,
                    image: getStageIcon(context, stageId),
                  ),
              ],
            ),
            SizedBox(height: context.theme.d.paddingSmall),
          ],
          if (profileUtils.reasonForBusiness.isNotEmpty) ...[
            SizedBox(height: context.theme.d.paddingSmall),
            ExpandableContainer(
              title: AppLocale.current.profileViewMotivation,
              description: profileUtils.reasonForBusiness,
            ),
          ],
          if (profileUtils.businessChallenges.isNotEmpty) ...[
            SizedBox(height: context.theme.d.paddingSmall),
            ExpandableContainer(
              title: AppLocale.current.businessChallengeTitle,
              description: profileUtils.businessChallenges,
            ),
          ],
          if (profileUtils.howCanMentorSupportMe.isNotEmpty) ...[
            SizedBox(height: context.theme.d.paddingSmall),
            ExpandableContainer(
              title: AppLocale.current.howCanMentorSupportMeSubtitle,
              description: profileUtils.howCanMentorSupportMe,
            ),
          ],
          if (StaticAppFeatures.businessImages && profileUtils.imageUrls.isNotEmpty) ...[
            SizedBox(height: context.theme.d.paddingSmall),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children:
                    profileUtils.imageUrls
                        .map(
                          (imageUrl) => Padding(
                            padding: EdgeInsets.only(right: context.theme.d.paddingSmall),
                            child: Image(
                              image: NetworkImage(imageUrl),
                              height: context.theme.d.imageGalleryTileSize.height,
                            ),
                          ),
                        )
                        .toList(),
              ),
            ),
          ],
          if (!AppUtility.displayDesktopUI(context) &&
              profileUtils.menteeExpertisesSought.isNotEmpty)
            profileUtils.createMenteeChipsSection(context, AppLocale.current.profileViewHelpWith),
        ],
      ),
    );
  }

  _showDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocale.current.invalidUrlTitle),
          content: Text(AppLocale.current.invalidUrlMessage),
          actions: [
            TextButton(
              child: Text(AppLocale.current.okTitle),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          ],
        );
      },
    );
  }

  Image? getStageIcon(BuildContext context, String stageId) {
    CompanyStageTextId? stageEnum =
        CompanyStageTextId.values.where((element) => element.name == stageId).firstOrNull;
    if (stageEnum == null) return null;
    AssetImage assetImage = const AssetImage(Assets.revenueStageWhite);
    switch (stageEnum) {
      case CompanyStageTextId.idea:
        assetImage = const AssetImage(Assets.ideaStageWhite);
        break;
      case CompanyStageTextId.operational:
        assetImage = const AssetImage(Assets.operationalStageWhite);
        break;
      case CompanyStageTextId.earning:
        assetImage = const AssetImage(Assets.revenueStageWhite);
      case CompanyStageTextId.profitable:
        assetImage = const AssetImage(Assets.profitableStageWhite);
        break;
    }
    return Image(image: assetImage, height: context.theme.d.iconSizeXLarge);
  }
}
