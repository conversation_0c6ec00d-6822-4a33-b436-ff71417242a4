import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/extensions.dart';
import '../../featured_widgets.dart';

class EditProfileExperienceAndEducation extends StatelessWidget {
  final User myUser;
  final List<ExperienceInput> experience;
  final List<EducationInput> education;
  final Function(EditProfileOptions, {int? index})? onAddEditAction;

  const EditProfileExperienceAndEducation({
    super.key,
    required this.myUser,
    this.experience = const [],
    this.education = const [],
    this.onAddEditAction,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _EditExperience(
          experience: experience,
          myUser: myUser,
          onAddEditAction: (editOption, {index}) {
            onAddEditAction?.call(editOption, index: index);
          },
        ),
        SizedBox(height: context.theme.d.paddingSmall),
        _EditEducation(
          education: education,
          myUser: myUser,
          onAddEditAction: (editOption, {index}) {
            onAddEditAction?.call(editOption, index: index);
          },
        ),
      ],
    );
  }
}

String _concatenateExperience(BuildContext context, ExperienceInput exp) {
  final String location = [
    exp.city,
    exp.state,
    exp.country,
  ].nonNulls.join(AppLocale.current.listSeparator);

  StringBuffer result = StringBuffer();
  if (exp.companyName.isNotEmpty) result.write(exp.companyName);
  if (exp.dateRange(AppLocale.current.datePresent).isNotEmpty) result.write('\n');
  if (exp.dateRange(AppLocale.current.datePresent).isNotEmpty) {
    result.write(exp.dateRange(AppLocale.current.datePresent));
  }
  if (location.trim().isNotEmpty) result.write('\n$location');

  return result.toString();
}

class _EditExperience extends StatelessWidget {
  final User myUser;
  final List<ExperienceInput> experience;
  final Function(EditProfileOptions, {int? index})? onAddEditAction;
  const _EditExperience({required this.experience, required this.myUser, this.onAddEditAction});

  @override
  Widget build(BuildContext context) {
    final bodySmallOnSurface = context.theme.textTheme.bodySmall?.copyWith(
      color: context.theme.colorScheme.onSurface,
    );

    final List<Widget> items = [];
    for (int i = 0; i < experience.length; i++) {
      final exp = experience[i];
      items.addAll([
        HeadingWithListTile(
          heading: i == 0 ? AppLocale.current.profileEditMainExperienceHeader : null,
          title: exp.position,
          subtitle: _concatenateExperience(context, exp),
          extraContent:
              (exp.companyUrl != null)
                  ? InkWell(
                    onTap: () => launchUrl(Uri.parse(exp.companyUrl!)),
                    child: Text(
                      exp.companyUrl!,
                      style: bodySmallOnSurface?.copyWith(
                        decoration: TextDecoration.underline,
                        color: context.theme.colorScheme.primary,
                      ),
                    ),
                  )
                  : null,
          onTap: () => onAddEditAction?.call(EditProfileOptions.editExperience, index: i),
        ),
      ]);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...items,
        if (experience.length < Limits.profileExperienceMaxSize) ...[
          HeadingWithListTile(
            title: AppLocale.current.profileEditMainExperienceAddSection,
            hideTrailing: true,
            onTap: () => onAddEditAction?.call(EditProfileOptions.newExperience),
          ),
        ],
      ],
    );
  }
}

String _concatenateEducation(BuildContext context, EducationInput edu) {
  final String titleAndMajor = [
    edu.title,
    edu.major,
  ].nonNulls.join(AppLocale.current.listSeparator);
  return edu.dateRange(AppLocale.current.datePresent).isNotEmpty
      ? '${edu.dateRange(AppLocale.current.datePresent)}\n$titleAndMajor'
      : titleAndMajor;
}

class _EditEducation extends StatelessWidget {
  final User myUser;
  final List<EducationInput> education;
  final Function(EditProfileOptions, {int? index})? onAddEditAction;
  const _EditEducation({required this.education, required this.myUser, this.onAddEditAction});

  @override
  Widget build(BuildContext context) {
    final List<Widget> items = [];
    for (int i = 0; i < education.length; i++) {
      final edu = education[i];
      items.addAll([
        HeadingWithListTile(
          heading: i == 0 ? AppLocale.current.profileEditMainEducationHeader : null,
          title: edu.schoolName,
          subtitle: _concatenateEducation(context, edu),
          onTap: () => onAddEditAction?.call(EditProfileOptions.editEducation, index: i),
        ),
      ]);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...items,
        if (education.length < Limits.profileEducationMaxSize) ...[
          HeadingWithListTile(
            title: AppLocale.current.profileEditMainEducationAddSection,
            hideTrailing: true,
            onTap: () => onAddEditAction?.call(EditProfileOptions.newEducation),
          ),
        ],
      ],
    );
  }
}
