import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class ProfilePageHeader extends StatelessWidget implements PreferredSizeWidget {
  final String userId;
  final String? appBarTitle;
  final String userFirstName;
  final MessageDirection invitationDirection;
  final bool showImpersonate;
  final Function? onAccept;
  final Function? onDecline;

  const ProfilePageHeader({
    super.key,
    required this.userId,
    required this.userFirstName,
    this.appBarTitle,
    required this.showImpersonate,
    this.invitationDirection = MessageDirection.unset,
    this.onAccept,
    this.onDecline,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    final GoRouter router = GoRouter.of(context);
    return Padding(
      padding:
          AppUtility.displayDesktopUI(context)
              ? EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall)
              : EdgeInsets.zero,
      child: AppBar(
        centerTitle: false,
        leading: router.canPop() ? _backIcon(context) : null,
        title: Text(
          appBarTitle ?? '',
          style: context.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w700,
            color: context.colorScheme.secondary,
          ),
        ),
        shape: AppUtility.topDivider(context),
        actions: [
          Consumer<UserProvider>(
            builder: (context, newUserProvider, _) {
              return _profileActionButton(context);
            },
          ),
        ],
      ),
    );
  }

  _backIcon(BuildContext context) {
    final GoRouter router = GoRouter.of(context);
    return backButton(context, onPressed: () => router.pop());
  }

  _profileActionButton(BuildContext context) {
    final GoRouter router = GoRouter.of(context);

    Widget ctaButton;
    switch (invitationDirection) {
      case MessageDirection.received:
        ctaButton = _createAcceptRejectInviteButtons(context, router);
        break;
      case MessageDirection.self:
        ctaButton = _createEditProfileButton(context);
      case MessageDirection.sent:
      case MessageDirection.unset:
      case MessageDirection.connected:
        ctaButton = _userProfileMenu(context);
        break;
    }

    if (showImpersonate && invitationDirection != MessageDirection.self) {
      return Row(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          CommonButton.textButton(
            context: context,
            title: AppLocale.current.profileViewHeaderImpersonate,
            onPressed: () {
              router.pushNamed(
                AppRoutes.impersonate.name,
                pathParameters: {RouteParams.userId: userId},
              );
            },
          ),
          Padding(padding: EdgeInsetsDirectional.fromSTEB(0, context.theme.d.paddingXSmall, 0, 0)),
          ctaButton,
        ],
      );
    }

    return ctaButton;
  }

  Widget _createAcceptRejectInviteButtons(BuildContext context, GoRouter router) {
    return Padding(
      padding: EdgeInsets.only(right: context.theme.d.paddingMedium),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CommonButton.textButton(
            context: context,
            title: AppLocale.current.actionDecline,
            onPressed: () async {
              onDecline?.call();
            },
          ),
          SizedBox(width: context.theme.d.paddingMedium),
          CommonButton.primaryRoundedRectangle(
            context: context,
            title: AppLocale.current.actionAccept,
            buttonSize: ButtonSize.small,
            onPressed: () async {
              onAccept?.call();
            },
          ),
        ],
      ),
    );
  }

  Widget _createEditProfileButton(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
        child:
            AppUtility.displayDesktopUI(context)
                ? CommonButton.outlinedButton(
                  context: context,
                  title: AppLocale.current.profileViewEditButtonLabel,
                  buttonSize: ButtonSize.medium,
                  onPressed: () => context.push(AppRoutes.profileEdit.path),
                  icon: Image.asset(
                    Assets.pencil,
                    height: context.theme.d.iconSizeSmall,
                    color: context.colorScheme.primary,
                  ),
                )
                : IconButton(
                  onPressed: () => context.push(AppRoutes.profileEdit.path),
                  icon: Image.asset(Assets.pencil, height: context.theme.d.iconSizeMedium),
                ),
      ),
    );
  }

  Widget _userProfileMenu(BuildContext context) {
    if (AppUtility.displayDesktopUI(context)) return const SizedBox();
    return UserActionPopupMenu(
      userFullName: userFirstName,
      userId: userId,
      includeBlockUserOption: true,
    );
  }
}
