import 'package:flutter/material.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../services/extensions.dart';

class BigProfileChip extends StatelessWidget {
  final String text;
  final IconData? icon;
  final Image? image;
  final ChipBackgroundColor? chipBackgroundColor;
  final Function()? onDeleted;

  const BigProfileChip({
    super.key,
    required this.text,
    this.icon,
    this.image,
    this.onDeleted,
    this.chipBackgroundColor = ChipBackgroundColor.secondary,
  });

  @override
  Widget build(BuildContext context) {
    var (bgColor, textIconColor) = _getChipBackgroundColor(
      context,
      this.chipBackgroundColor ?? ChipBackgroundColor.secondary,
    );
    Color chipBackgroundColor = bgColor;
    Color iconColor = textIconColor;

    TextStyle? chipTextStyle = context.theme.textTheme.labelLarge?.copyWith(color: textIconColor);

    return Chip(
      avatar:
          icon != null ? Icon(icon, color: iconColor, size: context.theme.d.iconSizeSmall) : image,
      label: Text(text, style: chipTextStyle, overflow: TextOverflow.ellipsis),
      backgroundColor: chipBackgroundColor,
      side: BorderSide.none,
      visualDensity: const VisualDensity(
        horizontal: VisualDensity.minimumDensity,
        vertical: VisualDensity.minimumDensity,
      ),
      labelPadding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingXxSmall),
      padding: EdgeInsets.symmetric(
        horizontal: context.theme.d.paddingSmall,
        vertical: context.theme.d.chipPadding,
      ),
      deleteIcon: Icon(
        MicromentorIcons.close,
        size: context.theme.d.iconSizeMedium,
        color: iconColor,
      ),
      onDeleted: onDeleted,
    );
  }

  (Color bgColor, Color textIconColor) _getChipBackgroundColor(
    BuildContext context,
    ChipBackgroundColor chipBackgroundColor,
  ) {
    switch (chipBackgroundColor) {
      case ChipBackgroundColor.primary:
        return (context.theme.colorScheme.inversePrimary, context.theme.colorScheme.shadow);
      case ChipBackgroundColor.secondary:
        return (context.theme.colorScheme.secondary, context.theme.colorScheme.surface);
      case ChipBackgroundColor.tertiary:
        return (
          context.theme.colorScheme.tertiaryContainer,
          context.theme.colorScheme.onTertiaryContainer,
        );
    }
  }
}
