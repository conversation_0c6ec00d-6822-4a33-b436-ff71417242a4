import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';

class UserRoleGradientWidget extends StatelessWidget {
  final bool isMentor;
  final String label;
  const UserRoleGradientWidget({required this.label, required this.isMentor, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: context.theme.d.paddingSmall,
        vertical: context.theme.d.paddingXxSmall,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors:
              (isMentor)
                  ? [
                    context.theme.colorScheme.tertiaryContainer,
                    context.theme.colorScheme.primaryFixedDim,
                  ]
                  : [
                    context.theme.colorScheme.onTertiary,
                    context.theme.colorScheme.inversePrimary,
                  ],
        ),
        borderRadius: BorderRadius.circular(context.theme.d.fontSizeMediumLarge),
      ),
      child: Text(
        label,
        style: context.theme.textTheme.labelSmall?.copyWith(
          fontSize: context.theme.d.fontSizeSmall,
          fontWeight: FontWeight.w500,
          color: context.colorScheme.onPrimary,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}
