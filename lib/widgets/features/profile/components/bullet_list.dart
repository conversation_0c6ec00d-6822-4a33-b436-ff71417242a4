import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';

import '../../../../constants/parts/micromentor_icons.dart';

class BulletText extends StatelessWidget {
  final String text;
  const BulletText({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(
          MicromentorIcons.circle,
          color: context.colorScheme.tertiary,
          size: context.theme.d.paddingSmall,
        ),
        SizedBox(width: context.theme.d.paddingXxSmall),
        Text(
          text,
          style: context.theme.textTheme.titleMedium?.copyWith(
            color: context.theme.colorScheme.onSurface,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }
}
