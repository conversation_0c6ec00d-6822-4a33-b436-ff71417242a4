import 'package:flutter/material.dart';
import 'package:mm_flutter_app/constants/parts/micromentor_icons.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/widgets/features/profile/components/bullet_list.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../services/extensions.dart';

class ProfileAboutMe extends StatelessWidget {
  final String? regionOfResidence;
  final String? cityOfResidence;
  final String? countryOfResidence;
  final String? regionFrom;
  final String? cityFrom;
  final String? countryFrom;
  final List<String> languages;
  final String? linkedInUrl;

  const ProfileAboutMe({
    super.key,
    this.regionOfResidence,
    this.cityOfResidence,
    this.countryOfResidence,
    this.regionFrom,
    this.cityFrom,
    this.countryFrom,
    this.linkedInUrl,
    this.languages = const [],
  });

  Widget _createChipsSection(BuildContext context, String? title, List<BulletText> chips) {
    return chips.isNotEmpty
        ? Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (title != null)
              Text(
                title,
                style: context.theme.textTheme.titleMedium?.copyWith(
                  color: context.theme.colorScheme.surfaceBright,
                  fontWeight: FontWeight.w500,
                ),
              ),
            SizedBox(height: context.theme.d.paddingXxSmall),
            Wrap(
              spacing: context.theme.d.paddingXSmall,
              runSpacing: context.theme.d.paddingXSmall,
              children: chips.map((chip) => chip).toList(),
            ),
          ],
        )
        : const SizedBox();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: context.theme.d.paddingMedium,
            vertical: context.theme.d.paddingXxSmall,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppLocale.current.profileViewAboutMe,
                style: context.theme.textTheme.titleMedium!.copyWith(
                  color: context.theme.colorScheme.surfaceBright,
                  fontWeight: FontWeight.w700,
                ),
              ),
              SizedBox(height: context.theme.d.paddingXxSmall),
              if (linkedInUrl?.isNotEmpty == true)
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [_linkedinUrlLogo(context)],
                ),
              if (languages.isNotEmpty) ...[
                _createChipsSection(
                  context,
                  AppLocale.current.profileViewMyLanguages,
                  _createLanguageBullets(languages),
                ),
                SizedBox(height: context.theme.d.paddingSmall),
              ],
            ],
          ),
        ),
      ],
    );
  }

  _linkedinUrlLogo(BuildContext context) {
    if (linkedInUrl?.isEmpty == true) return const SizedBox();
    return Container(
      decoration: ShapeDecoration(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusSmall),
        ),
        color: context.theme.colorScheme.onPrimary,
      ),
      child: IconButton(
        style: IconButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusSmall),
          ),
        ),
        icon: Icon(MicromentorIcons.linkedin, color: context.theme.colorScheme.secondary),
        onPressed: () {
          debugPrint(linkedInUrl);
          _launchUrl(Uri.parse(linkedInUrl!));
        },
      ),
    );
  }

  Future<void> _launchUrl(Uri url) async {
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }
}

List<BulletText> _createLanguageBullets(List<String> languages) {
  return languages.map((language) {
    return BulletText(text: language);
  }).toList();
}
