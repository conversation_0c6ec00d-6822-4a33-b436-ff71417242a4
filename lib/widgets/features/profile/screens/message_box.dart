import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../models/locale_model.dart';
import '../../../../services/firebase/analytic_service.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class MessageBox extends StatelessWidget {
  final TextEditingController textEditingController;
  final bool showButtonsInline;
  final Function()? onChanged;
  final Function? onSendAction;

  const MessageBox({
    super.key,
    required this.textEditingController,
    this.showButtonsInline = false,
    this.onChanged,
    this.onSendAction,
  });

  @override
  Widget build(BuildContext context) {
    final localeModel = Provider.of<LocaleModel>(context, listen: false);

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: context.theme.d.paddingMedium,
        vertical: context.theme.d.paddingSmall,
      ),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                AppLocale.current.inviteCreateCustomizeMessagePrompt,
                style: context.theme.textTheme.titleMedium?.copyWith(
                  color: context.theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(width: context.theme.d.paddingSmall),
              if (kIsWeb && !AppUtility.isMobilePlatform())
                Tooltip(
                  decoration: BoxDecoration(
                    color: context.colorScheme.onInverseSurface,
                    borderRadius: BorderRadius.circular(context.theme.d.paddingMedium),
                  ),
                  padding: EdgeInsets.symmetric(
                    vertical: context.theme.d.paddingMedium,
                    horizontal: context.theme.d.paddingLarge,
                  ),
                  richMessage: TextSpan(
                    text: '${AppLocale.current.inviteCreateMessageTipsTitle}\n\n',
                    style: context.theme.textTheme.bodySmall?.copyWith(
                      color: context.colorScheme.onPrimary,
                    ),
                    children: [
                      TextSpan(
                        text: AppLocale.current.inviteCreateMessageTipsSubtitle,
                        style: context.theme.textTheme.bodySmall?.copyWith(
                          height: context.theme.d.lineHeightX,
                          color: context.colorScheme.onPrimary,
                        ),
                      ),
                    ],
                  ),
                  child: Icon(
                    MicromentorIcons.infoOutline,
                    size: context.theme.d.iconSizeMedium,
                    color: context.theme.colorScheme.onSurface,
                  ),
                ),
            ],
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingMedium),
            child: TextField(
              style: context.theme.textTheme.bodyMedium?.copyWith(
                color: context.theme.colorScheme.onSurface,
              ),
              textAlign:
                  localeModel.isArabic(text: textEditingController.text)
                      ? TextAlign.right
                      : TextAlign.left,
              textDirection:
                  localeModel.isArabic(text: textEditingController.text)
                      ? TextDirection.rtl
                      : TextDirection.ltr,
              controller: textEditingController,
              keyboardType: TextInputType.multiline,
              minLines: 5,
              maxLines: 5,
              textCapitalization: TextCapitalization.sentences,
              onChanged: (_) => onChanged?.call(),
              decoration: InputDecoration(
                filled: true,
                fillColor: context.theme.colorScheme.surface,
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: context.theme.colorScheme.surfaceBright,
                    width: 0.4,
                  ),
                  borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusMedium),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusMedium),
                ),
                hintText: AppLocale.current.inviteCreateMessagePlaceholder,
                helperStyle: context.theme.textTheme.bodyMedium?.copyWith(
                  color: context.theme.colorScheme.scrim,
                ),
              ),
            ),
          ),
          SizedBox(height: context.theme.d.paddingMedium),
          showButtonsInline
              ? Container(
                alignment: Alignment.centerRight,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _clearButton(context),
                    SizedBox(width: context.theme.d.paddingMedium),
                    _sendButton(context),
                  ],
                ),
              )
              : Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _sendButton(context),
                  SizedBox(height: context.theme.d.paddingMedium),
                  _clearButton(context),
                ],
              ),
        ],
      ),
    );
  }

  _sendButton(BuildContext context) {
    return CommonButton.primaryRoundedRectangle(
      context: context,
      title: AppLocale.current.actionSend,
      isFullWidth: showButtonsInline ? false : true,
      buttonSize: showButtonsInline ? ButtonSize.small : ButtonSize.large,
      onPressed:
          _isMessageEmpty()
              ? null
              : () {
                AnalyticService.invitationSent();
                _sendInviteAction(context);
              },
    );
  }

  _clearButton(BuildContext context) {
    return CommonButton.outlinedButton(
      context: context,
      title: AppLocale.current.actionClear,
      isFullWidth: showButtonsInline ? false : true,
      buttonSize: showButtonsInline ? ButtonSize.small : ButtonSize.large,
      onPressed:
          _isMessageEmpty()
              ? null
              : () {
                textEditingController.clear();
                onChanged?.call();
              },
    );
  }

  _sendInviteAction(BuildContext context) async {
    if (textEditingController.text.isEmpty) return;
    onSendAction?.call();
  }

  _isMessageEmpty() {
    return textEditingController.text.trim().isEmpty;
  }
}
