import 'package:flutter/material.dart';

import '../../../../generatedAppLocale/l10n.dart';
import '../../../../utilities/debug_logger.dart';
import '../../../widgets.dart';

class EditMentoringPreferencesScreen extends StatefulWidget {
  const EditMentoringPreferencesScreen({super.key});

  @override
  State<EditMentoringPreferencesScreen> createState() => _EditMentoringPreferencesScreenState();
}

class _EditMentoringPreferencesScreenState extends State<EditMentoringPreferencesScreen> {
  // late final UserProvider _userProvider;
  // late final User? _myUser;
  List<SelectableChip> _selectedChips = [];

  // @override
  // void initState() {
  //   super.initState();
  //   _userProvider = Provider.of<UserProvider>(context, listen: false);
  //   _myUser = _userProvider.myUser;
  //   // TODO - Set preselected values from myUser
  // }

  List<SelectableChip> _createPreferenceChips() {
    // TODO: Implement in backend and return with content provider
    List<String> translations = [
      AppLocale.current.profileEditSectionMentorPreferencesOption1,
      AppLocale.current.profileEditSectionMentorPreferencesOption2,
      AppLocale.current.profileEditSectionMentorPreferencesOption3,
      AppLocale.current.profileEditSectionMentorPreferencesOption4,
      AppLocale.current.profileEditSectionMentorPreferencesOption5,
      AppLocale.current.profileEditSectionMentorPreferencesOption6,
    ];
    return translations
        .map((e) => SelectableChip(chipContent: e, textId: e))
        .toList(growable: false);
  }

  @override
  Widget build(BuildContext context) {
    return EditProfileTemplate(
      title: AppLocale.current.profileEditSectionMentorPreferencesTitle,
      subtitle: AppLocale.current.profileEditSectionMentorPreferencesSubtitle,
      body: SelectableListTile.checkbox(
        chips: _createPreferenceChips(),
        onSelect: (chips) {
          setState(() => _selectedChips = chips);
          DebugLogger.info(_selectedChips.toString()); //TODO - Use value
        },
      ),
      editUserProfile: null, // TODO - update mentoring preferences
    );
  }
}
