import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../widgets.dart';

class EditOriginLocationScreen extends StatefulWidget {
  const EditOriginLocationScreen({super.key});

  @override
  State<EditOriginLocationScreen> createState() => _EditOriginLocationScreenState();
}

class _EditOriginLocationScreenState extends State<EditOriginLocationScreen> {
  late final UserProvider _userProvider;
  late final TextEditingController _textEditingController;
  String? _originLocation;
  late User? _myUser;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myUser = _userProvider.myUser;
    _textEditingController = TextEditingController(
      text: _myUser?.cityOfOrigin, //TODO - Use region and country too
    );
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditProfileTemplate(
      title: AppLocale.current.profileEditSectionAboutOriginLocationTitle,
      body: Form(
        child: TextFormFieldWidget(
          prefixIcon: const Icon(MicromentorIcons.search),
          label: AppLocale.current.profileEditSectionAboutOriginLocationInputLabel,
          hint: AppLocale.current.profileEditSectionAboutOriginLocationInputHint,
          textController: _textEditingController,
          onChanged: (value) {
            setState(() {
              _originLocation = value;
            });
          },
        ),
      ),
      editUserProfile: () async {
        final result = await _userProvider.updateUser(
          input: Input$UserInput(
            id: _myUser?.id,
            cityOfOrigin: _originLocation, // todo: - Use region and country too
          ),
          options: UpdateObjectOptions<User>(
            loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
            // If we need to switch to isUpdatedFunc:
            // isUpdatedFunc: (User user) => user.cityOfOrigin == _originLocation,
          ),
        );

        if (result.hasError) {
          return result.operationException;
        }
      },
    );
  }
}
