import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/operations_user.graphql.dart';
import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/base/operation_result.dart';
import '../../../../services/graphql/providers/content_provider.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../shared/selectable_list_tile.dart';
import '../components/edit_profile_template.dart';

class EditIndustriesScreen extends StatefulWidget {
  const EditIndustriesScreen({super.key});

  @override
  State<EditIndustriesScreen> createState() => _EditIndustriesScreenState();
}

class _EditIndustriesScreenState extends State<EditIndustriesScreen> {
  late final ContentProvider _contentProvider;
  late final UserProvider _userProvider;
  late final List<SelectableChip> _industryChips;
  late final bool _isEntrepreneur;
  late final int _maxSelections;
  late final List<SelectableChip> _initialValues;
  late final User? _myUser;

  List<SelectableChip> _selectedChips = [];

  @override
  void initState() {
    super.initState();
    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myUser = _userProvider.myUser;
    _isEntrepreneur = _myUser?.seeksHelp == true;
    _industryChips =
        _contentProvider.industryOptions
            ?.map((e) => SelectableChip(chipContent: e.translatedValue ?? '', textId: e.textId))
            .toList() ??
        [];
    _industryChips.sort((a, b) => a.chipContent.compareTo(b.chipContent));
    _maxSelections =
        _isEntrepreneur
            ? Limits.profileEntrepreneurIndustryMaxSize
            : Limits.profileMentorIndustryMaxSize;

    // Set preselected industries
    final maybeMentorGroupMembership = _myUser?.groupMemberships
        .where((g) => g.groupIdent == GroupIdent.mentors.name)
        .firstOrNull
        ?.maybeWhen(mentorsGroupMembership: (g) => g, orElse: () => null);
    final maybeMenteeGroupMembership = _myUser?.groupMemberships
        .where((g) => g.groupIdent == GroupIdent.mentees.name)
        .firstOrNull
        ?.maybeWhen(menteesGroupMembership: (g) => g, orElse: () => null);
    if (_isEntrepreneur) {
      _initialValues =
          maybeMenteeGroupMembership!.industry != null
              ? [
                SelectableChip(
                  chipContent: maybeMenteeGroupMembership.industry!.translatedValue!,
                  textId: maybeMenteeGroupMembership.industry!.textId,
                ),
              ]
              : [];
    } else {
      _initialValues =
          maybeMentorGroupMembership!.industries
              .map((e) => SelectableChip(chipContent: e.translatedValue!, textId: e.textId))
              .toList();
    }
    _selectedChips = _initialValues;
  }

  @override
  Widget build(BuildContext context) {
    return EditProfileTemplate(
      title:
          _isEntrepreneur
              ? AppLocale.current.profileEditSectionBusinessIndustryTitle
              : AppLocale.current.profileEditSectionMentorIndustryTitle,
      subtitle:
          _isEntrepreneur
              ? AppLocale.current.profileEditSectionBusinessIndustrySubtitle
              : AppLocale.current.profileEditSectionMentorIndustrySubtitle,
      body: SelectableListTile.checkbox(
        chips: _industryChips,
        maxSelection: _maxSelections,
        onSelect: (chips) {
          setState(() => _selectedChips = chips);
        },
        initialSelection: _initialValues,
      ),
      noChangeInData: _selectedChips.firstOrNull?.textId == _initialValues.firstOrNull?.textId,
      editUserProfile: () async {
        OperationResult<void>? result;
        if (_isEntrepreneur) {
          result = await _userProvider.updateMenteesGroupMembership(
            input: Input$MenteesGroupMembershipInput(
              id:
                  _myUser?.groupMemberships
                      .firstWhere((g) => g.groupIdent == GroupIdent.mentees.name)
                      .id,
            ).copyWith(industryTextId: _selectedChips.firstOrNull?.textId),
          );
        } else {
          result = await _userProvider.updateMentorsGroupMembership(
            input: Input$MentorsGroupMembershipInput(
              id:
                  _myUser?.groupMemberships
                      .firstWhere((g) => g.groupIdent == GroupIdent.mentors.name)
                      .id,
              industriesTextIds: _selectedChips.map((e) => e.textId).toList(),
            ),
          );
        }

        if (result.gqlQueryResult.hasException) {
          return result.gqlQueryResult.exception;
        }

        await _userProvider.loadUser(
          userId: _myUser?.id,
          options: LoadObjectOptions<User>(
            pollingConfig: PollingConfig<User>(prevUpdatedAt: _myUser?.updatedAt),
          ),
        );
      },
    );
  }
}
