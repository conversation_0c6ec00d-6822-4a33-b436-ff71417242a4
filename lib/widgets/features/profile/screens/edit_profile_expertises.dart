import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/operations_user.graphql.dart';
import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/base/operation_result.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../widgets.dart';

class EditExpertisesScreen extends StatefulWidget {
  final bool isTopExpertises;

  const EditExpertisesScreen({super.key, required this.isTopExpertises});

  @override
  State<EditExpertisesScreen> createState() => _EditExpertisesScreenState();
}

class _EditExpertisesScreenState extends State<EditExpertisesScreen> {
  late final ContentProvider _contentProvider;
  late final UserProvider _userProvider;
  late final List<SelectableChip> _expertiseChips;
  late final bool _isEntrepreneur;
  late final List<SelectableChip> _initialValues;
  late final User? _myUser;
  final int _maxSelections = 3;

  List<SelectableChip> _selectedChips = [];

  @override
  void initState() {
    super.initState();
    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myUser = _userProvider.myUser;
    _isEntrepreneur = _myUser!.seeksHelp == true;
    _expertiseChips =
        _contentProvider.expertiseOptions
            ?.map((e) => SelectableChip(chipContent: e.translatedValue ?? '', textId: e.textId))
            .toList() ??
        [];
    _expertiseChips.sort((a, b) => a.chipContent.compareTo(b.chipContent));
    // Set preselected expertises
    final maybeMentorGroupMembership = _myUser.groupMemberships
        .where((g) => g.groupIdent == GroupIdent.mentors.name)
        .firstOrNull
        ?.maybeWhen(mentorsGroupMembership: (g) => g, orElse: () => null);
    final maybeMenteeGroupMembership = _myUser.groupMemberships
        .where((g) => g.groupIdent == GroupIdent.mentees.name)
        .firstOrNull
        ?.maybeWhen(menteesGroupMembership: (g) => g, orElse: () => null);
    if (_isEntrepreneur) {
      _initialValues =
          maybeMenteeGroupMembership!.soughtExpertises
              .map((e) => SelectableChip(chipContent: e.translatedValue!, textId: e.textId))
              .toList();
    } else {
      _initialValues =
          maybeMentorGroupMembership!.expertises
              .map((e) => SelectableChip(chipContent: e.translatedValue!, textId: e.textId))
              .toList();
    }
    _selectedChips = _initialValues;
  }

  @override
  Widget build(BuildContext context) {
    final String title;
    final String subtitle;
    if (_isEntrepreneur) {
      if (widget.isTopExpertises) {
        title = AppLocale.current.profileEditSectionBusinessTopicsTopTitle;
        subtitle = AppLocale.current.profileEditSectionBusinessTopicsTopSubtitle;
      } else {
        title = AppLocale.current.profileEditSectionBusinessTopicsAdditionalTitle;
        subtitle = AppLocale.current.profileEditSectionBusinessTopicsAdditionalSubtitle;
      }
    } else {
      if (widget.isTopExpertises) {
        title = AppLocale.current.profileEditSectionMentorExpertisesTopTitle;
        subtitle = AppLocale.current.profileEditSectionMentorExpertisesTopSubtitle;
      } else {
        title = AppLocale.current.profileEditSectionMentorExpertisesAdditionalTitle;
        subtitle = AppLocale.current.profileEditSectionMentorExpertisesAdditionalSubtitle;
      }
    }

    return EditProfileTemplate(
      title: title,
      subtitle: subtitle,
      body: SelectableListTile.checkbox(
        chips: _expertiseChips,
        maxSelection: _maxSelections,
        onSelect: (chips) {
          setState(() => _selectedChips = chips);
        },
        initialSelection: _initialValues,
      ),
      noChangeInData: noChangeInValue(),
      editUserProfile: () async {
        //TODO: handle widget.isTopExpertises
        final List<String> newExpertiseTextIds = _selectedChips.map((e) => e.textId).toList();
        newExpertiseTextIds.sort();

        OperationResult<void>? result;
        if (_isEntrepreneur) {
          result = await _userProvider.updateMenteesGroupMembership(
            input: Input$MenteesGroupMembershipInput(
              id:
                  _myUser?.groupMemberships
                      .firstWhere((g) => g.groupIdent == GroupIdent.mentees.name)
                      .id,
              soughtExpertisesTextIds: newExpertiseTextIds,
            ),
          );
        } else {
          result = await _userProvider.updateMentorsGroupMembership(
            input: Input$MentorsGroupMembershipInput(
              id:
                  _myUser?.groupMemberships
                      .firstWhere((g) => g.groupIdent == GroupIdent.mentors.name)
                      .id,
              expertisesTextIds: newExpertiseTextIds,
            ),
          );
        }

        if (result.gqlQueryResult.hasException) {
          return result.gqlQueryResult.exception;
        }

        await _userProvider.loadUser(
          userId: _myUser?.id,
          options: LoadObjectOptions<User>(
            pollingConfig: PollingConfig<User>(prevUpdatedAt: _myUser?.updatedAt),
          ),
        );
      },
    );
  }

  bool noChangeInValue() {
    List<String> selectedChips = _selectedChips.map((e) => e.textId).toList();

    List<String> initialValue = _initialValues.map((e) => e.textId).toList();

    if (selectedChips.length != initialValue.length) {
      return false;
    }

    // Sort both lists
    selectedChips.sort();
    initialValue.sort();

    // Compare each element
    for (int i = 0; i < selectedChips.length; i++) {
      if (selectedChips[i] != initialValue[i]) {
        return false;
      }
    }

    return true;
  }
}
