import 'package:another_flushbar/flushbar.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../models/models.dart';
import '../../../../services/firebase/analytic_service.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/loading/loading_provider.dart';
import '../../../../utilities/navigation_mixin.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class ProfileContentScreen extends StatefulWidget {
  final User user;
  final bool isMyProfile;

  const ProfileContentScreen({super.key, required this.user, required this.isMyProfile});

  @override
  State<ProfileContentScreen> createState() => _ProfileContentScreenState();
}

class _ProfileContentScreenState extends State<ProfileContentScreen>
    with NavigationMixin<ProfileContentScreen>, TickerProviderStateMixin {
  late final ChannelsProvider _channelsProvider;
  late final InvitationsProvider _invitationsProvider;
  late final InboxProvider _inboxProvider;
  late final UserProvider _userProvider;
  late ScaffoldModel _scaffoldModel;
  late final TabController tabController;
  MessageDirection? invitationDirection;
  ProfilePageHeader? appBar;
  late ProfileUtility profileUtils;
  bool showVacationPromptDialog = false;
  String? invitationId;
  late bool isDesktop;
  ValueNotifier<bool> isLoading = ValueNotifier(false);
  (String?, bool)? channelData;

  @override
  void initState() {
    super.initState();

    _channelsProvider = Provider.of<ChannelsProvider>(context, listen: false);
    _invitationsProvider = Provider.of<InvitationsProvider>(context, listen: false);
    _inboxProvider = Provider.of<InboxProvider>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _scaffoldModel = Provider.of<ScaffoldModel>(context, listen: false);
    tabController = TabController(length: 1, vsync: this);
    profileUtils = ProfileUtility.fromFindId(widget.user);
  }

  @override
  void didChangeDependencies() {
    isDesktop = AppUtility.displayDesktopUI(context);
    _setMessageDirection();
    super.didChangeDependencies();
  }

  _setMessageDirection() async {
    final userId = widget.user.id;
    final firstName = widget.user.firstName ?? '';
    String? appBarTitle;
    final sentInvitationId = _inboxProvider.getSentInvitationIdForUser(widget.user.id);
    final receivedInvitationId = _inboxProvider.getReceivedInvitationIdForUser(widget.user.id);
    channelData = await _inboxProvider.getOneOnOneChannelIdForUser(userId);
    final channelId = channelData?.$1;

    if (widget.isMyProfile) {
      setState(() {
        invitationDirection = MessageDirection.self;
      });
      appBarTitle = AppLocale.current.myProfile;
    } else if (channelId != null) {
      // Users are already connected
      setState(() {
        invitationDirection = MessageDirection.connected;
      });
      appBarTitle = AppLocale.current.viewProfile;
    } else if (receivedInvitationId != null) {
      // There is a pending invitation received from this user
      setState(() {
        invitationDirection = MessageDirection.received;
      });
      invitationId = receivedInvitationId;
    } else if (sentInvitationId != null) {
      // There is a pending invitation sent to this user
      setState(() {
        invitationDirection = MessageDirection.sent;
      });
      appBarTitle = AppLocale.current.inboxInvitesSentTitle;
      invitationId = sentInvitationId;
    } else {
      // There is no connection between users
      setState(() {
        invitationDirection = MessageDirection.unset;
      });
      appBarTitle = AppLocale.current.viewProfile;
    }

    appBar = ProfilePageHeader(
      appBarTitle: appBarTitle,
      userId: userId,
      userFirstName: firstName,
      invitationDirection: invitationDirection ?? MessageDirection.unset,
      showImpersonate: !_userProvider.isImpersonating && _userProvider.isAdmin,
      onAccept: () async {
        await onAccept();
      },
      onDecline: () async {
        await onDecline();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading.value) {
      return const Loading();
    }

    return Scaffold(
      appBar: appBar,
      body: isDesktop ? _desktopView() : _mobileView(),
      floatingActionButton: isDesktop ? const SizedBox.shrink() : _floatingActionButtonWidget(),
    );
  }

  Widget _mobileView() {
    return SingleChildScrollView(
      padding: EdgeInsets.only(bottom: context.theme.d.paddingXLarge),
      child: Padding(
        padding: EdgeInsets.only(bottom: context.theme.d.paddingSmall),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            //TODO: - Business and academic experiences to show in header must be marked as such in the backend
            ProfileBasicInfo(
              user: widget.user,
              isMyProfile: widget.isMyProfile,
              invitationDirection: invitationDirection,
            ),
            _tabbarWidget(),
            if (profileUtils.isMentee && profileUtils.menteeCompany != null) ...[
              SizedBox(height: context.theme.d.paddingMedium),
              AboutMyBusiness(user: widget.user),
            ],
            if (profileUtils.isMentee && profileUtils.menteeCompany == null) ...[
              SizedBox(height: context.theme.d.paddingMedium),
              profileUtils.createMenteeChipsSection(context, AppLocale.current.profileViewHelpWith),
            ],
            if (profileUtils.isMentor) ...[
              HowCanIHelpSection(
                expertises: profileUtils.mentorExpertises,
                industries: profileUtils.mentorIndustries,
                // TODO: - Mentoring preferences
                reasonsForMentoring: profileUtils.reasonsForMentoring,
                howICanHelpMentees: profileUtils.howICanHelpMentees,
              ),
              const Divider(),
            ],
            _profileAboutMe(),
            if (((widget.user.businessExperiences != null &&
                    widget.user.businessExperiences!.isNotEmpty) ||
                (widget.user.academicExperiences != null &&
                    widget.user.academicExperiences!.isNotEmpty)))
              ..._experienceWidget(),
          ],
        ),
      ),
    );
  }

  Widget _desktopView() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 3,
          child: ListView(
            padding: EdgeInsets.only(
              top: context.theme.d.paddingSmall,
              left: context.theme.d.paddingSmall,
              right: context.theme.d.paddingSmall,
              bottom: context.theme.d.paddingXLarge,
            ),
            children: [
              ProfileBasicInfo(
                user: widget.user,
                isMyProfile: widget.isMyProfile,
                invitationDirection: invitationDirection,
                button: _inviteStatusDesktopButton(),
              ),
              if (profileUtils.menteeExpertisesSought.isNotEmpty)
                Container(
                  width: double.maxFinite,
                  margin: EdgeInsets.only(
                    right: context.theme.d.paddingSmall,
                    left: context.theme.d.paddingMedium,
                    top: context.theme.d.paddingSmall,
                    bottom: context.theme.d.paddingSmall,
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: context.theme.d.paddingMedium,
                    vertical: context.theme.d.paddingSmall,
                  ),
                  decoration: BoxDecoration(
                    color: context.colorScheme.onPrimary,
                    border: Border.all(
                      width: context.theme.d.borderWidthRegular,
                      color: context.colorScheme.outline,
                    ),
                    borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
                  ),
                  child: profileUtils.createMenteeChipsSection(
                    context,
                    AppLocale.current.profileViewHelpWith,
                  ),
                ),
            ],
          ),
        ),
        Expanded(
          flex: 6,
          child: Container(
            padding: EdgeInsets.all(context.theme.d.paddingSmall),
            margin: EdgeInsets.only(
              top: context.theme.d.paddingSmall,
              bottom: context.theme.d.paddingMedium,
              left: context.theme.d.paddingSmall,
              right: context.theme.d.paddingMedium,
            ),
            decoration: BoxDecoration(
              color: context.colorScheme.onPrimary,
              border: Border.all(
                width: context.theme.d.borderWidthRegular,
                color: context.colorScheme.outline,
              ),
              borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
            ),
            child: ListView(
              padding: EdgeInsets.all(context.theme.d.paddingSmall),
              children: [
                if (profileUtils.isMentee && profileUtils.menteeCompany != null) ...[
                  SizedBox(height: context.theme.d.paddingMedium),
                  AboutMyBusiness(user: widget.user),
                ],
                if (profileUtils.isMentor) ...[
                  HowCanIHelpSection(
                    expertises: profileUtils.mentorExpertises,
                    industries: profileUtils.mentorIndustries,
                    // TODO: - Mentoring preferences
                    reasonsForMentoring: profileUtils.reasonsForMentoring,
                    howICanHelpMentees: profileUtils.howICanHelpMentees,
                  ),
                  const Divider(),
                ],
                _profileAboutMe(),
                if (((widget.user.businessExperiences != null &&
                        widget.user.businessExperiences!.isNotEmpty) ||
                    (widget.user.academicExperiences != null &&
                        widget.user.academicExperiences!.isNotEmpty)))
                  ..._experienceWidget(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Widgets
  _profileAboutMe() {
    return ProfileAboutMe(
      regionOfResidence: profileUtils.regionOfResidence(context),
      cityOfResidence: profileUtils.cityOfResidence(context),
      countryOfResidence: profileUtils.countryOfResidence(context),
      regionFrom: profileUtils.regionFrom(context),
      cityFrom: profileUtils.cityFrom(context),
      countryFrom: profileUtils.countryFrom(context),
      languages: profileUtils.languages().where((e) => e.isNotEmpty).toList(growable: false),
      linkedInUrl: profileUtils.linkedInUrl,
    );
  }

  _tabbarWidget() {
    return TabBar(
      controller: tabController,
      labelColor: context.theme.colorScheme.onSurface,
      tabs: [
        Tab(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(MicromentorIcons.personSearch, color: context.colorScheme.onSurface),
              SizedBox(width: context.theme.d.paddingSmall),
              Text(
                AppLocale.current.profileViewOverview,
                style: context.theme.textTheme.titleMedium!.copyWith(
                  color: context.theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
        //TODO: Hidding this because Endoresment functionality is not up yet
        // Tab(
        //   child: Row(
        //     mainAxisAlignment: MainAxisAlignment.center,
        //     children: [
        //       Icon(
        //         Icons.thumb_up_off_alt_outlined,
        //         color: context.colorScheme.onSurface,
        //       ),
        //       SizedBox(
        //         width: context.theme.d.paddingSmall,
        //       ),
        //       Text(
        //         l10n.endorsement,
        //         style: context.theme.textTheme.titleMedium!.copyWith(
        //           color: context.theme.colorScheme.onSurface,
        //         ),
        //       ),
        //     ],
        //   ),
        // ),
      ],
    );
  }

  _experienceWidget() {
    return [
      ExperienceAndEducation(
        experience:
            widget.user.businessExperiences
                ?.map(
                  (e) => ExperienceInput(
                    position: e.jobTitle ?? '',
                    companyName: e.businessName,
                    start: e.startDate,
                    end: e.endDate,
                    city: e.city,
                    state: e.state,
                    country: e.country,
                  ),
                )
                .toList() ??
            [],
        education:
            widget.user.academicExperiences
                ?.map(
                  (e) => EducationInput(
                    schoolName: e.institutionName,
                    start: e.startDate,
                    end: e.endDate,
                    title: e.degreeType,
                    major: e.fieldOfStudy,
                  ),
                )
                .toList() ??
            [],
        theme: context.theme,
      ),
    ];
  }

  _floatingActionButtonWidget() {
    // TODO: remove this variable when MM2 is deprecated
    final userHasUsedMm3 = widget.user.hasSignedInToMm3 ?? false;
    return Align(
      alignment: Alignment.bottomCenter,
      child: Padding(
        padding: EdgeInsets.only(left: context.theme.d.paddingXLarge),
        child:
            (invitationDirection == MessageDirection.unset)
                ? _inviteToConnectButton()
                : (MessageDirection.sent == invitationDirection && userHasUsedMm3)
                ? _cancelRequestButton()
                : (MessageDirection.connected == invitationDirection)
                ? _messageButton()
                : (MessageDirection.received == invitationDirection ||
                    MessageDirection.sent == invitationDirection)
                ? _viewInviteButton()
                : null,
      ),
    );
  }

  _inviteStatusDesktopButton() {
    // TODO: remove this variable when MM2 is deprecated
    final userHasUsedMm3 = widget.user.hasSignedInToMm3 ?? false;
    return (invitationDirection == MessageDirection.unset)
        ? _inviteToConnectButton()
        : (MessageDirection.sent == invitationDirection && userHasUsedMm3)
        ? _cancelRequestButton()
        : (MessageDirection.connected == invitationDirection)
        ? _messageButton()
        : (MessageDirection.received == invitationDirection ||
            MessageDirection.sent == invitationDirection)
        ? _viewInviteButton()
        : null;
  }

  _inviteToConnectButton() {
    return CommonButton.primaryRoundedRectangle(
      context: context,
      title: AppLocale.current.inviteCreateTitle,
      buttonSize: ButtonSize.medium,
      onPressed: () {
        if (isDesktop) {
          showDialog(
            context: context,
            barrierDismissible: true,
            builder: (context) {
              return DialogTemplate.withCustomContent(
                title: AppLocale.current.inviteCreateTitle,
                content: SizedBox(
                  height: context.theme.d.inviteToConnectDialogHeight,
                  child: InviteToConnectScreen(userId: widget.user.id),
                ),
                showButtons: false,
              );
            },
          );
          return;
        }
        router.push('${AppRoutes.profileInvite.path}/${widget.user.id}');
      },
    );
  }

  _cancelRequestButton() {
    return CommonButton.primaryRoundedRectangle(
      context: context,
      title: AppLocale.current.profileViewHeaderWithdraw,
      buttonSize: ButtonSize.medium,
      onPressed: () async {
        await _invitationsProvider.withdrawChannelInvitation(
          channelInvitationId: invitationId ?? '',
        );
        AnalyticService.invitationWithdraw();
        if (mounted && context.canPop()) {
          context.pop();
        }
      },
    );
  }

  onAccept() async {
    await _invitationsProvider.acceptChannelInvitation(
      channelInvitationId: invitationId ?? '',
      senderUserId: widget.user.id,
    );
    final ChannelForUser? newChannel =
        _channelsProvider.channels
            .where((e) => e.participants.any((p) => p.user.id == widget.user.id))
            .firstOrNull;
    _scaffoldModel.setParams(index: Tabs.inbox.index);
    if (newChannel != null) {
      router.replace('${AppRoutes.inboxChats.path}/${newChannel.id}');
    } else {
      router.replace(AppRoutes.inboxChats.path);
    }
  }

  onDecline() async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return DeclineChannelInvitationDialog(
          name: widget.user.firstName,
          continueAction: (Enum$DeclineChannelInvitationReasonTextId selectedReason) async {
            if (selectedReason == Enum$DeclineChannelInvitationReasonTextId.tooBusy) {
              setState(() {
                showVacationPromptDialog = true;
              });
            }

            await _invitationsProvider.declineChannelInvitation(
              channelInvitationId: invitationId ?? '',
              reasonTextId: selectedReason,
            );
            if (!showVacationPromptDialog) {
              _scaffoldModel.setParams(index: Tabs.inbox.index);
              router.replace(AppRoutes.inboxInvitesReceived.path);
            }
          },
        );
      },
    );

    if (mounted) {
      if (showVacationPromptDialog && !(_userProvider.myUser?.isOnVacation ?? false)) {
        await _showVacationModePromptDialog();
        _scaffoldModel.setParams(index: Tabs.inbox.index);
        router.replace(AppRoutes.inboxInvitesReceived.path);
      }
    }
  }

  _showVacationModePromptDialog() async {
    await showDialog(
      context: context,
      builder: (dialogContext) {
        return DialogTemplate(
          title: AppLocale.current.vacationPromptDialogTitle,
          description: AppLocale.current.vacationPromptDialogMsg,
          onAction: () async {
            Loader.show(dialogContext);

            await _userProvider.updateUser(
              input: Input$UserInput(id: _userProvider.myUser?.id, isOnVacation: true),
            );

            if (dialogContext.mounted) {
              Loader.hide(dialogContext);

              Flushbar(
                message: AppLocale.current.vacationModeOn,
                messageColor: dialogContext.colorScheme.surface,
                messageSize: dialogContext.theme.d.fontSizeMedium,
                duration: AppUtility.snackBarDuration,
              ).show(dialogContext);

              Navigator.pop(dialogContext);
            }
          },
          actionButtonTitle: AppLocale.current.vacationPromptDialogActionButtonTitle,
          cancelButtonTitle: AppLocale.current.vacationPromptDialogCancelButtonTitle,
        );
      },
    );
  }

  Widget _messageButton() {
    return CommonButton.primaryRoundedRectangle(
      context: context,
      icon: const Icon(MicromentorIcons.chatBubbleOutline),
      iconPosition: IconPosition.atRight,
      key: const Key('message'),
      title: AppLocale.current.message,
      buttonSize: ButtonSize.medium,
      onPressed: () async {
        Provider.of<ScaffoldModel>(context, listen: false).setParams(index: Tabs.inbox.index);
        final channelId = channelData?.$1;
        if (!mounted && channelId == null) return;
        (channelData?.$2 ?? false)
            ? context.goNamed(
              AppRoutes.inboxArchivedChannelId.name,
              pathParameters: {RouteParams.channelId: channelId ?? ''},
            )
            : context.go('${AppRoutes.inboxChats.path}/$channelId');
      },
    );
  }

  Widget _viewInviteButton() {
    return CommonButton.primaryRoundedRectangle(
      context: context,
      key: const Key('viewinvite'),
      title: AppLocale.current.view_invitation,
      buttonSize: ButtonSize.medium,
      onPressed: () {
        bool isSentInvitation = invitationDirection == MessageDirection.sent;

        if (AppUtility.displayDesktopUI(context, isChatScreen: true)) {
          Provider.of<ScaffoldModel>(context, listen: false).setParams(index: Tabs.inbox.index);
          isSentInvitation
              ? context.replaceNamed(AppRoutes.inviteSentWeb.name, extra: invitationId ?? '')
              : context.replaceNamed(
                AppRoutes.newInviteReceivedWeb.name,
                extra: invitationId ?? '',
              );
        } else {
          isSentInvitation
              ? context.push('${AppRoutes.inboxInvitesSent.path}/$invitationId')
              : context.push('${AppRoutes.inboxInvitesReceived.path}/$invitationId');
        }
      },
    );
  }
}
