import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/__generated/schema/operations_user.graphql.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../../utilities/loading/loading_provider.dart';
import '../../../widgets.dart';

class EditExperienceScreen extends StatefulWidget {
  final int? experienceIndex;

  const EditExperienceScreen({super.key, this.experienceIndex});

  @override
  State<EditExperienceScreen> createState() => _EditExperienceScreenState();
}

class _EditExperienceScreenState extends State<EditExperienceScreen> {
  late final UserProvider _userProvider;
  late final TextEditingController _roleController;
  late final TextEditingController _companyController;
  late final TextEditingController _locationController;
  late final TextEditingController _startDateController;
  late final TextEditingController _endDateController;
  bool _isCurrentRole = false;
  bool _showInHeader = false;
  final _formKey = GlobalKey<FormState>();
  bool _isBackEnabled = true;
  final durationMaxRange = 50;
  final GlobalKey<State> _dialogKey = GlobalKey<State>();
  late final Query$FindUserById$findUserById$businessExperiences? oldExperience;
  late User? _myUser;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myUser = _userProvider.myUser;
    _roleController = TextEditingController();
    _companyController = TextEditingController();
    _locationController = TextEditingController();
    _startDateController = TextEditingController();
    _endDateController = TextEditingController();
    if (_myUser?.businessExperiences != null && widget.experienceIndex != null) {
      oldExperience = _myUser?.businessExperiences![widget.experienceIndex!];

      _roleController.text = oldExperience?.jobTitle ?? '';
      _companyController.text = oldExperience?.businessName ?? '';
      _locationController.text = oldExperience?.city ?? ''; //TODO use state too
      _startDateController.text = oldExperience?.startDate?.year.toString() ?? '';
      _endDateController.text = oldExperience?.endDate?.year.toString() ?? '';
      if (oldExperience?.endDate?.year == null) _isCurrentRole = true;
    } else {
      oldExperience = null;
    }
  }

  @override
  void dispose() {
    _roleController.dispose();
    _companyController.dispose();
    _locationController.dispose();
    _startDateController.dispose();
    _endDateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? _) async {
        if (didPop) return;
        if (_dialogKey.currentContext != null && Navigator.canPop(_dialogKey.currentContext!)) {
          Navigator.pop(_dialogKey.currentContext!);
        }
      },
      child: EditProfileTemplate(
        title:
            widget.experienceIndex == null
                ? AppLocale.current.profileEditSectionExperienceAddTitle
                : AppLocale.current.profileEditSectionExperienceEditTitle,
        actions:
            (widget.experienceIndex == null)
                ? null
                : [
                  IconButton(
                    onPressed: _showDeleteDialog,
                    icon: Icon(
                      MicromentorIcons.delete,
                      color: context.colorScheme.onSurfaceVariant,
                      size: context.theme.d.iconSizeLarge,
                    ),
                    padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall),
                  ),
                ],
        body: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormFieldWidget(
                label: AppLocale.current.profileEditSectionExperienceRoleInputLabel,
                hint: AppLocale.current.profileEditSectionExperienceRoleInputHint,
                contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
                textController: _roleController,
                textCapitalization: TextCapitalization.sentences,
                maxLength: 50,
                onChanged: (_) => setState(() {}),
              ),
              SizedBox(height: context.theme.d.paddingLarge),
              TextFormFieldWidget(
                label: AppLocale.current.profileEditSectionExperienceCompanyInputLabel,
                hint: AppLocale.current.profileEditSectionExperienceCompanyInputHint,
                contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
                textController: _companyController,
                textCapitalization: TextCapitalization.sentences,
                maxLength: 50,
                onChanged: (_) => setState(() {}),
              ),
              SizedBox(height: context.theme.d.paddingLarge),
              TextFormFieldWidget(
                label: AppLocale.current.profileEditSectionExperienceLocationInputLabel,
                hint: AppLocale.current.editProfileExperienceLocationHint,
                contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
                textCapitalization: TextCapitalization.sentences,
                textController: _locationController,
                onChanged: (_) => setState(() {}),
              ),
              SizedBox(height: context.theme.d.paddingXLarge),
              TextFormFieldWidget(
                label: AppLocale.current.profileEditSectionExperienceDateStartInputLabel,
                hint: AppLocale.current.profileEditSectionExperienceDateInputHint,
                contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
                textController: _startDateController,
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  setState(() {
                    _isBackEnabled = _formKey.currentState?.validate() ?? false;
                  });
                },
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(4),
                ],
                validator: (value) {
                  return context.theme.validator.validateYear(
                    value,
                    notAfter: _endDateController.text,
                    maxRange: durationMaxRange.toString(),
                  );
                },
              ),
              SizedBox(height: context.theme.d.paddingLarge),
              CheckboxListTile(
                value: _isCurrentRole,
                onChanged: (value) {
                  setState(() => _isCurrentRole = value ?? false);
                },
                controlAffinity: ListTileControlAffinity.leading,
                title: Text(
                  AppLocale.current.profileEditSectionExperienceDateEndPresentInputLabel,
                  style: context.theme.textTheme.titleMedium?.copyWith(
                    color: context.colorScheme.surfaceBright,
                  ),
                ),
              ),
              SizedBox(height: context.theme.d.paddingLarge),
              TextFormFieldWidget(
                label: AppLocale.current.profileEditSectionExperienceDateEndInputLabel,
                hint: AppLocale.current.profileEditSectionExperienceDateInputHint,
                contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
                textController: _endDateController,
                keyboardType: TextInputType.number,
                enabled: !_isCurrentRole,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(4),
                ],
                onChanged: (value) {
                  setState(() {
                    _isBackEnabled = _formKey.currentState?.validate() ?? false;
                  });
                },
                validator: (value) {
                  return context.theme.validator.validateYear(
                    value,
                    notBefore: _startDateController.text,
                    maxRange: durationMaxRange.toString(),
                  );
                },
              ),
              SizedBox(height: context.theme.d.paddingLarge),
              if (context.theme.appFeatures.currentRoleToggle)
                InkWell(
                  onTap: () {
                    setState(() => _showInHeader = !_showInHeader);
                  },
                  child: ListTile(
                    leading: Switch(
                      value: _showInHeader,
                      onChanged: (value) {
                        setState(() => _showInHeader = value);
                      },
                    ),
                    title: Text(
                      AppLocale.current.profileEditSectionExperienceShowInHeaderInputLabel,
                      style: context.theme.textTheme.bodyLarge?.copyWith(
                        color: context.theme.colorScheme.onSurface,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
        isBackEnabled: _isBackEnabled,
        noChangeInData: _noChangeInExperience(),
        editUserProfile: () async {
          List<Input$BusinessExperienceInput> experienceInputs = _getExperienceInputs() ?? [];
          final inputExperience = Input$BusinessExperienceInput(
            jobTitle: _roleController.text.trim(),
            businessName: _companyController.text.trim(),
            city: _locationController.text.trim(),
            startDate:
                _startDateController.text.isNotEmpty
                    ? DateTime(
                      int.parse(_startDateController.text),
                    ).add(const Duration(days: 1)).toUtc()
                    : null,
            endDate:
                _endDateController.text.isNotEmpty && !_isCurrentRole
                    ? DateTime(
                      int.parse(_endDateController.text),
                    ).add(const Duration(days: 1)).toUtc()
                    : null,
            userId: _myUser?.id,
          );

          if (widget.experienceIndex == null) {
            experienceInputs.add(inputExperience);
          } else {
            experienceInputs[widget.experienceIndex!] = inputExperience;
          }
          return _updateUser(experienceInputs);
        },
      ),
    );
  }

  _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (dialogContext) {
        return DialogTemplate(
          key: _dialogKey,
          title: AppLocale.current.deleteExperienceDialogHeading,
          description: AppLocale.current.deleteExperienceTitle,
          actionButtonTitle: AppLocale.current.delete,
          onAction: () {
            Navigator.pop(dialogContext);
            _deleteAction();
          },
        );
      },
    );
  }

  _deleteAction() async {
    if (widget.experienceIndex == null) return;
    Loader.show(context);
    _myUser?.businessExperiences?.removeAt(widget.experienceIndex!);
    await _updateUser(_getExperienceInputs());
    if (!mounted) return;
    // check this video : from flutter team https://www.youtube.com/watch?v=bzWaMpD1LHY

    Loader.hide(context);
    GoRouter.of(context).pop();
  }

  List<Input$BusinessExperienceInput>? _getExperienceInputs() {
    return _myUser?.businessExperiences?.isNotEmpty == true
        ? _myUser?.businessExperiences
            ?.map(
              (experience) => Input$BusinessExperienceInput(
                id: experience.id,
                jobTitle: experience.jobTitle,
                businessName: experience.businessName,
                city: experience.city,
                startDate: experience.startDate,
                endDate: experience.endDate,
                userId: _myUser?.id,
              ),
            )
            .toList()
        : null;
  }

  Future<OperationException?> _updateUser(
    List<Input$BusinessExperienceInput>? experienceInputs,
  ) async {
    final result = await _userProvider.updateUser(
      input: Input$UserInput(id: _myUser?.id).copyWith(businessExperiences: experienceInputs),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );
    if (result.hasError) {
      return result.operationException;
    }
    return null;
  }

  _noChangeInExperience() {
    //TODO: need to discuss this case
    if (_roleController.text.isEmpty || _companyController.text.isEmpty) {
      return _roleController.text.isEmpty || _companyController.text.isEmpty;
    }

    if (_roleController.text.trim() == (oldExperience?.jobTitle ?? '').trim() &&
        _companyController.text.trim() == (oldExperience?.businessName ?? '').trim() &&
        _locationController.text.trim() == (oldExperience?.city ?? '').trim() &&
        _startDateController.text.trim() ==
            (oldExperience?.startDate?.year ?? '').toString().trim() &&
        _endDateController.text.trim() == (oldExperience?.endDate?.year ?? '').toString().trim() &&
        _isCurrentRole == (oldExperience?.endDate?.year ?? true)) {
      return true;
    }
    return false;
  }
}
