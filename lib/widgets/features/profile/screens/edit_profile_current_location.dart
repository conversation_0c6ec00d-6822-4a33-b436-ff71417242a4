import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../widgets.dart';

class EditCurrentLocationScreen extends StatefulWidget {
  const EditCurrentLocationScreen({super.key});

  @override
  State<EditCurrentLocationScreen> createState() => _EditCurrentLocationScreenState();
}

class _EditCurrentLocationScreenState extends State<EditCurrentLocationScreen> {
  late final UserProvider _userProvider;
  late final ContentProvider _contentProvider;
  late final TextEditingController _textEditingController;
  Country? _selectedCountry;
  Country? _oldSelectedCountry;
  String? validatorMessage;
  late User? _myUser;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myUser = _userProvider.myUser;

    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    _selectedCountry =
        _contentProvider.countryOptions
            ?.toList()
            .where((element) => element.textId == _myUser?.countryOfResidenceTextId)
            .firstOrNull;
    _textEditingController = TextEditingController(text: _selectedCountry?.translatedValue ?? '');
    _oldSelectedCountry = _selectedCountry;
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditProfileTemplate(
      title: AppLocale.current.profileEditMainAboutCurrentCountrySection,
      isBackEnabled: _selectedCountry != null,
      body: AutocompleteTextField<Country>(
        textEditingController: _textEditingController,
        label: AppLocale.current.signupLocationCountryInputLabel,
        hint: AppLocale.current.signupLocationInputHint,
        showClose: true,
        showPrefixSearchIcon: true,
        itemBuilder: (context, country) {
          return ListTile(title: Text(country.translatedValue ?? ''));
        },
        suggestionsCallback: (String search) {
          return _contentProvider.countryOptions
              ?.toList()
              .where(
                (country) =>
                    ('${country.translatedValue} ${country.textId}').toLowerCase().contains(
                      search.trim().toLowerCase(),
                    ) ==
                    true,
              )
              .toList();
        },
        emptyBuilder: (_) {
          return ListTile(title: Text(AppLocale.current.emptyLocationMessage));
        },
        onClose: (controller) {
          setState(() {
            controller.text = '';
            _selectedCountry = null;
            validate();
          });
        },
        onChange: () {
          setState(() {
            _selectedCountry = null;
            validate();
          });
        },
        onSelected: (country) {
          setState(() {
            _selectedCountry = country;
            _textEditingController.text = country.translatedValue ?? '';
            validate();
          });
        },
        validatorMessage: validatorMessage,
      ),
      noChangeInData: _oldSelectedCountry?.translatedValue == _selectedCountry?.translatedValue,
      editUserProfile: () async {
        final result = await _userProvider.updateUser(
          input: Input$UserInput(
            id: _myUser?.id,
          ).copyWith(countryOfResidenceTextId: _selectedCountry?.textId),
          options: UpdateObjectOptions<User>(
            loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
            // If we need to switch to isUpdatedFunc:
            // isUpdatedFunc: (User user) => user.cityOfResidence == _value,
          ),
        );

        if (result.hasError) {
          return result.operationException;
        }
      },
    );
  }

  validate() {
    validatorMessage = _selectedCountry == null ? AppLocale.current.countryValidationError : null;
  }
}
