import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../widgets.dart';

class EditCompanyLocationScreen extends StatefulWidget {
  const EditCompanyLocationScreen({super.key});

  @override
  State<EditCompanyLocationScreen> createState() => _EditCompanyLocationScreenState();
}

class _EditCompanyLocationScreenState extends State<EditCompanyLocationScreen> {
  late final UserProvider _userProvider;
  late final TextEditingController _textEditingController;
  late final User? _myUser;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myUser = _userProvider.myUser;
    _textEditingController = TextEditingController(text: _myUser?.companies?.firstOrNull?.location);
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditProfileTemplate(
      title: AppLocale.current.profileEditSectionBusinessLocationTitle,
      body: Form(
        child: TextFormFieldWidget(
          prefixIcon: const Icon(MicromentorIcons.search),
          label: AppLocale.current.profileEditSectionBusinessLocationInputLabel,
          hint: AppLocale.current.profileEditSectionBusinessLocationInputHint,
          textController: _textEditingController,
          textCapitalization: TextCapitalization.sentences,
          onChanged: (_) => setState(() {}),
        ),
      ),
      noChangeInData:
          (_myUser?.companies?.firstOrNull?.location?.trim() == _textEditingController.text.trim()),
      editUserProfile: () async {
        String? location = _textEditingController.text.trim();

        final result = await _userProvider.updateCompany(
          input: Input$CompanyInput(id: _myUser?.companies?.firstOrNull?.id, location: location),
        );

        if (result.gqlQueryResult.hasException) {
          return result.gqlQueryResult.exception;
        }

        await _userProvider.loadUser(
          userId: _myUser?.id,
          options: LoadObjectOptions<User>(
            pollingConfig: PollingConfig<User>(
              isUpdatedFunc: (User user) => user.companies?.firstOrNull?.location == location,
            ),
          ),
        );
      },
    );
  }
}
