import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/__generated/schema/operations_user.graphql.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/loading/loading_provider.dart';
import '../../../widgets.dart';

class EditEducationScreen extends StatefulWidget {
  final int? experienceIndex;

  const EditEducationScreen({super.key, this.experienceIndex});

  @override
  State<EditEducationScreen> createState() => _EditEducationScreenState();
}

class _EditEducationScreenState extends State<EditEducationScreen> {
  late final UserProvider _userProvider;
  late final TextEditingController _schoolController;
  late final TextEditingController _degreeController;
  late final TextEditingController _fieldController;
  late final TextEditingController _startDateController;
  late final TextEditingController _endDateController;
  bool _showInHeader = false;
  bool _isBackEnabled = true;
  final _formKey = GlobalKey<FormState>();
  final GlobalKey<State> _dialogKey = GlobalKey<State>();
  late final Query$FindUserById$findUserById$academicExperiences? oldacademicExperience;
  late User? _myuser;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myuser = _userProvider.myUser;
    _schoolController = TextEditingController();
    _degreeController = TextEditingController();
    _fieldController = TextEditingController();
    _startDateController = TextEditingController();
    _endDateController = TextEditingController();
    if (widget.experienceIndex != null) {
      oldacademicExperience = _myuser?.academicExperiences![widget.experienceIndex!];

      _schoolController.text = oldacademicExperience?.institutionName ?? '';
      _degreeController.text = oldacademicExperience?.degreeType ?? '';
      _fieldController.text = oldacademicExperience?.fieldOfStudy ?? '';
      _startDateController.text = oldacademicExperience?.startDate?.year.toString() ?? '';
      _endDateController.text = oldacademicExperience?.endDate?.year.toString() ?? '';
    } else {
      oldacademicExperience = null;
    }
  }

  @override
  void dispose() {
    _schoolController.dispose();
    _degreeController.dispose();
    _fieldController.dispose();
    _startDateController.dispose();
    _endDateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? _) async {
        if (didPop) return;
        if (_dialogKey.currentContext != null && Navigator.canPop(_dialogKey.currentContext!)) {
          Navigator.pop(_dialogKey.currentContext!);
        }
      },
      child: EditProfileTemplate(
        title:
            widget.experienceIndex == null
                ? AppLocale.current.profileEditSectionEducationAddTitle
                : AppLocale.current.profileEditSectionEducationEditTitle,
        actions:
            (widget.experienceIndex == null)
                ? null
                : [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall),
                    child: IconButton(
                      onPressed: _showDeleteDialog,
                      icon: Icon(
                        MicromentorIcons.delete,
                        color: context.colorScheme.onSurfaceVariant,
                        size: context.theme.d.iconSizeLarge,
                      ),
                    ),
                  ),
                ],
        body: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormFieldWidget(
                label: AppLocale.current.profileEditSectionEducationSchoolInputLabel,
                hint: AppLocale.current.profileEditSectionEducationSchoolInputHint,
                contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
                textController: _schoolController,
                textCapitalization: TextCapitalization.sentences,
                maxLength: 50,
                onChanged: (_) => setState(() {}),
              ),
              SizedBox(height: context.theme.d.paddingLarge),
              TextFormFieldWidget(
                label: AppLocale.current.profileEditSectionEducationDegreeInputLabel,
                hint: AppLocale.current.profileEditSectionEducationDegreeInputHint,
                contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
                textController: _degreeController,
                textCapitalization: TextCapitalization.sentences,
                maxLength: 50,
                onChanged: (_) => setState(() {}),
              ),
              SizedBox(height: context.theme.d.paddingLarge),
              TextFormFieldWidget(
                label: AppLocale.current.profileEditSectionEducationStudyFieldInputLabel,
                hint: AppLocale.current.profileEditSectionEducationStudyFieldInputHint,
                contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
                textCapitalization: TextCapitalization.sentences,
                textController: _fieldController,
                onChanged: (_) => setState(() {}),
              ),
              SizedBox(height: context.theme.d.paddingXLarge),
              TextFormFieldWidget(
                label: AppLocale.current.profileEditSectionEducationDateStartInputLabel,
                hint: AppLocale.current.profileEditSectionEducationDateInputHint,
                contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
                textController: _startDateController,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(4),
                ],
                onChanged: (value) {
                  setState(() {
                    _isBackEnabled = _formKey.currentState?.validate() ?? false;
                  });
                },
                validator: (value) {
                  return context.theme.validator.validateYear(
                    value,
                    notAfter: _endDateController.text,
                  );
                },
              ),
              SizedBox(height: context.theme.d.paddingLarge),
              TextFormFieldWidget(
                label: AppLocale.current.profileEditSectionEducationDateEndInputLabel,
                hint: AppLocale.current.profileEditSectionEducationDateInputHint,
                contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
                textController: _endDateController,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(4),
                ],
                onChanged: (value) {
                  setState(() {
                    _isBackEnabled = _formKey.currentState?.validate() ?? false;
                  });
                },
                validator: (value) {
                  return context.theme.validator.validateYear(
                    value,
                    notBefore: _startDateController.text,
                  );
                },
              ),
              SizedBox(height: context.theme.d.paddingLarge),
              if (StaticAppFeatures.editProfileShowEducationInHeader)
                InkWell(
                  onTap: () {
                    setState(() => _showInHeader = !_showInHeader);
                  },
                  child: ListTile(
                    leading: Switch(
                      value: _showInHeader,
                      onChanged: (value) {
                        setState(() => _showInHeader = value);
                      },
                    ),
                    title: Text(
                      AppLocale.current.profileEditSectionEducationShowInHeaderInputLabel,
                      style: context.theme.textTheme.bodyLarge?.copyWith(
                        color: context.theme.colorScheme.onSurface,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
        isBackEnabled: _isBackEnabled,
        noChangeInData: _noChangeInEducation(),
        editUserProfile: () async {
          List<Input$AcademicExperienceInput> experienceInputs = _getExperienceInputs() ?? [];

          final inputExperience = Input$AcademicExperienceInput(
            institutionName: _schoolController.text.trim(),
            degreeType: _degreeController.text.trim(),
            fieldOfStudy: _fieldController.text.trim(),
            startDate:
                _startDateController.text.isNotEmpty
                    ? DateTime(
                      int.parse(_startDateController.text.trim()),
                    ).add(const Duration(days: 1)).toUtc()
                    : null,
            endDate:
                _endDateController.text.isNotEmpty
                    ? DateTime(
                      int.parse(_endDateController.text.trim()),
                    ).add(const Duration(days: 1)).toUtc()
                    : null,
          );

          if (widget.experienceIndex == null) {
            experienceInputs.add(inputExperience);
          } else {
            experienceInputs[widget.experienceIndex!] = inputExperience;
          }
          return _updateUser(experienceInputs);
        },
      ),
    );
  }

  _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (dialogContext) {
        return DialogTemplate(
          key: _dialogKey,
          title: AppLocale.current.deleteEducationDialogHeading,
          description: AppLocale.current.deleteEducationTitle,
          actionButtonTitle: AppLocale.current.delete,
          onAction: () {
            Navigator.pop(dialogContext);
            _deleteAction();
          },
        );
      },
    );
  }

  _deleteAction() async {
    if (widget.experienceIndex == null) return;
    Loader.show(context);
    _myuser?.academicExperiences?.removeAt(widget.experienceIndex!);
    await _updateUser(_getExperienceInputs());
    if (!mounted) return;
    Loader.hide(context);
    GoRouter.of(context).pop();
  }

  List<Input$AcademicExperienceInput>? _getExperienceInputs() {
    return _myuser?.academicExperiences?.isNotEmpty == true
        ? _myuser?.academicExperiences
            ?.map(
              (experience) => Input$AcademicExperienceInput(
                id: experience.id,
                institutionName: experience.institutionName,
                degreeType: experience.degreeType,
                fieldOfStudy: experience.fieldOfStudy,
                startDate: experience.startDate,
                endDate: experience.endDate,
                userId: _myuser?.id,
              ),
            )
            .toList()
        : null;
  }

  Future<OperationException?> _updateUser(
    List<Input$AcademicExperienceInput>? experienceInputs,
  ) async {
    final result = await _userProvider.updateUser(
      input: Input$UserInput(id: _myuser?.id).copyWith(academicExperiences: experienceInputs),
      options: UpdateObjectOptions<User>(
        loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
      ),
    );

    if (result.hasError) {
      return result.operationException;
    }
    return null;
  }

  _noChangeInEducation() {
    //TODO: need to discuss this case
    if (_schoolController.text.isEmpty) {
      return _schoolController.text.isEmpty;
    }

    if (_schoolController.text.trim() == (oldacademicExperience?.institutionName ?? '').trim() &&
        _degreeController.text.trim() == (oldacademicExperience?.degreeType ?? '').trim() &&
        _fieldController.text.trim() == (oldacademicExperience?.fieldOfStudy ?? '').trim() &&
        _startDateController.text.trim() ==
            (oldacademicExperience?.startDate?.year ?? '').toString().trim() &&
        _endDateController.text.trim() ==
            (oldacademicExperience?.endDate?.year ?? '').toString().trim()) {
      return true;
    }
    return false;
  }
}
