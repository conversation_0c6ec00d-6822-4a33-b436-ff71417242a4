import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../widgets.dart';

class EditCompanyMissionScreen extends StatefulWidget {
  const EditCompanyMissionScreen({super.key});

  @override
  State<EditCompanyMissionScreen> createState() => _EditCompanyMissionScreenState();
}

class _EditCompanyMissionScreenState extends State<EditCompanyMissionScreen> {
  late final UserProvider _userProvider;
  late final TextEditingController _textEditingController;
  late final User? _myUser;
  String? _companyMission;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myUser = _userProvider.myUser;
    _textEditingController = TextEditingController(
      text: _myUser?.companies?.firstOrNull?.description,
    );
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditProfileTemplate(
      title: AppLocale.current.profileEditSectionBusinessMissionTitle,
      body: Form(
        child: TextFormFieldWidget(
          hint: AppLocale.current.profileEditSectionBusinessMissionInputHint,
          textController: _textEditingController,
          textCapitalization: TextCapitalization.sentences,
          maxLength: 1000,
          maxLines: 6,
          onChanged: (value) {
            setState(() {
              _companyMission = value;
            });
          },
        ),
      ),
      editUserProfile: () async {
        final result = await _userProvider.updateCompany(
          input: Input$CompanyInput(
            id: _myUser?.companies?.firstOrNull?.id,
            description: _companyMission,
          ),
        );

        if (result.gqlQueryResult.hasException) {
          return result.gqlQueryResult.exception;
        }

        await _userProvider.loadUser(
          userId: _myUser?.id,
          options: LoadObjectOptions<User>(
            pollingConfig: PollingConfig<User>(
              isUpdatedFunc:
                  (User user) => user.companies?.firstOrNull?.description == _companyMission,
            ),
          ),
        );
      },
    );
  }
}
