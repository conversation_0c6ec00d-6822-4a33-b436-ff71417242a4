import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../widgets.dart';

class EditCompanyNameScreen extends StatefulWidget {
  const EditCompanyNameScreen({super.key});

  @override
  State<EditCompanyNameScreen> createState() => _EditCompanyNameScreenState();
}

class _EditCompanyNameScreenState extends State<EditCompanyNameScreen> {
  late final UserProvider _userProvider;
  late final TextEditingController _textEditingController;
  late final User? _myUser;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myUser = _userProvider.myUser;
    _textEditingController = TextEditingController(text: _myUser?.companies?.firstOrNull?.name);
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditProfileTemplate(
      title: AppLocale.current.profileEditSectionBusinessNameTitle,
      body: Form(
        child: TextFormFieldWidget(
          label: AppLocale.current.profileEditSectionBusinessNameInputLabel,
          hint: AppLocale.current.profileEditSectionBusinessNameInputHint,
          textController: _textEditingController,
          textCapitalization: TextCapitalization.sentences,
          onChanged: (_) => setState(() {}),
        ),
      ),
      noChangeInData: _myUser?.companies?.firstOrNull?.name == _textEditingController.text.trim(),
      editUserProfile: () async {
        String companyName = _textEditingController.text.trim();

        final result = await _userProvider.updateCompany(
          input: Input$CompanyInput(id: _myUser?.companies?.firstOrNull?.id, name: companyName),
        );

        if (result.gqlQueryResult.hasException) {
          return result.gqlQueryResult.exception;
        }

        await _userProvider.loadUser(
          userId: _myUser?.id,
          options: LoadObjectOptions<User>(
            pollingConfig: PollingConfig<User>(
              isUpdatedFunc: (User user) => user.companies?.firstOrNull?.name == companyName,
            ),
          ),
        );
      },
    );
  }
}
