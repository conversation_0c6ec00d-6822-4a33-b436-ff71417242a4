import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/operations_user.graphql.dart';
import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../widgets.dart';

class EditBusinessChallengeScreen extends StatefulWidget {
  const EditBusinessChallengeScreen({super.key});

  @override
  State<EditBusinessChallengeScreen> createState() => _EditBusinessChallengeScreenState();
}

class _EditBusinessChallengeScreenState extends State<EditBusinessChallengeScreen> {
  late final UserProvider _userProvider;
  late final TextEditingController _textEditingController;
  late final String _menteeGroupMembershipId;
  late final User? _myUser;
  String? _businessChallenges;
  String? _oldBusinessChallenges;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myUser = _userProvider.myUser;
    final maybeMenteeGroupMembership = _myUser?.groupMemberships
        .where((g) => g.groupIdent == GroupIdent.mentees.name)
        .firstOrNull
        ?.maybeWhen(menteesGroupMembership: (g) => g, orElse: () => null);
    _menteeGroupMembershipId = maybeMenteeGroupMembership!.id;
    _oldBusinessChallenges = maybeMenteeGroupMembership.currentChallenges;
    _textEditingController = TextEditingController(text: _oldBusinessChallenges);
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditProfileTemplate(
      title: AppLocale.current.businessChallengeTitle,
      body: Form(
        child: TextFormFieldWidget(
          hint: AppLocale.current.profileEditSectionBusinessReasonInputHint,
          textController: _textEditingController,
          textCapitalization: TextCapitalization.sentences,
          maxLength: 1000,
          maxLines: 6,
          onChanged: (value) {
            setState(() {
              _businessChallenges = value;
            });
          },
        ),
      ),
      noChangeInData: _oldBusinessChallenges?.trim() == _textEditingController.text.trim(),
      editUserProfile: () async {
        final result = await _userProvider.updateMenteesGroupMembership(
          input: Input$MenteesGroupMembershipInput(
            id: _menteeGroupMembershipId,
            currentChallenges: _businessChallenges,
          ),
        );

        if (result.gqlQueryResult.hasException) {
          return result.gqlQueryResult.exception;
        }

        await _userProvider.loadUser(
          userId: _myUser?.id,
          options: LoadObjectOptions<User>(
            pollingConfig: PollingConfig<User>(
              isUpdatedFunc: (User user) {
                final menteeGroupMembership = user.groupMemberships
                    .where((g) => g.groupIdent == GroupIdent.mentees.name)
                    .firstOrNull
                    ?.maybeWhen(menteesGroupMembership: (g) => g, orElse: () => null);

                return menteeGroupMembership != null &&
                    menteeGroupMembership.currentChallenges == _businessChallenges;
              },
            ),
          ),
        );
      },
    );
  }
}
