import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:textfield_tags/textfield_tags.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../widgets.dart';

class EditOtherLanguagesScreen extends StatefulWidget {
  const EditOtherLanguagesScreen({super.key});

  @override
  State<EditOtherLanguagesScreen> createState() => _EditOtherLanguagesScreenState();
}

class _EditOtherLanguagesScreenState extends State<EditOtherLanguagesScreen> {
  late final UserProvider _userProvider;
  late final ContentProvider _contentProvider;
  final _otherLanguagesController = StringTagController();
  late double extraScrollingSpace;
  late final User? _myUser;
  final Set<String> _initialValue = {};
  bool hasDataChange = false;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myUser = _userProvider.myUser;
    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    extraScrollingSpace = 0;
    if (_myUser != null) {
      _initialValue.addAll(_myUser.spokenLanguages.map((e) => e.translatedValue!));
    }

    for (var element in _initialValue) {
      _otherLanguagesController.addTag(element);
    }
  }

  @override
  void dispose() {
    try {
      _otherLanguagesController.dispose();
    } catch (_) {}
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditProfileTemplate(
      title: AppLocale.current.profileEditSectionAboutLanguageOthersTitle,
      subtitle: AppLocale.current.profileEditSectionAboutLanguageOthersSubtitle,
      extraScrollingSpace: extraScrollingSpace,
      body: _dropDownWidget(),
      showAccountSettingsLink: true,
      noChangeInData: _noChangeInValue(),
      editUserProfile: () async {
        List<String>? spokenLanguagesTextIds =
            _otherLanguagesController.getTags
                ?.map(
                  (t) =>
                      _contentProvider.languageOptions
                          ?.firstWhere((o) => o.translatedValue == t)
                          .textId,
                )
                .nonNulls
                .toList();
        final result = await _userProvider.updateUser(
          input: Input$UserInput(
            id: _myUser?.id,
            spokenLanguagesTextIds: spokenLanguagesTextIds ?? [],
          ),
          options: UpdateObjectOptions<User>(
            loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
            // If we need to switch to isUpdatedFunc:
            // isUpdatedFunc: (User user) {
            //   var textIds = user.spokenLanguages.map((e) => e.translatedValue).toList();
            //   Function unOrdDeepEq = const DeepCollectionEquality.unordered().equals;
            //   return unOrdDeepEq(textIds, _otherLanguagesController.getTags);
            // },
          ),
        );

        if (result.hasError) {
          return result.operationException;
        }
      },
    );
  }

  bool _noChangeInValue() {
    List<String> spokenLanguagesTextIds =
        _otherLanguagesController.getTags
            ?.map(
              (t) =>
                  _contentProvider.languageOptions
                      ?.firstWhere((o) => o.translatedValue == t)
                      .textId,
            )
            .nonNulls
            .toList() ??
        [];

    List<String> initialValue =
        _initialValue
            .map(
              (t) =>
                  _contentProvider.languageOptions
                      ?.firstWhere((o) => o.translatedValue == t)
                      .textId,
            )
            .nonNulls
            .toList();

    //No change in value
    if ((spokenLanguagesTextIds.isEmpty && !hasDataChange) && initialValue.isNotEmpty) {
      return true;
    }
    if (spokenLanguagesTextIds.length != initialValue.length) {
      return false;
    }

    // Sort both lists
    spokenLanguagesTextIds.sort();
    initialValue.sort();

    // Compare each element
    for (int i = 0; i < spokenLanguagesTextIds.length; i++) {
      if (spokenLanguagesTextIds[i] != initialValue[i]) {
        return false;
      }
    }

    return true;
  }

  _dropDownWidget() {
    return MultiSelectDropdown(
      editable: true,
      tagsController: _otherLanguagesController,
      options:
          _contentProvider.languageOptions?.map((e) => e.translatedValue).nonNulls.toList() ?? [],
      selectedOptions: _initialValue,
      label: AppLocale.current.profileEditOtherLanguageInputLabel,
      notFoundMessage: AppLocale.current.multiSelectDropdownLanguageNotFoundMsg,
      onDropdownSizeChanged: (double height) {
        setState(() {
          extraScrollingSpace = height;
        });
      },
      onChange:
          () => setState(() {
            hasDataChange = true;
          }),
    );
  }
}
