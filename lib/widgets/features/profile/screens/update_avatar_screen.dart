import 'dart:io';
import 'dart:typed_data' as typed_data;
import 'dart:ui' as ui;

import 'package:another_flushbar/flushbar.dart';
import 'package:crop_image/crop_image.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mm_flutter_app/__generated/schema/schema.graphql.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/user_provider.dart';
import 'package:mm_flutter_app/utilities/errors/error_handler.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/uploaded_assets_provider.dart';
import '../../../../utilities/file_helpers.dart';
import '../../../../utilities/loading/loading_provider.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class UpdateAvatarScreen extends StatefulWidget {
  const UpdateAvatarScreen.mobile({super.key});
  const UpdateAvatarScreen.desktop({super.key});

  @override
  State<UpdateAvatarScreen> createState() => _UpdateAvatarScreenState();
}

class _UpdateAvatarScreenState extends State<UpdateAvatarScreen> {
  late UploadedAssetsProvider _uploadedAssetsProvider;
  late final UserProvider _userProvider;

  bool isDragging = false;
  ValueNotifier<XFile?> selectedFile = ValueNotifier<XFile?>(null);
  ValueNotifier<Image?> croppedImage = ValueNotifier<Image?>(null);
  ValueNotifier<bool?> showingLoader = ValueNotifier<bool>(false);
  late List<String> acceptableFileType;

  late CropController controller;
  final ImagePicker _picker = ImagePicker();
  final GlobalKey<State> _dialogKey = GlobalKey<State>();
  late final User? myUser;
  late bool isDesktopView;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);

    acceptableFileType = FileHelpers.getSupportedImageFileExtensions();
    controller = CropController(
      aspectRatio: 1,
      defaultCrop: const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9),
    );
    _uploadedAssetsProvider = Provider.of<UploadedAssetsProvider>(context, listen: false);
    myUser = _userProvider.myUser;
  }

  uploadFile() async {
    if (croppedImage.value == null) {
      Flushbar(
        message: AppLocale.current.errorMessageUnknownError,
        backgroundColor: context.colorScheme.error,
        duration: AppUtility.snackBarDuration,
      ).show(context);

      return;
    }
    Loader.show(context);
    showingLoader.value = true;

    //cropped image quality decreasing to medium
    ui.Image bitmap = await controller.croppedBitmap(quality: ui.FilterQuality.medium);

    String? error = await _uploadedAssetsProvider.uploadAssetFile(
      file: croppedImage.value!,
      imageBitmap: bitmap,
      assetType: Enum$UploadedAssetType.avatar,
    );

    if (mounted) {
      Loader.hide(context);
      showingLoader.value = false;
      if (error == null) {
        showDialog(
          context: context,
          builder: (dialogContext) {
            return DialogTemplate.withCustomContent(
              key: _dialogKey,
              title: AppLocale.current.updateAvatarSuccessTitle,
              showButtons: false,
              showCloseButton: false,
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(AppLocale.current.updateAvatarSuccessMsg),
                  SizedBox(height: context.theme.d.paddingMedium),
                  CommonButton.textButton(
                    context: context,
                    title: AppLocale.current.updateAvatarGotItText,
                    onPressed: () async {
                      Navigator.pop(dialogContext);
                      context.pop();
                    },
                  ),
                ],
              ),
            );
          },
        );
      } else {
        String errorMessage = error.isEmpty ? AppLocale.current.errorMessageUnknownError : error;
        Flushbar(
          message: errorMessage,
          backgroundColor: context.colorScheme.error,
          duration: AppUtility.snackBarDuration,
        ).show(context);
      }
    }
  }

  Future<Uint8List?> _getByteArrayOfSelectedFile(Object file) async {
    return await FileHelpers.getFileBinaryData(file) as typed_data.Uint8List;
  }

  @override
  void didChangeDependencies() {
    isDesktopView = AppUtility.displayDesktopUI(context);
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return _body();
  }

  Widget _body() {
    return SafeArea(
      child: PopScope(
        canPop: false,
        onPopInvokedWithResult: (bool didPop, Object? _) async {
          if (showingLoader.value != true) {
            if (_dialogKey.currentContext != null && Navigator.canPop(_dialogKey.currentContext!)) {
              Navigator.of(_dialogKey.currentContext!).pop();
            }
            if (didPop) return;
            if (context.canPop()) {
              context.pop();
            }
          }
        },
        child: Padding(
          padding: EdgeInsets.all(context.theme.d.fontSizeMediumLarge),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ValueListenableBuilder<Image?>(
                valueListenable: croppedImage,
                builder: (context, file, _) {
                  return (croppedImage.value != null)
                      ? _cropProfilePicture(context.theme.d.boxSizeXXLarge)
                      : ProfileImageWidget(
                        avatarUrl: myUser?.avatarUrl,
                        size: context.theme.d.boxSizeXXLarge,
                      );
                },
              ),
              SizedBox(height: context.theme.d.paddingXLarge),
              isDesktopView ? desktopButtons() : mobileButtons(),
              SizedBox(
                height:
                    isDesktopView ? context.theme.d.paddingLarge : context.theme.d.paddingMedium,
              ),
              ValueListenableBuilder<Image?>(
                valueListenable: croppedImage,
                builder: (context, file, _) {
                  Widget saveImageButton = CommonButton.primaryRoundedRectangle(
                    isFullWidth: isDesktopView,
                    context: context,
                    title: AppLocale.current.updateAvatarSavePictureTitle,
                    buttonSize: isDesktopView ? ButtonSize.large : ButtonSize.small,
                    onPressed: file == null ? null : () async => await uploadFile(),
                  );

                  if (isDesktopView) return saveImageButton;

                  return Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [saveImageButton],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  // cropping image code
  _selectedImageWidgetToCrop() {
    return FutureBuilder<Uint8List?>(
      future: _getByteArrayOfSelectedFile(selectedFile.value!),
      builder: (context, snapshot) {
        return snapshot.connectionState == ConnectionState.waiting
            ? const Center(child: CircularProgressIndicator())
            : snapshot.hasError
            ? Text('Error: ${snapshot.error}')
            : snapshot.hasData
            ? Container(
              padding: EdgeInsets.all(context.theme.d.paddingSmall),
              width: double.maxFinite,
              height: context.mediaQuerySize.height * 0.6,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusMedium),
              ),
              clipBehavior: Clip.hardEdge,
              child: CropImage(
                controller: controller,
                alwaysMove: true,
                image: Image.memory(snapshot.data!, fit: BoxFit.fill),
              ),
            )
            : const SizedBox(); // Placeholder or initial state
      },
    );
  }

  // crop image dialog
  _cropImageDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return DialogTemplate.withCustomContent(
          key: _dialogKey,
          contentPadding: EdgeInsets.zero,
          showButtons: false,
          showCloseButton: false,
          content: SizedBox(
            width: isDesktopView ? context.theme.d.desktopAppBreakpoint : null,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (selectedFile.value != null) _selectedImageWidgetToCrop(),
                Container(
                  color: context.theme.colorScheme.primaryContainer,
                  padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingSmall),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      IconButton(
                        icon: const Icon(MicromentorIcons.close),
                        onPressed: () {
                          Navigator.pop(context, true);
                          controller.rotation = CropRotation.up;
                          controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
                          controller.aspectRatio = 1.0;
                        },
                      ),
                      IconButton(
                        icon: const Icon(MicromentorIcons.rotate90degreesCcwOutlined),
                        onPressed: () async => controller.rotateLeft(),
                      ),
                      IconButton(
                        icon: const Icon(MicromentorIcons.rotate90degreesCwOutlined),
                        onPressed: () async => controller.rotateRight(),
                      ),
                      CommonButton.textButton(
                        context: context,
                        title: AppLocale.current.updateAvatarCropImageDoneAction,
                        onPressed: () async {
                          _finishedCropping();
                          Navigator.pop(context, true);
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _finishedCropping() async {
    final Image img = await controller.croppedImage();
    croppedImage.value = img;
  }

  galleryAction() async {
    Permission? permissionToAsk;

    try {
      if (!kIsWeb) {
        if (Platform.isAndroid) {
          final androidInfo = await DeviceInfoPlugin().androidInfo;
          permissionToAsk =
              (androidInfo.version.sdkInt <= 32) ? Permission.storage : Permission.photos;
        } else {
          // UnimplementedError: checkPermissionStatus() has not been implemented for Permission.photos on web.
          permissionToAsk = Permission.photos;
        }
      }

      if (permissionToAsk == null) {
        getImageFromGallery();
      } else {
        var status = await permissionToAsk.status;
        if (status == PermissionStatus.granted || status == PermissionStatus.limited) {
          getImageFromGallery();
        } else if (status == PermissionStatus.denied) {
          status = await permissionToAsk.request();

          if (status == PermissionStatus.granted || status == PermissionStatus.limited) {
            getImageFromGallery();
          } else if (status == PermissionStatus.permanentlyDenied) {
            if (!mounted) return;
            // Added this condition since in android request() function gives permanently denied after two attempts.
            showConfirmationDialog(context, () async {
              await openAppSettings();
            });
          }
        } else {
          if (!mounted) return;
          showConfirmationDialog(context, () async {
            bool isGranted = await openAppSettings();
            if (isGranted) {
              getImageFromGallery();
            }
          });
        }
      }
    } catch (e) {
      debugPrint('galleryAction failed: $e');
    }
  }

  Future getImageFromGallery() async {
    XFile? file = await _picker.pickImage(source: ImageSource.gallery);

    if (file != null) {
      selectedFile.value = file;
      if (context.mounted) {
        _cropImageDialog();
      }
    }
  }

  Future getImageFromCamera() async {
    XFile? file = await _picker.pickImage(source: ImageSource.camera);

    if (file != null) {
      selectedFile.value = file;
      if (context.mounted) {
        _cropImageDialog();
      }
    }
  }

  pickImageWeb() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowMultiple: false,
      allowedExtensions: acceptableFileType,
    );

    if (result != null) {
      final list =
          result.files.map((f) => XFile.fromData(f.bytes!, name: f.name, length: f.size)).toList();
      selectedFile.value = list.firstOrNull;
      if (context.mounted) {
        _cropImageDialog();
      }
    }
  }

  showConfirmationDialog(BuildContext context, Function onDone) {
    showDialog(
      context: context,
      builder: (dialogContext) {
        return DialogTemplate(
          title: AppLocale.current.permissionAccessTitle,
          description: AppLocale.current.permissionAccessDescription,
          actionButtonTitle: AppLocale.current.settings,
          onAction: () {
            onDone.call();
            context.pop();
          },
        );
      },
    );
  }

  _handleError(LoadObjectResult result) {
    AppErrorHandler(
      context: context,
      exception: result.operationException,
      errorCode: result.errorCode ?? Enum$ErrorCode.failedToUpdate,
    );
  }

  Widget desktopButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: ValueListenableBuilder<Image?>(
            valueListenable: croppedImage,
            builder: (context, file, _) {
              return CommonButton.outlinedButton(
                context: context,
                isFullWidth: false,
                title: AppLocale.current.updateAvatarSelectPictureTitle,
                buttonSize: ButtonSize.small,
                onPressed: () async => kIsWeb ? await pickImageWeb() : await galleryAction(),
              );
            },
          ),
        ),
        if (myUser?.avatarUrl != null) ...[
          SizedBox(width: context.theme.d.paddingMedium),
          Expanded(
            child: CommonButton.outlinedButton(
              context: context,
              title: AppLocale.current.updateAvatarRemovePictureTitle,
              onPressed: () async {
                showDialog(
                  context: context,
                  builder: (dialogContext) {
                    return DialogTemplate(
                      title: AppLocale.current.removeProfilePictureDialogTitle,
                      description: AppLocale.current.removeProfilePictureDialogDescription,
                      actionButtonTitle: AppLocale.current.removeProfilePictureDialogButtonLabel,
                      onAction: () async {
                        Loader.show(dialogContext);
                        final result = await _userProvider.updateUser(
                          input: Input$UserInput(id: myUser?.id, avatarUrl: ''),
                        );

                        if (result.hasError) {
                          _handleError(result);
                        } else {
                          if (dialogContext.mounted) {
                            Loader.hide(dialogContext);
                            Navigator.pop(dialogContext);
                            if (mounted) context.pop();
                          }
                        }
                      },
                    );
                  },
                );
              },
            ),
          ),
        ],
      ],
    );
  }

  Widget mobileButtons() {
    return Column(
      children: [
        ValueListenableBuilder<Image?>(
          valueListenable: croppedImage,
          builder: (context, file, _) {
            return CommonButton.textButton(
              context: context,
              isFullWidth: false,
              title: AppLocale.current.updateAvatarSelectPictureTitle,
              buttonSize: ButtonSize.small,
              onPressed: () async => kIsWeb ? await pickImageWeb() : await galleryAction(),
            );
          },
        ),
        if (AppUtility.isMobilePlatform())
          ValueListenableBuilder<Image?>(
            valueListenable: croppedImage,
            builder: (context, file, _) {
              return CommonButton.textButton(
                context: context,
                isFullWidth: false,
                title: AppLocale.current.updateAvatarTakePicture,
                buttonSize: ButtonSize.small,
                onPressed: () async {
                  await getImageFromCamera();
                },
              );
            },
          ),
        if (myUser?.avatarUrl != null)
          CommonButton.textButton(
            context: context,
            title: AppLocale.current.updateAvatarRemovePictureTitle,
            onPressed: () async {
              showDialog(
                context: context,
                builder: (dialogContext) {
                  return DialogTemplate(
                    title: AppLocale.current.removeProfilePictureDialogTitle,
                    description: AppLocale.current.removeProfilePictureDialogDescription,
                    actionButtonTitle: AppLocale.current.removeProfilePictureDialogButtonLabel,
                    onAction: () async {
                      Loader.show(dialogContext);
                      final result = await _userProvider.updateUser(
                        input: Input$UserInput(id: myUser?.id, avatarUrl: ''),
                      );

                      if (result.hasError) {
                        _handleError(result);
                      } else {
                        if (dialogContext.mounted) {
                          Loader.hide(dialogContext);
                          Navigator.pop(dialogContext);
                          if (mounted) context.pop();
                        }
                      }
                    },
                  );
                },
              );
            },
          ),
      ],
    );
  }

  Widget _cropProfilePicture(double size) {
    return Container(
      clipBehavior: Clip.hardEdge,
      decoration: const BoxDecoration(shape: BoxShape.circle),
      height: size,
      width: size,
      child: Image(image: croppedImage.value!.image, fit: BoxFit.cover),
    );
  }

  //TODO: we will need drad image code when we will add support for web as well
  // void _onDragDone(DropDoneDetails urls) async {
  //   final droppedFile = urls.files.first;

  //   //substring is to remove first '.' i.e '.jpg' = 'jpg';
  //   final String selectedFileType = path.extension(droppedFile.name).substring(1);

  //   if (!acceptableFileType.contains(selectedFileType)) {
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       SnackBar(
  //         content: Text(l10n.updateAvatarInvalidFileTypeError),
  //         backgroundColor: context.colorScheme.error,
  //       ),
  //     );
  //     return;
  //   }
  //   selectedFile.value = droppedFile;
  //   _cropImageDialog();
  // }
}
