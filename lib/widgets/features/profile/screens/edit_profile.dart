import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../../utilities/navigation_mixin.dart';
import '../../../widgets.dart';

class EditProfileScreen extends StatefulWidget {
  final EditProfileOptions? editOption;
  final int? educationExperienceIndex;
  const EditProfileScreen({super.key, this.editOption, this.educationExperienceIndex});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen>
    with NavigationMixin<EditProfileScreen>, UserProviderListener {
  late final UserProvider _userProvider;
  Future<LoadObjectResult>? _loadObjectResult;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _userProvider.addUserProviderListener(this);
    _loadObjectResult = _userProvider.loadUser(
      options: LoadObjectOptions<User>(fetchPolicy: FetchPolicy.cacheFirst),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.editOption != null) {
        _navigateToEditScreen(
          context,
          editOption: widget.editOption!,
          index: widget.educationExperienceIndex ?? 0,
        );
      }
    });
  }

  @override
  void dispose() {
    _userProvider.removeUserProviderListener('EditProfileScreen');

    super.dispose();
  }

  @override
  String get userProviderListenerName => 'EditProfileScreen';

  @override
  void onReceivedMyUser(final User myUser) {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    buildPageRouteScaffold((scaffold) {
      scaffold.setParams(hideNavBar: true);
    });

    return FutureBuilder<LoadObjectResult>(
      future: _loadObjectResult,
      builder: (BuildContext context, AsyncSnapshot<LoadObjectResult?> snapshot) {
        // We don't use the snapshot's data because it is not updated when the user was updated
        // later.
        final User? user = _userProvider.myUser;

        if (user == null || snapshot.connectionState != ConnectionState.done) {
          return const LoadingScreen();
        }
        if (snapshot.hasError) {
          return const CustomErrorWidget();
        }

        if (snapshot.hasData) {
          return Scaffold(
            appBar: AppBar(
              automaticallyImplyLeading: false,
              centerTitle: false,
              leading: backButton(
                context,
                onPressed:
                    () =>
                        context.canPop()
                            ? context.pop()
                            : context.pushReplacement(AppRoutes.profile.path),
              ),
              title: Text(
                AppLocale.current.profileEditTitle,
                style: context.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: context.colorScheme.secondary,
                ),
              ),
              shape: AppUtility.topDivider(context),
            ),
            body: SafeArea(
              child: EditProfileContent(
                myUser: user,
                onEdit:
                    (editOption, {index}) =>
                        _navigateToEditScreen(context, editOption: editOption, index: index ?? 0),
              ),
            ),
          );
        }
        return const CustomErrorWidget();
      },
    );
  }

  _navigateToEditScreen(
    BuildContext context, {
    required EditProfileOptions editOption,
    int index = 0,
  }) async {
    Widget editScreen;
    String editScreenRoute;

    switch (editOption) {
      case EditProfileOptions.pronouns:
        editScreen = const EditProfilePronounsScreen();
        editScreenRoute = AppRoutes.profileEditPronouns.path;
        break;
      case EditProfileOptions.linkedin:
        editScreen = const EditConnectLinkedInScreen();
        editScreenRoute = AppRoutes.profileEditLinkedin.path;
        break;
      case EditProfileOptions.country:
        editScreen = const EditCurrentLocationScreen();
        editScreenRoute = AppRoutes.profileEditCurrentLocation.path;
        break;
      case EditProfileOptions.originLocation:
        editScreen = const EditOriginLocationScreen();
        editScreenRoute = AppRoutes.profileEditOriginLocation.path;
        break;
      case EditProfileOptions.prefferedLanguages:
        editScreen = const EditPreferredLanguageScreen();
        editScreenRoute = AppRoutes.profileEditLanguagePreferred.path;
        break;
      case EditProfileOptions.otherLanguages:
        editScreen = const EditOtherLanguagesScreen();
        editScreenRoute = AppRoutes.profileEditLanguageOthers.path;
        break;
      case EditProfileOptions.companyName:
        editScreen = const EditCompanyNameScreen();
        editScreenRoute = AppRoutes.profileEditCompanyName.path;
        break;
      case EditProfileOptions.companyWebsite:
        editScreen = const EditCompanyWebsiteScreen();
        editScreenRoute = AppRoutes.profileEditCompanyWebsite.path;
        break;
      case EditProfileOptions.companyLocation:
        editScreen = const EditCompanyLocationScreen();
        editScreenRoute = AppRoutes.profileEditCompanyLocation.path;
        break;
      case EditProfileOptions.industryName:
        editScreen = const EditIndustriesScreen();
        editScreenRoute = AppRoutes.profileEditIndustries.path;
        break;
      case EditProfileOptions.companyStage:
        editScreen = const EditCompanyStageScreen();
        editScreenRoute = AppRoutes.profileEditCompanyStage.path;
        break;
      case EditProfileOptions.expertiseTop:
        editScreen = const EditExpertisesScreen(isTopExpertises: true);
        editScreenRoute = AppRoutes.profileEditExpertisesTop.path;
        break;
      case EditProfileOptions.expertiseAdditional:
        editScreen = const EditExpertisesScreen(isTopExpertises: false);
        editScreenRoute = AppRoutes.profileEditExpertisesAdditional.path;
        break;
      case EditProfileOptions.menteeBusinessReason:
        editScreen = const EditCompanyReasonScreen();
        editScreenRoute = AppRoutes.profileEditCompanyReason.path;
        break;
      case EditProfileOptions.industries:
        editScreen = const EditIndustriesScreen();
        editScreenRoute = AppRoutes.profileEditIndustries.path;
        break;
      case EditProfileOptions.mentoringPreference:
        editScreen = const EditMentoringPreferencesScreen();
        editScreenRoute = AppRoutes.profileEditMentoringPreferences.path;
        break;
      case EditProfileOptions.mentoringReason:
        editScreen = const EditReasonsForMentoringScreen();
        editScreenRoute = AppRoutes.profileEditReasonsForMentoring.path;
        break;
      case EditProfileOptions.howICanHelpMentees:
        editScreen = const EditHowICanHelpMenteesScreen();
        editScreenRoute = AppRoutes.profileEditHowICanHelpMentees.path;
        break;
      case EditProfileOptions.businessChallenge:
        editScreen = const EditBusinessChallengeScreen();
        editScreenRoute = AppRoutes.profileEditBusinessChallenge.path;
        break;
      case EditProfileOptions.howCanMentorSupportMe:
        editScreen = const EditHowCanMentorSupportMeScreen();
        editScreenRoute = AppRoutes.profileEditHowCanMentorSupportMe.path;
        break;
      case EditProfileOptions.newEducation:
        editScreen = const EditEducationScreen();
        editScreenRoute = AppRoutes.profileEditEducationNew.path;
        break;
      case EditProfileOptions.editEducation:
        editScreen = EditEducationScreen(experienceIndex: index);
        editScreenRoute = '${AppRoutes.profileEditEducation.path}/$index';
        break;
      case EditProfileOptions.newExperience:
        editScreen = const EditExperienceScreen();
        editScreenRoute = AppRoutes.profileEditExperienceNew.path;
        break;
      case EditProfileOptions.editExperience:
        editScreen = EditExperienceScreen(experienceIndex: index);
        editScreenRoute = '${AppRoutes.profileEditExperience.path}/$index';
        break;
    }

    if (AppUtility.displayDesktopUI(context)) {
      if (_isOnEditPage()) {
        context.canPop() ? context.pop() : context.pushReplacement(AppRoutes.profileEdit.path);
      }
      await showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) {
          return editScreen;
        },
      ).then((_) => setState(() {}));
    } else {
      context.push(editScreenRoute, extra: _userProvider.myUser);
    }
  }

  bool _isOnEditPage() {
    final path = GoRouterState.of(context).uri.path;
    return path.startsWith('/profile/edit/');
  }
}

class EditProfileContent extends StatelessWidget {
  final User myUser;
  final Function(EditProfileOptions, {int? index})? onEdit;

  const EditProfileContent({super.key, required this.myUser, this.onEdit});

  @override
  Widget build(BuildContext context) {
    return AppUtility.displayDesktopUI(context) ? _desktopView(context) : _mobileView(context);
  }

  Widget _desktopView(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: context.theme.d.paddingSmall),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: context.theme.d.boxSizeXxLarge,
            child: EditProfileHeaderAvatarWidget(myUser: myUser),
          ),
          Expanded(
            flex: 6,
            child: SingleChildScrollView(
              padding: EdgeInsets.only(
                right: context.theme.d.paddingLarge,
                bottom: context.theme.d.paddingMedium,
              ),
              child: Column(
                children: [
                  EditProfileAboutMe(
                    myUser: myUser,
                    onTapAction: (editOption) => onEdit?.call(editOption),
                  ),
                  _otherProfileDetails(context),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _mobileView(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          EditProfileHeaderAvatarWidget(myUser: myUser),
          EditProfileAboutMe(myUser: myUser, onTapAction: (editOption) => onEdit?.call(editOption)),
          _otherProfileDetails(context),
        ],
      ),
    );
  }

  Widget _otherProfileDetails(BuildContext context) {
    final profileUtils = ProfileUtility.fromFindId(myUser);

    return Column(
      children: [
        SizedBox(height: context.theme.d.paddingSmall),
        if (myUser.offersHelp == true)
          EditProfileHowCanIHelp(
            myUser: myUser,
            onTapAction: (editOption) => onEdit?.call(editOption),
          ),
        if (myUser.seeksHelp == true && profileUtils.menteeCompany != null)
          EditProfileAboutMyBusiness(
            myUser: myUser,
            onTapAction: (editOption) => onEdit?.call(editOption),
          ),
        SizedBox(height: context.theme.d.paddingSmall),

        // TODO: Why is this here? Should we delete the widget?
        // SoughtExpertises(expertiseLabels: expertiseLabels),
        EditProfileExperienceAndEducation(
          myUser: myUser,
          experience:
              myUser.businessExperiences
                  ?.map(
                    (e) => ExperienceInput(
                      position: e.jobTitle ?? '',
                      companyName: e.businessName,
                      start: e.startDate,
                      end: e.endDate,
                      city: e.city,
                      state: e.state,
                      country: e.country,
                    ),
                  )
                  .toList() ??
              [],
          education:
              myUser.academicExperiences
                  ?.map(
                    (e) => EducationInput(
                      schoolName: e.institutionName,
                      start: e.startDate,
                      end: e.endDate,
                      title: e.degreeType,
                      major: e.fieldOfStudy,
                    ),
                  )
                  .toList() ??
              [],
          onAddEditAction: (editOption, {index}) {
            onEdit?.call(editOption, index: index);
          },
        ),
      ],
    );
  }
}
