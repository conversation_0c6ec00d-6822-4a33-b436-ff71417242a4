import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tuple/tuple.dart';
import 'package:mm_flutter_app/widgets/features/profile/screens/profile_content.dart';
import '../../../../constants/constants.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/navigation_mixin.dart';
import '../../../../utilities/utility.dart';

class ProfileScreen extends StatefulWidget {
  final String? userId;

  const ProfileScreen({super.key, this.userId});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> with NavigationMixin<ProfileScreen> {
  late final bool _isMyProfile;
  late final InvitationsProvider _invitationsProvider;
  late final UserProvider _userProvider;
  Future<LoadObjectResult>? _loadObjectResult;

  @override
  void initState() {
    super.initState();

    _invitationsProvider = Provider.of<InvitationsProvider>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _isMyProfile = widget.userId == null;
    _loadObjectResult = _userProvider.loadUser(userId: widget.userId);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!isCurrentPageRoute()) {
      return;
    }
  }

  @override
  void didUpdateWidget(covariant ProfileScreen oldWidget) {
    if (oldWidget.userId != widget.userId) {
      _loadObjectResult = _userProvider.loadUser(userId: widget.userId);
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<LoadObjectResult>(
      future: _loadObjectResult,
      builder: (BuildContext context, AsyncSnapshot<LoadObjectResult?> userSnapshot) {
        final user = userSnapshot.data?.object;

        return AppUtility.widgetForAsyncSnapshot(
          snapshot: userSnapshot,
          onReady: () {
            return Selector<
              InvitationsProvider,
              Tuple2<List<MyChannelInvitation>?, List<MyChannelInvitation>?>
            >(
              selector:
                  (_, invitationsProvider) => Tuple2(
                    invitationsProvider.receivedInvitations,
                    invitationsProvider.sentInvitations,
                  ),
              shouldRebuild:
                  (oldValue, newValue) =>
                      !(const DeepCollectionEquality.unordered().equals(oldValue, newValue)) ||
                      _invitationsProvider.asyncState != AsyncState.loading,
              builder: (_, __, ___) {
                return AppUtility.widgetForAsyncState(
                  state:
                      _isMyProfile
                          ? AsyncState.ready
                          : _invitationsProvider.asyncState == AsyncState.error
                          ? AsyncState.ready
                          : _invitationsProvider.asyncState,
                  onReady: () {
                    return Scaffold(
                      body: SafeArea(
                        child: ProfileContentScreen(user: user!, isMyProfile: _isMyProfile),
                      ),
                    );
                  },
                );
              },
            );
          },
        );
      },
    );
  }
}
