import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/operations_user.graphql.dart';
import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../widgets.dart';

class EditCompanyReasonScreen extends StatefulWidget {
  const EditCompanyReasonScreen({super.key});

  @override
  State<EditCompanyReasonScreen> createState() => _EditCompanyReasonScreenState();
}

class _EditCompanyReasonScreenState extends State<EditCompanyReasonScreen> {
  late final UserProvider _userProvider;
  late final TextEditingController _textEditingController;
  late final String _menteeGroupMembershipId;
  late final User? _myUser;
  String? _companyReason;
  String? _companyOldReason;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myUser = _userProvider.myUser;
    final maybeMenteeGroupMembership = _myUser?.groupMemberships
        .where((g) => g.groupIdent == GroupIdent.mentees.name)
        .firstOrNull
        ?.maybeWhen(menteesGroupMembership: (g) => g, orElse: () => null);
    _menteeGroupMembershipId = maybeMenteeGroupMembership!.id;
    _companyOldReason = maybeMenteeGroupMembership.reasonsForStartingBusiness;
    _textEditingController = TextEditingController(text: _companyOldReason);
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditProfileTemplate(
      title: AppLocale.current.profileEditSectionBusinessReasonTitle,
      body: Form(
        child: TextFormFieldWidget(
          hint: AppLocale.current.profileEditSectionBusinessReasonInputHint,
          textController: _textEditingController,
          textCapitalization: TextCapitalization.sentences,
          maxLength: 1000,
          maxLines: 6,
          onChanged: (value) {
            setState(() {
              _companyReason = value;
            });
          },
        ),
      ),
      noChangeInData: _companyOldReason?.trim() == _textEditingController.text.trim(),
      editUserProfile: () async {
        final result = await _userProvider.updateMenteesGroupMembership(
          input: Input$MenteesGroupMembershipInput(
            id: _menteeGroupMembershipId,
            reasonsForStartingBusiness: _companyReason,
          ),
        );

        if (result.gqlQueryResult.hasException) {
          return result.gqlQueryResult.exception;
        }

        await _userProvider.loadUser(
          userId: _myUser?.id,
          options: LoadObjectOptions<User>(
            pollingConfig: PollingConfig<User>(
              isUpdatedFunc: (User user) {
                final menteeGroupMembership = user.groupMemberships
                    .where((g) => g.groupIdent == GroupIdent.mentees.name)
                    .firstOrNull
                    ?.maybeWhen(menteesGroupMembership: (g) => g, orElse: () => null);

                return menteeGroupMembership != null &&
                    menteeGroupMembership.reasonsForStartingBusiness == _companyReason;
              },
            ),
          ),
        );
      },
    );
  }
}
