import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class EditConnectLinkedInScreen extends StatefulWidget {
  const EditConnectLinkedInScreen({super.key});

  @override
  State<EditConnectLinkedInScreen> createState() => _EditConnectLinkedInScreenState();
}

class _EditConnectLinkedInScreenState extends State<EditConnectLinkedInScreen> {
  late final UserProvider _userProvider;
  late final TextEditingController _textEditingController;
  final _formKey = GlobalKey<FormState>();
  String? _linkedInUrl;

  String? _oldUrl;
  late User? _myUser;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myUser = _userProvider.myUser;
    _textEditingController = TextEditingController(
      text:
          _myUser?.websites
              ?.where((e) => e.label == WebsiteLabels.linkedIn.name)
              .firstOrNull
              ?.value,
    );
    _oldUrl =
        _myUser?.websites?.where((e) => e.label == WebsiteLabels.linkedIn.name).firstOrNull?.value;

    _linkedInUrl = _oldUrl;
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditProfileTemplate(
      title: AppLocale.current.profileEditSectionAboutLinkedInTitle,
      body: Form(
        key: _formKey,
        child: TextFormFieldWidget(
          label: AppLocale.current.profileEditSectionAboutLinkedInInputLabel,
          hint: AppLocale.current.profileEditSectionAboutLinkedInInputHint,
          contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
          textController: _textEditingController,
          onChanged: (value) {
            if (value.isEmpty == true) {
              setState(() {
                _linkedInUrl = null;
              });
              return;
            }
            if (_formKey.currentState!.validate()) {
              setState(() {
                final sanitizedValue = AppUtility.sanitizeUri(value);
                _linkedInUrl = sanitizedValue;
              });
            }
          },
          validator:
              (value) => context.theme.validator.validateLinkedinUri(
                value,
                errorMessage: AppLocale.current.validationLinkedinUriInvalid,
                host: 'www.linkedin.com',
              ),
        ),
      ),
      noChangeInData: _oldUrl?.trim() == _linkedInUrl?.trim(),
      editUserProfile: () async {
        final itemInput = Input$LabeledStringValueInput(
          label: WebsiteLabels.linkedIn.name,
          value: _linkedInUrl,
        );
        final result = await _userProvider.updateUser(
          input: Input$UserInput(
            id: _myUser?.id,
            websites:
                _myUser?.websites?.isNotEmpty == true
                    ? _myUser?.websites?.map((w) {
                      if (w.label == WebsiteLabels.linkedIn.name) {
                        return itemInput;
                      }
                      return Input$LabeledStringValueInput(
                        label: w.label,
                        value: w.value,
                        tags: w.tags,
                      );
                    }).toList()
                    : [itemInput],
          ),
          options: UpdateObjectOptions<User>(
            loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
            // If we need to switch to isUpdatedFunc:
            // isUpdatedFunc: (User user) {
            //   final linkedInLink = user.websites
            //       ?.where((e) => e.label == WebsiteLabels.linkedIn.name)
            //       .firstOrNull
            //       ?.value;
            //   return linkedInLink == _linkedInUrl;
            // },
          ),
        );

        if (result.hasError) {
          return result.operationException;
        }

        await _userProvider.loadUser(userId: _myUser?.id);
      },
    );
  }
}
