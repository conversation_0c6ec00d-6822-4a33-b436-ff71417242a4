import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/content_provider.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../widgets.dart';

class EditProfilePronounsScreen extends StatefulWidget {
  const EditProfilePronounsScreen({super.key});

  @override
  State<EditProfilePronounsScreen> createState() => _EditProfilePronounsScreenState();
}

class _EditProfilePronounsScreenState extends State<EditProfilePronounsScreen> {
  late final UserProvider _userProvider;
  late final ContentProvider _contentProvider;
  late final TextEditingController _pronounController;
  late final User? _myUser;

  final List<String> _selections = List.empty(growable: true);

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myUser = _userProvider.myUser;
    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    if (_myUser != null) {
      _selections.addAll(_myUser.pronouns.map((e) => e.textId));
    }
    _pronounController = TextEditingController(text: _myUser?.pronounsDisplay);
  }

  List _createCheckboxList() {
    return _contentProvider.pronounOptions
            ?.map((e) => _checkboxListTile(e.translatedValue ?? '', e.textId))
            .toList() ??
        [];
  }

  Widget _checkboxListTile(String label, String textId) {
    return CheckboxListTile(
      value: _selections.contains(textId),
      title: Text(
        label,
        style: context.theme.textTheme.bodyLarge?.copyWith(
          color: context.theme.colorScheme.onSurface,
          fontWeight: FontWeight.w400,
        ),
      ),
      controlAffinity: ListTileControlAffinity.leading,
      onChanged: (isSelected) {
        setState(() {
          (isSelected ?? false) ? _selections.add(textId) : _selections.remove(textId);

          _pronounController.text = _getPronounsConcatinatedString();
        });
      },
    );
  }

  String _getPronounsConcatinatedString() {
    if (_selections.isEmpty) return '';
    return _contentProvider.pronounOptions
            ?.where((element) => _selections.contains(element.textId))
            .map(
              (e) => _selections.length > 1 ? e.translatedValue?.split('/')[0] : e.translatedValue,
            )
            .toList()
            .join('/') ??
        '';
  }

  @override
  Widget build(BuildContext context) {
    return EditProfileTemplate(
      title: AppLocale.current.profileEditSectionAboutPronounsTitle,
      subtitle: AppLocale.current.profileEditSectionAboutPronounsSubtitle,
      body: Column(
        children: [
          ..._createCheckboxList(),
          SizedBox(height: context.theme.d.paddingSmall),
          TextFormFieldWidget(
            label: AppLocale.current.editPronounsLabel,
            textController: _pronounController,
            readOnly: true,
          ),
        ],
      ),
      noChangeInData: _myUser?.pronounsDisplay.trim() == _pronounController.text.trim(),
      editUserProfile: () async {
        if (_myUser?.pronounsDisplay.trim() == _pronounController.text.trim()) {
          return null;
        }
        final result = await _userProvider.updateUser(
          input: Input$UserInput(id: _myUser?.id, pronounsTextIds: _selections),
          options: UpdateObjectOptions<User>(
            useServiceRequest: true,
            loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
            // If we need to switch to isUpdatedFunc:
            // isUpdatedFunc: (User user) {
            //   var textIds = user.pronouns.map((e) => e.textId).toList();
            //   Function unOrdDeepEq = const DeepCollectionEquality.unordered().equals;
            //   return unOrdDeepEq(textIds, _selections);
            // },
          ),
        );

        if (result.hasError) {
          return result.operationException;
        }
      },
    );
  }
}
