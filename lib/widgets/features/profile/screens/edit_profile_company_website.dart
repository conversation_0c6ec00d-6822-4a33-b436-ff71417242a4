import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../widgets.dart';

class EditCompanyWebsiteScreen extends StatefulWidget {
  const EditCompanyWebsiteScreen({super.key});

  @override
  State<EditCompanyWebsiteScreen> createState() => _EditCompanyWebsiteScreenState();
}

class _EditCompanyWebsiteScreenState extends State<EditCompanyWebsiteScreen> {
  late final UserProvider _userProvider;
  late final User? _myUser;
  late final TextEditingController _textEditingController;
  final _formKey = GlobalKey<FormState>();
  bool _isBackEnabled = true;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myUser = _userProvider.myUser;
    _textEditingController = TextEditingController(
      text: _myUser?.companies?.firstOrNull?.websites?.first.value,
    );
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditProfileTemplate(
      title: AppLocale.current.profileEditSectionBusinessWebsiteTitle,
      body: Form(
        key: _formKey,
        child: TextFormFieldWidget(
          label: AppLocale.current.profileEditSectionBusinessWebsiteInputLabel,
          hint: AppLocale.current.profileEditSectionBusinessWebsiteInputHint,
          contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
          textController: _textEditingController,
          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^[a-zA-Z0-9\-_.:/]+$'))],
          onChanged: (_) {
            setState(() {
              _isBackEnabled = _formKey.currentState?.validate() ?? false;
            });
          },
          validator: (value) {
            return context.theme.validator.validateUrl(value);
          },
        ),
      ),
      isBackEnabled: _isBackEnabled,
      noChangeInData:
          _myUser?.companies?.firstOrNull?.websites?.first.value.trim() ==
          _textEditingController.text.trim(),
      editUserProfile: () async {
        String? companyWebsite = _textEditingController.text.trim();

        final result = await _userProvider.updateCompany(
          input: Input$CompanyInput(
            id: _myUser?.companies?.firstOrNull?.id,
            websites: [Input$LabeledStringValueInput().copyWith(value: companyWebsite)],
          ),
        );

        if (result.gqlQueryResult.hasException) {
          return result.gqlQueryResult.exception;
        }

        await _userProvider.loadUser(
          userId: _myUser?.id,
          options: LoadObjectOptions<User>(
            pollingConfig: PollingConfig<User>(
              isUpdatedFunc:
                  (User user) =>
                      user.companies?.firstOrNull?.websites?.first.value == companyWebsite,
            ),
          ),
        );
      },
    );
  }
}
