import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../shared/radio_button_cards.dart';
import '../components/edit_profile_template.dart';

class EditCompanyStageScreen extends StatefulWidget {
  const EditCompanyStageScreen({super.key});

  @override
  State<EditCompanyStageScreen> createState() => _EditCompanyStageScreenState();
}

class _EditCompanyStageScreenState extends State<EditCompanyStageScreen> {
  late final UserProvider _userProvider;
  late final User? _myUser;
  final List<String> _stageTextIds = [
    CompanyStageTextId.idea.name,
    CompanyStageTextId.operational.name,
    CompanyStageTextId.earning.name,
    CompanyStageTextId.profitable.name,
  ];
  late final int _initialValue;
  int _selectedStageIndex = 0;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myUser = _userProvider.myUser;
    if (_myUser?.companies?.firstOrNull?.companyStage?.textId != null) {
      _initialValue = _stageTextIds.indexOf(_myUser!.companies!.firstOrNull!.companyStage!.textId);
      _selectedStageIndex = _initialValue;
    }
  }

  @override
  Widget build(BuildContext context) {
    final iconSize = context.theme.d.iconSizeXSmall + context.theme.d.iconSizeXLarge;
    return EditProfileTemplate(
      title: AppLocale.current.profileEditSectionBusinessStageTitle,
      subtitle: AppLocale.current.profileEditSectionBusinessStageSubtitle,
      body: RadioButtonCards(
        cards: [
          RadioCard(
            title: AppLocale.current.signupBusinessStageCard1Title,
            subtitle: AppLocale.current.signupBusinessStageCard1Description,
            leadingImage: Image(image: const AssetImage(Assets.ideaStage), height: iconSize),
          ),
          RadioCard(
            title: AppLocale.current.signupBusinessStageCard2Title,
            subtitle: AppLocale.current.signupBusinessStageCard2Description,
            leadingImage: Image(image: const AssetImage(Assets.operationalStage), height: iconSize),
          ),
          RadioCard(
            title: AppLocale.current.signupBusinessStageCard3Title,
            subtitle: AppLocale.current.signupBusinessStageCard3Description,
            leadingImage: Image(image: const AssetImage(Assets.revenueStage), height: iconSize),
          ),
          RadioCard(
            title: AppLocale.current.signupBusinessStageCard4Title,
            subtitle: AppLocale.current.signupBusinessStageCard4Description,
            leadingImage: Image(image: const AssetImage(Assets.profitableStage), height: iconSize),
          ),
        ],
        onSelectedCardChanged:
            (index) => setState(() {
              _selectedStageIndex = index;
            }),
        initialSelection: _initialValue,
      ),
      noChangeInData: _initialValue == _selectedStageIndex,
      editUserProfile: () async {
        final stageTextId = _stageTextIds[_selectedStageIndex];

        final result = await _userProvider.updateCompany(
          input: Input$CompanyInput(
            id: _myUser?.companies?.firstOrNull?.id,
            companyStageTextId: stageTextId,
          ),
        );

        if (result.gqlQueryResult.hasException) {
          return result.gqlQueryResult.exception;
        }

        await _userProvider.loadUser(
          userId: _myUser?.id,
          options: LoadObjectOptions<User>(
            pollingConfig: PollingConfig<User>(
              isUpdatedFunc:
                  (User user) => user.companies?.firstOrNull?.companyStageTextId == stageTextId,
            ),
          ),
        );
      },
    );
  }
}
