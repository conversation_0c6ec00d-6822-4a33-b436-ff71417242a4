import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:textfield_tags/textfield_tags.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../widgets.dart';

class EditPreferredLanguageScreen extends StatefulWidget {
  const EditPreferredLanguageScreen({super.key});

  @override
  State<EditPreferredLanguageScreen> createState() => _EditPreferredLanguageScreenState();
}

class _EditPreferredLanguageScreenState extends State<EditPreferredLanguageScreen> {
  late final UserProvider _userProvider;
  late final StringTagController _preferredLanguagesController;
  late final ContentProvider _contentProvider;
  late final User? _myUser;

  double extraScrollingSpace = 0;
  final Set<String> _initialSelection = {};
  bool hasValueChage = false;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _myUser = _userProvider.myUser;
    _contentProvider = Provider.of<ContentProvider>(context, listen: false);
    final lastSelectedValue = _myUser?.preferredLanguage?.translatedValue ?? '';
    if (lastSelectedValue.isNotEmpty) {
      _initialSelection.add(lastSelectedValue);
    }
    _preferredLanguagesController = StringTagController();
  }

  @override
  void dispose() {
    try {
      _preferredLanguagesController.dispose();
    } catch (_) {}
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditProfileTemplate(
      title: AppLocale.current.profileEditSectionAboutLanguagePreferredTitle,
      subtitle: AppLocale.current.profileEditSectionAboutLanguagePreferredSubtitle,
      extraScrollingSpace: extraScrollingSpace,
      noChangeInData:
          !(hasValueChage == true &&
              _preferredLanguagesController.getTags?.firstOrNull != _initialSelection.firstOrNull),
      body: _dropDownWidget(),
      showAccountSettingsLink: true,
      isBackEnabled: _preferredLanguagesController.getTags?.isNotEmpty != false,
      editUserProfile: () async {
        //if there is no change in language
        if (_preferredLanguagesController.getTags?.isNotEmpty == true &&
            (_preferredLanguagesController.getTags?.first == _initialSelection.firstOrNull)) {
          return null;
        }
        String? preferredLanguageTextId =
            _preferredLanguagesController.getTags
                ?.map(
                  (t) =>
                      _contentProvider.languageOptions
                          ?.firstWhere((o) => o.translatedValue == t)
                          .textId,
                )
                .nonNulls
                .toList()
                .firstOrNull;

        final result = await _userProvider.updateUser(
          input: Input$UserInput(id: _myUser?.id, preferredLanguageTextId: preferredLanguageTextId),
          options: UpdateObjectOptions<User>(
            loadServiceRequestOptions: LoadObjectOptionsForServiceRequest(),
            // If we need to switch to isUpdatedFunc:
            // isUpdatedFunc: (User user) {
            //   var updatedValue = user.preferredLanguage?.translatedValue ?? '';
            //   return updatedValue == _preferredLanguagesController.getTags?.firstOrNull;
            // },
          ),
        );

        if (result.hasError) {
          return result.operationException;
        }
      },
    );
  }

  _dropDownWidget() {
    return MultiSelectDropdown(
      singleSelect: true,
      label: AppLocale.current.profileEditPreferredLanguageInputLabel,
      editable: true,
      tagsController: _preferredLanguagesController,
      options:
          _contentProvider.languageOptions?.map((e) => e.translatedValue).nonNulls.toList() ?? [],
      selectedOptions: _initialSelection,
      notFoundMessage: AppLocale.current.multiSelectDropdownLanguageNotFoundMsg,
      requiredValueError: AppLocale.current.profileEditPreferredLanguageErrorMsg,
      onDropdownSizeChanged: (double height) {
        setState(() {
          extraScrollingSpace = height;
        });
      },
      onChange:
          () => setState(() {
            hasValueChage = true;
          }),
    );
  }
}
