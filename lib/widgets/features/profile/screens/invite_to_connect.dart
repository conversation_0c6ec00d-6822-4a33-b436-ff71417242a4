import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/router/app_router.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../models/models.dart';
import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../../utilities/navigation_mixin.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class InviteToConnectScreen extends StatefulWidget {
  final String userId;

  const InviteToConnectScreen({super.key, required this.userId});

  @override
  State<InviteToConnectScreen> createState() => _InviteToConnectScreenState();
}

class _InviteToConnectScreenState extends State<InviteToConnectScreen>
    with NavigationMixin<InviteToConnectScreen> {
  late final UserProvider _userProvider;
  TextEditingController? _textEditingController;
  User? user;
  late bool isDesktop;
  bool showLoading = false;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!isCurrentPageRoute()) return;
    isDesktop = AppUtility.displayDesktopUI(context);
  }

  @override
  Widget build(BuildContext context) {
    if (!isCurrentPageRoute()) return const SizedBox.shrink();
    if (user != null) return _content();

    if (!isDesktop) {
      buildPageRouteScaffold((scaffoldModel) {
        scaffoldModel.setParams(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            leading: backButton(context, onPressed: () => context.pop()),
            title: Text(AppLocale.current.inviteCreateTitle),
            centerTitle: false,
            shape: AppUtility.topDivider(context),
          ),
        );
      });
    }

    return FutureBuilder<LoadObjectResult>(
      future: _userProvider.loadUser(userId: widget.userId),
      builder: (BuildContext context, AsyncSnapshot<LoadObjectResult?> userSnapshot) {
        return AppUtility.widgetForAsyncSnapshot(
          snapshot: userSnapshot,
          onReady: () {
            user = userSnapshot.hasData ? userSnapshot.data?.object : null;

            if (user == null) {
              return const LoadingScreen();
            }
            return _content();
          },
        );
      },
    );
  }

  _content() {
    final profileUtils = ProfileUtility.fromFindId(user);
    String userFullName = AppUtility.getUserFullName(user?.firstName, user?.lastName);
    final Enum$UserProfileRole userProfileRole =
        user?.offersHelp == true ? Enum$UserProfileRole.mentor : Enum$UserProfileRole.mentee;
    _textEditingController ??= TextEditingController(
      text:
          userProfileRole == Enum$UserProfileRole.mentor
              ? AppLocale.current.inviteCreateMessageDefaultToMentor(userFullName)
              : AppLocale.current.inviteCreateMessageDefaultToEntrepreneur(userFullName),
    );

    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: SafeArea(
        child: Stack(
          children: [
            ListView(
              shrinkWrap: true,
              children: [
                Container(
                  margin: EdgeInsets.symmetric(
                    horizontal: context.theme.d.paddingMedium,
                    vertical: context.theme.d.paddingSmall,
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: context.theme.d.paddingMedium,
                    vertical: context.theme.d.paddingSmall,
                  ),
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                    color: context.colorScheme.onPrimary,
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                        width: context.theme.d.borderWidthRegular,
                        color: context.colorScheme.outlineVariant,
                      ),
                      borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXxLarge),
                    ),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      ProfileImageWidget(
                        avatarUrl: user?.avatarUrl,
                        isMentor: userProfileRole == Enum$UserProfileRole.mentor,
                      ),
                      SizedBox(width: context.theme.d.paddingMedium),
                      Expanded(
                        child: Stack(children: [profileUtils.profileInfoSection(context, false)]),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: context.theme.d.paddingLarge),
                if (_textEditingController != null)
                  MessageBox(
                    textEditingController: _textEditingController!,
                    showButtonsInline: isDesktop,
                    onChanged: () => setState(() {}),
                    onSendAction: () {
                      _sendInviteAction();
                    },
                  ),
                //only for mobile
                if (!kIsWeb || AppUtility.isMobilePlatform())
                  Container(
                    margin: EdgeInsets.symmetric(
                      horizontal: context.theme.d.paddingMedium,
                      vertical: context.theme.d.paddingSmall,
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: context.theme.d.paddingMedium,
                      vertical: context.theme.d.paddingMedium,
                    ),
                    decoration: BoxDecoration(
                      color: context.colorScheme.onSurfaceVariant,
                      borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusSmall),
                    ),
                    child: RichText(
                      text: TextSpan(
                        text: '${AppLocale.current.inviteCreateMessageTipsTitle}\n\n',
                        style: context.textTheme.bodyLarge?.copyWith(
                          color: context.colorScheme.onPrimary,
                          fontWeight: FontWeight.w400,
                        ),
                        children: [
                          TextSpan(
                            text: AppLocale.current.inviteCreateMessageTipsSubtitle,
                            style: context.textTheme.bodyLarge?.copyWith(
                              height: context.theme.d.lineHeightX,
                              color: context.colorScheme.onPrimary,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
            if (showLoading) _loadingWidget(),
          ],
        ),
      ),
    );
  }

  _loadingWidget() {
    return Center(
      child: SizedBox(
        width: context.theme.d.loadingWidgetSize.width,
        height: context.theme.d.loadingWidgetSize.height,
        child: CircularProgressIndicator(color: context.theme.colorScheme.primary, strokeWidth: 4),
      ),
    );
  }

  _sendInviteAction() async {
    InvitationsProvider invitationsProvider = Provider.of<InvitationsProvider>(
      context,
      listen: false,
    );

    ScaffoldModel scaffoldModel = Provider.of<ScaffoldModel>(context, listen: false);

    setState(() {
      showLoading = true;
    });

    await invitationsProvider
        .createChannelInvitation(
          createdBy: _userProvider.myUser?.id ?? '',
          recipientId: widget.userId,
          messageText: _textEditingController?.text ?? '',
        )
        .then((result) {
          setState(() {
            showLoading = false;
          });

          if (result.gqlQueryResult.hasException) {
            if (mounted) {
              AppErrorHandler(context: context, exception: result.gqlQueryResult.exception);
            }
            return;
          }
          if (result.response != null && mounted) {
            AppRouter.popUntilRoot(context);
            _showDailog(context, scaffoldModel);
          }
        });
  }

  _showDailog(BuildContext context, ScaffoldModel scaffoldModel) {
    showDialog(
      context: context,
      builder: (dialogContext) {
        return DialogTemplate.withCustomContent(
          titleImage: Image(
            height: context.theme.d.boxSizeXXLarge,
            image: const AssetImage(Assets.invitationSendNewImage),
          ),
          title: AppLocale.current.sentInviteSuccessTitle,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                AppLocale.current.sentInviteSuccessMessage(
                  _userProvider.myUser?.seeksHelp == false
                      ? AppLocale.current.titleEntrepreneurs.toLowerCase()
                      : AppLocale.current.titleMentors.toLowerCase(),
                ),
                textAlign: TextAlign.center,
                style: context.theme.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w400,
                  color: context.colorScheme.onSurface,
                ),
              ),
              SizedBox(height: context.theme.d.paddingMedium),
              if (scaffoldModel.selectedTabIndex != Tabs.explore.index)
                CommonButton.outlinedButton(
                  context: context,
                  title: AppLocale.current.exploreMoreTitle,
                  buttonSize: ButtonSize.large,
                  onPressed: () async {
                    Navigator.pop(dialogContext);
                    await Future.delayed(const Duration(milliseconds: 500));
                    if (scaffoldModel.scaffoldKey.currentContext != null) {
                      scaffoldModel.setParams(index: Tabs.explore.index);
                      scaffoldModel.scaffoldKey.currentContext!.go(AppRoutes.explore.path);
                    }
                  },
                ),
            ],
          ),
          showButtons: false,
        );
      },
    );
  }
}
