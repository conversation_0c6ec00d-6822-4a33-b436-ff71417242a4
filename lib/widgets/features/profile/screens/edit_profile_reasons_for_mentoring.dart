import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/operations_user.graphql.dart';
import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../widgets.dart';

class EditReasonsForMentoringScreen extends StatefulWidget {
  const EditReasonsForMentoringScreen({super.key});

  @override
  State<EditReasonsForMentoringScreen> createState() => _EditReasonsForMentoringScreenState();
}

class _EditReasonsForMentoringScreenState extends State<EditReasonsForMentoringScreen> {
  late final UserProvider _userProvider;
  late final TextEditingController _textEditingController;
  late final String _mentorGroupMembershipId;
  late final String? _oldReasonForMentoring;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    final profileUtils = ProfileUtility.fromFindId(_userProvider.myUser);
    _mentorGroupMembershipId = profileUtils.mentorGroupMembership?.id ?? '';
    _oldReasonForMentoring = profileUtils.mentorGroupMembership?.reasonsForMentoring;
    _textEditingController = TextEditingController(text: _oldReasonForMentoring);
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EditProfileTemplate(
      title: AppLocale.current.profileEditMainMentorWhyIMentor,
      body: Form(
        child: TextFormFieldWidget(
          hint: AppLocale.current.profileEditMainMentorWhyIMentorInputHint,
          textController: _textEditingController,
          textCapitalization: TextCapitalization.sentences,
          maxLength: 1000,
          maxLines: 6,
          onChanged: (_) {
            setState(() {});
          },
        ),
      ),
      noChangeInData: _textEditingController.text.trim() == (_oldReasonForMentoring ?? '').trim(),
      editUserProfile: () async {
        final result = await _userProvider.updateMentorsGroupMembership(
          input: Input$MentorsGroupMembershipInput(
            id: _mentorGroupMembershipId,
            reasonsForMentoring: _textEditingController.text.trim(),
          ),
        );

        if (result.gqlQueryResult.hasException) {
          return result.gqlQueryResult.exception;
        }

        await _userProvider.loadUser(
          userId: _userProvider.myUser?.id,
          options: LoadObjectOptions<User>(
            pollingConfig: PollingConfig<User>(
              isUpdatedFunc: (User user) {
                final maybeMentorGroupMembership = _userProvider.myUser?.groupMemberships
                    .where((g) => g.groupIdent == GroupIdent.mentors.name)
                    .firstOrNull
                    ?.maybeWhen(mentorsGroupMembership: (g) => g, orElse: () => null);

                return maybeMentorGroupMembership != null &&
                    maybeMentorGroupMembership.reasonsForMentoring ==
                        _textEditingController.text.trim();
              },
            ),
          ),
        );
      },
    );
  }
}
