import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';

import '../../../../constants/constants.dart';
import '../components/reset_password_template.dart';

class ResetPasswordCompletedScreen extends StatefulWidget {
  const ResetPasswordCompletedScreen({super.key});

  @override
  State<ResetPasswordCompletedScreen> createState() => _ResetPasswordCompletedScreenState();
}

class _ResetPasswordCompletedScreenState extends State<ResetPasswordCompletedScreen> {
  @override
  Widget build(BuildContext context) {
    final GoRouter router = GoRouter.of(context);

    return ResetPasswordTemplate(
      progress: 1.0,
      title: AppLocale.current.resetPasswordCompletedTitle,
      subtitle: AppLocale.current.resetPasswordCompletedSubtitle,
      body: const SizedBox(),
      isNextEnabled: () => true,
      showPreviousButton: false,
      onNextPressed: () {
        router.replaceNamed(
          AppRoutes.root.name,
          queryParameters: {RouteParams.nextRouteName: AppRoutes.home.name},
        );
      },
    );
  }
}
