import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../models/models.dart';
import '../../../../services/graphql/providers/multi_step_action_provider.dart';
import '../../../widgets.dart';

class ResetPasswordEnterNewPasswordScreen extends StatefulWidget {
  final String? actionId;
  final String? email;

  const ResetPasswordEnterNewPasswordScreen({super.key, this.actionId, this.email});

  @override
  State<ResetPasswordEnterNewPasswordScreen> createState() =>
      _ResetPasswordEnterNewPasswordScreenState();
}

class _ResetPasswordEnterNewPasswordScreenState extends State<ResetPasswordEnterNewPasswordScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late LocalDataModel _localData;
  late final TextEditingController _passwordController;
  late final TextEditingController _confirmPasswordController;
  late final TextEditingController _codeController;
  String? _actionId;
  Timer? _timer;
  int _secondsRemaining = 0;

  String? errorMessage;
  bool _processing = false;

  @override
  void initState() {
    super.initState();

    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _actionId = widget.actionId ?? '';
    _passwordController = TextEditingController(text: '');
    _confirmPasswordController = TextEditingController(text: '');
    _codeController = TextEditingController(text: '');
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final GoRouter router = GoRouter.of(context);

    return ResetPasswordTemplate(
      progress: 0.75,
      title: AppLocale.current.resetPasswordEnterNewPasswordTitle,
      subtitle: AppLocale.current.resetPasswordEnterNewPasswordSubtitle,
      body: _processing ? const CircularProgressIndicator() : _codePasswordWidget(context.theme),
      errorMessage: errorMessage,
      isNextEnabled:
          () =>
              (_passwordController.text.isNotEmpty &&
                  _confirmPasswordController.text.isNotEmpty &&
                  _codeController.text.isNotEmpty &&
                  !_processing &&
                  _formKey.currentState != null &&
                  _formKey.currentState!.validate()),
      showPreviousButton: !_processing,
      onNextPressed: () {
        if (_formKey.currentState != null && !_formKey.currentState!.validate()) {
          return;
        }

        setState(() {
          _processing = true;
          errorMessage = '';
        });

        MultiStepActionProvider provider = Provider.of<MultiStepActionProvider>(
          context,
          listen: false,
        );
        provider.verifyToken(
          actionId: _actionId ?? '',
          token: _codeController.text,
          newPassword: _passwordController.text,
          onSuccess: (userId, authToken) {
            _localData.userId = userId;
            _localData.authToken = authToken;
            router.pushNamed(
              AppRoutes.root.name,
              queryParameters: {RouteParams.nextRouteName: AppRoutes.home.name},
              // todo: The following route is probably no longer needed. If so,
              //       delete it.
              // router.replace(Routes.resetPasswordCompleted.path);
            );
            setState(() {
              _processing = false;
            });
          },
          onTokenMismatch: () {
            errorMessage = AppLocale.current.resetPasswordTokenMismatchError;
            setState(() {
              _processing = false;
            });
          },
          onFailedToSendNotification: () {
            errorMessage = AppLocale.current.resetPasswordFailedToSendNotification;
            setState(() {
              _processing = false;
            });
          },
          onUnknownErrorOccurred: () {
            errorMessage = AppLocale.current.errorMessageUnknownError;
            setState(() {
              _processing = false;
            });
          },
        );
      },
    );
  }

  _codePasswordWidget(ThemeData theme) {
    return Column(
      children: [
        TextFormFieldWidget(
          label: AppLocale.current.resetPasswordEnterCodeLabel,
          hint: AppLocale.current.resetPasswordEnterCodeHint,
          textController: _codeController,
          onChanged: (_) {
            setState(() {});
          },
        ),
        SizedBox(height: context.theme.d.paddingXxSmall),
        Container(
          alignment: Alignment.centerRight,
          padding: EdgeInsets.only(right: context.theme.d.paddingXxSmall),
          child: InkWell(
            onTap: () {
              if (_secondsRemaining == 0) {
                _resendCodeAction();
              }
            },
            child: Text(
              _secondsRemaining == 0
                  ? AppLocale.current.resendCodeTitle
                  : AppLocale.current.resendCodeInSeconds(_secondsRemaining),
              style: context.textTheme.labelLarge?.copyWith(
                color:
                    _secondsRemaining == 0
                        ? context.colorScheme.primary
                        : context.colorScheme.secondary,
              ),
            ),
          ),
        ),
        SizedBox(height: context.theme.d.paddingMedium),
        Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormFieldWidget(
                isPassword: true,
                validator: context.theme.validator.validatePassword,
                label: AppLocale.current.signupCredentialsPasswordInputLabel,
                textController: _passwordController,
                onChanged: (_) => setState(() {}),
              ),
              SizedBox(height: context.theme.d.paddingMedium),
              TextFormFieldWidget(
                isPassword: true,
                label: AppLocale.current.signupCredentialsPasswordConfirmInputLabel,
                textController: _confirmPasswordController,
                onChanged: (_) => setState(() {}),
                validator:
                    (value) => context.theme.validator.validatePassword(
                      value,
                      matchValue: _passwordController.text,
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  _resendCodeAction() async {
    _startTimer();

    MultiStepActionProvider provider = Provider.of<MultiStepActionProvider>(context, listen: false);

    var result = await provider.startResetPassword(email: widget.email ?? '');
    final resultError = result.gqlQueryResult.exception?.graphqlErrors.firstOrNull?.message;

    if (resultError != null) {
      errorMessage =
          resultError == 'invalidInput'
              ? AppLocale.current.resetPasswordEmailNotFound
              : AppLocale.current.errorMessageUnknownError;
    }
    _actionId = result.response?.actionId;
  }

  _startTimer() {
    _secondsRemaining = 30;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_secondsRemaining > 0) {
          _secondsRemaining -= 1;
        } else {
          _timer?.cancel();
        }
      });
    });
  }
}
