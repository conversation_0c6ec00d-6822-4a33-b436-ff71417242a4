import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';

import '../../../../constants/constants.dart';
import '../../../shared/text_form_field_widget.dart';
import '../components/reset_password_template.dart';

class ResetPasswordEnterCodeScreen extends StatefulWidget {
  final String? actionId;
  const ResetPasswordEnterCodeScreen({super.key, this.actionId});

  @override
  State<ResetPasswordEnterCodeScreen> createState() => _ResetPasswordEnterCodeScreenState();
}

class _ResetPasswordEnterCodeScreenState extends State<ResetPasswordEnterCodeScreen> {
  late final TextEditingController _codeController;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _codeController = TextEditingController(text: '');
  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final GoRouter router = GoRouter.of(context);

    return ResetPasswordTemplate(
      progress: 0.5,
      title: AppLocale.current.resetPasswordEnterCodeTitle,
      subtitle: AppLocale.current.resetPasswordEnterCodeSubtitle,
      body: TextFormFieldWidget(
        label: AppLocale.current.resetPasswordEnterCodeLabel,
        hint: AppLocale.current.resetPasswordEnterCodeHint,
        textController: _codeController,
        validator: context.theme.validator.validate2faCode,
        onChanged: (_) => setState(() {}),
      ),
      isNextEnabled:
          () =>
              _codeController.text.isNotEmpty &&
              _formKey.currentState != null &&
              _formKey.currentState?.validate() == true,
      onNextPressed:
          () => router.push(
            AppRoutes.resetPasswordEnterNewPassword.path,
            extra: {'actionId': widget.actionId, 'token': _codeController.text},
          ),
    );
  }
}
