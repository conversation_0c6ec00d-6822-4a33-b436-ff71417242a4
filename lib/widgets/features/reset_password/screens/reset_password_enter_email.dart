import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../services/graphql/providers/multi_step_action_provider.dart';
import '../../../shared/text_form_field_widget.dart';
import '../components/reset_password_template.dart';

class ResetPasswordEnterEmailScreen extends StatefulWidget {
  final String? enteredEmail;
  const ResetPasswordEnterEmailScreen({super.key, this.enteredEmail});

  @override
  State<ResetPasswordEnterEmailScreen> createState() => _ResetPasswordEnterEmailScreenState();
}

class _ResetPasswordEnterEmailScreenState extends State<ResetPasswordEnterEmailScreen> {
  late final TextEditingController _emailController;
  String? errorMessage;
  final _formKey = GlobalKey<FormState>();
  bool _processing = false;

  @override
  void initState() {
    super.initState();
    _emailController = TextEditingController();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.enteredEmail?.isNotEmpty == true) {
        setState(() {
          _emailController.text = widget.enteredEmail ?? '';
        });
      }
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final GoRouter router = GoRouter.of(context);

    return ResetPasswordTemplate(
      progress: 0.3,
      title: AppLocale.current.resetPasswordTitle,
      subtitle: AppLocale.current.resetPasswordSubtitle,
      errorMessage: errorMessage,
      body:
          _processing
              ? const Center(child: CircularProgressIndicator())
              : Form(
                key: _formKey,
                child: TextFormFieldWidget(
                  label: AppLocale.current.resetPasswordEmail,
                  prefixIcon: Icon(
                    MicromentorIcons.emailOutline,
                    color: context.colorScheme.outline,
                  ),
                  textController: _emailController,
                  keyboardType: TextInputType.emailAddress,
                  onChanged:
                      (_) => setState(() {
                        errorMessage = null;
                      }),
                  validator: context.theme.validator.validateEmail,
                ),
              ),
      isNextEnabled:
          () =>
              (_emailController.text.isNotEmpty &&
                  _formKey.currentState != null &&
                  _formKey.currentState?.validate() == true &&
                  !_processing),
      showPreviousButton: true,
      onNextPressed: () async {
        setState(() {
          _processing = true;
        });

        MultiStepActionProvider provider = Provider.of<MultiStepActionProvider>(
          context,
          listen: false,
        );

        var result = await provider.startResetPassword(email: _emailController.text);
        final resultError = result.gqlQueryResult.exception?.graphqlErrors.firstOrNull?.message;

        if (resultError != null) {
          setState(() {
            errorMessage =
                resultError == Enum$ErrorCode.userNotFound.name
                    ? AppLocale.current.resetPasswordEmailNotFound
                    : AppLocale.current.errorMessageUnknownError;
            _processing = false;
          });
        } else {
          setState(() {
            _processing = false;
          });
          router.push(
            AppRoutes.resetPasswordEnterNewPassword.path,
            extra: {'actionId': result.response?.actionId, 'email': _emailController.text},
          );
        }
      },
    );
  }
}
