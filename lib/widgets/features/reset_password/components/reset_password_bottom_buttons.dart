import 'package:flutter/material.dart';
import 'package:mm_flutter_app/widgets/shared/common_button.dart';

import '../../../../constants/constants.dart';
import '../../../../services/extensions.dart';

class ResetPasswordBottomButtons extends StatelessWidget {
  final String? leftButtonText;
  final String rightButtonText;
  final Function()? leftOnPress;
  final Function()? rightOnPress;

  const ResetPasswordBottomButtons({
    super.key,
    this.leftButtonText,
    required this.rightButtonText,
    this.leftOnPress,
    this.rightOnPress,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: context.theme.d.boxSizeMedium,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          if (leftButtonText != null)
            CommonButton.textButton(
              context: context,
              title: '$leftButtonText',
              onPressed: leftOnPress,
            ),
          // Send invites button
          if (leftButtonText != null) SizedBox(width: context.theme.d.paddingLarge),
          CommonButton.primaryRoundedRectangle(
            context: context,
            title: rightButtonText,
            onPressed: rightOnPress,
            buttonSize: ButtonSize.small,
          ),
        ],
      ),
    );
  }
}
