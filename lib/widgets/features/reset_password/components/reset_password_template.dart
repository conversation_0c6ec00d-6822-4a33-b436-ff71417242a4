import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';

import '../../../widgets.dart';

class ResetPasswordTemplate extends StatelessWidget {
  final double progress;
  final String title;
  final String? subtitle;
  final String? errorMessage;
  final Widget body;
  final bool showPreviousButton;
  final bool Function() isNextEnabled;
  final VoidCallback? onNextPressed;
  final AsyncState processingState;

  const ResetPasswordTemplate({
    super.key,
    required this.progress,
    required this.title,
    this.subtitle,
    this.errorMessage,
    required this.body,
    required this.isNextEnabled,
    this.showPreviousButton = true,
    this.onNextPressed,
    this.processingState = AsyncState.ready,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: AppOnboardingTemplate(
        resizeToAvoidBottomInset: true,
        showBackButton: true,
        onBack: () {
          context.pop();
        },
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingXLarge),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: context.colorScheme.outlineVariant.withValues(alpha: 0.3),
                  minHeight: context.theme.d.paddingSmall,
                  borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusSmall),
                ),
                SizedBox(height: context.theme.d.roundedRectRadiusMedium),
                Text(
                  title,
                  softWrap: true,
                  style: context.theme.textTheme.headlineSmall?.copyWith(
                    color: context.theme.colorScheme.surfaceBright,
                    fontWeight: FontWeight.w700,
                  ),
                  textAlign: TextAlign.start,
                ),
                SizedBox(height: context.theme.d.roundedRectRadiusMedium),
                if (subtitle != null) ...[
                  Text(
                    subtitle!,
                    style: context.theme.textTheme.bodyLarge?.copyWith(
                      color: context.theme.colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.start,
                  ),
                  SizedBox(height: context.theme.d.paddingMedium),
                ],
                body,
                if (errorMessage != null) ...[
                  SizedBox(height: context.theme.d.paddingMedium),
                  Text(
                    errorMessage!,
                    style: context.theme.textTheme.bodyMedium?.copyWith(
                      color: context.theme.colorScheme.error,
                    ),
                    textAlign: TextAlign.start,
                  ),
                ],
                SizedBox(height: context.theme.d.paddingLarge),
                CommonButton.primaryRoundedRectangle(
                  context: context,
                  isFullWidth: true,
                  key: const Key('btnSubmit'),
                  title: AppLocale.current.actionNext,
                  onPressed: isNextEnabled() ? onNextPressed : null,
                  buttonSize: ButtonSize.large,
                ),
                SizedBox(height: context.theme.d.paddingLarge),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
