import 'package:flutter/material.dart';
import 'package:mm_flutter_app/services/firebase/analytic_service.dart';
import 'package:mm_flutter_app/utilities/errors/error_handler.dart';
import 'package:mm_flutter_app/utilities/loading/loading_provider.dart';
import 'package:provider/provider.dart';

import '../../../constants/constants.dart';
import '../../../models/models.dart';
import '../../../services/graphql/providers/providers.dart';
import '../../../utilities/navigation_mixin.dart';
import '../../../utilities/utility.dart';
import 'channel.dart';

class ChannelWidget extends StatefulWidget {
  final Channel channel;

  const ChannelWidget({super.key, required this.channel});

  @override
  State<ChannelWidget> createState() => _ChannelWidgetState();
}

class _ChannelWidgetState extends State<ChannelWidget> with NavigationMixin<ChannelWidget> {
  final TextEditingController messageTextController = TextEditingController();
  final ScrollController listScrollController = ScrollController();
  final Duration _animationDuration = const Duration(milliseconds: 250);
  late final ChatModel _chatModel;
  late final UserProvider _userProvider;
  ChannelMessage? _focusedMessage; // Intended reply Message
  int messageHeaderMargin = 35;

  @override
  void initState() {
    super.initState();
    _chatModel = Provider.of<ChatModel>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _chatModel.createChannelSubscription();
    _chatModel.onNewMessage = _onNewMessage;
    _chatModel.onMessagesReceived = _onMessagesReceived;
    listScrollController.addListener(_fetchOlderMessages);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _chatModel.loadChannelMessages();
  }

  @override
  void didUpdateWidget(ChannelWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!AppUtility.displayDesktopUI(context, isChatScreen: true)) return;
    if (widget.channel.id == _chatModel.channelId) return;
    _chatModel.resetChannelId(widget.channel.id);
    _chatModel.loadChannelMessages();
  }

  void _onNewMessage(ChannelMessage message) {
    if (isCurrentPageRoute() &&
        !AppUtility.isChannelMessageMarkedAsSeen(message, _userProvider.myUser?.id)) {
      _chatModel.markMessagesAsRead();
    }
    if (_chatModel.channelMessages.last.createdBy == _userProvider.myUser?.id) {
      _scrollDown();
    }
  }

  void _onMessagesReceived(List<ChannelMessage> messages) {
    if (isCurrentPageRoute() &&
        AppUtility.containsUnseenChannelMessages(messages, _userProvider.myUser?.id)) {
      _chatModel.markMessagesAsRead();
    }
  }

  void _fetchOlderMessages() async {
    if (listScrollController.position.extentAfter < messageHeaderMargin &&
        !_chatModel.endOfResults &&
        _chatModel.loadMessageHistoryState != AsyncState.loading) {
      Loader.show(context);
      await _chatModel.loadChannelMessages();
      if (mounted) Loader.hide(context);
    }
  }

  @override
  void dispose() {
    _chatModel.cancelChannelSubscription();
    _chatModel.onNewMessage = null;
    messageTextController.dispose();
    listScrollController.dispose();
    super.dispose();
  }

  void _scrollDown() {
    if (listScrollController.hasClients) {
      WidgetsBinding.instance.addPostFrameCallback(
        (_) => listScrollController.animateTo(
          listScrollController.position.minScrollExtent,
          duration: _animationDuration,
          curve: Curves.easeInOutBack,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Selector<ChatModel, List<ChannelMessage>>(
      selector: (_, chatModel) => chatModel.channelMessages,
      shouldRebuild: (_, __) => true,
      builder: (_, __, child) {
        return AppUtility.widgetForAsyncState(
          state: _chatModel.loadLatestMessagesState,
          onReady:
              () => SafeArea(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: ChannelMessagesWidget(
                        channel: widget.channel,
                        myUser: _userProvider.myUser!,
                        chatMessages: _chatModel.channelMessages,
                        participants: widget.channel.participants,
                        listScrollController: listScrollController,
                        onSetReplyingTo: (message) {
                          setState(() {
                            _focusedMessage = message;
                          });
                        },
                      ),
                    ),
                    child!,
                  ],
                ),
              ),
        );
      },
      child: MessageInput(
        replyingTo: _focusedMessage,
        participants: widget.channel.participants,
        onSubmit: (val, replyToMessageId) async {
          final result = await _chatModel.createChannelMessage(
            channelId: widget.channel.id,
            messageText: val,
            replyToMessageId: replyToMessageId,
          );
          AnalyticService.sentMessages();
          if (result.gqlQueryResult.hasException && context.mounted) {
            AppErrorHandler(context: context, exception: result.gqlQueryResult.exception);
          }

          setState(() {
            _focusedMessage = null;
          });
        },
        onClearReply: () {
          setState(() {
            _focusedMessage = null;
          });
        },
      ),
    );
  }
}
