import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:provider/provider.dart';

import '../../../../services/extensions.dart';
import '../../../constants/parts/micromentor_icons.dart';
import '../../../services/graphql/providers/providers.dart';

class MessageReply extends StatelessWidget {
  const MessageReply({
    super.key,
    required this.replyMessage,
    required this.participants,
    this.onClose,
  });

  final List<ChannelParticipant> participants;
  final ChannelMessage replyMessage;
  final VoidCallback? onClose;

  bool _isCurrentUser({userId, context}) {
    return Provider.of<UserProvider>(context, listen: false).myUser?.id == userId;
  }

  String _participantName({userId}) {
    final ChannelParticipant? participant =
        participants.where((item) => item.user.id == userId).firstOrNull;
    if (participant != null) {
      final String fullName = '${participant.user.firstName} ${participant.user.lastName}';
      return fullName.trim().split(RegExp(' +')).take(1).join();
    }
    return '';
  }

  @override
  Widget build(BuildContext context) {
    final isUser = _isCurrentUser(userId: replyMessage.createdBy, context: context);

    return Material(
      elevation: context.theme.d.elevationLevel1,
      color:
          isUser
              ? context.theme.colorScheme.primaryContainer
              : context.theme.colorScheme.tertiaryContainer,
      borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusSmall),
      child: Stack(
        children: [
          PositionedDirectional(
            top: context.theme.d.zero,
            bottom: context.theme.d.zero,
            start: context.theme.d.zero,
            child: Container(
              decoration: BoxDecoration(
                color:
                    isUser ? context.theme.colorScheme.primary : context.theme.colorScheme.tertiary,
                borderRadius: BorderRadiusDirectional.horizontal(
                  start: Radius.circular(context.theme.d.roundedRectRadiusSmall),
                ),
              ),
              width: context.theme.d.paddingSmall,
            ),
          ),
          Padding(
            padding: EdgeInsetsDirectional.only(start: context.theme.d.paddingSmall),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(context.theme.d.paddingSmall),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _participantName(userId: replyMessage.createdBy),
                          maxLines: 1,
                          style: context.theme.textTheme.bodyMedium?.copyWith(
                            color:
                                isUser
                                    ? context.theme.colorScheme.onPrimaryContainer
                                    : context.theme.colorScheme.onTertiaryContainer,
                            overflow: TextOverflow.ellipsis,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          replyMessage.deletedAt == null
                              ? replyMessage.messageText!
                              : AppLocale.current.inboxMessagesStatusDeletedByOther,
                          maxLines: 1,
                          style: context.theme.textTheme.bodyMedium?.copyWith(
                            color:
                                isUser
                                    ? context.theme.colorScheme.onPrimaryContainer
                                    : context.theme.colorScheme.onTertiaryContainer,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                if (onClose != null)
                  IconButton(
                    padding: EdgeInsets.zero,
                    // An empty BoxConstraints overrides the default size, moreso than setting the visualDensity
                    constraints: const BoxConstraints(),
                    //  Hide the splash effect
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    icon: Icon(MicromentorIcons.close, size: context.theme.d.iconSizeLarge),
                    onPressed: onClose,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
