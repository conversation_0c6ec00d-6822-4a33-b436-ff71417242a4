import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mm_flutter_app/constants/parts/micromentor_icons.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/utilities/errors/error_handler.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/messages_provider.dart';
import '../../../models/models.dart';
import 'channel.dart';

class MessageContextMenu extends StatefulWidget {
  final ChannelMessageWidget messageBubble;

  const MessageContextMenu({super.key, required this.messageBubble});

  @override
  State<MessageContextMenu> createState() => _MessageContextMenuState();
}

class _MessageContextMenuState extends State<MessageContextMenu> {
  late final ChatModel _chatModel;
  late final MessagesProvider _messagesProvider;
  final GlobalKey _key = GlobalKey();

  @override
  void initState() {
    super.initState();
    _chatModel = Provider.of<ChatModel>(context, listen: false);
    _messagesProvider = Provider.of<MessagesProvider>(context, listen: false);
  }

  void _showContextDialog(bool isEditing) {
    final maxTopValue = context.mediaQuerySize.height * 0.7;
    showDialog(
      context: context,
      builder: (dialogContext) {
        final ThemeData theme = Theme.of(dialogContext);
        final renderBox = _key.currentContext?.findRenderObject() as RenderBox?;
        final position = renderBox?.localToGlobal(Offset.zero);
        final size = renderBox?.size;
        double verticalOffset = 0;
        if (kIsWeb) {
          verticalOffset = 0;
        } else if (position!.dy + renderBox!.size.height > maxTopValue) {
          verticalOffset = context.mediaQuerySize.height * 0.16;
        } else {
          verticalOffset += (context.theme.d.customToolbarHeight - kToolbarHeight);
        }
        final focusedMessageBubble = SizedBox(
          width: size?.width ?? context.theme.d.zero,
          height: size?.height ?? context.theme.d.zero,
          child: widget.messageBubble,
        );
        return GestureDetector(
          onTap: () => _closeDialog(dialogContext),
          child: Scaffold(
            backgroundColor: Colors.transparent,
            body: Stack(
              children: [
                Positioned(
                  top: 0,
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(color: Colors.black.withValues(alpha: 0.4)),
                ),
                if (!isEditing)
                  PositionedDirectional(
                    start: widget.messageBubble.isSentByMe ? null : context.theme.d.paddingMedium,
                    end: widget.messageBubble.isSentByMe ? context.theme.d.paddingMedium : null,
                    top:
                        position!.dy + renderBox!.size.height < maxTopValue
                            ? position.dy - verticalOffset
                            : null,
                    bottom:
                        position.dy + renderBox.size.height > maxTopValue
                            ? context.theme.d.paddingMedium
                            : null,
                    width: context.mediaQuerySize.width - 2 * context.theme.d.paddingMedium,
                    child: Column(
                      crossAxisAlignment:
                          widget.messageBubble.isSentByMe
                              ? CrossAxisAlignment.end
                              : CrossAxisAlignment.start,
                      children: [
                        focusedMessageBubble,
                        if (widget.messageBubble.isSentByMe && !widget.messageBubble.isDeleted)
                          _createBottomMessageMenu(dialogContext, theme),
                      ],
                    ),
                  ),
                if (isEditing)
                  Builder(
                    builder: (editDialogContext) {
                      final controller = TextEditingController();
                      controller.text = widget.messageBubble.message.messageText!;
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          const Spacer(),
                          Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: context.theme.d.paddingMedium,
                              vertical: context.theme.d.paddingSmall,
                            ),
                            child: focusedMessageBubble,
                          ),
                          Material(
                            color: Colors.transparent,
                            child: MessageInput(
                              participants: widget.messageBubble.participants,
                              autoFocus: true,
                              controller: controller,
                              onSubmit: (messageText, _) async {
                                final result = await _messagesProvider.updateMessage(
                                  input: Input$ChannelMessageInput(
                                    channelId: widget.messageBubble.message.channelId,
                                    id: widget.messageBubble.message.id,
                                    messageText: messageText,
                                  ),
                                );
                                if (result.gqlQueryResult.hasException && dialogContext.mounted) {
                                  AppErrorHandler(
                                    context: dialogContext,
                                    exception: result.gqlQueryResult.exception,
                                  );
                                }
                                if (dialogContext.mounted) {
                                  _closeDialog(dialogContext);
                                }
                              },
                            ),
                          ),
                        ],
                      );
                    },
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _closeDialog(BuildContext dialogContext) {
    Navigator.pop(dialogContext);
  }

  void _editMessage(BuildContext dialogContext) {
    _showContextDialog(true);
  }

  void _deleteMessage(BuildContext dialogContext) async {
    final result = await _chatModel.deleteMessage(widget.messageBubble.message.id);
    if (dialogContext.mounted) {
      if (result.gqlQueryResult.hasException) {
        AppErrorHandler(context: dialogContext, exception: result.gqlQueryResult.exception);
      }
      _closeDialog(dialogContext);
    }
  }

  Widget _createBottomMessageMenu(BuildContext dialogContext, ThemeData theme) {
    double menuWidth = theme.d.messageMenuWidth;
    return Material(
      elevation: context.theme.d.elevationLevel1,
      textStyle: context.theme.textTheme.bodyLarge?.copyWith(
        color: context.theme.colorScheme.onPrimaryContainer,
      ),
      borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusSmall),
      color: context.theme.colorScheme.primaryContainer,
      child: SizedBox(
        width: menuWidth,
        child: Column(
          children: [
            PopupMenuItem(
              onTap: () => _editMessage(dialogContext),
              child: ListTile(
                title: Text(AppLocale.current.inboxMessagesActionEdit),
                leading: Icon(
                  MicromentorIcons.editOutline,
                  color: context.theme.colorScheme.onSurface,
                  size: context.theme.d.iconSizeLarge,
                ),
              ),
            ),
            PopupMenuItem(
              onTap: () => _deleteMessage(dialogContext),
              child: ListTile(
                title: Text(AppLocale.current.inboxMessagesActionDelete),
                leading: Icon(
                  MicromentorIcons.deleteOutline,
                  color: context.theme.colorScheme.onSurface,
                  size: context.theme.d.iconSizeLarge,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      key: _key,
      onLongPress: () => _showContextDialog(false),
      onSecondaryTap: () => _showContextDialog(false),
      child: Builder(
        builder: (context) {
          return widget.messageBubble;
        },
      ),
    );
  }
}

//TODO: This code will need once we show edit/delete message menu so keeping it for future reference
/*       canPop: false,
      onPopInvoked: (_) {
        if (_dialogKey.currentState != null && Navigator.canPop(_dialogKey.currentContext!)) {
          Navigator.of(_dialogKey.currentContext!).pop();
        }
        context.pop();
      }, */
