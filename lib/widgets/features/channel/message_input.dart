import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:provider/provider.dart';

import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/channels_provider.dart';
import '../../../../services/graphql/providers/messages_provider.dart';
import '../../../constants/parts/micromentor_icons.dart';
import '../../../models/locale_model.dart';
import 'message_reply.dart';

class MessageInput extends StatefulWidget {
  const MessageInput({
    super.key,
    required this.participants,
    required this.onSubmit,
    this.onClearReply,
    this.controller,
    this.replyingTo,
    this.onTapEmptyInput,
    this.autoFocus = false,
  });

  final List<ChannelParticipant> participants;
  final TextEditingController? controller;
  final VoidCallback? onTapEmptyInput;
  final Function(String messageText, String? replyingToMessageId) onSubmit;
  final Function? onClearReply;
  final ChannelMessage? replyingTo;
  final bool? autoFocus;

  @override
  State<MessageInput> createState() => _MessageInputState();
}

class _MessageInputState extends State<MessageInput> {
  late TextEditingController _controller;
  late final LocaleModel _localeModel;
  late bool _autoFocus;
  bool _enableSend = false;
  ChannelMessage? _replyingTo;
  final logicalKeySet = <LogicalKeyboardKey>[
    LogicalKeyboardKey.shift,
    LogicalKeyboardKey.meta,
    LogicalKeyboardKey.enter,
  ];

  void _sendMessage() {
    widget.onSubmit(_controller.text.trim(), _replyingTo?.id);
    setState(() {
      _controller.clear();
      _replyingTo = null;
    });
  }

  @override
  void initState() {
    _controller = widget.controller ?? TextEditingController();
    _localeModel = Provider.of<LocaleModel>(context, listen: false);
    _controller.addListener(() {
      setState(() {
        _enableSend = _controller.text.isNotEmpty;
      });
    });
    _autoFocus = widget.autoFocus == true;
    _replyingTo = widget.replyingTo;
    super.initState();
  }

  @override
  void didUpdateWidget(covariant MessageInput oldWidget) {
    setState(() {
      _replyingTo = widget.replyingTo;
    });
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _controller.dispose();
    _replyingTo = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: context.theme.colorScheme.onPrimary,
      padding: EdgeInsets.all(context.theme.d.paddingSmall),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: context.colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusLarge),
              ),
              child: Column(
                children: [
                  if (_replyingTo != null)
                    Padding(
                      padding: EdgeInsets.all(context.theme.d.paddingSmall),
                      child: MessageReply(
                        replyMessage: _replyingTo!,
                        participants: widget.participants,
                        onClose: () {
                          setState(() {
                            _replyingTo = null;
                          });
                          widget.onClearReply!();
                        },
                      ),
                    ),
                  Actions(
                    actions: {
                      SendMesssageAction: CallbackAction<SendMesssageAction>(
                        onInvoke: (intent) {
                          if (!_enableSend) return null;
                          _sendMessage();
                          return null;
                        },
                      ),
                    },
                    child: Shortcuts(
                      shortcuts: {
                        LogicalKeySet(
                              LogicalKeyboardKey.meta,
                              LogicalKeyboardKey.shift,
                              LogicalKeyboardKey.enter,
                            ):
                            const SendMesssageAction(),
                      },
                      child: TextField(
                        minLines: 1,
                        maxLines: 10,
                        style: context.theme.textTheme.bodyLarge,
                        autofocus: _autoFocus,
                        controller: _controller,
                        textCapitalization: TextCapitalization.sentences,
                        textAlign:
                            _localeModel.isArabic(text: _controller.text)
                                ? TextAlign.right
                                : TextAlign.left,
                        textDirection:
                            _localeModel.isArabic(text: _controller.text)
                                ? TextDirection.rtl
                                : TextDirection.ltr,
                        keyboardType: TextInputType.multiline,
                        onTap: widget.onTapEmptyInput,
                        decoration: InputDecoration(
                          contentPadding: EdgeInsets.symmetric(
                            vertical: context.theme.d.paddingXxSmall,
                            horizontal: context.theme.d.paddingMedium,
                          ),
                          hintText: AppLocale.current.inboxMessagesInputHint,
                          hintStyle: context.theme.textTheme.bodyLarge?.copyWith(
                            color: context.colorScheme.scrim,
                          ),
                          border: InputBorder.none,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(width: context.theme.d.paddingXSmall),
          IconButton(
            icon: const Icon(MicromentorIcons.sendRounded),
            padding: EdgeInsets.all(context.theme.d.fontSizeSmall),
            disabledColor: context.colorScheme.primary.withValues(alpha: 0.5),
            onPressed: _enableSend ? _sendMessage : null,
            color: context.colorScheme.primary,
            style: IconButton.styleFrom(
              backgroundColor: context.colorScheme.primaryContainer,
              disabledBackgroundColor: context.colorScheme.primaryContainer,
            ),
          ),
        ],
      ),
    );
  }
}

class SendMesssageAction extends Intent {
  const SendMesssageAction();
}
