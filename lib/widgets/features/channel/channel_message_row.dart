import 'package:flutter/material.dart';

import '../../../services/graphql/providers/providers.dart';
import 'channel.dart';

class ChannelMessageRow extends StatelessWidget {
  final ChannelMessage message;
  final List<ChannelParticipant> participants;
  final List<ChannelMessage> chatMessages;
  final bool isSentByMe;

  const ChannelMessageRow({
    super.key,
    required this.message,
    required this.participants,
    required this.chatMessages,
    required this.isSentByMe,
  });

  @override
  Widget build(BuildContext context) {
    ChannelMessage? replyingTo;
    if (message.replyToMessageId != null) {
      try {
        replyingTo = chatMessages.firstWhere((item) => item.id == message.replyToMessageId);
      } catch (e) {
        replyingTo = null;
      }
    }

    final bubble = ChannelMessageWidget(
      message: message,
      replyingTo: replyingTo,
      participants: participants,
      isDeleted: message.deletedAt != null,
      isSentByMe: isSentByMe,
    );
    return Row(
      mainAxisAlignment: isSentByMe ? MainAxisAlignment.end : MainAxisAlignment.start,
      children: [MessageContextMenu(messageBubble: bubble)],
    );
  }
}
