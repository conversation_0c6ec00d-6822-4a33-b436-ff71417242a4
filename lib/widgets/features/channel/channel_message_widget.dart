import 'package:flutter/material.dart';
import 'package:flutter_linkify/flutter_linkify.dart';
import 'package:intl/intl.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/models/locale_model.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';
import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/channels_provider.dart';
import '../../../../services/graphql/providers/messages_provider.dart';
import '../../../../utilities/emoji_utils/emoji_utils.dart';
import 'message_reply.dart';

class ChannelMessageWidget extends StatelessWidget {
  final List<ChannelParticipant> participants;
  final ChannelMessage message;
  final ChannelMessage? replyingTo;
  final bool isDeleted;
  final bool isSentByMe;

  const ChannelMessageWidget({
    super.key,
    required this.message,
    required this.participants,
    this.replyingTo,
    required this.isDeleted,
    required this.isSentByMe,
  });

  Widget _buildMessageText(ThemeData theme) {
    final isEmoji =
        EmojiUtils.isOnlyEmoji(message.messageText ?? '') && message.replyToMessageId == null;
    if (isDeleted) {
      return Text(
        isSentByMe
            ? AppLocale.current.inboxMessagesStatusDeletedByMe
            : AppLocale.current.inboxMessagesStatusDeletedByOther,
        style:
            isSentByMe
                ? theme.textTheme.bodyLarge?.copyWith(
                  fontStyle: FontStyle.italic,
                  color: theme.disabledColor,
                )
                : theme.textTheme.bodyLarge?.copyWith(
                  fontStyle: FontStyle.italic,
                  color: theme.colorScheme.onSurface,
                ),
      );
    } else {
      return SelectableLinkify(
        text: message.messageText ?? '',
        selectionControls: MaterialTextSelectionControls(),
        onOpen: (link) async => await AppUtility.openLink(link.url),
        linkStyle: theme.textTheme.bodyLarge?.copyWith(
          color: isSentByMe ? const Color(0xFFB0D0FF) : theme.colorScheme.tertiary,
        ),
        textAlign: isEmoji && isSentByMe ? TextAlign.end : null,
        style: theme.textTheme.bodyLarge?.copyWith(
          fontSize: isEmoji ? theme.textTheme.displayMedium?.fontSize : null,
          color: isSentByMe ? theme.colorScheme.onPrimary : theme.colorScheme.onSurface,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final LocaleModel localeModel = Provider.of<LocaleModel>(context, listen: false);

    final sentAt =
        DateFormat.jm(
          localeModel.getCurrentLanguageCode(),
        ).format(message.createdAt.toLocal()).toLowerCase();
    final sizeZero = context.theme.d.zero;
    final mediaQueryWidth = context.mediaQuerySize.width * 0.5;
    return Container(
      margin: EdgeInsets.symmetric(vertical: context.theme.d.paddingXxSmall),
      constraints: BoxConstraints(
        minWidth: context.theme.d.messageChannelMinWidth,
        maxWidth:
            mediaQueryWidth > context.theme.d.maxDesktopAppWidth
                ? context.theme.d.messageChannelMaxWidth
                : mediaQueryWidth,
      ),
      child: Material(
        elevation: context.theme.d.elevationLevel1,
        borderRadius: BorderRadiusDirectional.only(
          topStart: Radius.circular(context.theme.d.roundedRectRadiusLarge),
          topEnd: Radius.circular(context.theme.d.roundedRectRadiusSmall),
          bottomStart: Radius.circular(context.theme.d.roundedRectRadiusLarge),
          bottomEnd:
              isSentByMe ? Radius.zero : Radius.circular(context.theme.d.roundedRectRadiusSmall),
        ),
        color:
            isSentByMe
                ? context.theme.colorScheme.secondary
                : context.theme.colorScheme.primaryContainer,
        child: Padding(
          padding: EdgeInsets.all(context.theme.d.fontSizeSmall),
          child: Stack(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (replyingTo != null && !isDeleted)
                    Padding(
                      padding: EdgeInsets.only(bottom: context.theme.d.paddingSmall),
                      child: MessageReply(replyMessage: replyingTo!, participants: participants),
                    ),
                  // TODO: need to add custom paint
                  _buildMessageText(context.theme),
                  SizedBox(height: context.theme.d.paddingLarge),
                ],
              ),
              PositionedDirectional(
                bottom: sizeZero,
                end: sizeZero,
                child: Text(
                  message.editedAt != null
                      ? AppLocale.current.inboxMessagesStatusEdited(sentAt)
                      : sentAt,
                  style: context.theme.textTheme.labelSmall?.copyWith(
                    color:
                        isSentByMe ? context.colorScheme.onPrimary : context.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
