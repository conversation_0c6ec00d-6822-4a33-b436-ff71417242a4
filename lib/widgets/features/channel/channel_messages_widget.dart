import 'package:flutter/material.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/models/locale_model.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:provider/provider.dart';

import '../../../constants/constants.dart';
import '../../../services/graphql/providers/providers.dart';
import 'channel.dart';

class ChannelMessagesWidget extends StatelessWidget {
  final Channel channel;
  final User myUser;
  final List<ChannelParticipant> participants;
  final List<ChannelMessage> chatMessages;
  final ScrollController listScrollController;
  final Function(ChannelMessage message) onSetReplyingTo; //TODO - Temporarily disabled.

  const ChannelMessagesWidget({
    super.key,
    required this.channel,
    required this.myUser,
    required this.participants,
    required this.chatMessages,
    required this.listScrollController,
    required this.onSetReplyingTo,
  });

  @override
  Widget build(BuildContext context) {
    List<ChannelMessage> messages = chatMessages.reversed.toList();
    final LocaleModel localeModel = Provider.of<LocaleModel>(context, listen: false);

    return GroupedListView<ChannelMessage, String>(
      elements: messages,
      clipBehavior: Clip.none,
      // We do not want the builder to sort again...
      sort: false,
      reverse: true,
      order: GroupedListOrder.DESC,
      useStickyGroupSeparators: false,
      padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
      groupBy: (message) => DateFormat('yyyy-MM-dd').format(message.createdAt.toLocal()).toString(),
      indexedItemBuilder: (context, ChannelMessage message, i) {
        final message = messages[i];

        return ChannelMessageRow(
          key: ObjectKey(message.id),
          message: message,
          chatMessages: chatMessages,
          participants: participants,
          isSentByMe: message.createdBy == myUser.id,
        );
      },
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      controller: listScrollController,
      groupHeaderBuilder:
          (ChannelMessage message) => Center(
            child: Container(
              margin: EdgeInsets.all(context.theme.d.paddingSmall),
              padding: EdgeInsets.symmetric(
                horizontal: context.theme.d.paddingSmall,
                vertical: context.theme.d.paddingXxSmall,
              ),
              decoration: ShapeDecoration(
                color: context.colorScheme.tertiary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXSmall),
                ),
              ),
              child: Text(
                DateTime.now().day == message.createdAt.toLocal().day
                    ? AppLocale.current.dateToday[0].toUpperCase() +
                        AppLocale.current.dateToday.substring(1)
                    : DateFormat(
                      'EEE. MMM dd',
                      localeModel.getCurrentLanguageCode(),
                    ).format(message.createdAt.toLocal()),
                style: context.textTheme.labelMedium?.copyWith(
                  color: context.colorScheme.onPrimary,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ),
    );
  }
}
