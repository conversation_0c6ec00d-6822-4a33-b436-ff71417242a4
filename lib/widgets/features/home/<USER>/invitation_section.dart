import 'dart:math';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/models/scaffold_model.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/invitations_provider.dart';
import '../../../../utilities/navigation_mixin.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class InvitationSection extends StatefulWidget {
  final User myUser;
  const InvitationSection({super.key, required this.myUser});

  @override
  State<InvitationSection> createState() => _InvitationSectionState();
}

class _InvitationSectionState extends State<InvitationSection>
    with NavigationMixin<InvitationSection> {
  late final InvitationsProvider _invitationsProvider;
  late ScaffoldModel _scaffoldModel;

  @override
  void initState() {
    super.initState();
    _invitationsProvider = Provider.of<InvitationsProvider>(context, listen: false);
    _scaffoldModel = Provider.of<ScaffoldModel>(context, listen: false);
  }

  InboxInvitationTile _createInvitationTile(
    BuildContext context,
    MyChannelInvitation receivedInvitation,
  ) {
    return InboxInvitationTile(
      userName: AppUtility.getUserFullName(
        receivedInvitation.sender?.firstName,
        receivedInvitation.sender?.lastName,
      ),
      companyName:
          (receivedInvitation.sender?.seeksHelp == true
              ? receivedInvitation.sender?.companies?.firstOrNull?.name
              : receivedInvitation.sender?.businessExperiences?.firstOrNull?.businessName) ??
          '',
      // TODO: mentees don't always have a job title, display the company name instead for mentees
      userJobTitle:
          receivedInvitation.sender?.seeksHelp == true
              ? ''
              : receivedInvitation.sender?.businessExperiences?.firstOrNull?.jobTitle ?? '',
      invitationStatus: receivedInvitation.status,
      avatarUrl: receivedInvitation.sender?.avatarUrl,
      buttonOnPressed: () {
        _scaffoldModel.setParams(index: Tabs.inbox.index);
        (AppUtility.displayDesktopUI(context, isChatScreen: true))
            ? context.replaceNamed(
              AppRoutes.newInviteReceivedWeb.name,
              extra: receivedInvitation.id,
            )
            : context.push('${AppRoutes.inboxInvitesReceived.path}/${receivedInvitation.id}');
      },
    );
  }

  List<Widget> _createInvitationList(List<MyChannelInvitation> receivedInvitations) {
    List<Widget> invitationWidgets = [];
    for (int i = 0; i < min(Limits.homeInvitationsListMaxSize, receivedInvitations.length); i++) {
      invitationWidgets.add(_createInvitationTile(context, receivedInvitations[i]));
    }
    return invitationWidgets;
  }

  @override
  Widget build(BuildContext context) {
    return Selector<InvitationsProvider, String>(
      selector: (_, invitationProvider) => invitationProvider.unseenInvitationIds,
      builder: (context, _, __) {
        if (_invitationsProvider.unseenInvitations.isNotEmpty != true) {
          return const SizedBox();
        }
        return Padding(
          padding: EdgeInsets.only(top: context.theme.d.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  vertical: context.theme.d.paddingSmall,
                  horizontal: context.theme.d.paddingMedium,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  children: [
                    Row(
                      children: [
                        Text(
                          AppLocale.current.homeInvitationSectionTitle,
                          style: context.theme.textTheme.titleMedium?.copyWith(
                            color: context.theme.colorScheme.onSecondaryContainer,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        SizedBox(width: context.theme.d.paddingSmall),
                        NotificationBubble(value: _invitationsProvider.unseenInvitations.length),
                      ],
                    ),
                    if (_invitationsProvider.unseenInvitations.length >
                        Limits.homeInvitationsListMaxSize)
                      CommonButton.textButton(
                        context: context,
                        title: AppLocale.current.listSeeAll,
                        onPressed: () {
                          _scaffoldModel.setParams(index: Tabs.inbox.index);

                          (AppUtility.displayDesktopUI(context, isChatScreen: true))
                              ? context.replaceNamed(AppRoutes.newInviteReceivedWeb.name)
                              : context.push(AppRoutes.inboxInvitesReceived.path);
                        },
                        textStyle: context.theme.textTheme.labelLarge?.copyWith(
                          color: context.theme.colorScheme.primary,
                        ),
                      ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.only(bottom: context.theme.d.paddingSmall),
                child: Center(
                  child: Column(
                    children:
                        _invitationsProvider.unseenInvitations.isNotEmpty == true
                            ? _createInvitationList(_invitationsProvider.unseenInvitations)
                            : [],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
