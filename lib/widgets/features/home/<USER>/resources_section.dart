import 'package:flutter/material.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:provider/provider.dart';
import '../../../../constants/constants.dart';
import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/providers.dart';

class ResourceImage extends StatelessWidget {
  final String subheading;
  final String trainingUrlPath;
  final String userId;
  final bool openBrowser;
  final bool useWebview;
  final UserProvider userProvider;
  final String? networkImageUrl;
  final AssetImage? resourceImage;
  final Function? onTapAction;

  const ResourceImage({
    super.key,
    required this.subheading,
    required this.trainingUrlPath,
    required this.userId,
    required this.userProvider,
    this.openBrowser = false,
    this.useWebview = false,
    this.networkImageUrl,
    this.resourceImage,
    this.onTapAction,
  });

  @override
  Widget build(BuildContext context) {
    // must have at least one image
    if (resourceImage == null && (networkImageUrl == null || networkImageUrl!.isEmpty)) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(
        top: context.theme.d.paddingSmall,
        bottom: context.theme.d.paddingSmall,
      ),
      child: InkWell(
        onTap: () {
          onTapAction?.call();
        },
        radius: 50,
        child: Column(
          children: [
            // Image(image: resourceImage),
            networkImageUrl != null && networkImageUrl!.isNotEmpty
                ? Image.network(networkImageUrl!)
                : Image(image: resourceImage!),
            Padding(
              padding: EdgeInsets.only(top: context.theme.d.paddingSmall),
              child: Text(
                subheading,
                style: context.theme.textTheme.titleSmall?.copyWith(
                  color: context.theme.colorScheme.onSurface,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ResourcesSection extends StatefulWidget {
  final String userId;

  const ResourcesSection({super.key, required this.userId});

  @override
  ResourcesSectionState createState() => ResourcesSectionState();
}

class ResourcesSectionState extends State<ResourcesSection> {
  late final UserProvider _userProvider;
  late final VtsProvider _vtsProvider;
  late Future<List<Training>> _trainingsFuture;

  @override
  void initState() {
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _vtsProvider = Provider.of<VtsProvider>(context, listen: false);
    _trainingsFuture = loadTrainings();
    super.initState();
  }

  // get VTS trainings
  Future<List<Training>> loadTrainings() async {
    return await _vtsProvider.findTrainingsForMe();
  }

  List<Widget> _createResourceTiles(BuildContext context, List<Training> trainings) {
    List<Widget> resourceTiles = [];

    resourceTiles =
        trainings.map((Training training) {
          final String? imageUrl =
              training.imageUrls != null && training.imageUrls!.isNotEmpty
                  ? training.imageUrls![0]
                  : null;

          // examples of how to check if training is in progress or completed
          // final bool trainingIsInProgress = training.isTrainingCompletedForMe != true && training.myLatestTrainingSession?.isInProgress != null && training.myLatestTrainingSession!.isInProgress;
          // final bool trainingIsComplete = training.isTrainingCompletedForMe
          // If the user reviews a training after they complete it, the training's latest session in training.myLatestTrainingSession may not be completed, so it cannot be used to determine if the training is in progress or completed.

          return ResourceImage(
            resourceImage: const AssetImage(Assets.blankAvatar),
            trainingUrlPath: training.relativeUrlPath ?? '',
            networkImageUrl: imageUrl,
            subheading: training.title ?? '',
            userId: widget.userId,
            userProvider: _userProvider,
            openBrowser: true,
            useWebview: true,
            onTapAction: () async {
              await _vtsProvider.startTraining(
                context,
                training.title ?? '',
                training.mm2Id,
                training.relativeUrlPath,
              );
            },
          );
        }).toList();

    // if the list is empty, show the default resources
    if (resourceTiles.isEmpty) {
      resourceTiles = [
        Text(
          AppLocale.current.homeResourcesTitle,
          style: context.theme.textTheme.titleLarge?.copyWith(
            color: context.theme.colorScheme.onSurface,
          ),
        ),
        ResourceImage(
          resourceImage: const AssetImage(Assets.blankAvatar),
          trainingUrlPath: Identifiers.dummyTrainingUrlPath,
          subheading: AppLocale.current.homeResourcesItemMentoringFirstSteps,
          userId: widget.userId,
          userProvider: _userProvider,
          openBrowser: true,
          useWebview: true,
          onTapAction: () async {
            await _vtsProvider.startTraining(
              context,
              AppLocale.current.homeResourcesItemMentoringFirstSteps,
              null,
              Identifiers.dummyTrainingUrlPath,
            );
          },
        ),
        ResourceImage(
          resourceImage: const AssetImage(Assets.blankAvatar),
          trainingUrlPath: Identifiers.dummyTrainingUrlPath,
          subheading: AppLocale.current.homeResourcesItemBuildingFuture,
          userId: widget.userId,
          userProvider: _userProvider,
          openBrowser: true,
          useWebview: true,
          onTapAction: () async {
            await _vtsProvider.startTraining(
              context,
              AppLocale.current.homeResourcesItemMentoringFirstSteps,
              null,
              Identifiers.dummyTrainingUrlPath,
            );
          },
        ),
        ResourceImage(
          resourceImage: const AssetImage(Assets.blankAvatar),
          trainingUrlPath: Identifiers.dummyTrainingUrlPath,
          subheading: AppLocale.current.homeResourcesItemMarketingBusiness,
          userId: widget.userId,
          userProvider: _userProvider,
          openBrowser: true,
          useWebview: true,
          onTapAction: () async {
            await _vtsProvider.startTraining(
              context,
              AppLocale.current.homeResourcesItemMentoringFirstSteps,
              null,
              Identifiers.dummyTrainingUrlPath,
            );
          },
        ),
      ];
    }
    return resourceTiles;
  }

  @override
  Widget build(BuildContext context) {
    if (!StaticAppFeatures.resources) {
      return const SizedBox.shrink();
    }

    return FutureBuilder<List<Training>>(
      future: _trainingsFuture,
      builder: (BuildContext context, AsyncSnapshot<List<Training>> snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const CircularProgressIndicator();
        } else if (snapshot.hasError) {
          return Text('Error: ${snapshot.error}');
        } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Text('No trainings available');
        } else {
          final List<Training> trainings = snapshot.data!;
          return Padding(
            padding: EdgeInsets.symmetric(
              vertical: context.theme.d.paddingSmall,
              horizontal: context.theme.d.paddingMedium,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: _createResourceTiles(context, trainings),
            ),
          );
        }
      },
    );
  }
}
