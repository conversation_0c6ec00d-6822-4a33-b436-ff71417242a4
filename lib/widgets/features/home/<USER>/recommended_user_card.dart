import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../models/models.dart';
import '../../../../services/extensions.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class RecommendedUserCard extends StatelessWidget {
  final ProfileUtility profileUtils;
  final String userId;
  final double? cardWidth;
  final String? channelId;
  final bool isArchivedChannel;
  final String? sentInvitationId;
  final String? receivedInvitationId;
  final VoidCallback? onTap;

  const RecommendedUserCard({
    super.key,
    required this.profileUtils,
    required this.userId,
    this.channelId,
    this.isArchivedChannel = false,
    this.sentInvitationId,
    this.receivedInvitationId,
    this.onTap,
    this.cardWidth,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXLarge),
      ),
      clipBehavior: Clip.hardEdge,
      child: InkWell(
        onTap: onTap,
        child: AppUtility.displayDesktopUI(context) ? _desktopView(context) : _mobileView(context),
      ),
    );
  }

  Widget _mobileView(BuildContext context) {
    final ThemeData theme = context.theme;

    return Container(
      constraints: BoxConstraints(maxWidth: cardWidth ?? double.infinity),
      padding: EdgeInsets.all(theme.d.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ProfileImageWidget(
                avatarUrl: profileUtils.avatarUrl,
                isMentor: profileUtils.isMentor,
              ),
              SizedBox(width: theme.d.paddingMedium),
              Expanded(child: Stack(children: [profileUtils.profileInfoSection(context, false)])),
              if (channelId != null)
                IconButton(
                  icon: Icon(
                    MicromentorIcons.chatBubbleOutline,
                    size: context.theme.d.iconSizeLarge,
                  ),
                  key: const Key('message'),
                  onPressed: () {
                    Provider.of<ScaffoldModel>(
                      context,
                      listen: false,
                    ).setParams(index: Tabs.inbox.index);
                    isArchivedChannel
                        ? context.go('${AppRoutes.inboxChats.path}/$channelId')
                        : context.goNamed(
                          AppRoutes.inboxArchivedChannelId.name,
                          pathParameters: {RouteParams.channelId: channelId ?? ''},
                        );
                  },
                ),
              if (!(sentInvitationId == null && receivedInvitationId == null))
                CommonButton.textButton(
                  context: context,
                  key: const Key('viewinvite'),
                  title: AppLocale.current.view_invitation,
                  onPressed: () {
                    bool isSentInvitation = sentInvitationId != null;

                    isSentInvitation
                        ? context.push('${AppRoutes.inboxInvitesSent.path}/$sentInvitationId')
                        : context.push(
                          '${AppRoutes.inboxInvitesReceived.path}/$receivedInvitationId',
                        );
                  },
                  isFullWidth: false,
                  buttonSize: ButtonSize.small,
                ),
            ],
          ),
          if (profileUtils.expertises.isNotEmpty) ...[
            SizedBox(height: theme.d.paddingMedium),
            _createExpertisesWidget(theme),
          ],
        ],
      ),
    );
  }

  Widget _desktopView(BuildContext context) {
    final ThemeData theme = context.theme;

    // Check if the user has both roles (mentor and entrepreneur) or a single role.
    // Use 2 lines for isDualRoleUser to avoid overflow and 3 lines for a single role user.
    bool isDualRoleUser =
        ((profileUtils.reasonForBusiness).isNotEmpty &&
            (profileUtils.reasonsForMentoring).isNotEmpty);

    return Container(
      width: theme.d.webUserCardSize.width,
      constraints: BoxConstraints(
        maxHeight: theme.d.webUserCardSize.height,
        minHeight: theme.d.zero,
      ),
      padding: EdgeInsets.all(theme.d.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ProfileImageWidget(
                avatarUrl: profileUtils.avatarUrl,
                isMentor: profileUtils.isMentor,
              ),
              SizedBox(width: theme.d.paddingMedium),
              Expanded(
                child: Stack(
                  children: [
                    profileUtils.profileInfoSection(
                      context,
                      false,
                      fullNameMaxLine: isDualRoleUser ? 2 : 3,
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (profileUtils.expertises.isNotEmpty) ...[
            SizedBox(height: theme.d.paddingMedium),
            _createExpertisesWidget(theme),
          ],
          if (profileUtils.reasonForBusiness.isNotEmpty) ...[
            SizedBox(height: context.theme.d.paddingSmall),
            _headerTextWidget(
              theme,
              AppLocale.current.profileViewMotivation,
              profileUtils.reasonForBusiness,
              maxLines: isDualRoleUser ? 2 : 5,
            ),
          ],
          if ((profileUtils.reasonsForMentoring).isNotEmpty) ...[
            SizedBox(height: context.theme.d.paddingSmall),
            _headerTextWidget(
              theme,
              AppLocale.current.profileEditMainMentorWhyIMentor,
              profileUtils.reasonsForMentoring,
              maxLines: isDualRoleUser ? 2 : 5,
            ),
          ],
          const Spacer(),
          SizedBox(height: theme.d.paddingMedium),
          (channelId != null)
              ? CommonButton.outlinedButton(
                context: context,
                icon: Icon(MicromentorIcons.chatBubbleOutline, size: context.theme.d.iconSizeLarge),
                iconPosition: IconPosition.atRight,
                key: const Key('message'),
                title: AppLocale.current.message,
                onPressed: () {
                  Provider.of<ScaffoldModel>(
                    context,
                    listen: false,
                  ).setParams(index: Tabs.inbox.index);
                  context.go('${AppRoutes.inboxChats.path}/$channelId');
                },
                isFullWidth: true,
                buttonSize: ButtonSize.large,
              )
              : (!(sentInvitationId == null && receivedInvitationId == null))
              ? CommonButton.outlinedButton(
                context: context,
                key: const Key('viewinvitation'),
                title: AppLocale.current.view_invitation,
                buttonSize: ButtonSize.large,
                isFullWidth: true,
                onPressed: () {
                  bool isSentInvitation = sentInvitationId != null;
                  if (AppUtility.displayDesktopUI(context, isChatScreen: true)) {
                    Provider.of<ScaffoldModel>(
                      context,
                      listen: false,
                    ).setParams(index: Tabs.inbox.index);
                    isSentInvitation
                        ? context.replaceNamed(
                          AppRoutes.inviteSentWeb.name,
                          extra: sentInvitationId,
                        )
                        : context.replaceNamed(
                          AppRoutes.newInviteReceivedWeb.name,
                          extra: receivedInvitationId,
                        );
                  } else {
                    isSentInvitation
                        ? context.push('${AppRoutes.inboxInvitesSent.path}/$sentInvitationId')
                        : context.push(
                          '${AppRoutes.inboxInvitesReceived.path}/$receivedInvitationId',
                        );
                  }
                },
              )
              : CommonButton.primaryRoundedRectangle(
                context: context,
                key: const Key('viewProfile'),
                title: AppLocale.current.viewProfile,
                onPressed: onTap,
                isFullWidth: true,
                buttonSize: ButtonSize.xLarge,
              ),
          SizedBox(height: theme.d.paddingSmall),
        ],
      ),
    );
  }

  List<Widget> _createExpertiseChips() {
    List<Widget> rowChildren = [];
    for (String expertise in profileUtils.expertises.take(Limits.expertisesQuickViewMaxChips)) {
      rowChildren.addAll([
        ExpertiseChip(expertise: expertise, chipBackgroundColor: ChipBackgroundColor.tertiary),
      ]);
    }
    return rowChildren;
  }

  Widget _createExpertisesWidget(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          profileUtils.isMentor
              ? AppLocale.current.profileQuickCardMyExpertises
              : AppLocale.current.userCardEntrepreneurTitle,
          style: theme.textTheme.titleSmall?.copyWith(
            color: theme.colorScheme.surfaceBright,
            fontWeight: FontWeight.w600,
          ),
          softWrap: true,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(height: theme.d.paddingSmall),
        Wrap(
          spacing: theme.d.paddingXSmall,
          runSpacing: theme.d.paddingXSmall,
          children: _createExpertiseChips(),
        ),
      ],
    );
  }

  Widget _headerTextWidget(ThemeData theme, String header, String details, {int? maxLines = 3}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          header,
          style: theme.textTheme.titleSmall?.copyWith(
            color: theme.colorScheme.surfaceBright,
            fontWeight: FontWeight.w700,
          ),
        ),
        Text(
          details,
          softWrap: true,
          maxLines: maxLines,
          overflow: TextOverflow.ellipsis,
          style: theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.onSurface),
        ),
      ],
    );
  }
}
