import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/parts/micromentor_icons.dart';
import '../../../../services/extensions.dart';

class ReminderBanner extends StatefulWidget {
  final String titleText;
  final String subtitleText;
  final String ctaText;
  final int profileCompletionPercentage;

  const ReminderBanner({
    super.key,
    required this.titleText,
    required this.subtitleText,
    required this.ctaText,
    required this.profileCompletionPercentage,
  });

  @override
  State<ReminderBanner> createState() => _ReminderBannerState();
}

class _ReminderBannerState extends State<ReminderBanner> {
  bool showCard = true;

  @override
  Widget build(BuildContext context) {
    if (showCard) return const SizedBox();
    return Padding(
      padding: EdgeInsets.only(
        right: context.theme.d.paddingMedium,
        left: context.theme.d.paddingMedium,
        top: context.theme.d.paddingMedium,
      ),
      child: Card(
        elevation: context.theme.d.elevationLevel0,
        color: context.colorScheme.onInverseSurface,
        child: Padding(
          padding: EdgeInsets.fromLTRB(
            context.theme.d.paddingMedium,
            context.theme.d.paddingSmall,
            context.theme.d.paddingMedium,
            context.theme.d.paddingMedium,
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () {
                      setState(() {
                        showCard = false;
                      });
                    },
                    child: Icon(MicromentorIcons.close, color: context.colorScheme.outline),
                  ),
                ],
              ),
              Row(
                children: <Widget>[
                  _buildProgressCircleColumn(context),
                  SizedBox(width: context.theme.d.paddingLarge),
                  _buildTextColumn(context),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressCircleColumn(BuildContext context) {
    return Column(
      children: [
        Stack(
          children: [
            SizedBox(
              width: context.theme.d.profilePhotoRadiusSmall * 2,
              height: context.theme.d.profilePhotoRadiusSmall * 2,
              child: CircularProgressIndicator(value: widget.profileCompletionPercentage / 100),
            ),
            SizedBox(
              width: context.theme.d.profilePhotoRadiusSmall * 2,
              height: context.theme.d.profilePhotoRadiusSmall * 2,
              child: Center(
                child: Text(
                  AppLocale.current.homePercentageProfileCompletion(
                    widget.profileCompletionPercentage,
                  ),
                  textAlign: TextAlign.center,
                  style: context.textTheme.labelMedium?.copyWith(
                    color: context.colorScheme.primary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTextColumn(BuildContext context) {
    final GoRouter router = GoRouter.of(context);

    return Expanded(
      flex: 1,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            widget.titleText,
            style: context.textTheme.titleSmall,
            textAlign: TextAlign.start,
            softWrap: true,
          ),
          SizedBox(height: context.theme.d.paddingXxSmall),
          Padding(
            padding: EdgeInsets.only(bottom: context.theme.d.paddingMedium),
            child: Text(
              widget.subtitleText,
              style: context.textTheme.bodySmall?.copyWith(
                color: context.colorScheme.onSecondaryContainer,
              ),
              textAlign: TextAlign.start,
              softWrap: true,
            ),
          ),
          InkWell(
            onTap: () {
              router.push(AppRoutes.profile.path);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  widget.ctaText,
                  style: context.textTheme.labelLarge?.copyWith(
                    color: context.colorScheme.onSecondaryContainer,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(width: context.theme.d.paddingSmall),
                Icon(
                  MicromentorIcons.arrowForwardRounded,
                  color: context.colorScheme.onSecondaryContainer,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class MaybeReminderBanner extends StatelessWidget {
  final User myUser;

  const MaybeReminderBanner({super.key, required this.myUser});

  @override
  Widget build(BuildContext context) {
    DateTime? updatedAt = myUser.updatedAt?.toLocal();
    if (myUser.profileCompletionPercentage < 50) {
      return ReminderBanner(
        titleText: AppLocale.current.homeReminderBannerProfileCompleteTitle,
        subtitleText: AppLocale.current.homeReminderBannerProfileCompleteSubtitle,
        ctaText: AppLocale.current.homeReminderBannerProfileCompletePrompt,
        profileCompletionPercentage: myUser.profileCompletionPercentage,
      );
    } else if (updatedAt != null &&
        DateTime.now().difference(updatedAt.toLocal()).inDays > 30 * 6) {
      return ReminderBanner(
        titleText: AppLocale.current.homeReminderBannerProfileUpdateTitle,
        subtitleText: AppLocale.current.homeReminderBannerProfileUpdateSubtitle,
        ctaText: AppLocale.current.homeReminderBannerProfileUpdatePrompt,
        profileCompletionPercentage: myUser.profileCompletionPercentage,
      );
    } else {
      return const SizedBox();
    }
  }
}
