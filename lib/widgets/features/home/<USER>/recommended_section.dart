import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/services/graphql/providers/inbox_provider.dart';
import 'package:provider/provider.dart';

import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../models/models.dart';
import '../../../../services/extensions.dart';
import '../../../../utilities/utility.dart';
import '../../../widgets.dart';

class RecommendedSection extends StatefulWidget {
  final User user;

  const RecommendedSection({super.key, required this.user});

  @override
  State<RecommendedSection> createState() => _RecommendedSectionState();
}

class _RecommendedSectionState extends State<RecommendedSection> {
  late final bool _isEntrepreneur;
  late final ScaffoldModel _scaffoldModel;
  late bool isDesktop;
  late final ExploreCardFiltersModel filterModel;

  @override
  void initState() {
    super.initState();
    _isEntrepreneur = widget.user.seeksHelp == true;

    _scaffoldModel = Provider.of<ScaffoldModel>(context, listen: false);

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      ExploreCardFiltersModel filterModel = Provider.of<ExploreCardFiltersModel>(
        context,
        listen: false,
      );

      filterModel.isSourceHome = true;
      (filterModel.searchId ?? '').isEmpty
          ? filterModel.initializeFilters(context)
          : filterModel.clearSearchResult(clearSearchId: false);
      await filterModel.fetchSearchResults(context);
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    isDesktop = AppUtility.displayDesktopUI(context);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ExploreCardFiltersModel>(
      builder: (context, filterModel, child) {
        if (filterModel.isLoading) {
          return const Loading();
        }
        return Column(
          children: [
            RecommendedUsersHeading(
              title:
                  _isEntrepreneur
                      ? AppLocale.current.homeRecommendedEntrepreneurTitle
                      : AppLocale.current.homeRecommendedMentorTitle,
              subtitle: AppLocale.current.homeRecommendedSubtitle,
              scaffoldModel: _scaffoldModel,
            ),
            RecommendedUsersScroll(
              isEntrepreneur: _isEntrepreneur,
              recommendedUsers: filterModel.searchResults,
              theme: context.theme,
              isDesktop: isDesktop,
            ),
          ],
        );
      },
    );
  }
}

class RecommendedUsersHeading extends StatelessWidget {
  final String title;
  final String subtitle;
  final VoidCallback? seeAllOnPressed;
  final ScaffoldModel scaffoldModel;

  const RecommendedUsersHeading({
    super.key,
    required this.title,
    required this.subtitle,
    required this.scaffoldModel,
    this.seeAllOnPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: context.theme.d.paddingMedium,
        vertical: context.theme.d.paddingSmall,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Flexible(
                child: Text(
                  title,
                  style: context.theme.textTheme.titleLarge?.copyWith(
                    color: context.theme.colorScheme.onSecondaryContainer,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              SizedBox(width: context.theme.d.paddingSmall),
              CommonButton.textButton(
                context: context,
                title: AppLocale.current.listSeeMore,
                onPressed: () {
                  scaffoldModel.setParams(index: Tabs.explore.index);
                  context.go(AppRoutes.explore.path);
                },
                textStyle: context.theme.textTheme.labelLarge?.copyWith(
                  color: context.theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          Text(subtitle, textAlign: TextAlign.left),
        ],
      ),
    );
  }
}

class RecommendedUsersScroll extends StatelessWidget {
  final List<UserSearchResult> recommendedUsers;
  final bool isEntrepreneur;
  final ThemeData theme;
  final bool isDesktop;

  const RecommendedUsersScroll({
    super.key,
    required this.isEntrepreneur,
    required this.recommendedUsers,
    required this.theme,
    required this.isDesktop,
  });

  List<Widget> _createUserCards(GoRouter router, BuildContext context) {
    int noOfCards = recommendedUsers.length;
    if (isDesktop) {
      double cardWidth = theme.d.webUserCardSize.width + (theme.d.paddingMedium * 2);

      double extraPadding = context.theme.d.paddingLarge;
      noOfCards = (context.mediaQuerySize.width - extraPadding) ~/ cardWidth;
    }

    return recommendedUsers.take(noOfCards).map((user) {
      final profileUtil = ProfileUtility.fromSearch(user);
      final inboxProvider = Provider.of<InboxProvider>(context, listen: false);

      return FutureBuilder(
        future: inboxProvider.getOneOnOneChannelIdForUser(user.id),
        builder: (context, snap) {
          final channelId = snap.hasData ? snap.data?.$1 : null;
          String? sentInvitationId;
          String? receivedInvitationId;
          if (channelId == null) {
            sentInvitationId = inboxProvider.getSentInvitationIdForUser(user.id);
            if (sentInvitationId == null) {
              receivedInvitationId = inboxProvider.getReceivedInvitationIdForUser(user.id);
            }
          }
          return RecommendedUserCard(
            profileUtils: profileUtil,
            cardWidth: theme.d.mobileUserCardWidth,
            userId: user.id,
            channelId: channelId,
            sentInvitationId: sentInvitationId,
            receivedInvitationId: receivedInvitationId,
            onTap: () {
              router.pushNamed(
                AppRoutes.profileId.name,
                pathParameters: {RouteParams.userId: user.id},
              );
            },
          );
        },
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingSmall),
          child:
              isDesktop
                  ? _usersList(context)
                  : Center(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: _usersList(context),
                    ),
                  ),
        ),
      ],
    );
  }

  _usersList(BuildContext context) {
    final GoRouter router = GoRouter.of(context);

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: _createUserCards(router, context),
        ),
      ),
    );
  }
}
