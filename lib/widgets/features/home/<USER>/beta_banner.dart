import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/models/locale_model.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../constants/parts/micromentor_icons.dart';

class BetaBanner extends StatefulWidget {
  const BetaBanner({super.key});

  @override
  State<BetaBanner> createState() => _BetaBannerState();
}

class _BetaBannerState extends State<BetaBanner> {
  late bool showBanner;
  late bool isDesktop;
  late final LocaleModel _localeModel;

  @override
  void initState() {
    showBanner = true;
    _localeModel = Provider.of<LocaleModel>(context, listen: false);

    super.initState();
  }

  @override
  void didChangeDependencies() {
    isDesktop = AppUtility.displayDesktopUI(context);
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return !showBanner
        ? const SizedBox.shrink()
        : Stack(
          alignment: AlignmentDirectional.topEnd,
          children: [
            Container(
              margin: EdgeInsets.symmetric(
                horizontal: context.theme.d.paddingMedium,
                vertical: context.theme.d.paddingMedium,
              ),
              padding: EdgeInsets.symmetric(
                horizontal: context.theme.d.paddingMedium,
                vertical: context.theme.d.paddingMedium,
              ),
              decoration: BoxDecoration(
                color: context.theme.colorScheme.primary,
                borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusSmall),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          AppLocale.current.homeBetaBannerMsgPart1,
                          style: context.textTheme.titleMedium?.copyWith(
                            color: context.colorScheme.onPrimary,
                            fontWeight: FontWeight.w700,
                            height: context.theme.d.lineHeightX,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: context.theme.d.paddingXSmall),
                  RichText(
                    text: TextSpan(
                      text: '${AppLocale.current.homeBetaBannerMsgPart2} ',
                      style: context.textTheme.bodyMedium?.copyWith(
                        color: context.colorScheme.onPrimary,
                        fontWeight: FontWeight.w400,
                        height: context.theme.d.lineHeightX,
                      ),
                      children: <TextSpan>[
                        TextSpan(
                          text: AppLocale.current.homeBetaBannerMsgPart3,
                          style: context.textTheme.bodyMedium?.copyWith(
                            color: context.colorScheme.onPrimary,
                            fontWeight: FontWeight.w700,
                            decoration: TextDecoration.underline,
                            decorationColor: context.colorScheme.onPrimary,
                          ),
                          recognizer:
                              TapGestureRecognizer()
                                ..onTap = () async {
                                  final uri = Uri.parse(
                                    _localeModel.isArabic(text: null)
                                        ? Identifiers.arabicContactUrl
                                        : Identifiers.contactUrl,
                                  );
                                  if (await canLaunchUrl(uri)) {
                                    await launchUrl(uri);
                                  }
                                },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.all(context.theme.d.paddingXSmall * 2),
              child: IconButton(
                onPressed:
                    () => setState(() {
                      showBanner = false;
                    }),
                icon: Icon(
                  MicromentorIcons.close,
                  size: context.theme.d.iconSizeMedium,
                  color: context.colorScheme.onPrimary,
                ),
              ),
            ),
          ],
        );
  }
}
