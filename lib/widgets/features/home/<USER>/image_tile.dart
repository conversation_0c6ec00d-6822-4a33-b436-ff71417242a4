import 'package:flutter/material.dart';

import '../../../../services/extensions.dart';

class ImageTile extends StatelessWidget {
  final ImageProvider image;
  final String title;
  final String subtitle;
  final bool isCircle;
  final ThemeData theme;

  const ImageTile({
    super.key,
    required this.image,
    required this.title,
    required this.subtitle,
    required this.isCircle,
    required this.theme,
  });

  Widget _makeImage() {
    if (isCircle) {
      return Padding(
        padding: EdgeInsets.all(theme.d.paddingSmall),
        child: CircleAvatar(
          radius: theme.d.profilePhotoRadiusSmall,
          child: CircleAvatar(radius: theme.d.profilePhotoRadiusSmall, backgroundImage: image),
        ),
      );
    } else {
      return Padding(
        padding: EdgeInsets.only(top: theme.d.paddingSmall, bottom: theme.d.paddingMedium),
        child: Container(
          width: theme.d.imageGalleryImageSize.width,
          height: theme.d.imageGalleryImageSize.height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(theme.d.roundedRectRadiusMedium),
            image: DecorationImage(image: image, fit: BoxFit.fill, alignment: Alignment.topCenter),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(theme.d.paddingXxSmall),
      child: Card(
        child: Padding(
          padding: EdgeInsets.symmetric(
            vertical: theme.d.paddingSmall,
            horizontal: theme.d.paddingMedium,
          ),
          child: Align(
            alignment: Alignment.center,
            child: SizedBox(
              width: theme.d.imageGalleryTileSize.width,
              height: theme.d.imageGalleryTileSize.height,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  _makeImage(),
                  const Spacer(),
                  Text(
                    title,
                    style: context.theme.textTheme.titleSmall?.copyWith(
                      color: context.theme.colorScheme.secondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const Spacer(),
                  Padding(
                    padding: EdgeInsets.only(
                      top: theme.d.paddingXxSmall,
                      bottom: theme.d.paddingSmall,
                    ),
                    child: Text(
                      subtitle,
                      style: context.theme.textTheme.bodySmall?.copyWith(
                        color: context.theme.colorScheme.tertiary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
