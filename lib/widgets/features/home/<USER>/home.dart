import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/navigation_mixin.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../../../generatedAppLocale/l10n.dart';
import '../../../../models/locale_model.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../widgets.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with NavigationMixin<HomeScreen> {
  late final UserProvider _userProvider;
  late final LocaleModel _localeModel;
  late bool isDesktop;
  Future<LoadObjectResult>? _userFuture;

  @override
  void initState() {
    super.initState();
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _localeModel = Provider.of<LocaleModel>(context, listen: false);
    _userFuture = _userProvider.loadUser();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    isDesktop = AppUtility.displayDesktopUI(context);
  }

  @override
  Widget build(BuildContext context) {
    final user = _userProvider.myUser;
    return user != null
        ? _buildContent(user)
        : FutureBuilder<LoadObjectResult>(
          key: const ValueKey('future'),
          future: _userFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const LoadingScreen(key: ValueKey('loading'));
            }
            if (!snapshot.hasData || snapshot.data?.object == null) {
              return const Center(key: ValueKey('error'), child: Text('Failed to load user.'));
            }
            final loadedUser = snapshot.data!.object as User;
            return _buildContent(loadedUser);
          },
        );
  }

  Widget _buildContent(User user) {
    buildPageRouteScaffold((scaffold) {
      scaffold.setParams();
    });
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        centerTitle: false,
        actions: isDesktop ? null : const [AccountActionMenu()],
        toolbarHeight: context.theme.d.homeToolbarHeight,
        shape: AppUtility.topDivider(context),
        title: _greetingWidget(user),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: isDesktop ? context.theme.d.zero : context.theme.d.paddingSmall,
        ),
        child: SafeArea(
          child: ListView(
            children: [
              if (!isDesktop) SizedBox(height: context.theme.d.paddingMedium),
              const BetaBanner(),
              SizedBox(height: context.theme.d.paddingSmall),
              MaybeReminderBanner(myUser: user),
              InvitationSection(myUser: user),
              RecommendedSection(user: user),
              // ResourcesSection(userId: user.id),
            ],
          ),
        ),
      ),
    );
  }

  String _getGreeting(String? fullName) {
    int hour = DateTime.now().hour;
    String timeOfDayGreeting;
    if (hour >= 5 && hour < 12) {
      timeOfDayGreeting = AppLocale.current.homeGreetingMorning;
    } else if (hour >= 12 && hour < 18) {
      timeOfDayGreeting = AppLocale.current.homeGreetingAfternoon;
    } else {
      timeOfDayGreeting = AppLocale.current.homeGreetingEvening;
    }
    return fullName != null
        ? isDesktop
            ? '$timeOfDayGreeting, $fullName'
            : '$timeOfDayGreeting,\n$fullName'
        : timeOfDayGreeting;
  }

  Widget _greetingWidget(User user) {
    Widget greetingTitle = Text(
      _getGreeting(AppUtility.getUserFullName(user.firstName, user.lastName)),
      style: context.theme.appBarTheme.titleTextStyle?.copyWith(
        fontSize: context.theme.d.fontSizeXLarge,
        height: AppUtility.isMobileBrowser() && _localeModel.isArabic() ? 1.1 : null,
      ),
      overflow: TextOverflow.ellipsis,
    );
    if (isDesktop || !StaticAppFeatures.vts) {
      return greetingTitle;
    }
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          onTap: () {
            context.push(AppRoutes.profile.path);
          },
          child: ProfileImageWidget(
            avatarUrl: user.avatarUrl,
            size: context.theme.d.profilePhotoRadiusLarge,
          ),
        ),
        SizedBox(width: context.theme.d.paddingMedium),
        Expanded(child: greetingTitle),
      ],
    );
  }
}
