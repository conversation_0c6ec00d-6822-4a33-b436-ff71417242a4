import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/models/locale_model.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../services/extensions.dart';
import '../../../../services/graphql/providers/user_provider.dart';
import '../../../shared/common_dialog_template.dart';

class ProfileHeader extends StatefulWidget {
  final String? avatarUrl;
  final String profileMessage;

  const ProfileHeader({super.key, this.avatarUrl, required this.profileMessage});

  @override
  State<ProfileHeader> createState() => _ProfileHeaderState();
}

class _ProfileHeaderState extends State<ProfileHeader> {
  late final UserProvider _userProvider;
  late final LocaleModel _localeModel;

  @override
  void initState() {
    super.initState();

    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _localeModel = Provider.of<LocaleModel>(context, listen: false);
  }

  @override
  Widget build(BuildContext context) {
    var backgroundImage =
        widget.avatarUrl != null
            ? NetworkImage(widget.avatarUrl!) as ImageProvider<Object>
            : const AssetImage(Assets.blankAvatar);

    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(
        context.theme.d.paddingMedium,
        context.theme.d.paddingLarge,
        context.theme.d.paddingSmall,
        0,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusSmall),
            child: Image(
              image: backgroundImage,
              width: context.theme.d.iconSizeXxLarge,
              height: context.theme.d.iconSizeXxLarge,
            ),
          ),
          SizedBox(width: context.theme.d.paddingMedium),
          Expanded(
            child: Text(
              widget.profileMessage,
              textAlign: TextAlign.start,
              softWrap: true,
              style: context.theme.textTheme.titleLarge?.copyWith(
                color: context.theme.colorScheme.onSurface,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          SizedBox(width: context.theme.d.paddingMedium),
          PopupMenuButton(
            itemBuilder:
                (BuildContext context) => [
                  PopupMenuItem(
                    child: Text(AppLocale.current.vacationModeTitle),
                    onTap: () => _showVacationDialog(context),
                  ),
                  PopupMenuItem(
                    child: Text(AppLocale.current.accountHeader),
                    onTap: () => context.push(AppRoutes.accountSettings.path),
                  ),
                  if (_userProvider.isImpersonating)
                    PopupMenuItem(
                      child: Text(AppLocale.current.actionClearImpersonation),
                      onTap: () async {
                        await _userProvider.clearImpersonation();
                        if (context.mounted) {
                          context.push(AppRoutes.home.path);
                        }
                      },
                    ),
                  PopupMenuItem(
                    child: Text(AppLocale.current.helpCenterTitle),
                    onTap: () async {
                      await AppUtility.openLink(
                        _localeModel.isArabic()
                            ? Identifiers.arabicHelpCenterUrl
                            : Identifiers.helpCenterUrl,
                      );
                    },
                  ),
                  PopupMenuItem(
                    child: Text(AppLocale.current.actionLogOut),
                    onTap: () => _showLogoutDialog(context),
                  ),
                  if (_userProvider.isAdmin)
                    PopupMenuItem(
                      child: Text(AppLocale.current.runAdminTaskTitle),
                      onTap: () => context.push(AppRoutes.admin.path),
                    ),
                ],
          ),
        ],
      ),
    );
  }

  _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) {
        return DialogTemplate(
          title: AppLocale.current.logOutDialogHeading,
          description: AppLocale.current.logOutSubtitle,
          onAction: () async {
            await Provider.of<UserProvider>(context, listen: false).signOutUser(context);
            if (context.mounted) {
              context.pop();
            }
          },
        );
      },
    );
  }
}

_showVacationDialog(BuildContext context) {
  var userProvider = Provider.of<UserProvider>(context, listen: false);
  bool isSwitched = userProvider.myUser?.isOnVacation ?? false;

  showDialog(
    context: context,
    builder: (dialogContext) {
      return StatefulBuilder(
        builder: (BuildContext context, StateSetter setState) {
          return DialogTemplate.withCustomContent(
            title: AppLocale.current.vacationModeTitle,
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        isSwitched
                            ? AppLocale.current.vacationModeOn
                            : AppLocale.current.vacationModeOff,
                        textAlign: TextAlign.start,
                        style: context.textTheme.titleMedium,
                      ),
                    ),
                    Switch(
                      value: isSwitched,
                      onChanged: (value) {
                        setState(() {
                          isSwitched = value;
                        });
                      },
                    ),
                  ],
                ),
                SizedBox(height: context.theme.d.paddingSmall),
                Text(
                  isSwitched
                      ? AppLocale.current.vacationOnModeSubTitle
                      : AppLocale.current.vacationOffModeSubTitle,
                  textAlign: TextAlign.start,
                ),
              ],
            ),
            onAction: () {
              userProvider
                  .updateUser(
                    input: Input$UserInput(id: userProvider.myUser?.id, isOnVacation: isSwitched),
                  )
                  .then((value) {
                    if (context.mounted) {
                      Navigator.pop(context);
                    }
                  });
            },
          );
        },
      );
    },
  );
}
