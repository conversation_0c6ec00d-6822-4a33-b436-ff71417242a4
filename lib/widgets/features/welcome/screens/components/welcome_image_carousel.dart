import 'package:carousel_slider/carousel_slider.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';

import '../../../../../constants/constants.dart';

class WelcomeImageCarousel extends StatefulWidget {
  const WelcomeImageCarousel({super.key});

  @override
  State<WelcomeImageCarousel> createState() => _WelcomeImageCarouselState();
}

class _WelcomeImageCarouselState extends State<WelcomeImageCarousel> {
  int _dotIndicatorIndex = 0;
  CarouselSliderController carouselController = CarouselSliderController();

  late final List<String> images;

  late List<String> messages;
  late List<String> accessibilityMessages;
  late bool isDesktop;

  @override
  void initState() {
    messages = [];
    images = [
      Assets.startScreenStockImage1,
      Assets.startScreenStockImage2,
      Assets.startScreenStockImage3,
    ];
    super.initState();
  }

  @override
  void didChangeDependencies() {
    isDesktop = AppUtility.displayDesktopUI(context);
    messages = [
      AppLocale.current.welcomePreviewSlide1,
      AppLocale.current.welcomePreviewSlide2,
      AppLocale.current.welcomePreviewSlide3,
    ];
    accessibilityMessages = [
      AppLocale.current.accessibilityWelcomeSlide1,
      AppLocale.current.welcomePreviewSlide2,
      AppLocale.current.welcomePreviewSlide3,
    ];

    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return _content();
  }

  Widget _content() {
    return LayoutBuilder(
      builder: (context, boxConstraints) {
        double aspectRatio = 1;
        bool shouldShowImage = true;

        if (isDesktop) {
          double widthToHeight = boxConstraints.maxWidth / boxConstraints.maxHeight;

          aspectRatio =
              boxConstraints.maxHeight < context.theme.d.vacationDialogContentWidth
                  ? (widthToHeight * widthToHeight)
                  : context.theme.d.carousalImageAspectRatio;

          shouldShowImage = aspectRatio < context.theme.d.imageAspectRatioThreshold;
        }

        return GestureDetector(
          onTap: () {
            carouselController.nextPage(
              duration: const Duration(milliseconds: 300),
              curve: Curves.linear,
            );
          },
          child: Column(
            mainAxisSize: isDesktop ? MainAxisSize.min : MainAxisSize.max,
            children: [
              isDesktop
                  ? Flexible(
                    child: _carouselSlider(
                      aspectRatio: aspectRatio.ceilToDouble(),
                      shouldShowImage: shouldShowImage,
                    ),
                  )
                  : Expanded(child: _carouselSlider()),
              Semantics(
                label: AppLocale.current.accessibilityWelcomePageIndicator(
                  _dotIndicatorIndex + 1,
                  images.length,
                ),
                child: DotsIndicator(
                  dotsCount: images.length,
                  position: _dotIndicatorIndex.toDouble(),
                  decorator: DotsDecorator(
                    color: context.theme.colorScheme.inversePrimary,
                    activeColor: context.theme.colorScheme.tertiary,
                    activeSize: context.theme.d.welcomeCarouselIndicatorSize,
                  ),
                  onTap: (position) => setState(() => _dotIndicatorIndex = position),
                ),
              ),
              SizedBox(height: context.theme.d.paddingMedium),
            ],
          ),
        );
      },
    );
  }

  Widget _carouselSlider({double? aspectRatio, bool shouldShowImage = true}) {
    return CarouselSlider(
      carouselController: carouselController,
      items:
          images.map((img) {
            return MergeSemantics(
              child: Column(
                children: [
                  (aspectRatio == null)
                      ? Semantics(
                        label: accessibilityMessages[_dotIndicatorIndex],
                        child: SizedBox(
                          height: context.theme.d.boxSizeXXLarge,
                          child: Image.asset(images[_dotIndicatorIndex]),
                        ),
                      )
                      : (shouldShowImage)
                      ? Semantics(
                        label: accessibilityMessages[_dotIndicatorIndex],
                        child: AspectRatio(
                          aspectRatio: aspectRatio,
                          child: Image.asset(images[_dotIndicatorIndex]),
                        ),
                      )
                      : const SizedBox(),
                  SizedBox(height: context.theme.d.paddingXLarge),
                  Semantics(
                    excludeSemantics: true,
                    child: Text(
                      messages[_dotIndicatorIndex],
                      textAlign: TextAlign.center,
                      style: context.theme.textTheme.bodyLarge?.copyWith(
                        color: context.theme.colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
      options: CarouselOptions(
        height: context.theme.d.welcomePreviewSlide,
        autoPlay: false,
        viewportFraction: 1,
        onPageChanged: (i, _) {
          setState(() => _dotIndicatorIndex = i);
        },
      ),
    );
  }
}
