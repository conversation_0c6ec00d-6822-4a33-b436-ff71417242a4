import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/debug_logger.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../constants/constants.dart';
import '../../../../models/models.dart';
import '../../../widgets.dart';

class DataUseConsentModal extends StatefulWidget {
  const DataUseConsentModal({super.key});

  @override
  State<DataUseConsentModal> createState() => _DataUseConsentModalState();
}

class _DataUseConsentModalState extends State<DataUseConsentModal> {
  late final LocalDataModel _localData;
  late final LocaleModel _localeModel;
  late final ScrollController _scrollController;
  final _formKey = GlobalKey<FormState>();
  final _declinationStatementKey = GlobalKey();
  bool _calledBuild = false;
  bool _scrollable = true;
  bool? dataUseConsent;
  late bool isDesktop;
  // MC requires us to enable users to explicitly express their intent to decline consent

  @override
  void initState() {
    super.initState();
    _localData = Provider.of<LocalDataModel>(context, listen: false);
    _localeModel = Provider.of<LocaleModel>(context, listen: false);
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    isDesktop = AppUtility.displayDesktopUI(context);

    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
  }

  void onChangedDataUseConsent(bool? value) {
    setState(() {
      dataUseConsent = value;
    });
  }

  bool _canGoNext() {
    if (_formKey.currentState != null && _formKey.currentState?.validate() != true) {
      return false;
    }
    //user can go next only if consent is given
    return dataUseConsent == true;
  }

  @override
  Widget build(BuildContext context) {
    final GoRouter router = GoRouter.of(context);

    if (!_calledBuild) {
      // We want the scroll bar to always be visible in this view if there is hidden content
      // * without _calledBuild, this will loop.
      _calledBuild = true;
      SchedulerBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _scrollable = _scrollController.position.maxScrollExtent > 0;
        });
      });
    }

    return AppOnboardingTemplate(
      showBackButton: true,
      onBack: () => router.pop(false),
      formKey: _formKey,
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _wrapper(
            child: Scrollbar(
              thumbVisibility: _scrollable,
              controller: _scrollController,
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingLarge),
                controller: _scrollController,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      AppLocale.current.signupDataUseConsentTitle,
                      softWrap: true,
                      style: context.theme.textTheme.headlineLarge?.copyWith(
                        color: context.theme.colorScheme.secondary,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: context.theme.d.paddingLarge),
                      child: Column(
                        children: [
                          MarkdownBody(
                            data: AppLocale.current.signupDataUseConsentBody(
                              getLocalizedUrl(
                                urlType: LegalDocumentType.privacyPolicy,
                                localeModel: _localeModel,
                              ),
                            ),
                            styleSheet: MarkdownStyleSheet.fromTheme(context.theme).copyWith(
                              p: context.textTheme.bodyLarge?.copyWith(
                                color: context.colorScheme.onSurface,
                              ),
                              a: context.textTheme.bodyLarge?.copyWith(
                                color: context.colorScheme.tertiary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            onTapLink: (_, url, __) => launchUrl(Uri.parse(url!)),
                          ),
                          SizedBox(height: context.theme.d.paddingMedium),
                          Text(
                            AppLocale.current.signupDataUseConsentWithdrawlClause,
                            style: context.textTheme.bodyLarge?.copyWith(
                              color: context.colorScheme.onSurface,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                    RadioTileWidget<bool>(
                      title: AppLocale.current.signupDataUseConsentAcceptInputLabel,
                      value: true,
                      groupValue: dataUseConsent,
                      onChanged: (value) => onChangedDataUseConsent(value),
                    ),
                    RadioTileWidget<bool>(
                      title: AppLocale.current.signupDataUseConsentDeclineInputLabel,
                      value: false,
                      groupValue: dataUseConsent,
                      onChanged: (value) => onChangedDataUseConsent(value),
                    ),
                    Visibility(
                      visible: dataUseConsent == false,
                      child: Text(
                        key: _declinationStatementKey,
                        AppLocale.current.signupDataUseConsentDeclinationStatement,
                        style: context.textTheme.bodyLarge?.copyWith(
                          color: context.colorScheme.error,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              color: Colors.transparent,
              child: Container(
                margin: EdgeInsets.symmetric(
                  vertical: context.theme.d.paddingMedium,
                  horizontal: context.theme.d.paddingLarge,
                ),
                color: context.theme.scaffoldBackgroundColor,
                child: SafeArea(
                  child: CommonButton.primaryRoundedRectangle(
                    isFullWidth: true,
                    context: context,
                    title: AppLocale.current.actionNext,
                    buttonSize: ButtonSize.large,
                    onPressed:
                        !_canGoNext()
                            ? null
                            : () {
                              DebugLogger.info(
                                'signupDataUseConsent.onGoNext: Anonymous user has consented to data practices.',
                              );
                              _localData.dataUseConsent = true;
                              router.pop(_localData.dataUseConsent);
                            },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _wrapper({required Widget child}) {
    return isDesktop ? Flexible(child: child) : Expanded(child: child);
  }
}
