import 'dart:io';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:mm_flutter_app/widgets/shared/language_dropdown.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../constants/constants.dart';
import '../../../shared/app_onboarding_template.dart';
import '../../../shared/common_button.dart';
import '../welcome.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> with WidgetsBindingObserver {
  late bool userDeniedTrackingConsent = true;
  final CarouselSliderController carouselController = CarouselSliderController();

  final List<String> images = [
    Assets.startScreenStockImage1,
    Assets.startScreenStockImage2,
    Assets.startScreenStockImage3,
  ];

  bool isDataFetched = false;
  bool checkingPermission = false;

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
  }

  @override
  void didChangeDependencies() async {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (!kIsWeb && Platform.isIOS && state == AppLifecycleState.resumed && !checkingPermission) {
      checkingPermission = true;
      checkAppTrackingTransparencyPermission().then((_) => checkingPermission = false);
    }
  }

  Future<void> checkAppTrackingTransparencyPermission() async {
    final status = await Permission.appTrackingTransparency.status;
    // If permanently denied they'll need to go to their device settings
    if (status == PermissionStatus.denied) {
      Permission.appTrackingTransparency.request().then((value) {
        setState(() {
          userDeniedTrackingConsent = value.isDenied || value.isPermanentlyDenied;
          setState(() {});
        });
      });
    } else if (status == PermissionStatus.granted) {
      await FacebookAuth.i.autoLogAppEventsEnabled(true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppOnboardingTemplate(
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _header(),
              SizedBox(height: context.theme.d.paddingMedium),
              _carousel(),
              _foooterButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _header() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        (AppUtility.displayDesktopUI(context))
            ? const Spacer()
            : Semantics(
              label: AppLocale.current.accessibilityMMLogo,
              child: SizedBox(
                height: context.theme.d.boxSizeMedium,
                child: const Image(image: AssetImage(Assets.brandLogoWithName)),
              ),
            ),
        const LanguageDropdown(),
      ],
    );
  }

  Widget _carousel() {
    return const Flexible(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [Flexible(child: WelcomeImageCarousel())],
      ),
    );
  }

  _foooterButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        CommonButton.primaryRoundedRectangle(
          key: const Key('getStartedButton'),
          context: context,
          title: AppLocale.current.welcomeButtonLabel,
          buttonSize: ButtonSize.large,
          isFullWidth: true,
          onPressed: () {
            context.push(AppRoutes.signup.path);
          },
        ),
        SizedBox(height: context.theme.d.paddingMedium),
        CommonButton.outlinedButton(
          key: const Key('loginButton'),
          context: context,
          title: AppLocale.current.login,
          buttonSize: ButtonSize.large,
          isFullWidth: true,
          onPressed: () => context.push(AppRoutes.signin.path),
        ),
        SizedBox(height: context.theme.d.paddingMedium),
      ],
    );
  }
}
