import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';

import '../../../../__generated/schema/schema.graphql.dart';
import '../../../../constants/constants.dart';
import '../../../../generatedAppLocale/l10n.dart';
import '../../../../models/models.dart';
import '../../../../services/firebase/analytic_service.dart';
import '../../../../services/graphql/providers/providers.dart';
import '../../../../utilities/errors/error_handler.dart';
import '../../../../utilities/loading/loading_provider.dart';
import '../../../widgets.dart';

class SignInScreen extends StatefulWidget {
  final String nextRouteName;

  const SignInScreen({super.key, required this.nextRouteName});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  final _focusNode = FocusNode();
  final _formKey = GlobalKey<FormState>();
  late final InboxProvider _inboxProvider;
  late final UserProvider _userProvider;
  late final OnboardingModel _onboardingModel;

  late final TextEditingController emailController;
  late final TextEditingController passwordController;

  // Temp flag for disabling the Facebook button until we can support Limited Login via `flutter_facebook_auth`
  late bool userDeniedTrackingConsent = true;

  @override
  void initState() {
    super.initState();
    _inboxProvider = Provider.of<InboxProvider>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
    _onboardingModel = Provider.of<OnboardingModel>(context, listen: false);
    emailController = TextEditingController();
    passwordController = TextEditingController();

    // Check if the user has permitted tracking
    if (!kIsWeb && Platform.isIOS) {
      Permission.appTrackingTransparency.status.then((value) {
        setState(() {
          userDeniedTrackingConsent = value.isDenied || value.isPermanentlyDenied;
          setState(() {});
        });
      });
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final onboardingModel = Provider.of<OnboardingModel>(context, listen: false);

      if ((onboardingModel.belongsToEBRDGroup)) {
        final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
        themeProvider.setEBRDColorScheme();
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppOnboardingTemplate(
      body: SafeArea(
        child: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(_focusNode),
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.all(context.theme.d.paddingMedium),
              child: Form(
                key: _formKey,
                child: AutofillGroup(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: Text(
                                  AppLocale.current.signinTitle,
                                  style: context.theme.textTheme.headlineLarge?.copyWith(
                                    color: context.theme.colorScheme.secondary,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              ),
                              SizedBox(width: context.theme.d.paddingMedium),
                              const LanguageDropdown(),
                            ],
                          ),
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  AppLocale.current.signinSubtitle,
                                  style: context.theme.textTheme.bodyLarge?.copyWith(
                                    color: context.theme.colorScheme.onSurface,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      SizedBox(height: context.theme.d.paddingLarge),
                      TextFormFieldWidget(
                        key: const Key('username'),
                        label: AppLocale.current.signinEmailInputLabel,
                        textController: emailController,
                        autofillHints: const [AutofillHints.username],
                        keyboardType: TextInputType.emailAddress,
                        contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
                        onChanged:
                            (_) => setState(() {
                              _formKey.currentState!.validate();
                            }),
                        focusedBorderColor: context.theme.colorScheme.primary,
                        validator: context.theme.validator.validateEmail,
                        onFieldSubmitted: _isFormValid() ? (_) => _onSubmit() : null,
                      ),
                      SizedBox(height: context.theme.d.paddingMedium),
                      TextFormFieldWidget(
                        key: const Key('password'),
                        isPassword: true,
                        label: AppLocale.current.signinPasswordInputLabel,
                        textController: passwordController,
                        autofillHints: const [AutofillHints.password],
                        focusedBorderColor: context.theme.colorScheme.primary,
                        contentPadding: EdgeInsets.all(context.theme.d.paddingMedium),
                        onChanged:
                            (_) => setState(() {
                              _formKey.currentState!.validate();
                            }),
                        validator: context.theme.validator.validatePassword,
                        onFieldSubmitted: _isFormValid() ? (_) => _onSubmit() : null,
                      ),
                      SizedBox(height: context.theme.d.paddingMedium),
                      CommonButton.primaryRoundedRectangle(
                        context: context,
                        isFullWidth: true,
                        key: const Key('loginButton'),
                        title: AppLocale.current.login,
                        onPressed: _isFormValid() ? _onSubmit : null,
                        buttonSize: ButtonSize.large,
                      ),
                      SizedBox(height: context.theme.d.paddingSmall),
                      CommonButton.textButton(
                        context: context,
                        title: AppLocale.current.signinForgotPasswordPrompt,
                        onPressed: _openResetPasswordScreen,
                        textStyle: context.theme.textTheme.titleMedium?.copyWith(
                          color: context.theme.colorScheme.primary,
                        ),
                      ),
                      SizedBox(height: context.theme.d.paddingMedium),
                      if (StaticAppFeatures.socialLogins)
                        Padding(
                          padding: EdgeInsets.only(
                            left: context.theme.d.paddingXLarge,
                            right: context.theme.d.paddingXLarge,
                            bottom: context.theme.d.paddingMedium,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              const Expanded(child: Divider()),
                              SizedBox(width: context.theme.d.paddingMedium),
                              Text(
                                AppLocale.current.or,
                                style: context.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              SizedBox(width: context.theme.d.paddingMedium),
                              const Expanded(child: Divider()),
                            ],
                          ),
                        ),
                      SizedBox(height: context.theme.d.paddingMedium),
                      if (StaticAppFeatures.socialLogins)
                        Center(
                          child: SignInWithButtons(
                            showLabel: false,
                            divider: false,
                            showOwnIdentityProvider: false,
                            showDisabledTrackingHelpText:
                                !kIsWeb && Platform.isIOS && userDeniedTrackingConsent,
                            onSelectIdentityProvider: (provider) async {
                              Loader.show(context);
                              ScaffoldMessenger.of(context).clearSnackBars();
                              final isSuccess = await _userProvider.startSignIn(provider, context);
                              if (!isSuccess) {
                                if (context.mounted) {
                                  Provider.of<LocalDataModel>(context, listen: false).clear();
                                  //TODO: - need to pass social signin exception/error here
                                  AppErrorHandler(
                                    context: context,
                                    exception: null,
                                    errorCode: Enum$ErrorCode.failedToSignin,
                                  );
                                }
                              }
                              if (context.mounted) Loader.hide(context);
                              AnalyticService.userLogIn(provider.name);
                            },
                          ),
                        ),
                      if (StaticAppFeatures.socialLogins)
                        SizedBox(height: context.theme.d.paddingLarge),
                      Text(
                        AppLocale.current.signinDontHaveAccountPrompt,
                        style: context.theme.textTheme.titleMedium?.copyWith(
                          color: context.theme.colorScheme.onSurface,
                        ),
                      ),
                      CommonButton.textButton(
                        context: context,
                        title: AppLocale.current.signupText,
                        onPressed: _openSignUpScreen,
                        textStyle: context.theme.textTheme.titleMedium?.copyWith(
                          color: context.theme.colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _openResetPasswordScreen() {
    context.push(AppRoutes.resetpassword.path, extra: emailController.text);
  }

  void _openSignUpScreen() {
    ScaffoldMessenger.of(context).clearSnackBars();
    context.pushReplacement(AppRoutes.signup.path);
  }

  bool _isFormValid() =>
      emailController.text.isNotEmpty &&
      passwordController.text.isNotEmpty &&
      _formKey.currentState != null &&
      _formKey.currentState?.validate() == true;

  void _onSubmit() async {
    FocusManager.instance.primaryFocus?.unfocus();

    final router = GoRouter.of(context);
    Loader.show(context);

    final signInResult = await _userProvider.signInUser(
      email: emailController.text,
      password: passwordController.text,
    );

    if (!mounted) return;

    if (signInResult.gqlQueryResult.hasException) {
      Loader.hide(context);

      AppErrorHandler(
        context: context,
        // TODO: - commented till we get proper message from backend
        // exception: signInResult.gqlQueryResult.exception,
        message: AppLocale.current.signinFailed,
      );
      return;
    }

    AnalyticService.userLogIn('email');
    await _userProvider.loadUser();

    if (_userProvider.isOnboardingCompleted(_onboardingModel)) await _inboxProvider.start();

    if (mounted) {
      Loader.hide(context);
    }

    AppUtility.checkForNonMigratedGroups(_userProvider.myUser);

    router.pushNamed(
      AppRoutes.root.name,
      queryParameters: {RouteParams.nextRouteName: widget.nextRouteName},
    );
  }
}
