import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/base/operation_result.dart';
import 'package:mm_flutter_app/services/graphql/providers/providers.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../widgets.dart';

class IndividualTrainingLandingScreen extends StatefulWidget {
  final String trainingId;

  const IndividualTrainingLandingScreen({super.key, required this.trainingId});

  @override
  State<IndividualTrainingLandingScreen> createState() => _IndividualTrainingLandingScreenState();
}

class _IndividualTrainingLandingScreenState extends State<IndividualTrainingLandingScreen> {
  late final VtsProvider _vtsProvider;
  late bool isDesktop;

  IndividualTraining? training;

  @override
  void initState() {
    _vtsProvider = Provider.of<VtsProvider>(context, listen: false);
    super.initState();
  }

  @override
  void didChangeDependencies() {
    isDesktop = AppUtility.displayDesktopUI(context);
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    if (training != null) return _content();
    return FutureBuilder<OperationResult<IndividualTraining>>(
      initialData: null,
      future: _vtsProvider.findTrainingById(trainingId: widget.trainingId),
      builder:
          (context, snapshot) => AppUtility.widgetForAsyncSnapshot(
            snapshot: snapshot,
            onReady: () {
              training = snapshot.data?.response;
              return _content();
            },
          ),
    );
  }

  Widget _content() {
    return Scaffold(
      appBar: AppBar(
        centerTitle: false,
        toolbarHeight: context.theme.d.homeToolbarHeight,
        automaticallyImplyLeading: false,
        leading: backButton(context, onPressed: () => GoRouter.of(context).pop()),
        shape: AppUtility.topDivider(context),
        title: Text(
          AppLocale.current.trainingCourse,
          style: context.theme.appBarTheme.titleTextStyle?.copyWith(
            fontSize: context.theme.d.fontSizeXLarge,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(context.theme.d.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TrainingCard.initWithIndividualTraining(
              individualTraining: training,
              showDescription: isDesktop,
              isLandscape: isDesktop,
              showFullDescription: true,
              onContinue: () async {
                await _vtsProvider.startTraining(
                  context,
                  training?.title ?? '',
                  training?.mm2Id,
                  training?.relativeUrlPath,
                  isTrainingPassedForMe: training?.isTrainingPassedForMe,
                );
              },
            ),
            if (!isDesktop) ...[
              SizedBox(height: context.theme.d.paddingMedium),
              MarkdownBody(
                data: training?.about ?? '',
                selectable: true,
                shrinkWrap: true,
                styleSheet: MarkdownStyleSheet.fromTheme(context.theme).copyWith(
                  textAlign: WrapAlignment.start,
                  p: context.textTheme.bodyLarge?.copyWith(
                    color: context.colorScheme.onSurface,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ],
            SizedBox(height: context.theme.d.paddingMedium),
            _lessonAndSupportWidget(training?.trainingContentPages),
            SizedBox(height: context.theme.d.paddingMedium),

            //TODO: Hardcoded series progress card
            // TrainingSeriesProgressCard(
            //   seriesName: 'sustainable small business',
            //   totalModules: 3,
            //   progress: 0,
            //   isLandscape: isDesktop,
            // ),
          ],
        ),
      ),
    );
  }

  Widget _lessonAndSupportWidget(List<TrainingContentPage>? trainingContentPages) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall),
      child:
          isDesktop
              ? Row(
                children: [
                  Expanded(child: _lessonPlanWidgetList(training?.trainingContentPages)),
                  SizedBox(height: context.theme.d.paddingMedium),
                  //TODO: commenting hardcoded provider, will need this in future
                  // _supportSourceWidget(),
                ],
              )
              : Column(
                children: [
                  _lessonPlanWidgetList(training?.trainingContentPages),
                  SizedBox(height: context.theme.d.paddingMedium),
                  //TODO: commenting hardcoded provider, will need this in future
                  // _supportSourceWidget(),
                ],
              ),
    );
  }

  Widget _lessonPlanWidgetList(List<TrainingContentPage>? trainingContentPages) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocale.current.lessonPlan,
          style: context.textTheme.titleMedium?.copyWith(
            color: context.colorScheme.surfaceBright,
            fontWeight: FontWeight.w700,
            fontSize: context.theme.d.fontSizeLarge,
          ),
        ),
        SizedBox(height: context.theme.d.paddingMedium),
        NestedListView(items: _vtsProvider.convertLessonsToMap(trainingContentPages)),
      ],
    );
  }

  /*   Widget _supportSourceWidget() {
    return SizedBox(
      width: context.theme.d.trainingSourceSize.width,
      height: context.theme.d.trainingSourceSize.height,
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            context.theme.d.roundedRectRadiusXLarge,
          ),
        ),
        clipBehavior: Clip.hardEdge,
        elevation: context.theme.d.elevationLevel5,
        shadowColor: context.colorScheme.outline,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(AppLocale.current.withSupportFrom),
            SizedBox(height: context.theme.d.paddingMedium),
            const Image(
              image: AssetImage(Assets.identityProviderGoogleIcon),
            ),
          ],
        ),
      ),
    );
  } */
}
