import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/providers.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:mm_flutter_app/widgets/widgets.dart';
import 'package:provider/provider.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import '../../../constants/parts/micromentor_icons.dart';

class TrainingsLandingWebScreen extends StatefulWidget {
  const TrainingsLandingWebScreen({super.key});

  @override
  State<TrainingsLandingWebScreen> createState() => _TrainingsLandingWebScreenState();
}

class _TrainingsLandingWebScreenState extends State<TrainingsLandingWebScreen> {
  late AutoScrollController _inProgressController;
  late AutoScrollController _recommendedController;
  late AutoScrollController _completedController;

  List<AutoScrollController> _groupedScrollControllers = [];

  late final VtsProvider _vtsProvider;
  late final UserProvider _userProvider;
  bool isStriveIndonesia = false;

  final int maxCardCount = 3;
  List<Training> inProgress = [];
  List<Training> recommended = [];
  List<Training> completed = [];

  @override
  void initState() {
    super.initState();

    _vtsProvider = Provider.of<VtsProvider>(context, listen: false);

    _userProvider = Provider.of<UserProvider>(context, listen: false);
    final groupIdent = ProfileUtility.fromFindId(_userProvider.myUser).groupIdent.toLowerCase();
    isStriveIndonesia = groupIdent == GroupIdent.striveIndonesia.name.toLowerCase();
    _vtsProvider.resetCurrentInitialIndex();
    if (isStriveIndonesia) return;

    _inProgressController = AutoScrollController(axis: Axis.horizontal);
    _recommendedController = AutoScrollController(axis: Axis.horizontal);
    _completedController = AutoScrollController(axis: Axis.horizontal);
  }

  @override
  void dispose() {
    if (isStriveIndonesia) {
      for (var controller in _groupedScrollControllers) {
        controller.dispose();
      }
    } else {
      _completedController.dispose();
      _inProgressController.dispose();
      _recommendedController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: false,
        automaticallyImplyLeading: false,
        toolbarHeight: context.theme.d.homeToolbarHeight,
        shape: AppUtility.topDivider(context),
        title: Text(
          AppLocale.current.myTraningTitle,
          style: context.theme.appBarTheme.titleTextStyle?.copyWith(
            fontSize: context.theme.d.fontSizeXLarge,
          ),
        ),
      ),
      body:
          _userProvider.myUser == null
              ? const SizedBox.shrink()
              : FutureBuilder<List<Training>>(
                initialData: null,
                future: _vtsProvider.findTrainingsForMe(),
                builder:
                    (context, snapshot) => AppUtility.widgetForAsyncSnapshot(
                      snapshot: snapshot,
                      onReady: () {
                        if (isStriveIndonesia) return _striveIndonesiaContent();
                        return _content();
                      },
                    ),
              ),
    );
  }

  Widget _content() {
    return SafeArea(
      child: Consumer<VtsProvider>(
        builder: (context, vtsProvider, _) {
          _filterTrainings(vtsProvider.myTrainings);
          return ListView(
            children: [
              if (inProgress.isNotEmpty)
                _buildHorizontalList(
                  title: AppLocale.current.trainingStatusInProgress,
                  items: inProgress,
                  controller: _inProgressController,
                  nextCount: _vtsProvider.getCurrentIndex(TrainingStatus.inProgress) + maxCardCount,
                  backCount: _vtsProvider.getCurrentIndex(TrainingStatus.inProgress) - maxCardCount,
                  type: TrainingStatus.inProgress,
                ),
              if (recommended.isNotEmpty)
                _buildHorizontalList(
                  title: AppLocale.current.trainingStatusRecommended,
                  items: recommended,
                  controller: _recommendedController,
                  nextCount: _vtsProvider.getCurrentIndex(TrainingStatus.notStarted) + maxCardCount,
                  backCount: _vtsProvider.getCurrentIndex(TrainingStatus.notStarted) - maxCardCount,
                  type: TrainingStatus.notStarted,
                ),
              if (completed.isNotEmpty)
                _buildHorizontalList(
                  title: AppLocale.current.trainingStatusCompleted,
                  items: completed,
                  controller: _completedController,
                  topPadding: context.theme.d.paddingLarge,
                  nextCount: _vtsProvider.getCurrentIndex(TrainingStatus.passed) + maxCardCount,
                  backCount: _vtsProvider.getCurrentIndex(TrainingStatus.passed) - maxCardCount,
                  type: TrainingStatus.passed,
                ),
            ],
          );
        },
      ),
    );
  }

  _striveIndonesiaContent() {
    final Map<String, List<Training>> groupedTrainings = _vtsProvider.getResourceTags();
    List<String> resourceTags = groupedTrainings.keys.toList();
    _vtsProvider.sortStriveResourceGroups(resourceTags);

    for (var tag in resourceTags) {
      groupedTrainings[tag] =
          _vtsProvider.myTrainings.where((training) {
            return training.tags?.contains(tag) == true;
          }).toList();
    }
    _groupedScrollControllers = List.generate(
      resourceTags.length,
      (index) => AutoScrollController(axis: Axis.horizontal),
    );
    _vtsProvider.tagsIndexList = List.generate(resourceTags.length, (index) => 0);

    return SafeArea(
      child: Consumer<VtsProvider>(
        builder: (context, vtsProvider, _) {
          return ListView.builder(
            itemCount: resourceTags.length,
            itemBuilder: (context, index) {
              final title =
                  resourceTags[index].split(':').length > 1
                      ? resourceTags[index].split(':').last
                      : resourceTags[index];
              return _buildHorizontalList(
                title: title,
                items: groupedTrainings[resourceTags[index]] ?? [],
                controller: _groupedScrollControllers[index],
                topPadding: context.theme.d.paddingLarge,
                nextCount: _vtsProvider.tagsIndexList[index] + maxCardCount,
                backCount: _vtsProvider.tagsIndexList[index] - maxCardCount,
                type: TrainingStatus.tags,
                striveIdTrainingIndex: index,
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildHorizontalList({
    required String title,
    required List<dynamic> items,
    required AutoScrollController controller,
    required int nextCount,
    required int backCount,
    required TrainingStatus type,
    double topPadding = 0,
    int striveIdTrainingIndex = 0,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              _titleWidget(title, topPadding),
              const Spacer(),
              IconButton(
                icon: const Icon(MicromentorIcons.arrowBackIosRounded),
                color: context.colorScheme.onSurface,
                iconSize: context.theme.d.iconSizeMedium,
                onPressed:
                    backCount >= 0
                        ? () => _scrollBackward(
                          controller,
                          type,
                          backCount,
                          index: striveIdTrainingIndex,
                        )
                        : null,
              ),
              IconButton(
                icon: const Icon(MicromentorIcons.arrowForwardIosRounded),
                color: context.colorScheme.onSurface,
                iconSize: context.theme.d.iconSizeMedium,
                onPressed:
                    items.length > nextCount
                        ? () => _scrollForward(
                          controller,
                          type,
                          nextCount,
                          index: striveIdTrainingIndex,
                        )
                        : null,
              ),
            ],
          ),
          SizedBox(
            height: context.theme.d.desktopTrainingCardHeight,
            child: ListView.builder(
              key: PageStorageKey('title $title'),
              physics: const NeverScrollableScrollPhysics(),
              scrollDirection: Axis.horizontal,
              controller: controller,
              itemCount: items.length,
              itemBuilder: (context, index) {
                return AutoScrollTag(
                  key: ValueKey(index),
                  controller: controller,
                  index: index,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall),
                    child: Column(children: [_trainingCardWidget(items[index])]),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _trainingCardWidget(dynamic trainingComponent) {
    if (trainingComponent is Training) {
      return TrainingCard.initWithTraining(
        training: trainingComponent,
        onContinue: () {
          context.pushNamed(
            AppRoutes.individualTraining.name,
            pathParameters: {RouteParams.individualTrainingId: trainingComponent.id},
          );
        },
      );
    }

    return TrainingCard.initWithTrainingSeries(
      trainingSeries: trainingComponent,
      onContinue: () {
        context.pushNamed(
          AppRoutes.trainingSeries.name,
          pathParameters: {RouteParams.trainingSeriesId: trainingComponent.id},
        );
      },
    );
  }

  Widget _titleWidget(String title, double topPadding) {
    return Padding(
      padding: EdgeInsets.only(top: topPadding, bottom: context.theme.d.paddingSmall),
      child: Text(
        title,
        style: context.textTheme.titleMedium?.copyWith(
          color: context.colorScheme.surfaceBright,
          fontWeight: FontWeight.w700,
          fontSize: context.theme.d.fontSizeMediumLarge,
        ),
      ),
    );
  }

  void _scrollForward(
    AutoScrollController controller,
    TrainingStatus type,
    int count, {
    int index = 0,
  }) async {
    await controller.scrollToIndex(count, preferPosition: AutoScrollPosition.begin);
    _vtsProvider.updateIndex(type, _getCardCount(), true, index: index);
  }

  void _scrollBackward(
    AutoScrollController controller,
    TrainingStatus type,
    int count, {
    int index = 0,
  }) async {
    await controller.scrollToIndex(count, preferPosition: AutoScrollPosition.begin);
    _vtsProvider.updateIndex(type, _getCardCount(), false, index: index);
  }

  int _getCardCount() {
    return context.mediaQuerySize.width < context.theme.d.desktopAppBreakpointTrainingDashboard
        ? maxCardCount - 1
        : maxCardCount;
  }

  _filterTrainings(List<Training> trainings) {
    recommended =
        trainings.where((training) {
          return training.myLatestTrainingSession == null;
        }).toList();
    inProgress =
        trainings.where((training) {
          return training.isTrainingPassedForMe == false &&
              training.myLatestTrainingSession?.isInProgress == true;
        }).toList();
    completed =
        trainings.where((training) {
          return training.isTrainingPassedForMe == true;
        }).toList();
  }
}
