import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/providers.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:mm_flutter_app/widgets/features/profile/components/profile_utility.dart';
import 'package:provider/provider.dart';

import 'components/training_card.dart';

class TrainingsLandingScreen extends StatefulWidget {
  const TrainingsLandingScreen({super.key});

  @override
  State<TrainingsLandingScreen> createState() => _TrainingsLandingScreenState();
}

class _TrainingsLandingScreenState extends State<TrainingsLandingScreen> {
  late List<Training> inProgress;
  late List<Training> recommended;
  late List<Training> completed;

  late VtsProvider _vtsProvider;
  bool isStriveIndonesia = false;

  @override
  void initState() {
    _vtsProvider = Provider.of<VtsProvider>(context, listen: false);
    final user = Provider.of<UserProvider>(context, listen: false).myUser;
    final groupIdent = ProfileUtility.fromFindId(user).groupIdent.toLowerCase();
    isStriveIndonesia = groupIdent == GroupIdent.striveIndonesia.name.toLowerCase();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: false,
        automaticallyImplyLeading: false,
        toolbarHeight: context.theme.d.homeToolbarHeight,
        shape: AppUtility.topDivider(context),
        title: Text(
          AppLocale.current.myTraningTitle,
          style: context.theme.appBarTheme.titleTextStyle?.copyWith(
            fontSize: context.theme.d.fontSizeXLarge,
          ),
        ),
      ),
      body: FutureBuilder<List<Training>>(
        initialData: null,
        future: _vtsProvider.findTrainingsForMe(),
        builder:
            (context, snapshot) => AppUtility.widgetForAsyncSnapshot(
              snapshot: snapshot,
              onReady: () {
                _filterTrainings(snapshot.data ?? []);
                if (isStriveIndonesia) return _striveIndonesiaContent();
                return _content();
              },
            ),
      ),
    );
  }

  _striveIndonesiaContent() {
    final Map<String, List<Training>> groupedTrainings = _vtsProvider.getResourceTags();
    List<String> resourceTags = groupedTrainings.keys.toList();
    _vtsProvider.sortStriveResourceGroups(resourceTags);

    final trainingSections = [];
    for (var tag in resourceTags) {
      final title = tag.split(':').length > 1 ? tag.split(':').last : tag;

      trainingSections.add(
        _TrainingSection(
          title: title,
          items: groupedTrainings[tag] ?? [],
          topPadding: context.theme.d.paddingXLarge,
        ),
      );
    }

    return SafeArea(
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
        itemCount: resourceTags.length + _vtsProvider.myTrainings.length,
        itemBuilder: (context, index) {
          for (_TrainingSection section in trainingSections) {
            if (index == 0) {
              return _titleWidget(section.title, section.topPadding);
            }
            index -= 1;
            if (index < section.items.length) {
              return trainingCardWidget(section.items[index]);
            }
            index -= section.items.length;
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _content() {
    final trainingSections = [
      if (inProgress.isNotEmpty)
        _TrainingSection(title: AppLocale.current.trainingStatusInProgress, items: inProgress),
      if (recommended.isNotEmpty)
        _TrainingSection(
          title: AppLocale.current.trainingStatusRecommended,
          items: recommended,
          topPadding: context.theme.d.paddingXLarge,
        ),
      if (completed.isNotEmpty)
        _TrainingSection(
          title: AppLocale.current.trainingStatusCompleted,
          items: completed,
          topPadding: context.theme.d.paddingXLarge,
        ),
    ];

    return SafeArea(
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
        itemCount: _getItemCount(),
        itemBuilder: (context, index) {
          for (_TrainingSection section in trainingSections) {
            if (index == 0) {
              return _titleWidget(section.title, section.topPadding);
            }
            index -= 1;
            if (index < section.items.length) {
              return trainingCardWidget(section.items[index]);
            }
            index -= section.items.length;
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _titleWidget(String title, double topPadding) {
    return Padding(
      padding: EdgeInsets.only(top: topPadding, bottom: context.theme.d.paddingSmall),
      child: Text(
        title,
        style: context.textTheme.titleMedium?.copyWith(
          color: context.colorScheme.onSecondaryContainer,
          fontWeight: FontWeight.w700,
          fontSize: context.theme.d.fontSizeMedium18,
        ),
      ),
    );
  }

  int _getItemCount() {
    int count = 0;

    count +=
        (inProgress.isNotEmpty ? inProgress.length + 1 : 0) +
        (recommended.isNotEmpty ? recommended.length + 1 : 0) +
        (completed.isNotEmpty ? completed.length + 1 : 0);

    return count;
  }

  Widget trainingCardWidget(dynamic trainingComponent) {
    if (trainingComponent is Training) {
      return TrainingCard.initWithTraining(
        training: trainingComponent,
        onContinue: () {
          context.pushNamed(
            AppRoutes.individualTraining.name,
            pathParameters: {RouteParams.individualTrainingId: trainingComponent.id},
          );
        },
      );
    }

    return TrainingCard.initWithTrainingSeries(
      trainingSeries: trainingComponent,
      onContinue: () {
        context.pushNamed(
          AppRoutes.trainingSeries.name,
          pathParameters: {RouteParams.trainingSeriesId: trainingComponent.id},
        );
      },
    );
  }

  _filterTrainings(List<Training> trainings) {
    recommended =
        trainings.where((training) {
          return training.myLatestTrainingSession == null;
        }).toList();
    inProgress =
        trainings.where((training) {
          return training.isTrainingPassedForMe == false ||
              training.myLatestTrainingSession?.isInProgress == true;
        }).toList();
    completed =
        trainings.where((training) {
          return training.isTrainingPassedForMe == true;
        }).toList();
  }
}

class _TrainingSection {
  final String title;
  final List<dynamic> items;
  final double topPadding;

  _TrainingSection({required this.title, required this.items, this.topPadding = 0});
}
