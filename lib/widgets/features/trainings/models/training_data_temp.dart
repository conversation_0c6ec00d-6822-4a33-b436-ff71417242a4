import 'package:mm_flutter_app/constants/constants.dart';
import 'training_model.dart';
import 'training_series_model.dart';

class TrainingData {
  static List<TrainingModel> getTrainings() {
    var training1 = TrainingModel(
      title: 'trainig1',
      about: 'training1 description',
      progress: 0,
      status: TrainingStatus.notStarted,
      lessonPlan: 'trainig1 lessonPlan',
    );

    var training2 = TrainingModel(
      title: 'trainig2',
      about: 'training2 description',
      progress: 5,
      status: TrainingStatus.inProgress,
      lessonPlan: 'trainig2 lessonPlan',
    );

    var training3 = TrainingModel(
      title: 'trainig3',
      about: 'training3 description',
      progress: 10,
      status: TrainingStatus.passed,
      lessonPlan: 'trainig3 lessonPlan',
    );

    return [training1, training2, training3];
  }

  static List<TrainingSeriesModel> getTrainingSeriesList() {
    return [trainingSeries1, trainingSeries2];
  }

  static List<TrainingSeriesModel> getAllTrainingSeries() {
    return [trainingSeries1, trainingSeries2, trainingSeries3, trainingSeries4, trainingSeries5];
  }

  ///////// Training series /////////

  static var trainingSeries1 = TrainingSeriesModel(
    id: '1',
    title: 'Training Series1',
    description: 'Training Series1 Description goes here',
    about:
        'This section will introduce the link between climate change and entrepreneurship, and explain why we, as entrepreneurs, need to pursue climate resilience. At the end of this section, you will be ready to begin taking concrete steps to tackle climate change, benefit from its opportunities, and build your business\'s climate resilience.',
    lessonPlan: '''- Understand the crucial connection between climate change and entrepreneurship
- Learn how to build a climate-resilient business
- Cover the fundamentals of climate response in the Middle East and North Africa, including:
  - Mitigation strategies
  - Adaptation strategies
  - Benefits of climate-conscious operations
- Gain insights into climate finance
- Empower yourself to access resources and support your business transition
''',
    status: TrainingStatus.inProgress,
    progress: 7,
    trainings: TrainingData.getTrainings(),
  );

  static var trainingSeries2 = TrainingSeriesModel(
    id: '2',
    title: 'Training Series2',
    description: 'Training Series2 Description goes here',
    about:
        'This section will introduce the link between climate change and entrepreneurship, and explain why we, as entrepreneurs, need to pursue climate resilience. At the end of this section, you will be ready to begin taking concrete steps to tackle climate change, benefit from its opportunities, and build your business\'s climate resilience.',
    lessonPlan: '''- Understand the crucial connection between climate change and entrepreneurship
- Learn how to build a climate-resilient business
- Cover the fundamentals of climate response in the Middle East and North Africa, including:
  - Mitigation strategies
  - Adaptation strategies
  - Benefits of climate-conscious operations
- Gain insights into climate finance
- Empower yourself to access resources and support your business transition
''',
    status: TrainingStatus.inProgress,
    progress: 3,
    trainings: TrainingData.getMoreTrainings(),
  );

  static var trainingSeries3 = TrainingSeriesModel(
    id: '3',
    title: 'Training Series2',
    description: 'Training Series2 Description goes here',
    about:
        'This section will introduce the link between climate change and entrepreneurship, and explain why we, as entrepreneurs, need to pursue climate resilience. At the end of this section, you will be ready to begin taking concrete steps to tackle climate change, benefit from its opportunities, and build your business\'s climate resilience.',
    lessonPlan: '''- Understand the crucial connection between climate change and entrepreneurship
- Learn how to build a climate-resilient business
- Cover the fundamentals of climate response in the Middle East and North Africa, including:
  - Mitigation strategies
  - Adaptation strategies
  - Benefits of climate-conscious operations
- Gain insights into climate finance
- Empower yourself to access resources and support your business transition
''',
    status: TrainingStatus.inProgress,
    progress: 3,
    trainings: TrainingData.getMoreTrainings(),
  );

  static var trainingSeries4 = TrainingSeriesModel(
    id: '4',
    title: 'Training Series completed',
    description: 'Training Series2 Description goes here: completed',
    about:
        'This section will introduce the link between climate change and entrepreneurship, and explain why we, as entrepreneurs, need to pursue climate resilience. At the end of this section, you will be ready to begin taking concrete steps to tackle climate change, benefit from its opportunities, and build your business\'s climate resilience.',
    lessonPlan: '''- Understand the crucial connection between climate change and entrepreneurship
- Learn how to build a climate-resilient business
- Cover the fundamentals of climate response in the Middle East and North Africa, including:
  - Mitigation strategies
  - Adaptation strategies
  - Benefits of climate-conscious operations
- Gain insights into climate finance
- Empower yourself to access resources and support your business transition
''',
    status: TrainingStatus.passed,
    trainings: TrainingData.getMoreTrainings(),
  );

  static var trainingSeries5 = TrainingSeriesModel(
    id: '5',
    title: 'Training Series not started',
    description: 'Training Description goes here: not started',
    about:
        'This section will introduce the link between climate change and entrepreneurship, and explain why we, as entrepreneurs, need to pursue climate resilience. At the end of this section, you will be ready to begin taking concrete steps to tackle climate change, benefit from its opportunities, and build your business\'s climate resilience.',
    lessonPlan:
        '• Understand the crucial connection between climate change and entrepreneurship\n• Learn how to build a climate-resilient business\n• Cover the fundamentals of climate response in the Middle East and North Africa, including:\n• Mitigation strategies\n• Adaptation strategies\n• Benefits of climate-conscious operations\n• Gain insights into climate finance\nEmpower yourself to access resources and support your business transition',
    status: TrainingStatus.notStarted,
    trainings: TrainingData.getMoreTrainings(),
  );
  ///////////////////////////////////////////

  static var individualTraining1 = TrainingModel(
    title: 'Individual Training1 without series',
    about: 'Individual Training1 without series description',
    progress: 0,
    status: TrainingStatus.notStarted,
    lessonPlan: 'Individual Training1 without series lessonPlan',
  );

  static var individualTraining2 = TrainingModel(
    title: 'Individual Training2 without series',
    about: 'Individual Training2 without series description',
    progress: 6,
    status: TrainingStatus.inProgress,
    lessonPlan: 'Individual Training2 without series lessonPlan',
  );

  static List<TrainingModel> getMoreTrainings() {
    var training1 = TrainingModel(
      title: 'trainig3',
      about: 'training3 description',
      progress: 0,
      status: TrainingStatus.notStarted,
      lessonPlan: 'trainig3 lessonPlan',
    );

    var training2 = TrainingModel(
      title: 'trainig4',
      about:
          'training4 description ddfs sd fsd fsd fsd fsdf sdf sd fds f sdf sd fs d fsdfsd fs f ds f s fdsf ds f sd f sdf sd f sdf sdf sdf sd fs df sd f sd f sdf sdfsd fsd f sd f sd fsd f sd fsdfsdfsdfs d sdfsd f sd fs d fsdfs dfsd f sdf sd fs dfs df sd f sdf sdf sdf ds f sdf sd f sd fs d f sdfds f sd f sd fs d fs df sd f',
      progress: 5,
      status: TrainingStatus.inProgress,
      lessonPlan: 'trainig4 lessonPlan',
    );

    var training3 = TrainingModel(
      title: 'trainig5',
      about: 'training5 description',
      progress: 10,
      status: TrainingStatus.passed,
      lessonPlan: 'trainig5 lessonPlan',
    );

    return [training1, training2, training3];
  }

  static List<dynamic> getInProgressTrainingsAndSeries() {
    TrainingModel training1 = TrainingModel(
      imageUrl: null,
      title: 'Climate Finance',
      about:
          'This free course empowers entrepreneurs to drive change within the booming green economy',
      lessonPlan: 'temp',
      status: TrainingStatus.inProgress,
    );
    TrainingModel training2 = TrainingModel(
      title: 'Climate Finance1',
      lessonPlan: '5',
      status: TrainingStatus.inProgress,
    );

    return [training1, training2, trainingSeries3];
  }

  static List<dynamic> getCompletedTrainingsAndSeries() {
    TrainingModel training1 = TrainingModel(
      title: 'trainig5',
      about: 'training5 description',
      status: TrainingStatus.passed,
      lessonPlan: 'trainig5 lessonPlan',
    );
    TrainingModel training2 = TrainingModel(
      title: 'trainig6',
      about: 'training6 description',
      status: TrainingStatus.passed,
      lessonPlan: 'trainig6 lessonPlan',
    );

    TrainingModel trainingPass = TrainingModel(
      title: 'new training',
      about: 'new training description',
      status: TrainingStatus.passed,
      lessonPlan: 'trainig lessonPlan new',
      isPassingScore: true,
    );
    TrainingModel trainingFail = TrainingModel(
      title: ' training ABC',
      about: 'ABC training description',
      status: TrainingStatus.passed,
      lessonPlan: 'trainig lessonPlan ABC',
      isPassingScore: false,
    );

    return [training1, training2, trainingSeries4, trainingPass, trainingFail];
  }

  static List<dynamic> getRecommendedTrainingsAndSeries() {
    TrainingModel training1 = TrainingModel(
      title: 'trainig not started yet',
      about: 'This training has to be started yet: description',
      status: TrainingStatus.notStarted,
      lessonPlan: 'trainig5 lessonPlan',
    );

    return [training1, trainingSeries5];
  }
}
