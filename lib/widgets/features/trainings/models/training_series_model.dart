import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/widgets/features/trainings/models/training_model.dart';

class TrainingSeriesModel {
  String? id;
  String? imageUrl;
  String? title;
  String? about;
  String? description;
  double? progress;
  String? lessonPlan;
  TrainingStatus? status;
  List<TrainingModel>? trainings;

  TrainingSeriesModel({
    required this.id,
    this.imageUrl,
    this.title,
    this.description,
    this.about,
    this.lessonPlan,
    this.progress,
    this.status,
    this.trainings,
  });

  TrainingSeriesModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    imageUrl = json['imageUrl'];
    title = json['title'];
  }
}
