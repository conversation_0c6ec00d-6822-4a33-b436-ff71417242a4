import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/utilities/utility.dart';

import '../../widgets.dart';

class TrainingSeriesLandingScreen extends StatefulWidget {
  final String trainingSeriesId;

  const TrainingSeriesLandingScreen({super.key, required this.trainingSeriesId});

  @override
  State<TrainingSeriesLandingScreen> createState() => _TrainingSeriesLandingScreenState();
}

class _TrainingSeriesLandingScreenState extends State<TrainingSeriesLandingScreen> {
  TrainingSeriesModel? trainingSeries;
  final ScrollController _scrollController = ScrollController();
  late bool isDesktop;

  @override
  void initState() {
    super.initState();

    trainingSeries =
        TrainingData.getAllTrainingSeries()
            .where((element) => element.id == widget.trainingSeriesId)
            .firstOrNull;
  }

  @override
  void didChangeDependencies() {
    isDesktop = AppUtility.displayDesktopUI(context);
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: false,
        toolbarHeight: context.theme.d.homeToolbarHeight,
        automaticallyImplyLeading: false,
        leading: backButton(context, onPressed: () => GoRouter.of(context).pop()),
        shape: AppUtility.topDivider(context),
        title: Text(
          trainingSeries?.title ?? '',
          style: context.theme.appBarTheme.titleTextStyle?.copyWith(
            fontSize: context.theme.d.fontSizeXLarge,
          ),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingMedium),
          child: isDesktop ? _desktopContent() : _mobileContent(),
        ),
      ),
    );
  }

  Widget _desktopContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TrainingCard.initWithTrainingSeries(
          trainingSeries: trainingSeries,
          isLandscape: isDesktop,
          showDetails: true,
        ),
        SizedBox(height: context.theme.d.paddingMedium),
        Row(children: [Expanded(child: _learnWidget()), _supportSourceWidget()]),
        SizedBox(height: context.theme.d.paddingMedium),
        _trainingCourses(),
        SizedBox(height: context.theme.d.paddingMedium),
      ],
    );
  }

  Widget _mobileContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TrainingCard.initWithTrainingSeries(
          trainingSeries: trainingSeries,
          isLandscape: isDesktop,
          showDetails: true,
        ),
        _divider(),
        _trainingCourses(),
        _divider(),
        _learnWidget(),
        SizedBox(height: context.theme.d.paddingMedium),
        Center(child: _supportSourceWidget()),
      ],
    );
  }

  Widget _divider() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: context.theme.d.paddingLarge),
      height: context.theme.d.paddingXxSmall,
      color: context.colorScheme.outlineVariant.withValues(alpha: 0.5),
    );
  }

  Widget _learnWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _headerTitle(AppLocale.current.trainingLearningTitle),
        SizedBox(height: context.theme.d.paddingSmall),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall),
          child: Text(
            trainingSeries?.lessonPlan ?? '',
            style: context.textTheme.bodyLarge?.copyWith(
              color: context.colorScheme.onSecondaryContainer,
            ),
          ),
        ),
      ],
    );
  }

  _trainingCourses() {
    final trainings = trainingSeries?.trainings ?? [];
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _headerTitle(AppLocale.current.trainingCourses),
        SizedBox(height: context.theme.d.paddingSmall),
        isDesktop
            ? SizedBox(
              height: context.theme.d.desktopTrainingCardHeight,
              child: ListView.builder(
                padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall),
                shrinkWrap: true,
                scrollDirection: Axis.horizontal,
                controller: _scrollController,
                itemCount: trainings.length,
                itemBuilder: (context, index) {
                  return Column(
                    children: [
                      TrainingCard.initWithTrainingModel(
                        trainingModel: trainings[index],
                        onContinue: () {},
                      ),
                    ],
                  );
                },
              ),
            )
            : ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: context.theme.d.paddingSmall),
              scrollDirection: Axis.vertical,
              shrinkWrap: true,
              itemCount: trainings.length,
              itemBuilder: (context, index) {
                return TrainingCard.initWithTrainingModel(
                  trainingModel: trainings[index],
                  onContinue: () {},
                );
              },
            ),
      ],
    );
  }

  _headerTitle(String title) {
    return Text(
      title,
      style: context.textTheme.titleMedium?.copyWith(
        color: context.colorScheme.surfaceBright,
        fontWeight: FontWeight.w700,
        fontSize: context.theme.d.fontSizeLarge,
      ),
    );
  }

  Widget _supportSourceWidget() {
    return SizedBox(
      width: context.theme.d.trainingSourceSize.width,
      height: context.theme.d.trainingSourceSize.height,
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXLarge),
        ),
        clipBehavior: Clip.hardEdge,
        elevation: context.theme.d.elevationLevel5,
        shadowColor: context.colorScheme.outline,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(AppLocale.current.withSupportFrom),
            SizedBox(height: context.theme.d.paddingMedium),
            const Image(image: AssetImage(Assets.identityProviderGoogleIcon)),
          ],
        ),
      ),
    );
  }
}
