import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';
import 'package:mm_flutter_app/services/graphql/providers/vts_provider.dart';
import 'package:mm_flutter_app/utilities/utility.dart';
import 'package:provider/provider.dart';

import '../../../widgets.dart';

class TrainingCard extends StatelessWidget {
  final Training? training;
  final IndividualTraining? individualTraining;
  final TrainingModel? trainingModel;
  final TrainingSeriesModel? trainingSeries;
  final bool showDetails;
  final bool isLandscape;
  final Function? onContinue;

  final String? imageUrl;
  final String? title;
  final String? about;
  final int? trainingNumber;
  final double? progress;
  final TrainingStatus? status;
  final int maxCardCount = 3;
  final bool showDescription;
  final bool showFullDescription;

  TrainingCard.initWithTrainingModel({
    super.key,
    required this.trainingModel,
    this.showDetails = false,
    this.isLandscape = false,
    this.showDescription = true,
    this.showFullDescription = false,
    this.onContinue,
  }) : title = trainingModel?.title,
       imageUrl = trainingModel?.imageUrl,
       about = trainingModel?.about,
       trainingNumber = null,
       status = trainingModel?.status,
       progress = trainingModel?.progress ?? 0,
       training = null,
       individualTraining = null,
       trainingSeries = null;

  TrainingCard.initWithIndividualTraining({
    super.key,
    required this.individualTraining,
    this.showDetails = false,
    this.isLandscape = false,
    this.showDescription = true,
    this.showFullDescription = false,
    this.onContinue,
  }) : title = individualTraining?.title,
       imageUrl = individualTraining?.imageUrls?.firstOrNull,
       about = individualTraining?.about,
       trainingNumber = null,
       status = VtsProvider.getIndividualTrainingStatus(individualTraining),
       progress = individualTraining?.myLatestTrainingSession?.percentCompleted ?? 0,
       trainingSeries = null,
       training = null,
       trainingModel = null;

  TrainingCard.initWithTrainingSeries({
    super.key,
    required this.trainingSeries,
    this.showDetails = false,
    this.isLandscape = false,
    this.showDescription = true,
    this.showFullDescription = false,
    this.onContinue,
  }) : title = trainingSeries?.title,
       imageUrl = trainingSeries?.imageUrl,
       about = trainingSeries?.about,
       trainingNumber = null, //trainingSeries?.trainings?.length ?? 0,
       status = trainingSeries?.status,
       progress = trainingSeries?.progress ?? 0,
       training = null,
       individualTraining = null,
       trainingModel = null;

  TrainingCard.initWithTraining({
    super.key,
    required this.training,
    this.showDetails = false,
    this.isLandscape = false,
    this.showDescription = true,
    this.showFullDescription = false,
    this.onContinue,
  }) : title = training?.title,
       imageUrl = training?.imageUrls?.firstOrNull,
       about = training?.about,
       trainingNumber = null,
       status = VtsProvider.getTrainingStatus(training),
       progress = training?.myLatestTrainingSession?.percentCompleted ?? 0,
       trainingSeries = null,
       individualTraining = null,
       trainingModel = null;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: _getCardWidth(context),
      child: IntrinsicHeight(
        child: Card(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXLarge),
          ),
          clipBehavior: Clip.hardEdge,
          elevation: context.theme.d.elevationLevel5,
          shadowColor: context.colorScheme.outline,
          child: isLandscape ? _landscapeView(context) : _mobileContent(context),
        ),
      ),
    );
  }

  Widget _landscapeView(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (imageUrl != null) Expanded(flex: 2, child: _imageWidget(context)),
        Expanded(
          flex: 3,
          child: Padding(
            padding: EdgeInsets.all(context.theme.d.paddingMedium),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                trainingNumber == null
                    ? _title(context)
                    : Row(
                      children: [
                        _trainingNumber(context),
                        SizedBox(width: context.theme.d.paddingSmall),
                        Expanded(child: _title(context)),
                      ],
                    ),
                SizedBox(height: context.theme.d.paddingSmall),
                TrainingProgressBar(
                  total: 100,
                  progress: progress ?? 0,
                  trainingStatus: status,
                  isPassingScore: status == TrainingStatus.passed,
                ),
                if (about != null && showDescription) ...[
                  SizedBox(height: context.theme.d.paddingMedium),
                  _description(context, about ?? ''),
                ],
                if (showDetails && trainingSeries != null) _trainingSeriesNote(context),
                SizedBox(height: context.theme.d.paddingMedium),
                _buttons(context),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _mobileContent(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _imageWidget(context),
        Padding(
          padding: EdgeInsets.all(context.theme.d.paddingMedium),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              trainingNumber == null
                  ? _title(context)
                  : Row(
                    children: [
                      _trainingNumber(context),
                      SizedBox(width: context.theme.d.paddingSmall),
                      Expanded(child: _title(context)),
                    ],
                  ),
              SizedBox(height: context.theme.d.paddingSmall),
              TrainingProgressBar(
                total: 100,
                progress: progress ?? 0,
                trainingStatus: status,
                isPassingScore: status == TrainingStatus.passed,
              ),
              if (about != null && showDescription) ...[
                SizedBox(height: context.theme.d.paddingMedium),
                _description(context, about ?? ''),
              ],
              if (showDetails && trainingSeries != null) _trainingSeriesNote(context),
              SizedBox(height: context.theme.d.paddingMedium),
              _buttons(context),
            ],
          ),
        ),
      ],
    );
  }

  Widget _imageWidget(BuildContext context) {
    return (imageUrl != null)
        ? AppUtility.noPreviewAvaliable(context)
        : Center(
          child: Image.network(
            imageUrl!,
            height: showFullDescription ? null : context.theme.d.boxSizeXXLarge,
            width: double.maxFinite,
            fit: BoxFit.fill,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return SizedBox(
                height: context.theme.d.boxSizeXXLarge,
                child: const Center(child: CircularProgressIndicator()),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return AppUtility.noPreviewAvaliable(context);
            },
          ),
        );
  }

  Widget _trainingNumber(BuildContext context) {
    return Container(
      width: context.theme.d.iconSizeXLarge,
      height: context.theme.d.iconSizeXLarge,
      alignment: Alignment.center,
      decoration: BoxDecoration(color: context.colorScheme.primary, shape: BoxShape.circle),
      child: Text(
        '$trainingNumber',
        style: context.theme.textTheme.bodyMedium?.copyWith(color: context.colorScheme.onPrimary),
      ),
    );
  }

  Widget _title(BuildContext context) {
    return Text(
      title ?? '',
      maxLines: 2,
      style: context.theme.textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.w500, // 700 in some cards
        color: context.colorScheme.onSecondaryContainer, // 18 in some cards
      ),
    );
  }

  Widget _description(BuildContext context, String content) {
    if (showFullDescription) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: MarkdownBody(
              data: content,
              selectable: true,
              shrinkWrap: true,
              styleSheet: MarkdownStyleSheet.fromTheme(context.theme).copyWith(
                textAlign: WrapAlignment.start,
                p: context.textTheme.bodyLarge?.copyWith(
                  color: context.colorScheme.onSurface,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ),
        ],
      );
    }

    final plainText =
        MarkdownBody(
          data: content,
          styleSheet: MarkdownStyleSheet.fromTheme(context.theme).copyWith(
            textAlign: WrapAlignment.start,
            p: context.textTheme.bodyMedium?.copyWith(
              color: context.colorScheme.onSurface,
              fontWeight: FontWeight.w400,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ).data;

    return Text(plainText, maxLines: 2, overflow: TextOverflow.ellipsis);
  }

  Widget _trainingSeriesNote(BuildContext context) {
    final content =
        'Complete all ${trainingSeries?.trainings?.length} modules of the sustainable small business course';
    return Padding(
      padding: EdgeInsets.only(top: context.theme.d.paddingMedium),
      child: Text(
        content,
        maxLines: 3,
        style: context.theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w700,
          color: context.colorScheme.onSurface,
        ),
      ),
    );
  }

  Widget _buttons(BuildContext context) {
    VtsProvider vtsProvider = Provider.of<VtsProvider>(context, listen: false);

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        IconButton(
          key: const Key('trainingCertificateAction'),
          onPressed:
              status == TrainingStatus.passed
                  ? () async {
                    debugPrint('Download certificate');
                    if (!kIsWeb) {
                      _downloadCertificateMobileDialog(context);
                    } else {
                      // download certificate. use the OTP url and redirect to the training certificate.
                      vtsProvider.downloadTrainingCertificate(
                        context,
                        training?.mm2Id,
                        training?.title,
                        kIsWeb,
                      );
                    }
                  }
                  : null,
          padding: EdgeInsets.zero,
          constraints: BoxConstraints(minWidth: context.theme.d.paddingXLarge),
          icon:
              status == TrainingStatus.passed
                  ? Image(
                    image: const AssetImage(Assets.certificateSelectedIcon),
                    height: context.theme.d.paddingXLarge,
                  )
                  : Image(
                    image: const AssetImage(Assets.certificateIcon),
                    height: context.theme.d.paddingXLarge,
                  ),
        ),
        SizedBox(width: context.theme.d.paddingSmall),
        // TODO - Hiding this icon as no action on it
        // IconButton(
        //   key: const Key('trainingListAction'),
        //   onPressed: status == TrainingStatus.passed ? () {} : null,
        //   padding: EdgeInsets.zero,
        //   constraints: BoxConstraints(
        //     minWidth: context.theme.d.paddingXLarge,
        //   ),
        //   icon: status == TrainingStatus.passed
        //       ? Image(
        //           image: const AssetImage(Assets.listSelectedIcon),
        //           height: context.theme.d.paddingXLarge,
        //         )
        //       : Image(
        //           image: const AssetImage(Assets.listIcon),
        //           height: context.theme.d.paddingXLarge,
        //         ),
        // ),
        const Spacer(),
        status == TrainingStatus.passed
            ? CommonButton.outlinedButton(
              context: context,
              key: const Key('trainingReviewAction'),
              title: AppLocale.current.reviewTitle,
              isFullWidth: false,
              onPressed: status == TrainingStatus.passed ? () => onContinue?.call() : null,
              buttonSize: ButtonSize.small,
            )
            : CommonButton.primaryRoundedRectangle(
              context: context,
              key: Key(
                status == TrainingStatus.inProgress
                    ? 'trainingContinueAction'
                    : 'trainingStartAction',
              ),
              title:
                  status == TrainingStatus.inProgress || status == TrainingStatus.failed
                      ? AppLocale.current.actionContinue
                      : AppLocale.current.startTitle,
              isFullWidth: false,
              onPressed: () => onContinue?.call(),
              buttonSize: ButtonSize.small,
            ),
      ],
    );
  }

  double? _getCardWidth(BuildContext context) {
    double? desktopCardWidth;
    if (AppUtility.displayDesktopUI(context) && !isLandscape) {
      //this(universalScreenMargin) is calculated from Appwrapper page, where we have added horizontal padding arround our child in _createDesktopScaffold method
      double universalScreenMargin = context.theme.d.paddingXLarge * 2;
      double availableWidth = context.mediaQuerySize.width - universalScreenMargin;

      int horizontalCardCount =
          availableWidth < context.theme.d.desktopAppBreakpointTrainingDashboard
              ? maxCardCount - 1
              : maxCardCount;
      desktopCardWidth =
          availableWidth / horizontalCardCount -
          (context.theme.d.paddingXLarge - horizontalCardCount);
    }

    return desktopCardWidth;
  }

  void _downloadCertificateMobileDialog(BuildContext context) async {
    VtsProvider vtsProvider = Provider.of<VtsProvider>(context, listen: false);

    await showDialog(
      context: context,
      builder:
          (context) => DialogTemplate.withCustomContent(
            showButtons: false,
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocale.current.downloadCertificate,
                  style: context.theme.textTheme.titleLarge?.copyWith(
                    color: Theme.of(context).colorScheme.surfaceBright,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                SizedBox(height: context.theme.d.paddingMedium),
                Text(
                  AppLocale.current.downloadCertificateDialogMsg,
                  style: context.theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w400,
                    color: context.colorScheme.onSurface,
                  ),
                ),
                SizedBox(height: context.theme.d.paddingMedium),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          key: const Key('certificateDownload'),
                          onPressed: () async {
                            await vtsProvider.downloadTrainingCertificate(
                              context,
                              training?.mm2Id,
                              training?.title,
                              kIsWeb ? true : false,
                            );

                            if (context.mounted) {
                              Navigator.pop(context);
                            }
                          },
                          padding: EdgeInsets.zero,
                          constraints: BoxConstraints(minWidth: context.theme.d.paddingXLarge),
                          icon: Image(
                            image: const AssetImage(Assets.certificateDownloadIcon),
                            height: context.theme.d.paddingXLarge,
                          ),
                        ),
                        Text(
                          AppLocale.current.download,
                          style: context.textTheme.bodyLarge?.copyWith(
                            color: context.colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(width: context.theme.d.paddingLarge),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          key: const Key('certificateView'),
                          onPressed: () {
                            vtsProvider.downloadTrainingCertificate(
                              context,
                              training?.mm2Id,
                              training?.title,
                              true,
                            );
                          },
                          padding: EdgeInsets.zero,
                          constraints: BoxConstraints(minWidth: context.theme.d.paddingXLarge),
                          icon: Image(
                            image: const AssetImage(Assets.certificateViewIcon),
                            height: context.theme.d.paddingXLarge,
                          ),
                        ),
                        Text(
                          AppLocale.current.view,
                          style: context.textTheme.bodyLarge?.copyWith(
                            color: context.colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
    );
  }
}
