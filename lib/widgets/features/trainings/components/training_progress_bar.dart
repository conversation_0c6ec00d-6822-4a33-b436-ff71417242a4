import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';

class TrainingProgressBar extends StatelessWidget {
  final double? total;
  final double? progress;
  final TrainingStatus? trainingStatus;
  final double? height;
  final bool? isPassingScore;

  const TrainingProgressBar({
    this.total,
    this.progress,
    this.height,
    this.isPassingScore,
    required this.trainingStatus,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    Color color = context.colorScheme.primary;

    if (_isTrainingResultDeclared()) {
      color =
          (isPassingScore ?? false)
              ? context.colorScheme.tertiaryFixedDim
              : context.colorScheme.onErrorContainer;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding:
              !_showProgressBarCount
                  ? EdgeInsets.symmetric(vertical: context.theme.d.paddingXSmall)
                  : EdgeInsets.zero,
          child: Row(
            children: [
              Expanded(
                child: Container(
                  height: height ?? context.theme.d.paddingSmall,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                      (height ?? context.theme.d.paddingSmall) / 2,
                    ),
                    color: context.colorScheme.outlineVariant.withValues(alpha: 0.4),
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: widthFactor(),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(context.theme.d.paddingXSmall),
                        color: color,
                      ),
                    ),
                  ),
                ),
              ),
              if (_showProgressBarCount) ...[
                SizedBox(width: context.theme.d.paddingSmall),
                Text(
                  '$progress/$total',
                  style: GoogleFonts.inter(
                    textStyle: context.textTheme.titleSmall?.copyWith(
                      color: context.colorScheme.onSecondaryContainer,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        if (_isTrainingResultDeclared()) ...[
          SizedBox(height: context.theme.d.paddingSmall),
          Text(
            (isPassingScore == true)
                ? AppLocale.current.trainingPassMessage
                : AppLocale.current.trainingFailMessage,
            style: context.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
        ],
      ],
    );
  }

  bool _isTrainingResultDeclared() {
    return trainingStatus == TrainingStatus.passed || trainingStatus == TrainingStatus.failed;
  }

  // Show count when the Training is in progress
  bool get _showProgressBarCount =>
      trainingStatus != null &&
      trainingStatus != TrainingStatus.passed &&
      trainingStatus != TrainingStatus.failed &&
      total != null &&
      progress! > 0;

  double? widthFactor() {
    switch (trainingStatus) {
      case TrainingStatus.notStarted:
        return 0;
      case TrainingStatus.inProgress:
        return (progress != null && progress! > 0) ? (progress! / 100) : 0.5;
      case TrainingStatus.passed || TrainingStatus.failed:
        return 1;
      default:
        return 0;
    }
  }
}
