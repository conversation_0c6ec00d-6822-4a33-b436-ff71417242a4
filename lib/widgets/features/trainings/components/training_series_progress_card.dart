import 'package:flutter/material.dart';
import 'package:mm_flutter_app/constants/constants.dart';
import 'package:mm_flutter_app/generatedAppLocale/l10n.dart';
import 'package:mm_flutter_app/services/extensions.dart';

import '../../../widgets.dart';

class TrainingSeriesProgressCard extends StatelessWidget {
  final String seriesName;
  final double totalModules;
  final double progress;
  final bool isLandscape;
  const TrainingSeriesProgressCard({
    required this.seriesName,
    required this.totalModules,
    required this.progress,
    required this.isLandscape,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(context.theme.d.roundedRectRadiusXLarge),
      ),
      clipBehavior: Clip.hardEdge,
      elevation: context.theme.d.elevationLevel5,
      shadowColor: context.colorScheme.outline,
      child: Padding(
        padding: EdgeInsets.all(context.theme.d.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Complete all $totalModules modules of the $seriesName course',
              style: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: context.colorScheme.onSecondaryContainer,
              ),
            ),
            SizedBox(height: context.theme.d.paddingSmall),
            TrainingProgressBar(
              total: totalModules,
              progress: progress,
              trainingStatus: TrainingStatus.notStarted,
            ),
            SizedBox(height: context.theme.d.paddingMedium),
            Row(
              mainAxisAlignment:
                  isLandscape ? MainAxisAlignment.center : MainAxisAlignment.spaceBetween,
              children: [
                if (isLandscape) const Spacer(),
                CommonButton.primaryRoundedRectangle(
                  context: context,
                  title: AppLocale.current.actionPrevious,
                  buttonSize: ButtonSize.small,
                  onPressed: () {},
                ),
                SizedBox(width: context.theme.d.paddingMedium),
                CommonButton.primaryRoundedRectangle(
                  context: context,
                  title: AppLocale.current.actionNext,
                  buttonSize: ButtonSize.small,
                  onPressed: () {},
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
