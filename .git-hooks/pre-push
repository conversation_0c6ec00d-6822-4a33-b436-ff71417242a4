#!/usr/bin/env bash

set -euo pipefail

if ! [ -x "$(command -v flutter)" ] &>/dev/null; then
    echo "Please install flutter at version 3.29.3"
    exit 1
fi

dart format --line-length 100 .
flutter test
flutter analyze
flutter gen-l10n

if ! [ -x "$(command -v pandoc)" ] &>/dev/null; then
    echo "Please install pandoc at version 3.1"
    exit 1
fi

pandoc \
    --columns=100 \
    --standalone \
    --from=gfm \
    --to=gfm \
    --output=README.md \
    README.md
