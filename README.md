# Micromentor Flutter App

This is Micromentor's main mentoring frontend.

This Flutter based application was started as part of a 6-month Google.org Fellowship project in
collaboration with [Micromentor](https://www.micromentor.org). The Fellowship project started April
1st, 2023 and ended October 31st. The final commit from the Google Fellows is tagged with
[final-fellowship-commit](https://github.com/micromentor-team/mentoring-flutter-app/releases/tag/final-fellowship-commit).

After the Fellowship project, <PERSON>mentor further worked on app. This happens in another, private
git repository. Micromentor will periodically update the open source repository
\[mentoring-flutter-app\](<https://github.com/micromentor-team/mentoring-flutter-app>.

## Getting Started

### Backend (Server)

The app needs a backend that provides it with the business data. The communication between app and
backend uses [GraphQL](https://graphql.org/) through an HTTP(S) connection. The basic principle of
this is simple: The app sends POST requests in the form of JSON text and receives the response from
the backend, again as JSON text. To configure this, a single URL - that of the backend's GraphQL API
root URL - is needed. There is one more component to the communication: subscriptions that are based
on HTTP websocket connections. Subscriptions allow the backend to notify the app of events, again
using GraphQL to format those event messages.

The app won't be functional without having been connected to a backend using the two variables
configured in the app flavor, `APP_GRAPHQL_URL` and `APP_SUBSCRIPTION_URL`. If you run the backend
locally in a developer environment, you likely need to use localhost URLs. The app's default `dev`
configuration uses them as such:

    APP_GRAPHQL_URL="http://localhost:3000/mmdata/api/graphql"
    APP_SUBSCRIPTION_URL="ws://localhost:3000/mmdata/api/graphql"

For different situations, like running the backend on a remote server or running the app on an
Android emulator, different URLs will be needed. These should be configured as different flavors in
the `lib/flavor_config.dart` file.

To set up a local backend you need access to `https://github.com/micromentor-team/mmdata`. Follow
the ["Getting Started"
instructions](https://github.com/micromentor-team/mmdata/blob/main/docs/getting-started.md).

### Install the Flutter development environment and tools

This assumes you are using a Mac computer. You can also use a Linux or Windows computer, but most of
the developers on this project use MacOS computers that allowed us to run the App in an iOS
simulator.

1.  Install Xcode and command-line tools in the Apple App Store

2.  If you are using a MacOS computer with an Apple Silicon CPU, you may have to install Rosetta 2:
    `softwareupdate --install-rosetta`.

3.  [Install the Flutter SDK](https://docs.flutter.dev/get-started/install)

4.  [Install Android
    Studio](https://developer.android.com/studio?gclid=EAIaIQobChMImt-so7DX_gIVYotoCR3K4wxfEAAYASAAEgJTUvD_BwE&gclsrc=aw.ds)

Note: Running the app in an iOS simulator requires macOS

### Clone mm-app

The sources to the Flutter app are located at: <https://github.com/micromentor-team/mm-flutter-app>

    <NAME_EMAIL>:micromentor-team/mm-flutter-app.git

#### Install the dependencies

If you are using the Android Studio, open `pubspec.yaml` and click on `pub get` on top right to
install all the dependencies.

Or run this in the terminal:

`flutter pub get`

#### Connect Firebase account

This app uses Firebase services, and therefore requires setting up an active account with the
following services enabled:

- Crashlytics
- Analytics
- Google Cloud Messaging

The Android and iOS projects already contain the necessary configuration for using Firebase, but the
files that contain the API key are not included in the repository (added to .gitignore). The
following procedure allows you to log into your Firebase account and regenerate these files in your
environment:

1.  Install [Firebase
    CLI](https://firebase.google.com/docs/cli?authuser=0#install_the_firebase_cli).

2.  Run the `firebase login` command to authenticate to your Firebase account. Ensure there are no
    errors before moving to the next step.

3.  Install the FlutterFire CLI by running the following command:
    `dart pub global activate flutterfire_cli`

4.  Run the following command from the root directory of the Flutter project:
    `flutterfire configure --project=micromentor-d72ff`. This command will connect your Firebase
    project to your local development environment by generating necessary files. Your Firebase
    project ID (micromentor-d72ff) can be found in the Firebase console.

5.  In the FlutterFire configuration prompt, make sure to select: **android**, **ios**, and **web**.

6.  Once the command completes, ensure the following files were generated in your environment:

    - /ios/firebase_app_id_file.json

    - /lib/firebase_options.dart

    - /android/app/google-services.json

    - /ios/Runner/GoogleService-Info.plist

The keys and FirebaseOptions generated in /lib/firebase_options.dart file we don't include this in
repository. These data has been added in GitHub repository secrets.

This is how it works. Keep all the secrets in .env.dart file. encode this file using Base64 and add
encoded string in GitHub repository secrets under name "FIREBASE". A step has been added in GitHub
workflow which will decode that string and paste data in .env.dart file.

## Add .env.dart in lib/services/firebase for firebase and appcheck related with WEB.

    import 'package:firebase_core/firebase_core.dart';

    const String GOOGLE_WEB_CLIENT_ID = "";
    const String GOOGLE_ANDROID_CLIENT_ID = "";
    const String GOOGLE_IOS_CLIENT_ID = "";

    const FIREBASE_OPTIONS_WEB = const FirebaseOptions(
      apiKey: '',
      appId: '',
      messagingSenderId: '',
      projectId: '',
      authDomain: '',
      storageBucket: '',
      measurementId: '',
    );

    const FIREBASE_OPTIONS_ANDROID = const FirebaseOptions(
      apiKey: '',
      appId: '',
      messagingSenderId: '',
      projectId: '',
      storageBucket: '',
    );

    const FIREBASE_OPTIONS_IOS = const FirebaseOptions(
      apiKey: '',
      appId: '',
      messagingSenderId: '',
      projectId: '',
      storageBucket: '',
      iosBundleId: '',
    );

    const FIREBASE_OPTIONS_MACOS = const FirebaseOptions(
      apiKey: '',
      appId: '',
      messagingSenderId: '',
      projectId: '',
      storageBucket: '',
      iosClientId: '',
      iosBundleId: '',
    );

7.  Run `build_runner.sh`

In the terminal run:

    ./build_runner.sh

8.  Run the app and confirm that there are no errors.

If you encounter any problems during these steps, refer to the Firebase and FlutterFire
documentation.

#### Enable git hooks

Link contents of `.git-hooks` directory to `.git/hooks`:

    `cd .git/hooks/ && ln -sfn ../../.git-hooks/* .`

### Build and run the app

In Android Studio, select a target platform (Chrome web browser, Android Simulator, etc.) and then
click on the `Run main.dart` button to build and the run the app on that platform. Shortcut :
`Control + R` (macOS).

### Run the app with a specific flavor

The app has different flavors for different environments:

- The `local` flavor is used for running the app with a local backend server.
- The `dev` flavor is used for running the app with a remote development backend server.
- The `prod` flavor is used for running the app with a remote production environment. (As of March
  2024, this is still a remote staging environment, but will soon be replaced by a production
  environment).
- To be added soon: the `staging` flavor is used for running the app with a remote staging
  environment, for internal app testing.

From the command line, run:

    `flutter run --flavor dev`

If you are running a local backend server, you will need to run the backend server first. Then, run
the app with the `local` flavor. An example to run the app in a local Android emulator with the ID
`emulator-5554` is as follows:

    `flutter run --flavor local -d emulator-5554`

Device-specific arguments can be passed to the `flutter run` command to specify the target device.
For example, to run the app with the production flavor in chrome, use the following command:

    `flutter run --flavor prod -d chrome`

Get your device ID(s) by running `flutter devices` in a terminal. Then, pass the desired device ID
as an argument to the `-d` parameter of the `flutter run` command.

### Run Widgetbook

In Android Studio, select `Edit Configurations` in the Run menu. Copy configurations of main.dart
and edit name as `widgetbook`. Edit `Dart entrypoint` to the path of your Widgetbook's main.dart.
For e.g. `mm_flutter_app/widgetbook/main.dart` at the place of `mm_flutter_app/lib/main.dart`.

Or to run from terminal, execute `flutter run -t widgetbook/main.dart`.

### Run Tests

In Android Studio, Open the test.dart file. Select the Run menu. Click the Run 'tests in
counter_test.dart' option.

### GraphQL Codegen

The [GraphQL Codegen](https://pub.dev/documentation/graphql_codegen) package is used to generate
model classes from the GraphQL schema and operations. The `lib/schema` directory contains a file
with all operations to be used from the app (queries and mutations), and another file with the full
schema (enum, type definitions, etc.).

Running the command `dart run build_runner build` from the root of the project generates Dart
classes inside the `lib/__generated/schema` directory. Import and use these classes normally to
represent input and output of the operations defined. These model classes will already contain the
correct fields, types, and other useful functions to transform to/from JSON.

Rerun the code generation command every time the schema and operations change to ensure that the
model classes are up to date.

### Getting new changes from the public repo

GitHub does not allow us to change the visibility of a forked repo, as seen
[here](https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/working-with-forks/about-permissions-and-visibility-of-forks#about-visibility-of-forks).
so if we fork the open source repo, it must also be public.

It is still possible to pull changes from the public repo. Here's how to do it, with a local branch
that we'll name "public":

``` bash
git remote add public https://github.com/micromentor-team/mentoring-flutter-app.git
git pull public master # Creates a merge commit
git push origin master
```

Pushing changes from this app to the public repo is more complicated. It may be easiest to manually
copy the changes to each file in the public repo, and then commit and push them there.

### Documents and resources for review and reference

For all developers working on this project with Micromentor, here are a few resources that you will
need to refer to. Please ask a co-worker or manager to share any of these resources with you if you
are unable to access them. The most important resources are the handoff doc and the Asana portfolio.

- [Google.org handoff
  doc](https://docs.google.com/document/d/16-qb76aSBpGn51X3WziXGvpMBf9lHUoBXwXy2UTlZyM/edit?resourcekey=0-0C8MIYXsR-SZ1mwB7enytQ#heading=h.fcoaz09629di)
  from the end of October 2023. This is a fairly comprehensive document that describes the app and
  its features as of October 31, 2023. It is a good place to start if you are new to the project.
  Note that some of the specific details of outstanding work may have changed since this document
  was written. If you have any questions about the current state of the app, please ask.
- [Asana portfolio for the app](https://app.asana.com/0/portfolio/1205952382851153/list): this
  contains several other portfolios, and is the single source of truth for product requirements. The
  state of this is in flux and there are a lot of details. The most important thing to know is that
  we plan to use the `Finds` portfolio to track undefined work and potential bugs, and the `Issues`
  portfolio to track bugs and features that have been identified, defined, and prioritized. If you
  have any questions about how we are organizing our work in Asana, please ask.
- [Figma
  wireframes](https://www.figma.com/file/oi7ObNdCDWZGehXN9zKpnt/Micromentor-3.0-Fellowship-Design-Final?type=design&node-id=1%3A4&mode=design&t=jyexvhOOOTMKhj6i-1):
  this is where we keep our wireframes/designs. Some of the designs are still being worked on, so be
  sure to ask if you are unsure whether a design is ready to be worked on.
- [PRD (product requirements doc) from October
  2023](https://docs.google.com/document/d/1qJhrJBv0HitU5APKyzyK1Oom8Mdttm6H0RMb4o9uZGw/edit): a
  list of old product requirements from October, 2023. This document is a bit out of date, but as of
  November 2023, it is still a good thing to read at some point. If you have any questions about the
  current state of the app, please ask.
- [Micromentor Master
  Tracker](https://docs.google.com/spreadsheets/d/1g0gaF-RHutsuhf0RRJOGqPmZJAFekIbQ-1ZNw5I-x9s/edit?resourcekey=0-wlE2dDBlK53PDuqday6s-Q#gid=990680046):
  This doc contains some roadmap planning, so it has some ideas for the future of the app in 2-5
  years.
- [Flutter Documentation](https://flutter.dev/docs): generally, this is the best place to start when
  you have a question about Flutter. It has a lot of information, and it is well organized.

### Expectations for research

When writing up research for an app feature or technical decision, it is best if the research is
written up in a Markdown `.md` file in the `docs` directory. This is the best place to put research
because it is easy to link to from other documents, it is easy to find, and everyone who can access
the code base has access to the doc.

When writing up research, please include the following information:

- The question(s) that the research is trying to answer, or relevant user stories from Asana tasks
- Some possible solutions to the question(s)
- The pros and cons of each solution
- A single recommended solution (if possible based on the information uncovered in the research)
- If there are any source(s) used by the research, please link the source(s) and describe them
  briefly. A source may be something like a link to Flutter documentation, a link to a StackOverflow
  answer, a link to a GitHub issue in the `mm-app` or `flutter` core repo, a link to a blog post,
  etc.

### Building iOS app for CodeMagic and locally with manual code signing

Here are some docs to refer to related to building the app with manual code signing:

- [CodeMagic docs on iOS code signing](https://docs.codemagic.io/code-signing/ios-code-signing/)
- [CodeMagic docs on building a Flutter
  app](https://blog.codemagic.io/how-to-sign-flutter-apps-code-magic/)
- [CodeMagic docs on loading variables for
  Firebase](https://docs.codemagic.io/knowledge-firebase/load-firebase-configuration/)
- [Flutter docs](https://flutter.dev/docs/deployment/ios#code-signing)

Build the app locally with the command
`flutter build ipa --build-name=1.0.0 --build-number=9 --export-options-plist=ios/export.plist`,
modifying the build number and name as appropriate.

`./codemagic.yaml` and `./ios/export.plist` both contain configurations for building the app,
codemagic.yaml for building remotely and the export.plist for building locally. Those files need to
be edited when certifications or provisioning profiles change.

Another file which contains XCode configuration settings is
`./ios/Runner.xcodeproj/project.pbxproj`, which is edited when the project is opened in XCode. There
are several other configuration files which are less important but occasionally should be edited.

### Troubleshooting broken iOS Simulator

If the iOS Simulator on mac is not working, clearing the XCode cache as recommended in [this stack
overflow](https://stackoverflow.com/questions/72229589/flutter-xcode-error-unable-to-boot-the-simulator/74409402#74409402)
can help.

### Deploy a web app to Firebase Hosting

1.  Prerequisite: your app should have all necessary firebase files, as described in the
    `./docs/Flutter-Web-Deployment.md` doc.
2.  Run `sh ./release_to_firebase_hosting_web.sh --flavor dev --target test`. That will deploy to
    the app hosted at `test.micromentor.org`. The `--flavor` argument is optional and defaults to
    `dev`. The `--target` argument is optional and defaults to `test`. Other options for `--target`
    are defined in the .firebaserc/firebase.json files. There are only 3 targets right now:
    - `test` for test.micromentor.org
    - `dev` for dev.micromentor.org (this target also is automatically deployed to by GH when a PR
      is merged to `develop`)
    - `prod` for app.micromentor.org, iqlaa.micromentor.org, and ebrd.micromentor.org

If you want to manually build the Flutter web app instead of using the release script, run these
steps after the above step 1:

2.  Run `flutter build web --dart-define=FLUTTER_APP_FLAVOR=dev --source-maps` from the main
    directory of the app. If there is no `--dart-define` argument, building the web app will default
    to the `prod` flavor.
3.  Run `firebase deploy` from the main directory of the app. This will deploy the app to the
    `https://micromentor-d72ff.web.app/` URL, assuming `micromentor-d72ff` continues to be the name
    of the project.

Instead of running `firebase deploy`, you can also run `firebase serve` to serve the app locally.
This will allow you to test the app locally using the firebase dev tools before deploying it to the
web.

When releasing to `prod`, you should always put a git tag on the commit that is being released. Do
this with these commands:

``` bash
git tag -a v1.0.0 -m "Release v1.1.1. Adds new feature X and rolls back broken feature Y."
git push origin v1.0.0
```
