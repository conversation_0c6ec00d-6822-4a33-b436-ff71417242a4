# MicroMentor Mock Server

NOTICE: This is not maintained and will not work with any version of the app in 2024. The last
working version of the mock server is unknown, but is likely a commit in December 2023.

This is a mock server that allows the frontend application to be run and tested locally. As mock
server, it is extremely limited in the amount of user flows and actions that are supported.

Run the commands below in: mm-flutter-app/mm-mock-server

## Setting Up

1.  Install dependencies with `npm install`.
2.  Run the mock server with `npm start`.
3.  Verify that the mock server is running at `http://localhost:4000/`.
4.  Run the Flutter app to have it connect to the mocked server.

# Sending Push Notifications

First download the service account and name it `firebase_service_account.json` in this current
directory
(<https://firebase.google.com/docs/admin/setup#initialize_the_sdk_in_non-google_environments>).

1.  Get the token from the flutter app
2.  Change the push_notification script with the right token
3.  Run `npm run push_notification`
