// some values from the backend, copy-pasted/hard coded as constants here.
// we could make this list smaller if this file is too long. it was easy to copy-paste the entire lists of values.

// expertises

export const expertises = [
  {
    textId: 'accountingAndFinance', mm2Id: 'ACCOUNTING_AND_FINANCE',
    value: 'Accounting and Finance',
    translatedValue: 'Accounting and Finance',
  },
  {
    textId: 'humanResources', mm2Id: 'HUMAN_RESOURCES',
    value: 'Human Resources',
    translatedValue: 'Human Resources',
  },
  {
    textId: 'international', mm2Id: 'INTERNATIONAL',
    value: 'International',
    translatedValue: 'International',
  },
  {
    textId: 'lawAndLegal', mm2Id: 'LAW_AND_LEGAL',
    value: 'Law and Legal',
    translatedValue: 'Law and Legal',
  },
  {
    textId: 'management', mm2Id: 'MANAGEMENT',
    value: 'Management',
    translatedValue: 'Management',
  },
  {
    textId: 'marketing', mm2Id: 'MARKETING',
    value: 'Marketing',
    translatedValue: 'Marketing',
  },
  {
    textId: 'operations', mm2Id: 'OPERATIONS',
    value: 'Operations',
    translatedValue: 'Operations',
  },
  {
    textId: 'sales', mm2Id: 'SALES',
    value: 'Sales',
    translatedValue: 'Sales',
  },
  {
    textId: 'startingUp', mm2Id: 'STARTING_UP',
    value: 'Starting Up',
    translatedValue: 'Starting Up',
  },
  {
    textId: 'sustainability', mm2Id: 'SUSTAINABILITY',
    value: 'Sustainability',
    translatedValue: 'Sustainability',
  },
  {
    textId: 'technologyAndInternet', mm2Id: 'TECHNOLOGY_AND_INTERNET',
    value: 'Technology and Internet',
    translatedValue: 'Technology and Internet',
  },
];

// industries

export const industries = [{
  textId: 'accountingAndTaxServices',
  mm2Id: 'ACCOUNTING_AND_TAX_SERVICES',
  value: 'Accounting and Tax Services',
  translatedValue: 'Accounting and Tax Services',
},
{
  textId: 'agriculture',
  mm2Id: 'AGRICULTURE',
  value: 'Agriculture',
  translatedValue: 'Agriculture',
},
{
  textId: 'animalsAndPets',
  mm2Id: 'ANIMALS_AND_PETS',
  value: 'Animals and Pets',
  translatedValue: 'Animals and Pets',
},
{
  textId: 'architectureAndEngineering',
  mm2Id: 'ARCHITECTURE_AND_ENGINEERING',
  value: 'Architecture and Engineering',
  translatedValue: 'Architecture and Engineering',
},
{
  textId: 'artisanAndCraftWork',
  mm2Id: 'ARTISAN_AND_CRAFT_WORK',
  value: 'Artisan and Craft Work',
  translatedValue: 'Artisan and Craft Work',
},
{
  textId: 'autoAndBikeMechanic',
  mm2Id: 'AUTO_AND_BIKE_MECHANIC',
  value: 'Auto and Bike Mechanic',
  translatedValue: 'Auto and Bike Mechanic',
},
{
  textId: 'beauty',
  mm2Id: 'BEAUTY',
  value: 'Beauty',
  translatedValue: 'Beauty',
},
{
  textId: 'bookstore',
  mm2Id: 'BOOKSTORE',
  value: 'Bookstore',
  translatedValue: 'Bookstore',
},
{
  textId: 'businessConsultingAndCoaching',
  mm2Id: 'BUSINESS_CONSULTING_AND_COACHING',
  value: 'Business Consulting and Coaching',
  translatedValue: 'Business Consulting and Coaching',
},
{
  textId: 'childcare',
  mm2Id: 'CHILDCARE',
  value: 'Childcare',
  translatedValue: 'Childcare',
},
{
  textId: 'cleaningServices',
  mm2Id: 'CLEANING_SERVICES',
  value: 'Cleaning Services',
  translatedValue: 'Cleaning Services',
},
{
  textId: 'constructionAndContracting',
  mm2Id: 'CONSTRUCTION_AND_CONTRACTING',
  value: 'Construction and Contracting',
  translatedValue: 'Construction and Contracting',
},
{
  textId: 'counselingAndMentalHealth',
  mm2Id: 'COUNSELING_AND_MENTAL_HEALTH',
  value: 'Counseling and Mental Health',
  translatedValue: 'Counseling and Mental Health',
},
{
  textId: 'digitalMarketingAndSocialMedia',
  mm2Id: 'DIGITAL_MARKETING_AND_SOCIAL_MEDIA',
  value: 'Digital Marketing and Social Media',
  translatedValue: 'Digital Marketing and Social Media',
},
{
  textId: 'disabilityServices',
  mm2Id: 'DISABILITY_SERVICES',
  value: 'Disability Services',
  translatedValue: 'Disability Services',
},
{
  textId: 'distributionAndLogistics',
  mm2Id: 'DISTRIBUTION_AND_LOGISTICS',
  value: 'Distribution and Logistics',
  translatedValue: 'Distribution and Logistics',
},
{
  textId: 'eCommerce',
  mm2Id: 'E_COMMERCE',
  value: 'E-Commerce',
  translatedValue: 'E-Commerce',
},
{
  textId: 'education',
  mm2Id: 'EDUCATION',
  value: 'Education',
  translatedValue: 'Education',
},
{
  textId: 'elderAndHomeHealthCare',
  mm2Id: 'ELDER_AND_HOME_HEALTH_CARE',
  value: 'Elder and Home Health Care',
  translatedValue: 'Elder and Home Health Care',
},
{
  textId: 'entertainmentAndEvents',
  mm2Id: 'ENTERTAINMENT_AND_EVENTS',
  value: 'Entertainment and Events',
  translatedValue: 'Entertainment and Events',
},
{
  textId: 'exportAndImport',
  mm2Id: 'EXPORT_AND_IMPORT',
  value: 'Export and Import',
  translatedValue: 'Export and Import',
},
{
  textId: 'fashion',
  mm2Id: 'FASHION',
  value: 'Fashion',
  translatedValue: 'Fashion',
},
{
  textId: 'financialServicesAndInsurance',
  mm2Id: 'FINANCIAL_SERVICES_AND_INSURANCE',
  value: 'Financial Services and Insurance',
  translatedValue: 'Financial Services and Insurance',
},
{
  textId: 'flowersAndGifts',
  mm2Id: 'FLOWERS_AND_GIFTS',
  value: 'Flowers and Gifts',
  translatedValue: 'Flowers and Gifts',
},
{
  textId: 'foodAndGrocery',
  mm2Id: 'FOOD_AND_GROCERY',
  value: 'Food and Grocery',
  translatedValue: 'Food and Grocery',
},
{
  textId: 'forestry',
  mm2Id: 'FORESTRY',
  value: 'Forestry',
  translatedValue: 'Forestry',
},
{
  textId: 'furniture',
  mm2Id: 'FURNITURE',
  value: 'Furniture',
  translatedValue: 'Furniture',
},
{
  textId: 'graphicAndWebDesign',
  mm2Id: 'GRAPHIC_AND_WEB_DESIGN',
  value: 'Graphic and Web Design',
  translatedValue: 'Graphic and Web Design',
},
{
  textId: 'healthAndWellness',
  mm2Id: 'HEALTH_AND_WELLNESS',
  value: 'Health and Wellness',
  translatedValue: 'Health and Wellness',
},
{
  textId: 'informationTechnologyServices',
  mm2Id: 'INFORMATION_TECHNOLOGY_SERVICES',
  value: 'Information Technology Services',
  translatedValue: 'Information Technology Services',
},
{
  textId: 'jewelryAndLuxuryGoods',
  mm2Id: 'JEWELRY_AND_LUXURY_GOODS',
  value: 'Jewelry and Luxury Goods',
  translatedValue: 'Jewelry and Luxury Goods',
},
{
  textId: 'landscaping',
  mm2Id: 'LANDSCAPING',
  value: 'Landscaping',
  translatedValue: 'Landscaping',
},
{
  textId: 'laundryAndTailoring',
  mm2Id: 'LAUNDRY_AND_TAILORING',
  value: 'Laundry and Tailoring',
  translatedValue: 'Laundry and Tailoring',
},
{
  textId: 'legalServices',
  mm2Id: 'LEGAL_SERVICES',
  value: 'Legal Services',
  translatedValue: 'Legal Services',
},
{
  textId: 'manufacturing',
  mm2Id: 'MANUFACTURING',
  value: 'Manufacturing',
  translatedValue: 'Manufacturing',
},
{
  textId: 'marketingAndAdvertising',
  mm2Id: 'MARKETING_AND_ADVERTISING',
  value: 'Marketing and Advertising',
  translatedValue: 'Marketing and Advertising',
},
{
  textId: 'mediaAndPublishing',
  mm2Id: 'MEDIA_AND_PUBLISHING',
  value: 'Media and Publishing',
  translatedValue: 'Media and Publishing',
},
{
  textId: 'nonprofitAndSocialEnterprise',
  mm2Id: 'NONPROFIT_AND_SOCIAL_ENTERPRISE',
  value: 'Nonprofit and Social Enterprise',
  translatedValue: 'Nonprofit and Social Enterprise',
},
{
  textId: 'performingArts',
  mm2Id: 'PERFORMING_ARTS',
  value: 'Performing Arts',
  translatedValue: 'Performing Arts',
},
{
  textId: 'personalAndExecutiveAssistance',
  mm2Id: 'PERSONAL_AND_EXECUTIVE_ASSISTANCE',
  value: 'Personal and Executive Assistance',
  translatedValue: 'Personal and Executive Assistance',
},
{
  textId: 'photographyAndAvServices',
  mm2Id: 'PHOTOGRAPHY_AND_AV_SERVICES',
  value: 'Photography and A/V Services',
  translatedValue: 'Photography and A/V Services',
},
{
  textId: 'publicRelations',
  mm2Id: 'PUBLIC_RELATIONS',
  value: 'Public Relations',
  translatedValue: 'Public Relations',
},
{
  textId: 'realEstate',
  mm2Id: 'REAL_ESTATE',
  value: 'Real Estate',
  translatedValue: 'Real Estate',
},
{
  textId: 'recreationAndOutdoorFitness',
  mm2Id: 'RECREATION_AND_OUTDOOR_FITNESS',
  value: 'Recreation and Outdoor Fitness',
  translatedValue: 'Recreation and Outdoor Fitness',
},
{
  textId: 'recruitingAndStaffing',
  mm2Id: 'RECRUITING_AND_STAFFING',
  value: 'Recruiting and Staffing',
  translatedValue: 'Recruiting and Staffing',
},
{
  textId: 'restaurantAndCatering',
  mm2Id: 'RESTAURANT_AND_CATERING',
  value: 'Restaurant and Catering',
  translatedValue: 'Restaurant and Catering',
},
{
  textId: 'retail',
  mm2Id: 'RETAIL',
  value: 'Retail',
  translatedValue: 'Retail',
},
{
  textId: 'sustainability',
  mm2Id: 'SUSTAINABILITY',
  value: 'Sustainability',
  translatedValue: 'Sustainability',
},
{
  textId: 'taxiAndLimoServices',
  mm2Id: 'TAXI_AND_LIMO_SERVICES',
  value: 'Taxi and Limo Services',
  translatedValue: 'Taxi and Limo Services',
},
{
  textId: 'translationAndLocalization',
  mm2Id: 'TRANSLATION_AND_LOCALIZATION',
  value: 'Translation and Localization',
  translatedValue: 'Translation and Localization',
},
{
  textId: 'travelAndHospitality',
  mm2Id: 'TRAVEL_AND_HOSPITALITY',
  value: 'Travel and Hospitality',
  translatedValue: 'Travel and Hospitality',
},
{
  textId: 'veterinary',
  mm2Id: 'VETERINARY',
  value: 'Veterinary',
  translatedValue: 'Veterinary',
},
{
  textId: 'webAndTechnology',
  mm2Id: 'WEB_AND_TECHNOLOGY',
  value: 'Web and Technology',
  translatedValue: 'Web and Technology',
},
{
  textId: 'wineAndSpirits',
  mm2Id: 'WINE_AND_SPIRITS',
  value: 'Wine and Spirits',
  translatedValue: 'Wine and Spirits',
},
{
  textId: 'writingAndEditing',
  mm2Id: 'WRITING_AND_EDITING',
  value: 'Writing and Editing',
  translatedValue: 'Writing and Editing',
},
];

// languages

export const languages = [
  { 'mm2Value': 'ara', 'textId': 'ar', 'shortLangCode': 'ar', 'longLangCode': 'ara', 'value': 'Arabic', 'translatedValue': 'Arabic', 'isUiLanguage': true },
  { 'mm2Value': 'ben', 'textId': 'bn', 'shortLangCode': 'bn', 'longLangCode': 'ben', 'value': 'Bengali', 'translatedValue': 'Bengali' },
  { 'mm2Value': 'mya', 'textId': 'my', 'shortLangCode': 'my', 'longLangCode': 'mya', 'value': 'Burmese', 'translatedValue': 'Burmese' },
  { 'mm2Value': 'yue', 'textId': 'zh', 'shortLangCode': 'zh', 'longLangCode': 'yue', 'value': 'Cantonese', 'translatedValue': 'Cantonese' },
  { 'mm2Value': 'eng', 'textId': 'en', 'shortLangCode': 'en', 'longLangCode': 'eng', 'value': 'English', 'translatedValue': 'English', 'isUiLanguage': true },
  { 'mm2Value': 'fra', 'textId': 'fr', 'shortLangCode': 'fr', 'longLangCode': 'fra', 'value': 'French', 'translatedValue': 'French' },
  { 'mm2Value': 'deu', 'textId': 'de', 'shortLangCode': 'de', 'longLangCode': 'deu', 'value': 'German', 'translatedValue': 'German' },
  { 'mm2Value': 'hin', 'textId': 'hi', 'shortLangCode': 'hi', 'longLangCode': 'hin', 'value': 'Hindi', 'translatedValue': 'Hindi' },
  { 'mm2Value': 'hat', 'textId': 'ht', 'shortLangCode': 'ht', 'longLangCode': 'hat', 'value': 'Haitian', 'translatedValue': 'Haitian' },
  { 'mm2Value': 'ind', 'textId': 'id', 'shortLangCode': 'id', 'longLangCode': 'ind', 'value': 'Bahasa Indonesia', 'translatedValue': 'Bahasa Indonesia', 'isUiLanguage': true },
  { 'mm2Value': 'jpn', 'textId': 'ja', 'shortLangCode': 'ja', 'longLangCode': 'jpn', 'value': 'Japanese', 'translatedValue': 'Japanese' },
  { 'mm2Value': 'kor', 'textId': 'ko', 'shortLangCode': 'ko', 'longLangCode': 'kor', 'value': 'Korean', 'translatedValue': 'Korean' },
  { 'mm2Value': 'cmn', 'textId': 'zh', 'shortLangCode': 'zh', 'longLangCode': 'cmn', 'value': 'Mandarin', 'translatedValue': 'Mandarin' },
  { 'mm2Value': 'por', 'textId': 'pt', 'shortLangCode': 'pt', 'longLangCode': 'por', 'value': 'Portuguese', 'translatedValue': 'Portuguese' },
  { 'mm2Value': 'rus', 'textId': 'ru', 'shortLangCode': 'ru', 'longLangCode': 'rus', 'value': 'Russian', 'translatedValue': 'Russian', 'isUiLanguage': true },
  { 'mm2Value': 'slk', 'textId': 'sk', 'shortLangCode': 'sk', 'longLangCode': 'slk', 'value': 'Slovak', 'translatedValue': 'Slovak' },
  { 'mm2Value': 'som', 'textId': 'so', 'shortLangCode': 'so', 'longLangCode': 'som', 'value': 'Somali', 'translatedValue': 'Somali', 'isUiLanguage': true },
  { 'mm2Value': 'spa', 'textId': 'es', 'shortLangCode': 'es', 'longLangCode': 'spa', 'value': 'Spanish', 'translatedValue': 'Spanish', 'isUiLanguage': true },
  { 'mm2Value': 'tgl', 'textId': 'tl', 'shortLangCode': 'tl', 'longLangCode': 'tgl', 'value': 'Tagalog', 'translatedValue': 'Tagalog' },
  { 'mm2Value': 'tha', 'textId': 'th', 'shortLangCode': 'th', 'longLangCode': 'tha', 'value': 'Thai', 'translatedValue': 'Thai' },
  { 'mm2Value': 'urd', 'textId': 'ur', 'shortLangCode': 'ur', 'longLangCode': 'urd', 'value': 'Urdu', 'translatedValue': 'Urdu' },
  { 'mm2Value': 'vie', 'textId': 'vi', 'shortLangCode': 'vi', 'longLangCode': 'vie', 'value': 'Vietnamese', 'translatedValue': 'Vietnamese' },
]

// company stages

export const companyStages = [
  {
    textId: 'idea',
    value: 'Idea',
    translatedValue: 'Idea',
    mm2Value: 'idea',
  },
  {
    textId: 'operational',
    value: 'Operational',
    translatedValue: 'Operational',
    mm2Value: 'operational',
  },
  {
    textId: 'earning',
    value: 'Earning',
    translatedValue: 'Earning',
    mm2Value: 'earning',
  },
  {
    textId: 'profitable',
    value: 'Profitable',
    translatedValue: 'Profitable',
    mm2Value: 'profitable',
  },
]

// company types

export const companyTypes = [
  {
    textId: 'forProfit',
    value: 'For-profit venture',
    translatedValue: 'For-profit venture',
    mm2Value: 'for-profit',
  },
  {
    textId: 'nonProfit',
    value: 'Non-profit organization',
    translatedValue: 'Non-profit organization',
    mm2Value: 'non-profit',
  },
  {
    textId: 'socialEnterprise',
    value: 'Social enterprise',
    translatedValue: 'Social enterprise',
    mm2Value: 'social_enterprise',
  },
  {
    textId: 'unsure',
    value: 'Unsure',
    translatedValue: 'Unsure',
    mm2Value: 'unsure',
  },
]

// education levels

export const educationLevels = [
  {
    textId: 'doctorate',
    value: 'Doctorate - PhD',
    translatedValue: 'Doctorate - PhD',
    mm2Value: 'Doctorate - PhD',
  },
  {
    textId: 'postgraduate',
    value: 'Postgraduate - Master\'s Degree/Honors Degree/J.D.',
    translatedValue: 'Postgraduate - Master\'s Degree/Honors Degree/J.D.',
    mm2Value: 'Postgraduate - Master\'s Degree/Honors Degree/J.D.',
  },
  {
    textId: 'undergraduate',
    value: 'Undergraduate - Bachelor\'s Degree',
    translatedValue: 'Undergraduate - Bachelor\'s Degree',
    mm2Value: 'Undergraduate - Bachelor\'s Degree',
  },
  {
    textId: 'postSecondary',
    value: 'Post-Secondary - Technical or Vocational Degree',
    translatedValue: 'Post-Secondary - Technical or Vocational Degree',
    mm2Value: 'Post-Secondary - Technical or Vocational Degree',
  },
  {
    textId: 'highSchool',
    value: 'Secondary - High School',
    translatedValue: 'Secondary - High School',
    mm2Value: 'Secondary - High School',
  },
  {
    textId: 'middleSchool',
    value: 'Secondary - Middle School or Junior High',
    translatedValue: 'Secondary - Middle School or Junior High',
    mm2Value: 'Secondary - Middle School or Junior High',
  },
  {
    textId: 'primary',
    value: 'Primary - Elementary School',
    translatedValue: 'Primary - Elementary School',
    mm2Value: 'Primary - Elementary School',
  },
];

// countries

export const countries = [{ "alpha2Key": "AF", "alpha3Key": "AFG", "value": "Afghanistan", "translatedValue": "Afghanistan", "textId": "AF", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "AL", "alpha3Key": "ALB", "value": "Albania", "translatedValue": "Albania", "textId": "AL", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "DZ", "alpha3Key": "DZA", "value": "Algeria", "translatedValue": "Algeria", "textId": "DZ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "AS", "alpha3Key": "ASM", "value": "American Samoa", "translatedValue": "American Samoa", "textId": "AS", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "AD", "alpha3Key": "AND", "value": "Andorra", "translatedValue": "Andorra", "textId": "AD", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "AO", "alpha3Key": "AGO", "value": "Angola", "translatedValue": "Angola", "textId": "AO", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "AI", "alpha3Key": "AIA", "value": "Anguilla", "translatedValue": "Anguilla", "textId": "AI", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "AQ", "alpha3Key": "ATA", "value": "Antarctica", "translatedValue": "Antarctica", "textId": "AQ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "AG", "alpha3Key": "ATG", "value": "Antigua and Barbuda", "translatedValue": "Antigua and Barbuda", "textId": "AG", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "AR", "alpha3Key": "ARG", "value": "Argentina", "translatedValue": "Argentina", "textId": "AR", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "AM", "alpha3Key": "ARM", "value": "Armenia", "translatedValue": "Armenia", "textId": "AM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "AW", "alpha3Key": "ABW", "value": "Aruba", "translatedValue": "Aruba", "textId": "AW", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "AU", "alpha3Key": "AUS", "value": "Australia", "translatedValue": "Australia", "textId": "AU", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "AT", "alpha3Key": "AUT", "value": "Austria", "translatedValue": "Austria", "textId": "AT", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "AZ", "alpha3Key": "AZE", "value": "Azerbaijan", "translatedValue": "Azerbaijan", "textId": "AZ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BS", "alpha3Key": "BHS", "value": "Bahamas", "translatedValue": "Bahamas", "textId": "BS", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BH", "alpha3Key": "BHR", "value": "Bahrain", "translatedValue": "Bahrain", "textId": "BH", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BD", "alpha3Key": "BGD", "value": "Bangladesh", "translatedValue": "Bangladesh", "textId": "BD", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BB", "alpha3Key": "BRB", "value": "Barbados", "translatedValue": "Barbados", "textId": "BB", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BY", "alpha3Key": "BLR", "value": "Belarus", "translatedValue": "Belarus", "textId": "BY", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BE", "alpha3Key": "BEL", "value": "Belgium", "translatedValue": "Belgium", "textId": "BE", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BZ", "alpha3Key": "BLZ", "value": "Belize", "translatedValue": "Belize", "textId": "BZ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BJ", "alpha3Key": "BEN", "value": "Benin", "translatedValue": "Benin", "textId": "BJ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BM", "alpha3Key": "BMU", "value": "Bermuda", "translatedValue": "Bermuda", "textId": "BM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BT", "alpha3Key": "BTN", "value": "Bhutan", "translatedValue": "Bhutan", "textId": "BT", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BO", "alpha3Key": "BOL", "value": "Bolivia", "translatedValue": "Bolivia", "textId": "BO", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BA", "alpha3Key": "BIH", "value": "Bosnia and Herzegovina", "translatedValue": "Bosnia and Herzegovina", "textId": "BA", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BW", "alpha3Key": "BWA", "value": "Botswana", "translatedValue": "Botswana", "textId": "BW", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BV", "alpha3Key": "BVT", "value": "Bouvet Island", "translatedValue": "Bouvet Island", "textId": "BV", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BR", "alpha3Key": "BRA", "value": "Brazil", "translatedValue": "Brazil", "textId": "BR", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "IO", "alpha3Key": "IOT", "value": "British Indian Ocean Territory", "translatedValue": "British Indian Ocean Territory", "textId": "IO", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BN", "alpha3Key": "BRN", "value": "Brunei Darussalam", "translatedValue": "Brunei Darussalam", "textId": "BN", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BG", "alpha3Key": "BGR", "value": "Bulgaria", "translatedValue": "Bulgaria", "textId": "BG", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BF", "alpha3Key": "BFA", "value": "Burkina Faso", "translatedValue": "Burkina Faso", "textId": "BF", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BI", "alpha3Key": "BDI", "value": "Burundi", "translatedValue": "Burundi", "textId": "BI", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "KH", "alpha3Key": "KHM", "value": "Cambodia", "translatedValue": "Cambodia", "textId": "KH", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CM", "alpha3Key": "CMR", "value": "Cameroon", "translatedValue": "Cameroon", "textId": "CM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CA", "alpha3Key": "CAN", "value": "Canada", "translatedValue": "Canada", "textId": "CA", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CV", "alpha3Key": "CPV", "value": "Cape Verde", "translatedValue": "Cape Verde", "textId": "CV", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "KY", "alpha3Key": "CYM", "value": "Cayman Islands", "translatedValue": "Cayman Islands", "textId": "KY", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CF", "alpha3Key": "CAF", "value": "Central African Republic", "translatedValue": "Central African Republic", "textId": "CF", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "TD", "alpha3Key": "TCD", "value": "Chad", "translatedValue": "Chad", "textId": "TD", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CL", "alpha3Key": "CHL", "value": "Chile", "translatedValue": "Chile", "textId": "CL", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CN", "alpha3Key": "CHN", "value": "People's Republic of China", "translatedValue": "People's Republic of China", "textId": "CN", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CX", "alpha3Key": "CXR", "value": "Christmas Island", "translatedValue": "Christmas Island", "textId": "CX", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CC", "alpha3Key": "CCK", "value": "Cocos (Keeling) Islands", "translatedValue": "Cocos (Keeling) Islands", "textId": "CC", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CO", "alpha3Key": "COL", "value": "Colombia", "translatedValue": "Colombia", "textId": "CO", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "KM", "alpha3Key": "COM", "value": "Comoros", "translatedValue": "Comoros", "textId": "KM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CG", "alpha3Key": "COG", "value": "Republic of the Congo", "translatedValue": "Republic of the Congo", "textId": "CG", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CD", "alpha3Key": "COD", "value": "Democratic Republic of the Congo", "translatedValue": "Democratic Republic of the Congo", "textId": "CD", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CK", "alpha3Key": "COK", "value": "Cook Islands", "translatedValue": "Cook Islands", "textId": "CK", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CR", "alpha3Key": "CRI", "value": "Costa Rica", "translatedValue": "Costa Rica", "textId": "CR", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CI", "alpha3Key": "CIV", "value": "Cote D'Ivoire", "translatedValue": "Cote D'Ivoire", "textId": "CI", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "HR", "alpha3Key": "HRV", "value": "Croatia", "translatedValue": "Croatia", "textId": "HR", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CU", "alpha3Key": "CUB", "value": "Cuba", "translatedValue": "Cuba", "textId": "CU", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CY", "alpha3Key": "CYP", "value": "Cyprus", "translatedValue": "Cyprus", "textId": "CY", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CZ", "alpha3Key": "CZE", "value": "Czech Republic", "translatedValue": "Czech Republic", "textId": "CZ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "DK", "alpha3Key": "DNK", "value": "Denmark", "translatedValue": "Denmark", "textId": "DK", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "DJ", "alpha3Key": "DJI", "value": "Djibouti", "translatedValue": "Djibouti", "textId": "DJ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "DM", "alpha3Key": "DMA", "value": "Dominica", "translatedValue": "Dominica", "textId": "DM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "DO", "alpha3Key": "DOM", "value": "Dominican Republic", "translatedValue": "Dominican Republic", "textId": "DO", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "EC", "alpha3Key": "ECU", "value": "Ecuador", "translatedValue": "Ecuador", "textId": "EC", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "EG", "alpha3Key": "EGY", "value": "Egypt", "translatedValue": "Egypt", "textId": "EG", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SV", "alpha3Key": "SLV", "value": "El Salvador", "translatedValue": "El Salvador", "textId": "SV", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GQ", "alpha3Key": "GNQ", "value": "Equatorial Guinea", "translatedValue": "Equatorial Guinea", "textId": "GQ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "ER", "alpha3Key": "ERI", "value": "Eritrea", "translatedValue": "Eritrea", "textId": "ER", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "EE", "alpha3Key": "EST", "value": "Estonia", "translatedValue": "Estonia", "textId": "EE", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "ET", "alpha3Key": "ETH", "value": "Ethiopia", "translatedValue": "Ethiopia", "textId": "ET", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "FK", "alpha3Key": "FLK", "value": "Falkland Islands (Malvinas)", "translatedValue": "Falkland Islands (Malvinas)", "textId": "FK", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "FO", "alpha3Key": "FRO", "value": "Faroe Islands", "translatedValue": "Faroe Islands", "textId": "FO", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "FJ", "alpha3Key": "FJI", "value": "Fiji", "translatedValue": "Fiji", "textId": "FJ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "FI", "alpha3Key": "FIN", "value": "Finland", "translatedValue": "Finland", "textId": "FI", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "FR", "alpha3Key": "FRA", "value": "France", "translatedValue": "France", "textId": "FR", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GF", "alpha3Key": "GUF", "value": "French Guiana", "translatedValue": "French Guiana", "textId": "GF", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "PF", "alpha3Key": "PYF", "value": "French Polynesia", "translatedValue": "French Polynesia", "textId": "PF", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "TF", "alpha3Key": "ATF", "value": "French Southern Territories", "translatedValue": "French Southern Territories", "textId": "TF", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GA", "alpha3Key": "GAB", "value": "Gabon", "translatedValue": "Gabon", "textId": "GA", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GM", "alpha3Key": "GMB", "value": "Republic of The Gambia", "translatedValue": "Republic of The Gambia", "textId": "GM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GE", "alpha3Key": "GEO", "value": "Georgia", "translatedValue": "Georgia", "textId": "GE", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "DE", "alpha3Key": "DEU", "value": "Germany", "translatedValue": "Germany", "textId": "DE", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GH", "alpha3Key": "GHA", "value": "Ghana", "translatedValue": "Ghana", "textId": "GH", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GI", "alpha3Key": "GIB", "value": "Gibraltar", "translatedValue": "Gibraltar", "textId": "GI", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GR", "alpha3Key": "GRC", "value": "Greece", "translatedValue": "Greece", "textId": "GR", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GL", "alpha3Key": "GRL", "value": "Greenland", "translatedValue": "Greenland", "textId": "GL", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GD", "alpha3Key": "GRD", "value": "Grenada", "translatedValue": "Grenada", "textId": "GD", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GP", "alpha3Key": "GLP", "value": "Guadeloupe", "translatedValue": "Guadeloupe", "textId": "GP", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GU", "alpha3Key": "GUM", "value": "Guam", "translatedValue": "Guam", "textId": "GU", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GT", "alpha3Key": "GTM", "value": "Guatemala", "translatedValue": "Guatemala", "textId": "GT", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GN", "alpha3Key": "GIN", "value": "Guinea", "translatedValue": "Guinea", "textId": "GN", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GW", "alpha3Key": "GNB", "value": "Guinea-Bissau", "translatedValue": "Guinea-Bissau", "textId": "GW", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GY", "alpha3Key": "GUY", "value": "Guyana", "translatedValue": "Guyana", "textId": "GY", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "HT", "alpha3Key": "HTI", "value": "Haiti", "translatedValue": "Haiti", "textId": "HT", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "HM", "alpha3Key": "HMD", "value": "Heard Island and McDonald Islands", "translatedValue": "Heard Island and McDonald Islands", "textId": "HM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "VA", "alpha3Key": "VAT", "value": "Holy See (Vatican City State)", "translatedValue": "Holy See (Vatican City State)", "textId": "VA", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "HN", "alpha3Key": "HND", "value": "Honduras", "translatedValue": "Honduras", "textId": "HN", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "HK", "alpha3Key": "HKG", "value": "Hong Kong", "translatedValue": "Hong Kong", "textId": "HK", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "HU", "alpha3Key": "HUN", "value": "Hungary", "translatedValue": "Hungary", "textId": "HU", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "IS", "alpha3Key": "ISL", "value": "Iceland", "translatedValue": "Iceland", "textId": "IS", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "IN", "alpha3Key": "IND", "value": "India", "translatedValue": "India", "textId": "IN", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "ID", "alpha3Key": "IDN", "value": "Indonesia", "translatedValue": "Indonesia", "textId": "ID", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "IR", "alpha3Key": "IRN", "value": "Islamic Republic of Iran", "translatedValue": "Islamic Republic of Iran", "textId": "IR", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "IQ", "alpha3Key": "IRQ", "value": "Iraq", "translatedValue": "Iraq", "textId": "IQ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "IE", "alpha3Key": "IRL", "value": "Ireland", "translatedValue": "Ireland", "textId": "IE", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "IL", "alpha3Key": "ISR", "value": "Israel", "translatedValue": "Israel", "textId": "IL", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "IT", "alpha3Key": "ITA", "value": "Italy", "translatedValue": "Italy", "textId": "IT", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "JM", "alpha3Key": "JAM", "value": "Jamaica", "translatedValue": "Jamaica", "textId": "JM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "JP", "alpha3Key": "JPN", "value": "Japan", "translatedValue": "Japan", "textId": "JP", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "JO", "alpha3Key": "JOR", "value": "Jordan", "translatedValue": "Jordan", "textId": "JO", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "KZ", "alpha3Key": "KAZ", "value": "Kazakhstan", "translatedValue": "Kazakhstan", "textId": "KZ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "KE", "alpha3Key": "KEN", "value": "Kenya", "translatedValue": "Kenya", "textId": "KE", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "KI", "alpha3Key": "KIR", "value": "Kiribati", "translatedValue": "Kiribati", "textId": "KI", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "KP", "alpha3Key": "PRK", "value": "North Korea", "translatedValue": "North Korea", "textId": "KP", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "KR", "alpha3Key": "KOR", "value": "South Korea", "translatedValue": "South Korea", "textId": "KR", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "KW", "alpha3Key": "KWT", "value": "Kuwait", "translatedValue": "Kuwait", "textId": "KW", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "KG", "alpha3Key": "KGZ", "value": "Kyrgyzstan", "translatedValue": "Kyrgyzstan", "textId": "KG", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "LA", "alpha3Key": "LAO", "value": "Lao People's Democratic Republic", "translatedValue": "Lao People's Democratic Republic", "textId": "LA", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "LV", "alpha3Key": "LVA", "value": "Latvia", "translatedValue": "Latvia", "textId": "LV", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "LB", "alpha3Key": "LBN", "value": "Lebanon", "translatedValue": "Lebanon", "textId": "LB", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "LS", "alpha3Key": "LSO", "value": "Lesotho", "translatedValue": "Lesotho", "textId": "LS", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "LR", "alpha3Key": "LBR", "value": "Liberia", "translatedValue": "Liberia", "textId": "LR", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "LY", "alpha3Key": "LBY", "value": "Libya", "translatedValue": "Libya", "textId": "LY", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "LI", "alpha3Key": "LIE", "value": "Liechtenstein", "translatedValue": "Liechtenstein", "textId": "LI", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "LT", "alpha3Key": "LTU", "value": "Lithuania", "translatedValue": "Lithuania", "textId": "LT", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "LU", "alpha3Key": "LUX", "value": "Luxembourg", "translatedValue": "Luxembourg", "textId": "LU", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MO", "alpha3Key": "MAC", "value": "Macao", "translatedValue": "Macao", "textId": "MO", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MG", "alpha3Key": "MDG", "value": "Madagascar", "translatedValue": "Madagascar", "textId": "MG", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MW", "alpha3Key": "MWI", "value": "Malawi", "translatedValue": "Malawi", "textId": "MW", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MY", "alpha3Key": "MYS", "value": "Malaysia", "translatedValue": "Malaysia", "textId": "MY", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MV", "alpha3Key": "MDV", "value": "Maldives", "translatedValue": "Maldives", "textId": "MV", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "ML", "alpha3Key": "MLI", "value": "Mali", "translatedValue": "Mali", "textId": "ML", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MT", "alpha3Key": "MLT", "value": "Malta", "translatedValue": "Malta", "textId": "MT", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MH", "alpha3Key": "MHL", "value": "Marshall Islands", "translatedValue": "Marshall Islands", "textId": "MH", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MQ", "alpha3Key": "MTQ", "value": "Martinique", "translatedValue": "Martinique", "textId": "MQ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MR", "alpha3Key": "MRT", "value": "Mauritania", "translatedValue": "Mauritania", "textId": "MR", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MU", "alpha3Key": "MUS", "value": "Mauritius", "translatedValue": "Mauritius", "textId": "MU", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "YT", "alpha3Key": "MYT", "value": "Mayotte", "translatedValue": "Mayotte", "textId": "YT", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MX", "alpha3Key": "MEX", "value": "Mexico", "translatedValue": "Mexico", "textId": "MX", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "FM", "alpha3Key": "FSM", "value": "Micronesia, Federated States of", "translatedValue": "Micronesia, Federated States of", "textId": "FM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MD", "alpha3Key": "MDA", "value": "Moldova, Republic of", "translatedValue": "Moldova, Republic of", "textId": "MD", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MC", "alpha3Key": "MCO", "value": "Monaco", "translatedValue": "Monaco", "textId": "MC", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MN", "alpha3Key": "MNG", "value": "Mongolia", "translatedValue": "Mongolia", "textId": "MN", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MS", "alpha3Key": "MSR", "value": "Montserrat", "translatedValue": "Montserrat", "textId": "MS", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MA", "alpha3Key": "MAR", "value": "Morocco", "translatedValue": "Morocco", "textId": "MA", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MZ", "alpha3Key": "MOZ", "value": "Mozambique", "translatedValue": "Mozambique", "textId": "MZ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MM", "alpha3Key": "MMR", "value": "Myanmar", "translatedValue": "Myanmar", "textId": "MM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "NA", "alpha3Key": "NAM", "value": "Namibia", "translatedValue": "Namibia", "textId": "NA", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "NR", "alpha3Key": "NRU", "value": "Nauru", "translatedValue": "Nauru", "textId": "NR", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "NP", "alpha3Key": "NPL", "value": "Nepal", "translatedValue": "Nepal", "textId": "NP", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "NL", "alpha3Key": "NLD", "value": "Netherlands", "translatedValue": "Netherlands", "textId": "NL", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "NC", "alpha3Key": "NCL", "value": "New Caledonia", "translatedValue": "New Caledonia", "textId": "NC", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "NZ", "alpha3Key": "NZL", "value": "New Zealand", "translatedValue": "New Zealand", "textId": "NZ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "NI", "alpha3Key": "NIC", "value": "Nicaragua", "translatedValue": "Nicaragua", "textId": "NI", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "NE", "alpha3Key": "NER", "value": "Niger", "translatedValue": "Niger", "textId": "NE", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "NG", "alpha3Key": "NGA", "value": "Nigeria", "translatedValue": "Nigeria", "textId": "NG", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "NU", "alpha3Key": "NIU", "value": "Niue", "translatedValue": "Niue", "textId": "NU", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "NF", "alpha3Key": "NFK", "value": "Norfolk Island", "translatedValue": "Norfolk Island", "textId": "NF", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MK", "alpha3Key": "MKD", "value": "The Republic of North Macedonia", "translatedValue": "The Republic of North Macedonia", "textId": "MK", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MP", "alpha3Key": "MNP", "value": "Northern Mariana Islands", "translatedValue": "Northern Mariana Islands", "textId": "MP", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "NO", "alpha3Key": "NOR", "value": "Norway", "translatedValue": "Norway", "textId": "NO", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "OM", "alpha3Key": "OMN", "value": "Oman", "translatedValue": "Oman", "textId": "OM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "PK", "alpha3Key": "PAK", "value": "Pakistan", "translatedValue": "Pakistan", "textId": "PK", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "PW", "alpha3Key": "PLW", "value": "Palau", "translatedValue": "Palau", "textId": "PW", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "PS", "alpha3Key": "PSE", "value": "State of Palestine", "translatedValue": "State of Palestine", "textId": "PS", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "PA", "alpha3Key": "PAN", "value": "Panama", "translatedValue": "Panama", "textId": "PA", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "PG", "alpha3Key": "PNG", "value": "Papua New Guinea", "translatedValue": "Papua New Guinea", "textId": "PG", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "PY", "alpha3Key": "PRY", "value": "Paraguay", "translatedValue": "Paraguay", "textId": "PY", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "PE", "alpha3Key": "PER", "value": "Peru", "translatedValue": "Peru", "textId": "PE", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "PH", "alpha3Key": "PHL", "value": "Philippines", "translatedValue": "Philippines", "textId": "PH", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "PN", "alpha3Key": "PCN", "value": "Pitcairn", "translatedValue": "Pitcairn", "textId": "PN", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "PL", "alpha3Key": "POL", "value": "Poland", "translatedValue": "Poland", "textId": "PL", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "PT", "alpha3Key": "PRT", "value": "Portugal", "translatedValue": "Portugal", "textId": "PT", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "PR", "alpha3Key": "PRI", "value": "Puerto Rico", "translatedValue": "Puerto Rico", "textId": "PR", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "QA", "alpha3Key": "QAT", "value": "Qatar", "translatedValue": "Qatar", "textId": "QA", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "RE", "alpha3Key": "REU", "value": "Reunion", "translatedValue": "Reunion", "textId": "RE", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "RO", "alpha3Key": "ROU", "value": "Romania", "translatedValue": "Romania", "textId": "RO", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "RU", "alpha3Key": "RUS", "value": "Russian Federation", "translatedValue": "Russian Federation", "textId": "RU", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "RW", "alpha3Key": "RWA", "value": "Rwanda", "translatedValue": "Rwanda", "textId": "RW", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SH", "alpha3Key": "SHN", "value": "Saint Helena", "translatedValue": "Saint Helena", "textId": "SH", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "KN", "alpha3Key": "KNA", "value": "Saint Kitts and Nevis", "translatedValue": "Saint Kitts and Nevis", "textId": "KN", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "LC", "alpha3Key": "LCA", "value": "Saint Lucia", "translatedValue": "Saint Lucia", "textId": "LC", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "PM", "alpha3Key": "SPM", "value": "Saint Pierre and Miquelon", "translatedValue": "Saint Pierre and Miquelon", "textId": "PM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "VC", "alpha3Key": "VCT", "value": "Saint Vincent and the Grenadines", "translatedValue": "Saint Vincent and the Grenadines", "textId": "VC", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "WS", "alpha3Key": "WSM", "value": "Samoa", "translatedValue": "Samoa", "textId": "WS", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SM", "alpha3Key": "SMR", "value": "San Marino", "translatedValue": "San Marino", "textId": "SM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "ST", "alpha3Key": "STP", "value": "Sao Tome and Principe", "translatedValue": "Sao Tome and Principe", "textId": "ST", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SA", "alpha3Key": "SAU", "value": "Saudi Arabia", "translatedValue": "Saudi Arabia", "textId": "SA", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SN", "alpha3Key": "SEN", "value": "Senegal", "translatedValue": "Senegal", "textId": "SN", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SC", "alpha3Key": "SYC", "value": "Seychelles", "translatedValue": "Seychelles", "textId": "SC", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SL", "alpha3Key": "SLE", "value": "Sierra Leone", "translatedValue": "Sierra Leone", "textId": "SL", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SG", "alpha3Key": "SGP", "value": "Singapore", "translatedValue": "Singapore", "textId": "SG", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SK", "alpha3Key": "SVK", "value": "Slovakia", "translatedValue": "Slovakia", "textId": "SK", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SI", "alpha3Key": "SVN", "value": "Slovenia", "translatedValue": "Slovenia", "textId": "SI", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SB", "alpha3Key": "SLB", "value": "Solomon Islands", "translatedValue": "Solomon Islands", "textId": "SB", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SO", "alpha3Key": "SOM", "value": "Somalia", "translatedValue": "Somalia", "textId": "SO", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "ZA", "alpha3Key": "ZAF", "value": "South Africa", "translatedValue": "South Africa", "textId": "ZA", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GS", "alpha3Key": "SGS", "value": "South Georgia and the South Sandwich Islands", "translatedValue": "South Georgia and the South Sandwich Islands", "textId": "GS", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "ES", "alpha3Key": "ESP", "value": "Spain", "translatedValue": "Spain", "textId": "ES", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "LK", "alpha3Key": "LKA", "value": "Sri Lanka", "translatedValue": "Sri Lanka", "textId": "LK", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SD", "alpha3Key": "SDN", "value": "Sudan", "translatedValue": "Sudan", "textId": "SD", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SR", "alpha3Key": "SUR", "value": "Suriname", "translatedValue": "Suriname", "textId": "SR", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SJ", "alpha3Key": "SJM", "value": "Svalbard and Jan Mayen", "translatedValue": "Svalbard and Jan Mayen", "textId": "SJ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SZ", "alpha3Key": "SWZ", "value": "Eswatini", "translatedValue": "Eswatini", "textId": "SZ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SE", "alpha3Key": "SWE", "value": "Sweden", "translatedValue": "Sweden", "textId": "SE", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CH", "alpha3Key": "CHE", "value": "Switzerland", "translatedValue": "Switzerland", "textId": "CH", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SY", "alpha3Key": "SYR", "value": "Syrian Arab Republic", "translatedValue": "Syrian Arab Republic", "textId": "SY", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "TW", "alpha3Key": "TWN", "value": "Taiwan, Province of China", "translatedValue": "Taiwan, Province of China", "textId": "TW", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "TJ", "alpha3Key": "TJK", "value": "Tajikistan", "translatedValue": "Tajikistan", "textId": "TJ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "TZ", "alpha3Key": "TZA", "value": "United Republic of Tanzania", "translatedValue": "United Republic of Tanzania", "textId": "TZ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "TH", "alpha3Key": "THA", "value": "Thailand", "translatedValue": "Thailand", "textId": "TH", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "TL", "alpha3Key": "TLS", "value": "Timor-Leste", "translatedValue": "Timor-Leste", "textId": "TL", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "TG", "alpha3Key": "TGO", "value": "Togo", "translatedValue": "Togo", "textId": "TG", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "TK", "alpha3Key": "TKL", "value": "Tokelau", "translatedValue": "Tokelau", "textId": "TK", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "TO", "alpha3Key": "TON", "value": "Tonga", "translatedValue": "Tonga", "textId": "TO", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "TT", "alpha3Key": "TTO", "value": "Trinidad and Tobago", "translatedValue": "Trinidad and Tobago", "textId": "TT", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "TN", "alpha3Key": "TUN", "value": "Tunisia", "translatedValue": "Tunisia", "textId": "TN", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "TR", "alpha3Key": "TUR", "value": "Türkiye", "translatedValue": "Türkiye", "textId": "TR", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "TM", "alpha3Key": "TKM", "value": "Turkmenistan", "translatedValue": "Turkmenistan", "textId": "TM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "TC", "alpha3Key": "TCA", "value": "Turks and Caicos Islands", "translatedValue": "Turks and Caicos Islands", "textId": "TC", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "TV", "alpha3Key": "TUV", "value": "Tuvalu", "translatedValue": "Tuvalu", "textId": "TV", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "UG", "alpha3Key": "UGA", "value": "Uganda", "translatedValue": "Uganda", "textId": "UG", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "UA", "alpha3Key": "UKR", "value": "Ukraine", "translatedValue": "Ukraine", "textId": "UA", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "AE", "alpha3Key": "ARE", "value": "United Arab Emirates", "translatedValue": "United Arab Emirates", "textId": "AE", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GB", "alpha3Key": "GBR", "value": "United Kingdom", "translatedValue": "United Kingdom", "textId": "GB", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "US", "alpha3Key": "USA", "value": "United States of America", "translatedValue": "United States of America", "textId": "US", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "UM", "alpha3Key": "UMI", "value": "United States Minor Outlying Islands", "translatedValue": "United States Minor Outlying Islands", "textId": "UM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "UY", "alpha3Key": "URY", "value": "Uruguay", "translatedValue": "Uruguay", "textId": "UY", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "UZ", "alpha3Key": "UZB", "value": "Uzbekistan", "translatedValue": "Uzbekistan", "textId": "UZ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "VU", "alpha3Key": "VUT", "value": "Vanuatu", "translatedValue": "Vanuatu", "textId": "VU", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "VE", "alpha3Key": "VEN", "value": "Venezuela", "translatedValue": "Venezuela", "textId": "VE", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "VN", "alpha3Key": "VNM", "value": "Vietnam", "translatedValue": "Vietnam", "textId": "VN", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "VG", "alpha3Key": "VGB", "value": "Virgin Islands, British", "translatedValue": "Virgin Islands, British", "textId": "VG", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "VI", "alpha3Key": "VIR", "value": "Virgin Islands, U.S.", "translatedValue": "Virgin Islands, U.S.", "textId": "VI", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "WF", "alpha3Key": "WLF", "value": "Wallis and Futuna", "translatedValue": "Wallis and Futuna", "textId": "WF", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "EH", "alpha3Key": "ESH", "value": "Western Sahara", "translatedValue": "Western Sahara", "textId": "EH", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "YE", "alpha3Key": "YEM", "value": "Yemen", "translatedValue": "Yemen", "textId": "YE", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "ZM", "alpha3Key": "ZMB", "value": "Zambia", "translatedValue": "Zambia", "textId": "ZM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "ZW", "alpha3Key": "ZWE", "value": "Zimbabwe", "translatedValue": "Zimbabwe", "textId": "ZW", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "AX", "alpha3Key": "ALA", "value": "Åland Islands", "translatedValue": "Åland Islands", "textId": "AX", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BQ", "alpha3Key": "BES", "value": "Bonaire, Sint Eustatius and Saba", "translatedValue": "Bonaire, Sint Eustatius and Saba", "textId": "BQ", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "CW", "alpha3Key": "CUW", "value": "Curaçao", "translatedValue": "Curaçao", "textId": "CW", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "GG", "alpha3Key": "GGY", "value": "Guernsey", "translatedValue": "Guernsey", "textId": "GG", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "IM", "alpha3Key": "IMN", "value": "Isle of Man", "translatedValue": "Isle of Man", "textId": "IM", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "JE", "alpha3Key": "JEY", "value": "Jersey", "translatedValue": "Jersey", "textId": "JE", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "ME", "alpha3Key": "MNE", "value": "Montenegro", "translatedValue": "Montenegro", "textId": "ME", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "BL", "alpha3Key": "BLM", "value": "Saint Barthélemy", "translatedValue": "Saint Barthélemy", "textId": "BL", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "MF", "alpha3Key": "MAF", "value": "Saint Martin (French part)", "translatedValue": "Saint Martin (French part)", "textId": "MF", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "RS", "alpha3Key": "SRB", "value": "Serbia", "translatedValue": "Serbia", "textId": "RS", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SX", "alpha3Key": "SXM", "value": "Sint Maarten (Dutch part)", "translatedValue": "Sint Maarten (Dutch part)", "textId": "SX", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "SS", "alpha3Key": "SSD", "value": "South Sudan", "translatedValue": "South Sudan", "textId": "SS", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }, { "alpha2Key": "XK", "alpha3Key": "XKX", "value": "Kosovo", "translatedValue": "Kosovo", "textId": "XK", "language": "en", "id": "647620845098f5861461f0ed", "optionType": "country" }];


// genders

export const genders = [
  {
    textId: 'm',
    value: 'Male',
    translatedValue: 'Male',
  },
  {
    textId: 'f',
    value: 'Female',
    translatedValue: 'Female',
  },
  {
    textId: 'x',
    value: 'Prefer to self-describe',
    translatedValue: 'Prefer to self-describe',
  },
  {
    textId: '-',
    value: 'I prefer not to share',
    translatedValue: 'I prefer not to share',
  },
]

export const pronouns = [
  {
    "textId": "m",
    "value": "he/him",
    "translatedValue": "he/him"
  },
  {
    "textId": "f",
    "value": "she/her",
    "translatedValue": "she/her"
  },
  {
    "textId": "x",
    "value": "they/them",
    "translatedValue": "they/them"
  }
]

// website types taken from Micromentor's django app (subject to change)

export const websiteTypes = [
  "facebook",
  "twitter",
  "venture",
  "linkedin",
  "other"
]