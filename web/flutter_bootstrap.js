{{flutter_js}}
{{flutter_build_config}}

window.addEventListener("flutter-first-frame", function () {
  var loadingIndicator = document.getElementById("loader");
  if (loadingIndicator) {
    loadingIndicator.remove();
  }
});

const config = {
  renderer: "canvaskit",
};
_flutter.loader.load({
  config: config,
  nonce: 'flutter-init',
  onEntrypointLoaded: async (engineInitializer) => {
    const appRunner = await engineInitializer.initializeEngine({
      nonce: 'flutter-init',
    });
    appRunner.runApp();
  },
});