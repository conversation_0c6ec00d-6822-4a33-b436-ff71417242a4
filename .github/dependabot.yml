version: 2
enable-beta-ecosystems: true
updates:
  - package-ecosystem: "pub"
    directory: "/"
    schedule:
      interval: weekly
      time: "09:00"
      timezone: Asia/Tokyo
    open-pull-requests-limit: 5
    # this is the default maximum
    rebase-strategy: "auto"
    ignore:
      - dependency-name: "mm-mock-server"

  - package-ecosystem: "gradle"
    directory: "/android"
    schedule:
      interval: weekly
      time: "09:00"
      timezone: Asia/Tokyo
    open-pull-requests-limit: 5
    # this is the default maximum
    rebase-strategy: "auto"
    ignore:
      - dependency-name: "mm-mock-server"