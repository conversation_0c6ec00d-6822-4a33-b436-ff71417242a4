# This file was auto-generated by the Firebase CLI
# (and later modified)
# https://github.com/firebase/firebase-tools

name: Deploy to Firebase Hosting on merge
'on':
  push:
    branches:
      - develop
jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.3'
          channel: 'stable'
      - name: Build runner
        run: sh ./build_runner.sh
      # create file at lib/firebase/.env.dart from secret FIREBASE
      - name: Create build secrets
        run: |
          echo ${{ secrets.FIREBASE }} | base64 --decode >> lib/services/firebase/.env.dart
      - run: flutter config --enable-web
      - run: flutter build web --dart-define=FLUTTER_APP_FLAVOR=dev --source-maps
      # TODO: change flavor to stag once it's up
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_MICROMENTOR_D72FF }}'
          channelId: live
          projectId: micromentor-d72ff
          target: dev # this is the target name assigned manually via the CLI to the site "dev-mm"
        env:
          FIREBASE_CLI_EXPERIMENTS: webframeworks

