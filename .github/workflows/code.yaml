name: Tests and formatters
on:
- pull_request
jobs:
  dart-formatting:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.29.3'
    - name: See if formatting changes anything
      run: dart format --line-length 100 --set-exit-if-changed .
  flutter-analyze:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.29.3'
    - name: Create build secrets
      run: |
        echo ${{ secrets.FIREBASE }} | base64 --decode >> lib/services/firebase/.env.dart
    - name: Fix subosito/flutter-action \#197
      run: git config --global --add safe.directory /opt/hostedtoolcache/flutter/stable-3.29.3-x64
  flutter-tests:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.29.3'
    - name: Create build secrets
      run: |
        echo ${{ secrets.FIREBASE }} | base64 --decode >> lib/services/firebase/.env.dart
    - name: Fix subosito/flutter-action \#197
      run: git config --global --add safe.directory /opt/hostedtoolcache/flutter/stable-3.29.3-x64
    - name: Fetch Dependencies
      run: flutter pub get
    - name: Generate translations
      run: dart run intl_utils:generate
    - name: Generate schema files
      run: dart run build_runner build
    - name: See if tests are failing
      run: flutter test
  flutter-translations:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.29.3'
    - name: Create build secrets
      run: |
        echo ${{ secrets.FIREBASE }} | base64 --decode >> lib/services/firebase/.env.dart
    - name: Fix subosito/flutter-action \#197
      run: git config --global --add safe.directory /opt/hostedtoolcache/flutter/stable-3.29.3-x64
    - name: Fetch Dependencies
      run: flutter pub get
    - name: See if translations can be generated
      run: dart run intl_utils:generate
    - name: Generate schema files
      run: dart run build_runner build
    - name: See if flutter linter passes
      run: flutter analyze
