name: Release App with Fastlane (Firebase App Distribution)
on:
  push:
    branches:
      - 'develop'
jobs:
  build_android:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.29.3'
    - name: Create build secrets
      run: |
        echo ${{ secrets.FIREBASE }} | base64 --decode >> lib/services/firebase/.env.dart
    - name: Add Google Services JSON
      run: 'echo "$GOOGLE_SERVICES_JSON_FILE" > android/app/google-services.json'
      env:
        GOOGLE_SERVICES_JSON_FILE: ${{ secrets.GOOGLE_SERVICES_JSON_FILE }}
    - name: Add key.properties
      run: 'echo "$ANDROID_KEY_PROPERTIES" > android/key.properties'
      env:
        ANDROID_KEY_PROPERTIES: ${{ secrets.ANDROID_KEY_PROPERTIES }}
    - name: Add upload-keystore.jks
      run: 'echo "$UPLOAD_KEYSTORE_JKS" | base64 --decode > android/fastlane/upload-keystore.jks'
      env:
        UPLOAD_KEYSTORE_JKS: ${{ secrets.UPLOAD_KEYSTORE_JKS }}
    - name: Set up ruby env
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: 2.7.2
        bundler-cache: true
    - name: Install dependencies
      run: cd android && bundle install
    - name: Run app distribution
      run: cd android && bundle exec fastlane build_and_distribute_android_with_firebase
      env:
        FIREBASE_CLI_TOKEN: ${{ secrets.FIREBASE_CLI_TOKEN }}
        FIREBASE_ANDROID_APP_ID: ${{ secrets.FIREBASE_ANDROID_APP_ID }}
