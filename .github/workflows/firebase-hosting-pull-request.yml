# This file was auto-generated by the Firebase CLI
# (and later modified)
# https://github.com/firebase/firebase-tools

name: Deploy to Firebase Hosting on PR
'on': pull_request
jobs:
  build_web_and_preview:
    name: Build Flutter (Web)
    if: '${{ github.event.pull_request.head.repo.full_name == github.repository }}'
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.29.3'
        channel: 'stable'
    - name: Build runner
      run: sh ./build_runner.sh
    # create file at lib/firebase/.env.dart from secret FIREBASE
    - name: Create build secrets
      run: |
        echo ${{ secrets.FIREBASE }} | base64 --decode >> lib/services/firebase/.env.dart
    - run: flutter config --enable-web
    - run: flutter build web --dart-define=FLUTTER_APP_FLAVOR=dev
    - uses: FirebaseExtended/action-hosting-deploy@v0
      with:
        repoToken: '${{ secrets.GITHUB_TOKEN }}'
        firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_MICROMENTOR_D72FF }}'
        projectId: micromentor-d72ff
        target: dev # this is the target name assigned manually via the CLI to the site "dev-mm"
      env:
        FIREBASE_CLI_EXPERIMENTS: webframeworks

