# This is initially triggered based on "Generate PR" in POEditor. Similarly, when a POEditor language event
# ("languge proofread") fires a callback to hit a POEditor webhook that generates a PR.
name: Generate a pull request with updated Translations from POEditor
on:
  push:
    branches:
      - 'develop'
    paths:
      - 'lib/l10n/intl_en.arb'

jobs:
  export-terms-and-translations-from-gui:
    name: Export Terms and Translations from POEditor
    runs-on: ubuntu-latest

    steps:
      - name: Checkout develop
        uses: actions/checkout@v4
        with:
          ref: develop

      - name: Checkout do-not-delete/translations
        uses: actions/checkout@v4
        with:
          ref: do-not-delete/translations
          path: rebaseTranslations

      - name: Rebase translations onto develop
        run: |
          cd rebaseTranslations
          git rebase ../
          git push --force

      - name: Export "Proofread" AR Terms and Translations
        env:
          EXPORT_AR: ${{ secrets.POEDITOR_EXPORT_AR_WEBHOOK }}
        run: |
          curl -X POST "$EXPORT_AR"

      - name: Export "Proofread" EN Terms and Translations
        env:
          EXPORT_EN: ${{ secrets.POEDITOR_EXPORT_EN_WEBHOOK }}
        run: |
          curl -X POST "$EXPORT_EN"

      - name: Export "Proofread" ES Terms and Translations
        env:
          EXPORT_ES: ${{ secrets.POEDITOR_EXPORT_ES_WEBHOOK }}
        run: |
          curl -X POST "$EXPORT_ES"

      - name: Export "Proofread" ID Terms and Translations
        env:
          EXPORT_ID: ${{ secrets.POEDITOR_EXPORT_ID_WEBHOOK }}
        run: |
          curl -X POST "$EXPORT_ID"

      - name: Export "Proofread" RU Terms and Translations
        env:
          EXPORT_RU: ${{ secrets.POEDITOR_EXPORT_RU_WEBHOOK }}
        run: |
          curl -X POST "$EXPORT_RU"
