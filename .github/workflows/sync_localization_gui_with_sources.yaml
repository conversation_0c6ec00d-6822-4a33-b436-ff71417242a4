name: Sync the Localization GUI with the sources
on:
  push:
    branches:
      - 'develop'
    paths:
      - 'lib/l10n/intl_en.arb'

jobs:
  sync-translations-from-sources:
    name: Sync the Localization GUI with the sources
    runs-on: ubuntu-latest
    strategy:
      matrix:
        language: ['ar', 'en', 'es', 'id', 'ru']

    steps:
      - name: Checkout the EN file
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            lib/l10n/intl_en.arb
          sparse-checkout-cone-mode: false

      - name: Upload (sync) EN Terms without Translations to POEditor
        env:
          KEY: ${{ secrets.POEDITOR_API_KEY }}
          PROJECT_ID: ${{ secrets.POEDITOR_PROJECT_ID }}
        run: |
          curl -X POST "https://api.poeditor.com/v2/projects/upload" \
            -H 'accept: application/json' \
            -H 'Content-Type: application/x-www-form-urlencoded' \
            -F api_token="$KEY" \
            -F id="$PROJECT_ID" \
            -F updating="terms" \
            -F file=@"lib/l10n/intl_en.arb" \
            -F sync_terms="1" \
            -F tags="{\"new\":[\"new\"], \"obsolete\":[\"obsolete\"]}"

      - name: Add EN Translations to POEditor (will not overwrite)
        env:
          IMPORT_EN: ${{ secrets.POEDITOR_IMPORT_EN_WEBHOOK }}
        run: |
          curl -X POST "$IMPORT_EN"

      - name: Machine Translate all new terms per language
        env:
          KEY: ${{ secrets.POEDITOR_API_KEY }}
          PROJECT_ID: ${{ secrets.POEDITOR_PROJECT_ID }}
        run: |
          curl -X POST "POST https://api.poeditor.com/v2/translations/automatic" \
            -H 'accept: application/json' \
            -H 'Content-Type: application/x-www-form-urlencoded' \
            -d api_token="$KEY" \
            -d id="$PROJECT_ID" \
            -d source_language="en" \
            -d provider_source_language="en" \
            -d provider="google" \
            -d target_languages="[{\"project_language\":\"ar\",\"provider_language\":\"ar\"},{\"project_language\":\"es\",\"provider_language\":\"es\"},{\"project_language\":\"id\",\"provider_language\":\"id\"},{\"project_language\":\"ru\",\"provider_language\":\"ru\"}]" \
            -d options="{\"tag\":\"new\",\"format\":\"text\"}"
