{"hosting": [{"target": "dev", "public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "headers": [{"source": "/.well-known/apple-app-site-association", "headers": [{"key": "Content-Type", "value": "application/json"}]}, {"source": "/apple-app-site-association", "headers": [{"key": "Content-Type", "value": "application/json"}]}, {"source": "**", "headers": [{"key": "Content-Security-Policy", "value": "script-src 'self' 'wasm-unsafe-eval' 'unsafe-inline' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com; frame-ancestors 'self' https://*.micromentor.org; font-src 'self' 'nonce-flutter-init' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com https://*.cloudfront.net https://*.amazonaws.com; frame-src 'self' 'nonce-flutter-init' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com ; img-src 'self' 'nonce-flutter-init' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com https://*.cloudfront.net https://*.amazonaws.com; manifest-src 'self' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com ; media-src 'self' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com https://*.cloudfront.net https://*.amazonaws.com; object-src 'self' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com https://*.amazonaws.com https://*.cloudfront.net; worker-src 'self' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com ; connect-src 'self' wss://*.micromentor.org https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com https://*.sentry.io https://*.cloudfront.net https://*.amazonaws.com https://*.google-analytics.com"}]}], "rewrites": [{"source": "**", "destination": "/index.html"}, {"source": "/.well-known/assetlinks.json", "destination": "/well-known/assetlinks.json"}], "frameworksBackend": {"region": "us-central1"}}, {"target": "test", "public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "headers": [{"source": "/.well-known/apple-app-site-association", "headers": [{"key": "Content-Type", "value": "application/json"}]}, {"source": "/apple-app-site-association", "headers": [{"key": "Content-Type", "value": "application/json"}]}, {"source": "**", "headers": [{"key": "Content-Security-Policy", "value": "script-src 'self' 'wasm-unsafe-eval' 'unsafe-inline' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com; frame-ancestors 'self' https://*.micromentor.org; font-src 'self' 'nonce-flutter-init' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com https://*.cloudfront.net https://*.amazonaws.com; frame-src 'self' 'nonce-flutter-init' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com ; img-src 'self' 'nonce-flutter-init' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com https://*.cloudfront.net https://*.amazonaws.com; manifest-src 'self' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com ; media-src 'self' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com https://*.cloudfront.net https://*.amazonaws.com; object-src 'self' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com https://*.amazonaws.com https://*.cloudfront.net; worker-src 'self' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com ; connect-src 'self' wss://*.micromentor.org https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com https://*.sentry.io https://*.cloudfront.net https://*.amazonaws.com https://*.google-analytics.com"}]}], "rewrites": [{"source": "**", "destination": "/index.html"}, {"source": "/.well-known/assetlinks.json", "destination": "/well-known/assetlinks.json"}], "frameworksBackend": {"region": "us-central1"}}, {"target": "prod", "public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "headers": [{"source": "/.well-known/apple-app-site-association", "headers": [{"key": "Content-Type", "value": "application/json"}]}, {"source": "/apple-app-site-association", "headers": [{"key": "Content-Type", "value": "application/json"}]}, {"source": "/.well-known/assetlinks.json", "headers": [{"key": "Content-Type", "value": "application/json"}]}, {"source": "**", "headers": [{"key": "Content-Security-Policy", "value": "script-src 'self' 'wasm-unsafe-eval' 'unsafe-inline' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com; frame-ancestors 'self' https://*.micromentor.org; font-src 'self' 'nonce-flutter-init' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com https://*.cloudfront.net https://*.amazonaws.com; frame-src 'self' 'nonce-flutter-init' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com ; img-src 'self' 'nonce-flutter-init' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com https://*.cloudfront.net https://*.amazonaws.com; manifest-src 'self' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com ; media-src 'self' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com https://*.cloudfront.net https://*.amazonaws.com; object-src 'self' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com https://*.amazonaws.com https://*.cloudfront.net; worker-src 'self' https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com ; connect-src 'self' wss://*.micromentor.org https://*.micromentor.org https://*.google.com https://*.googletagmanager.com https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com https://*.sentry.io https://*.cloudfront.net https://*.amazonaws.com https://*.google-analytics.com"}]}], "rewrites": [{"source": "**", "destination": "/index.html"}, {"source": "/.well-known/assetlinks.json", "destination": "/well-known/assetlinks.json"}], "frameworksBackend": {"region": "us-central1"}}]}