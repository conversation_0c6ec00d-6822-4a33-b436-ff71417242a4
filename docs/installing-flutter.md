# How to install Flutter as a developer for this repo

## Prerequisites

If you have an existing Flutter install, remove it before proceeding: `which flutter`
`sudo rm -r <flutter path output by the which flutter command>`

## Install Flutter via FVM

FVM is a Flutter Version Management tool that allows you to install and manage multiple Flutter
versions.

Follow the [instructions inn this doc](https://fvm.app/documentation/getting-started/installation).
On MacOS, you can use Homebrew to install FVM:

``` sh
brew tap leoafarias/fvm
brew install fvm
```

Finally, run `fvm use` once you are in the root of this repo to install the correct Flutter version.

## Install Flutter directly

If you prefer to install Flutter directly, follow the [official Flutter installation
instructions](https://flutter.dev/docs/get-started/install).

## Configuring a `.fvmrc` file

See these docs for more info about configuring an `.fvmrc` file:
<https://fvm.app/documentation/getting-started/configuration>
