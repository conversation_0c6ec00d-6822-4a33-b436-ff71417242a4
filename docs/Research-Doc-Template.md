# Choosing a (SOME TECHNOLOGY) solution

(define the problem and the requirements at a high level)

## Requirements

(break down the requirements into a list)

1.  **Do this thing**: Definition of the thing.
2.  **Do that thing**: Definition of the other thing.
3.  **Do not do this bad thing**: Definition of the bad thing.
4.  **Do this other thing** (optional): Definition of this thing. (This can be implemented later.)

## Available solutions

(Provide a list of available solutions, with a brief description of each. This is an appropriate
place to include links to docs, marketing materials, code samples, or other resources. These
solutions may be open-source libraries, SaaS APIs, other technologies, or even a manual solution
which requires no development.)

### Some Option

### Some Other Option

### Summary

(Some comparison of the available solutions, their pros and cons, and how they fit the
requirements.)

## Use cases

### X

## Conclusion / Recommendations

(Some logic for your recommendations)

W does not fulfill the requirements outlined above.

Doing Z would be really expensive, and is not a priority at this time. Y has more benefits than X.

Based on this, we should use Y.
