# Flutter Web Deployment Guidance

For organizations intending to deploy this Flutter application as a web application, the following
guidance is provided for hosting and deployment.

## Deployment during development

To quickly and easily get a deployment set up, follow the steps provided in the Firebase and Flutter
official resources:

- [Flutter web deployment guide](https://docs.flutter.dev/deployment/web)
- [Firebase hosting docs](https://firebase.google.com/docs/hosting/frameworks/flutter), including
  Flutter Web specific page

These other resources would be helpful with the Firebase hosting setup:

- [How to copy a preview of a new deployment to
  production](https://firebase.google.com/docs/hosting/test-preview-deploy)
- [Medium post similar to official docs, but with
  screenshots](https://medium.com/flutter/must-try-use-firebase-to-host-your-flutter-app-on-the-web-852ee533a469)

.env files are not copied into the Firebase Hosting environment (e.g. the address for the API
server). There are [several
ways](https://stackoverflow.com/questions/34442739/how-does-one-set-private-environment-variables-on-firebase-hosting)
to handle this, none of which have been implemented in this Flutter project. For simple deployments
without sensitive info in the .env files, some values could be set up as fallbacks in case the .env
file is not found.

## Deployment for production

For production deployments, the requirements for your app and the team's familiarity with app
infrastructure will determine how this should be deployed. The Firebase Hosting used in development
might be sufficient for your purposes. Deploying the app via a CDN like AWS S3+CloudFront, or using
Firebase Hosting, seem like they will be scalable and reliable enough for MVP.

[Here's an
example](https://spltech.co.uk/how-to-deploy-flutter-webapp-using-google-kubernetes-engine/) of how
to set up a Flutter Kubernetes service if we need that. This is a more complex setup and I am not
aware of any advantages it would have over Firebase Hosting or AWS S3+CloudFront for our use case.

Finally, [this Stack
Overflow](https://stackoverflow.com/questions/66560305/flutter-firebase-setting-different-deployment-targets-for-ios-android-and-w)
post has a very well-written guide for how to use Flavors in Firebase Web. The recommendations from
this stack overflow thread should be tested an integrated to this doc and to the main app README.md
file.

## Adding an additional site to Firebase Hosting

[See this post to add an additional site entirely from the
CLI](https://firebase.google.com/docs/hosting/multisites?authuser=1#cli-commands-with-deploy-targets).

It's also possible to create the site in the Firebase Hosting console and later assign it a target
ID.

At the moment (September 2024), there are 2 targets:

- `dev`, which has a site_id of `dev-mm`
- `test`, which has a site_id of `micromentor-d72ff`. This target is linked to test.micromentor.org.

To deploy to a specific target, use this command:

``` sh
flutter build web --dart-define=FLUTTER_APP_FLAVOR=dev --source-maps # ensure the latest build is in the build/web folder, and include --source-maps for sentry
firebase deploy --only hosting:TARGET_ID # replace TARGET_ID with the target ID, e.g. dev
```

## GitHub Actions / releases

The GitHub Actions workflow in this repository is set up to deploy to Firebase Hosting.

When a PR is merged to the `develop` branch, a workflow will run and deploy the app to the `dev`
target. That is found at /.github/firebase-hosting-merge.yml .

When a PR is created, a workflow will run and deploy a new channel of the app to the `dev` target.
That is found at /.github/firebase-hosting-pull_request.yml . This channel will only exist for a few
days, and the PR needs to be updated with new code for it to be redeployed.

To release to the `test` or any other target, manually run the deployment using
`flutter build web --dart-define=FLUTTER_APP_FLAVOR=dev && firebase deploy --only hosting:test`,
replacing `test` with the target ID and `dev` with the desired flavor.
