# iOS and Android branching system and release process

For Micromentor's iOS and Android applications, we have been releasing test applications to internal
testers on iOS via TestFlight and in Google's Play Console.

**IMPORTANT NOTE: please do not submit any apps for review from the Apple or Google team until this
has been discussed at a standup, or in the `mm-devs-all` Slack channel. This is to ensure that the
app is in a state that is ready for review. For all releases, the manual release process should be
used, so that after Apple or Google reviews the app, our team can choose to roll out any
corresponding backend changes to production at the same time that the app version is updated.**

## Branching system

The app has two principal branches:

1.  `main`
2.  `develop`

The `main` branch is the production branch. It should always contain the latest stable version of
the app. The `develop` branch is the development branch. It should contain the latest version of the
app that is being developed. The `develop` branch should be merged into the `main` branch when a new
version of the app is ready to be released.

For each new feature or bug fix, a new branch should be created from the `develop` branch. When the
feature or bug fix is ready, the branch should be merged back into the `develop` branch.

Here is the specific process for creating a new branch and merging it back into the `develop`
branch:

1.  Get the latest version of the app from the remote `develop` branch.
2.  Create a new branch from the `develop` branch. The branch name should be descriptive of the
    feature or bug fix that is being worked on. For example, if you are working on a feature to
    include audio calls in the app, you could name the branch `feature/audio-calls`.
3.  Make the necessary changes to the app in the new branch.
4.  When the feature or bug fix is ready and a PR is approved, merge the new branch back into the
    `develop` branch via one squashed commit.
5.  Push the changes to the remote `develop` branch.
6.  Delete the branch that was created in step 2.

There are 4 types of branches that can be created from the `develop` branch:

1.  `feature/branch-name`: A branch for a new feature that is being developed.
2.  `bugfix/branch-name`: A branch for a bug fix that is being developed on the `develop` branch.
3.  `release/vX.Y.Z`: A branch for a new release that is being developed.
4.  `hotfix/vX.Y.Z`: A branch for a critical bug fix that is being developed on the `main` branch.

## How to cut a release branch

When the `develop` branch is ready to be released, a new release branch should be created from the
`develop` branch. The release branch should be named `release/vX.Y.Z`, where `X.Y.Z` is the version
number of the release. For example, if the version number of the release is `1.0.0`, the release
branch should be named `release/v1.0.0`.

Here is the specific process for creating a new release branch:

1.  Get the latest version of the app from the remote `develop` branch.
2.  Create a new branch from the `develop` branch. The branch name should be `release/vX.Y.Z`, where
    `X.Y.Z` is the version number of the release.
3.  Update the version number in the `pubspec.yaml` file to `X.Y.Z`.
4.  Build the app as described below and upload it to the appropriate platform for testing.
5.  When the app has been QA'ed is ready to be released, merge the release branch into the `main`
    branch.
6.  Tag the `main` branch with the version number of the release. The tag should be named `vX.Y.Z`.
7.  Push the changes to the remote `main` branch and the tag to the remote repository.

Any intermediate bug fixes or features that are added in order to pass QA should be committed or
cherry-picked into the release branch and then into the `main` branch. The release branch should be
deleted after the release has been made, and the `develop` branch should be rebased with the latest
changes from the `main` branch.

The backend should follow a similar process in all of the relevant repositories. The production
backend should be updated to be compatible with the new version of the app before the app is
released.

## How to cut a hotfix branch

When a critical bug is found in the production version of the app, a hotfix branch should be created
from the `main` branch. The hotfix branch should be named `hotfix/vX.Y.Z`, where `X.Y.Z` is the
version number of the release that the hotfix is being made for. For example, if the version number
of the release is `1.0.0`, the hotfix branch should be named `hotfix/v1.0.1`.

After it passes QA, the hotfix branch should be merged back into the `main` branch with a squashed
commit. The `main` branch should be tagged with the version number of the hotfix. The tag should be
named `vX.Y.Z`. These changes should be merged back into the `develop` branch, too.

## How to build a new version of the app on iOS

1.  Get the latest version of the app from the `main` branch.
2.  Run the build runner script to generate the necessary files for the build:
    `sh ./build_runner.sh`
3.  Build the app using the following command:
    `flutter build ipa --flavor prod --release --build-name=1.0.1 --build-number=28 --export-options-plist=ios/export.plist.dist`
    Modify the `build-number`, `build-name` and `flavor` arguments as needed. For internal testing
    releases, the `dev` flavor should generally be used. This will only work if you are building on
    a Mac with Xcode installed and the App Store certificates added to your keychain.
4.  Upload the `.ipa` file to App Store Connect. This can be done manually or using the
    `Transporter` app. The `.ipa` file can be found in the `build/ios/ipa` directory as
    `mm_flutter_app.ipa`.
5.  (For an internal testing release) Wait while the build is processed and available to be added to
    an Internal Testing group. This can be done at [this
    page](https://appstoreconnect.apple.com/apps/6463767947/testflight/ios) by a user with a
    Developer role. It usually takes 5-15 minutes for the build to be processed.
6.  (For a production release) Create a new version in App Store Connect and submit the app for
    review.

For more information, refer to this Flutter doc about [building and releasing iOS
apps](https://docs.flutter.dev/deployment/ios).

## How to release a new version of the app on Android

1.  Get the latest version of the app from the `main` branch.
2.  Run the build runner script to generate the necessary files for the build:
    `sh ./build_runner.sh`
3.  Build the app using the following command:
    `flutter build appbundle --build-number 28 --release --flavor dev` Modify the `build-number` and
    `flavor` arguments as needed. For internal testing releases, the `dev` flavor should generally
    be used. This will only work if you have the correct keystore file on your device. The resulting
    file will be found at `build/app/outputs/bundle/devRelease/app-dev-release.aab`, where `dev` is
    the flavor.
4.  Upload the App bundle file manually to the Google Play console as an Internal Testing release.
    That can be done by creating a new release from [this
    page](https://play.google.com/console/u/1/developers/5652998586176484919/app/4974877940952730016/tracks/internal-testing).
5.  (For an internal testing release) For most build uploads, you can just press "next" repeatedly
    and create the release for internal testing. The app will generally be available to internal
    testers immediately after completing this step, but Google says it may take up to an hour.

For more general information, refer to this Flutter doc about [building and releasing Android
apps](https://docs.flutter.dev/deployment/android).

## How to use the release script to build apps for both iOS and Android

1.  Get the latest version of the app from the `main` branch.
2.  Run the app bundle creation script with arguments, such as:
    `sh ./build_app_bundles.sh --build_number 36 --build_name 1.0.1 --flavor dev` Modify the
    `build-number`, `build-name` and `flavor` arguments as needed. For internal testing releases,
    the `dev` flavor should generally be used, while for production, the `prod` flavor should be
    used. This will only work if you are building on a Mac with Xcode installed and the App Store
    certificates added to your keychain.

## How to access iOS and Android apps for testing

As of April 2024, we have not begun public beta testing, so all testers must be invited by email. In
the future, we should open up beta testing to the public. Apple's App Store requires a review prior
to each release to external beta testers, so we should plan for a minimum of one day of review time
before each external release.

For internal testers, please refer to [this
doc](https://docs.google.com/document/d/1Lq8Nl5-rb8D0dRbVNvyUeZGKU_XizC_APL4nLrBpdMo/edit?usp=sharing)
for instructions on how to test.
