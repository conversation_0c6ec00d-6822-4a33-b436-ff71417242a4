# Flutter Web Renderers: Canvas<PERSON><PERSON> vs HTMLRenderer

## Summary and Context

Flutter for web has had two renderers since the release of Flutter 2.0: CanvasKit and HTMLRenderer.
In April 2024, the Flutter team has announced in GitHub that they intend to deprecate HTMLRenderer
in the future, and that CanvasKit is the recommended renderer for Flutter web apps. Because of this,
and other reasons discussed below, we should use CanvasKit for our web app.

The default for Flutter web apps is currently (as of August 2024, and Flutter 3.24) CanvasKit.
CanvasKit uses WebGL to render Skia paint commands, and is the recommended renderer for Flutter web
apps. HTMLRenderer uses canvas elements, but with a greater reliance on the DOM. Our unreleased web
app is using the `auto` setting, which uses HTMLRenderer for mobile browsers, and CanvasKit for
desktop browsers. This was the default setting until mid-2024. Due to our time size, we should
select one of the two renderers to use for our web app. Additionally, if we upgrade to the latest
Flutter version, it will switch to using the CanvasKit renderer by
default[^6](https://github.com/flutter/flutter/issues/149826).

### Pros for CanvasKit

1.  Officially recommended by the <PERSON>lutter team, and it will definitely continue to be supported.
2.  There are likely less bugs than for HTMLRenderer, and urgent bugs will be prioritized and fixed
    more quickly.
3.  With browsers that support WebAssembly (i.e. latest chromium-based browsers), CanvasKit has
    better performance than HTMLRenderer.
4.  Our image upload plugin does not work with HTMLRenderer, but does work with CanvasKit\[^5\].

### Cons for CanvasKit

1.  Larger bundle size compared to HTMLRenderer (i.e. takes longer to download). This may be
    addressed if Safari and Firefox support WebAssembly in the future, but that could take years.
2.  Slower startup time compared to HTMLRenderer. It has worse performance, according to some
    developers who use Flutter (see discussion in GH\[^2\]), which is a concern for running
    animations on devices without much processing power.
3.  Some core Flutter features are not functional in CanvasKit, such as support for Platform views
    and some issues with CORS for network requests.
4.  Developers have reported issues with WebKit compatibility with CanvasKit, i.e. some features may
    not work in iOS browsers (all iOS browsers use WebKit, including Safari and Chrome\[^3\]).

Given the official support for CanvasKit and intent to deprecate the HTML renderer, we should use
CanvasKit for our web app. We may need to address some issues

### Accessibility

Refer to the [Flutter Web Accessibility](flutter-web-accessibility.md) document for more information
on accessibility in Flutter web apps. We have been reassured by Yegor of the Flutter core team that
both renderers have the same level of accessibility support.

### Resources

\[^1\] [Flutter team docs on web
renderers](https://docs.flutter.dev/platform-integration/web/renderers) \[^2\] [GitHub issue on
intent to deprecate HTMLRenderer](https://github.com/flutter/flutter/issues/145954) \[^3\] [Chrome
on iOS still uses WebKit (unable to find a more official source that is
clear)](https://www.macrumors.com/2024/01/25/third-party-default-browsers-eu-ios-17-4/) \[^4\]
[Scroll issue on iOS from 2020, still unresolved](https://github.com/flutter/flutter/issues/69529)
\[^5\] [Image upload plugin issue discussed in this
PR](https://github.com/micromentor-team/mm-app/pull/351) \[^6\] [Flutter issue on switching to
CanvasKit by default](https://github.com/flutter/flutter/issues/149826)
