# Choosing a deep linking solution

We are looking for a solution that will allow us to provide a user with a link that can open the app
in either the browser, the installed iOS/Android app, or open the app store if the app is not
installed. This is a common feature in many apps and is often used for marketing campaigns, sharing
content, and more.

## Requirements

1.  **Universal links**: One link should able to open the app in the browser, the installed app, or
    the app store.
2.  **Customizable**: The link should be customizable to include parameters such as UTM tags,
    campaign IDs, group IDs, etc.
3.  **Cross-platform**: The solution should work on both Android and iOS. It should also work on the
    web.
4.  **Deferred deep linking**: The solution should be able to handle deferred deep linking, which
    means that if the app is not installed, the user should be taken to the app store to install the
    app, and then redirected to the content they were trying to access. (This can be implemented
    later.)
5.  **Support for multiple subdomains**: We may want to use different subdomains for different
    clients, such as `app.micromentor.org` for the global community and `iqlaa.micromentor.org` for
    the Iqlaa program. The solution should support links with different subdomains.
6.  **User experience**: The solution should provide a good user experience, with a similar flow to
    other major apps that use deep linking. This is subjective and will need to be evaluated by the
    team using a proof of concept.

## Possible additional requirements

1.  Link preview: When a user shares a link on social media or in a chat app, a preview of the link
    should be shown.
2.  Analytics: The solution should provide analytics on the usage of the links, such as the number
    of clicks, installs, and conversions.
3.  Ease of implementation: The solution should be easy to implement in a cross-platform Flutter
    app.

## Excluded requirements

1.  Custom domain: It would be best to use a custom domain for the links, such as
    `link.micromentor.org`, instead of a subdomain owned by the deep linking service SaaS company.
    That would allow our links to be controlled by MM, as well as being more trustworthy to users.
    However, this is not a hard requirement.
2.  Price: We are not considering the price of the solution in this research. We will need to
    contact the companies for a quote.
3.  Privacy: We are not considering the privacy implications of the solution in this research. We
    will need to investigate this further.
4.  User experience: We are not considering the user experience of the solution in this research. We
    will need to investigate this further.

## Available solutions

### Open Source Solutions

There are open-source Flutter deep linking solutions available with reduced feature sets. One big
benefit of building our own solution is that we would not be locked in to a third-party service that
could raise its pricing or change its terms of service. We would also have complete control over the
user experience (given enough budget) and data privacy. However, it may end up being very expensive
to implement if we want to handle deferred deep links, so we should exclude that option.

The most widely used Flutter deep linking package is
[app_links](https://pub.dev/packages/app_links), which replaces the old
[uni_links](https://pub.dev/packages/uni_links) package. `app_links` provides basic deep linking
functionality. It would allow us to open the Flutter web application and then redirect the user to
the installed app if it's installed, or to the web app if it's not.

`app_links` does not support deferred deep linking, so we would need to implement that ourselves in
the backend or use another open source solution. Chris didn't find any open source solutions that
support deferred deep linking.

### Implementation notes for `app_links`

`app_links` requires domain verification for both Android and iOS. This involves hosting a file on
the domain that the app will be linked to. This is a simple process that can be done using the
Firebase Hosting CDN that we already use to host the static files used by our Flutter web
application. The `apple-app-site-association` file for iOS `assetlinks.json` file for Android and
the need to be hosted on the root and the `.well-known` directories respectively. This has already
been done and merged into the `develop` branch.

Alternatively, we could use AWS Cloudfront or another CDN to host the files, or set up a custom
webserver.

Hosting the domain verification files on the root domain would be better, as it would allow us to
use a wildcard in the Android and iOS app configurations. However, Squarespace does not support
this. One alternative we should explore is using app.micromentor.org (or a similar domain) as the
default app domain, and iqlaa.app.micromentor.org as a client subdomain. We could also use the root
domain on a different domain for the app, like micromentor.net, and use a subdomain for the client,
like iqlaa.micromentor.net.

Once we switch to deferred deep linking via a SaaS solution, none of this will be used, so we should
stick with the simplest flow and hard-code each subdomain for now.

### Firebase Dynamic Links

Firebase Dynamic Links is a free service that allows you to create a single link that works across
all platforms and devices.

However, this service will be deprecated in August 2025, so we will not consider it as an option.

### Branch.io

Branch.io is a popular deep linking service that provides a wide range of features, including
universal links, customizable links, deferred deep linking, and analytics.

Branch does not have an official Flutter plugin, but there is a well-maintained third-party plugin
called `flutter_branch_sdk`. This plugin supports Android, iOS, and the web. It provides all the
features of the Branch.io SDK, including deferred deep linking.

Multiple subdomains [appear to be supported by
Branch](https://help.branch.io/using-branch/docs/advanced-settings-configuration#redirect-allowlist).

### Adjust

Adjust is a marketing analytics platform that also provides deep linking services. It offers
universal links, customizable links, deferred deep linking, and analytics.

Adjust has a Flutter/Dart plugin which works for the Android and iOS platforms. The plugin is
open-source and can be found on GitHub. The Flutter plugin does not have a web implementation, so we
would need to determine if the deep linking will still work in web, or if we would need to use the
Web SDK for Adjust. Using the web SDK may be a lot of extra work.

[Adjust appears to support multiple
subdomains](https://help.adjust.com/en/article/user-destinations-ad-network), but this needs to be
tested.

### AppsFlyer

AppsFlyer is another marketing analytics platform that provides deep linking services. It offers
nearly the same set of features as Adjust.

AppsFlyer has the same issue as Adjust with the lack of Flutter web support.

AppsFlyer supports multiple subdomains for redirects. It's not clear how much effort it is to
maintain the multiple subdomains. See:
[1](https://support.appsflyer.com/hc/en-us/articles/25068636064529-Set-up-your-domain-redirect-allowlist)
[2](https://support.appsflyer.com/hc/en-us/articles/4460838224273-App-to-web-redirection)

### Custom Implementation

If we were interested in implementing Deferred Deep Linking ourselves, it appears that Branch [uses
the device clipboard or IP
addresses](https://help.branch.io/using-branch/docs/nativelink-deferred-deep-linking) to track users
past the install. Using the IP address is a privacy concern and may not be GDPR compliant. It also
may not work in some edge cases, as users could install the app on a different device or network
from the one they first used to download the app. Accessing the clipboard [requires user permission
in the latest iOS
versions](https://help.branch.io/developers-hub/docs/ios-advanced-features#:~:text=With%20iOS%2016%2C%20Apple%20introduced,requesting%20access%20to%20the%20pasteboard.).

For an exploration of this topic by Badoo, who built their own deep linking solution, [this blog
post](https://medium.com/bumble-tech/universal-links-for-android-and-ios-1ddb1e70cab0) is packed
with useful info and potential roadblocks we would face. Unfortunately, the blog post is from 2018
and may be outdated. Badoo did not implement deferred deep linking themselves, so it is not a
complete solution, just a source of information. The post describes in detail how AppsFlyer worked
at the time.

## Summary

### Short term solution for deep linking

For users with the app already installed, we do not need deferred deep linking.

In the short term for IQLAA and the global community migration, we should use the `app_links`
package to implement basic deep linking functionality. We can host the iOS and Android domain
verification assets using the Firebase Hosting CDN.

### Deferred deep linking

**Custom implementations** for deferred deep linking should not be attempted. This is a complex
problem with existing solutions. We should pay for a service that is widely used and trusted, and
which will maintain the service for the foreseeable future.

Multiple subdomains seems like a common feature for deep linking services, so we should be able to
use all 3 of Branch, Adjust, and AppsFlyer for this. However, we need t

**Branch.io** appears to be the best solution for our needs once we . It provides all the features
we require. The `flutter_branch_sdk` plugin is well-maintained and supports Android, iOS, and the
web.

**We should verify if the Flutter web support is necessary. If not, Adjust and AppsFlyer** may be
equally good options as Branch. They appear to be more widely used and recommended but it's hard to
say. It's likely that `go_router` will handle the web deep linking after the user lands on the app,
so we may not need to connect to the APIs on the Flutter web app. This is probably only important if
we want analytics on web traffic via the deep linking tool.

**We should also get price estimates** for each service to determine the most cost-effective
solution. Adjust and Branch do not have transparent pricing plans on their websites, so we would
need to contact them for a quote.

### Relevant Asana tasks

These two Asana tasks are relevant to this research:

1.  [General deferred deep linking
    task](https://app.asana.com/0/1208254692497659/1208425841460725/f)
2.  [Subdomain handling in emails](https://app.asana.com/0/1208254692497659/1208643706836898/f)
