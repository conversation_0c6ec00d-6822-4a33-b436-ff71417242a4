# Localization & Translations

> Files inside `lib/l10n` contain the definitions for all text displayed in the UI. Each `.arb` defines the same set of
localization strings in a different language. The Key naming convention groups Strings by feature, and then narrows 
> it down to the specific component and version.

For example, these are some Keys used when selecting expertises during onboarding:
```
signupExpertisesEntrepreneurSubTitle,
signupExpertisesEntrepreneurTitle,
...
signupExpertisesMentorSubtitle,
signupExpertisesMentorTitle,
```

## Translation Rules & Expectations
1. **Terms are NEVER added or removed in the POEditor GUI**
1. **From the Engineering perspective, we only ever make updates to the English source file.**
2. **"Translation updates" made to the source code should only amount to:**
   - the addition of new `key:value` pairs in the English locale file (`lib/l10n/intl_en.arb`)
   - the removal of `key:value` pairs in the English locale file (`lib/l10n/intl_en.arb`)
3. **Given our workflow, Translations (`values`) in the source code are essentially unused by POEditor.**
   - _Translations from the sources are only ever exported to POEditor when an update is made to `lib/l10n/intl_en.arb` at `main`._
     - When these Translations are added to POEditor, they do not overwrite anything that may already exist in the GUI.
       - This has two benefits:
         1. Given you have added or removed Terms (`keys`) in your PR, the workflow will add the new translations you've included
            (`pairs`) and they will be applied to any new Terms that aren't already translated. _In practice, you should expect
            your new Terms to include your included English translations, for complete `key:value` pairs in your update._
         2. Given there are existing Translation updates in POEditor when the workflow goes to add new Translations 
            (`values`), those changes will not be overwritten by what is in the source code already.

### POEditor
We are leveraging [POEditor](https://poeditor.com/), a web based translation GUI to integrate with our sources via Github.
Past enabling translators with a hosted GUI, we have some integrations and automations in place in an attempt to
streamline the workflow for introducing or updating translations.

- We have 2 projects, one for both Front and Back ends. We could have one, but we'd only be left with Tags to keep things organized.
- Each Project is linked to a handful of files:
  - 1 source of truth: `/blob/main/lib/l10n/intl_en.arb`
  - 1 file to export to, per language: `/blob/do-not-delete/translations/lib/l10n/intl_en.arb`, etc.
- We have 2 Callbacks, 1 for each Project, which hits the Generate PR Webhook from POEDitor when a language is entirely "proofread"
- We have N Webhooks, half for each End:
  - 1 Generate Pull Request, `do-not-delete/translations` onto `main`
    - That's it, it just creates the PR.
  - 1 Import Translations from the source of truth
    - Does not overwrite existing Translations in GUI
  - X Export Terms and Translations, one for each language.
    - Commits directly to the `do-not-delete/translations` branch



### Adding new Terms and Translations
Simply add the `key:value` pair to the `lib/l10n/intl_en.arb` and ensure those changes are reflected at `main`. Upon update,
a workflow will kick in and make the updates to the POEditor GUI to enable Translators. Your Translations will **not** 
overwrite what already exists in POEditor. It will however, write the initial translations as you provide them (`values`).

### Removing now obsolete Terms and Translations
Just the opposite of adding new Terms, simply remove `key:value` pairs from the `lib/l10n/intl_en.arb` and ensure those 
changes are reflected at `main`. Upon update, a workflow will kick in and make the updates to the POEditor GUI to tag the
removed Terms as obsolete, and remove them from the current Term list. The Translations will exist in the POEditor Translation
Memory, if for any reason this was to be reverted.

### Adding a new Language
Given there is an existing file in the sources for this new language, this process starts in POEditor. This requires Admin permissions in POEditor.
The new Language needs to be added to both the Frontend and Backend Projects for complete translations. It's straightforward, 
but there's a bit of configuration to take care of:
1. Navigate to [the Backend Project](https://poeditor.com/projects/view?id=691862) and click the + button to add a new Language
2. Navigate to [the Github Account Integration](https://poeditor.com/github/projects) and click the + button to link a new file
3. Select the Backend Project and the Language you want to link to in Github
4. **The file you want to link to is the appropriate language file in the `do-not-delete/translations` branch, in `mmdata`**
5. Click the settings (cog) button for the Language you just added in the Project to add an Export Rule
6. Add an export rule: `export: Proofread Strings`, `tagged with: empty`, `order by: Default Order`
7. Navigate to [the POEditor Webhooks](https://poeditor.com/github/webhooks) and click the + button to add a new Webhook 
8. Select `Export terms and translations` as the Operation, the Backend Project and the new Language you've added.
   _Copy this Webhook link to use in a later step!_
9. Navigate to [the mmdata repo Action Secrets page](https://github.com/micromentor-team/mm-app/settings/secrets/actions)
10. Add a new Repo Secret: `POEDITOR_EXPORT_<your new 2 letter country code>_WEBHOOK` and include the Webhook URL from step 8.
    - _These Webhooks require zero auth, so we stash them as Secrets._
11. Once the file has been linked, you just need to kick off the export workflow by Proofreading the entire new Language, or by 
    using the [PR Preset](https://poeditor.com/github/pr_presets) button for the right repo in POEditor (the play button) to
    generate a pull request and ultimately export your new Proofread Translations to Github.

**_Don't forget to replicate what you've done here for the Frontend as well!_**

## The Workflows and Propagating Change
There are two Github Workflows in place to support our integration with POEditor:
1. **Up:** `sync_localization_gui_with_sources`
2. **Down:** `generate_translation_updates_pull_request`

The flows are not all that complex, and amount to a combination of API calls to POEditor in concert with hitting some POEditor
Webhooks to leverage some built in functionality that is more complex if re-implemented in an Action.
> **Note:**
> All the POEditor related implementation details are stored as Github Secrets. The API key is an Ogranization secret.
The Webhook URL's do not require any auth at all, so I've stashed them out of sight.
The Project ID can be public, it's obfuscated in an effort to make the Backend and Frontend Actions as close to 1:1 as possible.


### Up Workflow (to POEditor GUI)
When an update is pushed to `intl_en.arb` at `main`:
1. Make an API call to upload our EN Terms (`keys`), removing any now obsolete Terms from the current Term list.
   - The other Languages are all based on the same EN Terms list, and will thus have them dropped from those lists as well.
2. Call a POEDitor Webhook to import our EN Translations (`values`)
   - This will not overwrite any existing Translations in POEditor
3. Make an API call to "Automatically" (_read machine_) Translate all of our untranslated Terms across all Languages.

At this point the Translators have a combination of these possible updates in the GUI to tend to:
- "Unproofread" Translations
- Translations tagged "new"
- Translations marked "fuzzy", given an EN `value` (Translation) was updated.


### Down Workflow (from POEditor GUI)
When the Generate Pull Request Webhook is used within the POEditor GUI, or when a Language event occurs: "proofread language":

1. Check out `main`
2. Check out `do-not-delete/translations` in another directory called `rebaseTranslations`
3. Rebase `do-not-delete/translations` onto `main`
4. Call a POEditor Webhook for each Language to commit the "Proofread" Terms and Translations directly to `do-not-delete/translations`

- At this point, we have a PR that is rebased onto `main` and includes any changes from the POEditor GUI that may be out of sync 
  with the source code. 

## Manual Workflows
### How to manually export translations from POEditor
1.  Go to the [Micromentor POEditor project](<https://poeditor.com/projects/view?id=691862>)
2.  Select the desired language to export (e.g. click on Arabic)
3.  Click on the `Export` button on the right side of the menu (that currently takes you
    [here](https://poeditor.com/projects/export_language?id=691862&id_language=7))
4.  Select the key-value JSON format and "Default" order
5.  Click on the `Export` button
6. Save the file as `intl_en.arb` in the `lib/l10n/` folder

### How to manually import Terms and Translations to POEditor
1. Go to the [Micromentor POEditor project](<https://poeditor.com/projects/view?id=691862>)
2. Select the desired language to import (e.g. click on Arabic)
3. Click on the `Import` button on the right side of the menu (that currently takes you
   [here](https://poeditor.com/projects/import_translations?id_language=7&id=691862))
4. Select your `ar.json` file to upload
5. Do not overwrite Translations unless absolutely necessary. All approved Translations should come from the GUI by way of "Proof Reading"
6. If you must overwrite, please do mark other Translations as fuzzy.
7. Click on the `Import translations` button 
