workflows:
  # android-workflow:
    # name: Android Workflow
    # instance_type: mac_mini_m1
    # max_build_duration: 120
    # environment:
    #   android_signing:
    #     - keystore_reference
    #   groups:
    #     - google_play # <-- (Includes GCLOUD_SERVICE_ACCOUNT_CREDENTIALS)
    #   vars:
    #     PACKAGE_NAME: "io.codemagic.flutteryaml" # <-- Put your package name here
    #     GOOGLE_PLAY_TRACK: "alpha"
    #   flutter: stable
    # scripts:
    #   - name: Set up local.properties
    #     script: |
    #       echo "flutter.sdk=$HOME/programs/flutter" > "$CM_BUILD_DIR/android/local.properties"
    #   - name: Get Flutter packages
    #     script: |
    #       flutter packages pub get
    #   - name: Flutter analyze
    #     script: |
    #       flutter analyze
    #   - name: Flutter unit tests
    #     script: |
    #       flutter test
    #     ignore_failure: true
    #   - name: Build AAB with Flutter
    #     script: |
    #       BUILD_NUMBER=$(($(google-play get-latest-build-number --package-name "$PACKAGE_NAME" --tracks="$GOOGLE_PLAY_TRACK") + 1))      
    #       flutter build appbundle --release \
    #         --build-name=1.0.$BUILD_NUMBER \
    #         --build-number=$BUILD_NUMBER
    # artifacts:
    #   - build/**/outputs/**/*.aab
    #   - build/**/outputs/**/mapping.txt
    #   - flutter_drive.log
    # publishing:
    #   email:
    #     recipients:
    #       - <EMAIL>
    #       - <EMAIL>
    #     notify:
    #       success: true
    #       failure: false
    #   google_play:
    #     credentials: $GCLOUD_SERVICE_ACCOUNT_CREDENTIALS
    #     track: $GOOGLE_PLAY_TRACK
    #     submit_as_draft: true
  ios-workflow:
    name: iOS Workflow
    instance_type: mac_mini_m2
    max_build_duration: 120
    # integrations:
    #   app_store_connect: codemagic
    environment:
      groups:
        - manual_code_signing_ios
        - firebase
        - ios_build_settings
      ios_signing:
        distribution_type: development
        bundle_identifier: org.micromentor.mmFlutterApp
      vars:
        APP_ID: ********** # <-- Put your APP ID here
      flutter: 3.29.3
      xcode: 16.0 # <-- set to specific version e.g. 14.3, 15.0 to avoid unexpected updates.
      cocoapods: 1.15.2
    scripts:
      - name: Set up code signing settings on Xcode project
        script: |
          xcode-project use-profiles
      - name: Run build runner
        script: |
          sh ./build_runner.sh
      - name: Load Firebase configuration
        script: | 
          #!/usr/bin/env sh
          set -e # exit on first failed command

          echo $IOS_FIREBASE_SECRET > $CM_BUILD_DIR/ios/Runner/GoogleService-Info.plist
          echo $IOS_FIREBASE_APP_ID_JSON > $CM_BUILD_DIR/ios/firebase_app_id_file.json
          echo $ENV_DART_CONTENT > $CM_BUILD_DIR/lib/services/firebase/.env.dart
      - name: Flutter analyze
        script: |
          flutter analyze
      - name: Flutter unit tests
        script: |
          flutter test
        ignore_failure: true
      - name: Flutter build ipa and automatic versioning
        # (temp) set build number to 9, the latest in the MM app store is 8
        # TODO: set up automatic versioning with the app-store-connect API integration
        script: |
          flutter build ipa --release \
            --build-name=$IOS_BUILD_NAME \
            --build-number=$IOS_BUILD_NUMBER \
            --flavor $FLAVOR \
            --export-options-plist=ios/export.plist.dist
    artifacts:
      - build/ios/ipa/*.ipa
      - /tmp/xcodebuild_logs/*.log
      - flutter_drive.log
    publishing:
      # See the following link for details about email publishing - https://docs.codemagic.io/publishing-yaml/distribution/#email
      email:
        recipients:
          - <EMAIL>
        notify:
          success: true # To receive a notification when a build succeeds
          failure: true # To not receive a notification when a build fails
      # app_store_connect:
      #   auth: integration

      #   # Configuration related to TestFlight (optional)
      #   # Note: This action is performed during post-processing.
      #   submit_to_testflight: true
      #   beta_groups: # Specify the names of beta tester groups that will get access to the build once it has passed beta review.
      #     - group name 1
      #     - group name 2

      #   # Configuration related to App Store (optional)
      #   # Note: This action is performed during post-processing.
      #   submit_to_app_store: false
  # web-workflow:
  #   name: Web app workflow
  #   max_build_duration: 10
  #   environment:
  #     flutter: stable
  #   scripts:
  #     - name: Get Flutter packages
  #       script: |
  #         flutter packages pub get
  #     - name: Flutter analyze
  #       script: |
  #         flutter analyze
  #     - name: Flutter unit tests
  #       script: |
  #         flutter test
  #     - name: Flutter build webapp
  #       script: |
  #         flutter build web --release
  #         cd build/web
  #         7z a -r ../web.zip ./*
  #   artifacts:
  #     - build/web.zip
  #     - flutter_drive.log
  #   publishing:
  #     email:
  #       recipients:
  #         - <EMAIL>


