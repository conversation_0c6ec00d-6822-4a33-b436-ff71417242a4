# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Build and deploy the Android app to Firebase app distrubtion"
  lane :build_and_distribute_android_with_firebase do
    lastest_version = firebase_app_distribution_get_latest_release(
      app: ENV["FIREBASE_ANDROID_APP_ID"],
      firebase_cli_token: ENV["FIREBASE_CLI_TOKEN"],
    )

    updated_version = lastest_version[:buildVersion].to_i + 1

    sh("flutter", "build", "apk", "--build-number=#{updated_version}")
    firebase_app_distribution(
      app: ENV["FIREBASE_ANDROID_APP_ID"],
      android_artifact_type: "APK",
      groups: "testers",
      firebase_cli_token: ENV["FIREBASE_CLI_TOKEN"],
      android_artifact_path: "../build/app/outputs/apk/release/app-release.apk"
    )
  end
end
