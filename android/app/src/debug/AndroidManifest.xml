<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="org.micromentor.mm_flutter_app">
    <!-- The INTERNET permission is required for development. Specifically,
         the Flutter tool needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <!-- copied from the main manifest -->
    <application
        android:enableOnBackInvokedCallback="true"
        android:networkSecurityConfig="@xml/network_config"
        android:allowBackup="false"
        android:usesCleartextTraffic="false">
            <!-- The cleartext setting is necessary for the webview to work with a local server for Android 9 and up. -->
        <activity
            android:name=".MainActivity"
            android:launchMode="singleInstance"
            android:taskAffinity=""
            android:exported="true"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="sensorPortrait"
            android:windowSoftInputMode="adjustResize">
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https" android:host="dev.micromentor.org" />
                <data android:scheme="https" android:host="test.micromentor.org" />
                <data android:scheme="https" android:host="app.micromentor.org" />
                <data android:scheme="https" android:host="iqlaa.micromentor.org" />
                <data android:scheme="https" android:host="ebrd.micromentor.org" />
                <data android:scheme="https" android:host="striveid.micromentor.org" />
                <data android:scheme="https" android:host="strivemx.micromentor.org" />
                <data android:scheme="https" android:host="orangecorners.micromentor.org" />
                <data android:scheme="https" android:host="finsus.micromentor.org" />
                <data android:scheme="https" android:host="finvero.micromentor.org" />
                <data android:scheme="https" android:host="banbajio.micromentor.org" />
                <data android:scheme="https" android:host="promover.micromentor.org" />
                <data android:scheme="https" android:host="proempleo.micromentor.org" />
                <data android:scheme="https" android:host="mastercard.micromentor.org" />
                <data android:scheme="https" />
                <!-- not possible to do a wildcard, as we must host the verification files on the root domain, and Squarespace does not support this. -->
                <!-- see https://forum.squarespace.com/topic/229876-well-known-folder-with-file-vertification/ and https://developer.android.com/training/app-links/verify-android-applinks -->
                <!-- <data android:scheme="http" android:host="*.micromentor.org" /> -->
                <!-- a workaround may be using subdomains like iqlaa.app.micromentor.org rather than iqlaa.micromentor.org -->
                <!-- https://stackoverflow.com/a/40466044 (discusses iOS universal links, not sure if this works on Android) -->
            </intent-filter>
        </activity>
        <activity
            android:name="com.facebook.CustomTabActivity"
            android:launchMode="singleInstance"
            android:taskAffinity=""
            android:exported="true">
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="@string/fb_login_protocol_scheme" />
                <data android:scheme="https" />
                <!-- not possible to do a wildcard, as we must host the verification files on the root domain, and Squarespace does not support this. -->
                <!-- see https://forum.squarespace.com/topic/229876-well-known-folder-with-file-vertification/ and https://developer.android.com/training/app-links/verify-android-applinks -->
                <!-- <data android:scheme="http" android:host="*.micromentor.org" /> -->
                <!-- a workaround may be using subdomains like iqlaa.app.micromentor.org rather than iqlaa.micromentor.org -->
                <!-- https://stackoverflow.com/a/40466044 (discusses iOS universal links, not sure if this works on Android) -->
            </intent-filter>
        </activity>
    </application>
</manifest>
