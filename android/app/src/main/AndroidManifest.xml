<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="org.micromentor.mm_flutter_app">
    <uses-permission android:name="android.permission.USE_BIOMETRIC"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>

    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="http" />
        </intent>
    </queries>

   <application
        android:label="Micromentor"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:allowBackup="false"
        android:usesCleartextTraffic="false">
        <activity
            android:name=".MainActivity"
            android:launchMode="singleInstance"
            android:taskAffinity=""
            android:exported="true"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="sensorPortrait"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
            <meta-data android:name="flutter_deeplinking_enabled" android:value="true" />
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />

                <data android:scheme="https" android:host="dev.micromentor.org" />
                <data android:scheme="https" android:host="test.micromentor.org" />
                <data android:scheme="https" android:host="app.micromentor.org" />
                <data android:scheme="https" android:host="iqlaa.micromentor.org" />
                <data android:scheme="https" android:host="ebrd.micromentor.org" />
                <data android:scheme="https" android:host="striveid.micromentor.org" />
                <data android:scheme="https" android:host="strivemx.micromentor.org" />
                <data android:scheme="https" android:host="orangecorners.micromentor.org" />
                <data android:scheme="https" android:host="finsus.micromentor.org" />
                <data android:scheme="https" android:host="finvero.micromentor.org" />
                <data android:scheme="https" android:host="banbajio.micromentor.org" />
                <data android:scheme="https" android:host="promover.micromentor.org" />
                <data android:scheme="https" android:host="proempleo.micromentor.org" />
                <data android:scheme="https" android:host="mastercard.micromentor.org" />
                <!-- not possible to do a wildcard, as we must host the verification files on the root domain, and Squarespace does not support this. -->
                <!-- see https://forum.squarespace.com/topic/229876-well-known-folder-with-file-vertification/ and https://developer.android.com/training/app-links/verify-android-applinks -->
                <!-- <data android:scheme="http" android:host="*.micromentor.org" /> -->
                <!-- a workaround may be using subdomains like iqlaa.app.micromentor.org rather than iqlaa.micromentor.org -->
                <!-- https://stackoverflow.com/a/40466044 (discusses iOS universal links, not sure if this works on Android) -->
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id"/>
        <meta-data android:name="com.facebook.sdk.ClientToken" android:value="@string/facebook_client_token"/>
        <meta-data android:name="firebase_crashlytics_collection_enabled" android:value="false" />

        <activity android:name="com.facebook.FacebookActivity"
            android:configChanges=
                "keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:label="@string/app_name" />
        <activity
            android:name="com.facebook.CustomTabActivity"
            android:launchMode="singleInstance"
            android:taskAffinity=""
            android:exported="true">
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="@string/fb_login_protocol_scheme" />
            </intent-filter>
        </activity>
    </application>
</manifest>
