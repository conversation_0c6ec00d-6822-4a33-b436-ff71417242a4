plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "org.jetbrains.kotlin.android"
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"
 }

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    compileSdkVersion flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion
    namespace "org.micromentor.mm_flutter_app"
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "org.micromentor.mm_flutter_app"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion  23
        targetSdkVersion  flutter.targetSdkVersion
        versionCode  flutterVersionCode.toInteger()
        versionName  flutterVersionName
        multiDexEnabled true
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
    }

    flavorDimensions "flavors"

    productFlavors {
        def prodFlavorSigningConfig = signingConfigs.release

        local {
            dimension "flavors"
            signingConfig prodFlavorSigningConfig
        }
        dev {
            dimension "flavors"
            signingConfig prodFlavorSigningConfig
        }
        prod {
            dimension "flavors"
            signingConfig prodFlavorSigningConfig
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation platform('com.google.firebase:firebase-bom:32.1.0')
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"
    implementation 'com.facebook.android:facebook-android-sdk:latest.release'
    implementation "androidx.multidex:multidex:2.0.1"
}