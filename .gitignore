# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/
.fvm/
lib/__generated/
lib/generatedAppLocale/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
/android/app/.cxx

# iOS Related
# Add this line if you want to avoid checking in the XCode/iOS project configuration.
# /ios/Runner/Info.plist

# Devenv
.env.dart
.direnv/
.devenv*
devenv.local.nix
.flutter/

# Dependency directories
node_modules/

# Firebase account configuration
/ios/firebase_app_id_file.json
/android/app/google-services.json
# Below file doesn't contain secrets. We can remove it from the repo when syncing to the open source app.
# /ios/Runner/GoogleService-Info.plist
*firebase_service_account.json
/lib/firebase_options.dart

*service_account.json
*upload-keystore.jks
android/key.properties
android/fastlane/metadata/*/*/images/*Screenshots/
android/fastlane/report.xml
/.env
/devtools_options.yaml

# firebase hosting
.firebase/
