import 'package:flutter_test/flutter_test.dart';
import 'package:mm_flutter_app/utilities/utility.dart';

void main() {
  group('AppUtility', () {
    test('getFullName', () {
      expect(AppUtility.getUserFullName('Alois', 'Gumer'), 'Alois Gumer');
      expect(AppUtility.getUserFullName('Alois', ''), 'Alois');
      expect(AppUtility.getUserFullName('Alois', null), 'Alois');
      expect(AppUtility.getUserFullName('', 'Gumer'), 'Gumer');
      expect(AppUtility.getUserFullName(null, 'Gumer'), 'Gumer');
      expect(AppUtility.getUserFullName(null, null), '');
    });
  });
}
