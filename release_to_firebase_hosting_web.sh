#!/bin/bash

# Usage of the script with named parameters
# sh ./release_to_firebase_hosting_web.sh --flavor stag --target test

# Default values in case not all parameters are provided
FLAVOR="dev"
TARGET="test"

# Parse named parameters
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --flavor) FLAVOR="$2"; shift ;;
        --target) TARGET="$2"; shift ;;
        *) echo "Unknown parameter: $1"; exit 1 ;;
    esac
    shift
done

# avoid getting local changes into the build, but don't erase them from a developer machine
git stash

# build the latest version of the app
sh ./build_runner.sh

# Build the web app with the specified parameters
flutter build web --csp --dart-define=FLUTTER_APP_FLAVOR=$FLAVOR --release --source-maps

# Release the changes to the Firebase test web app
firebase deploy --only hosting:$TARGET
