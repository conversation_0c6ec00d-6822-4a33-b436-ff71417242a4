{"nodes": {"devenv": {"locked": {"dir": "src/modules", "lastModified": 1689595120, "narHash": "sha256-n9q2m9/ul18MnrHjDRWE7vlqc9SIgbCoM/cihvySTzQ=", "owner": "cachix", "repo": "devenv", "rev": "892ddef1fba6a956c172e6c1bb7c830795c713a2", "type": "github"}, "original": {"dir": "src/modules", "owner": "cachix", "repo": "devenv", "type": "github"}}, "flake-compat": {"flake": false, "locked": {"lastModified": 1673956053, "narHash": "sha256-4gtG9iQuiKITOjNQQeQIpoIB6b16fm+504Ch3sNKLd8=", "owner": "edols<PERSON>", "repo": "flake-compat", "rev": "35bb57c0c8d8b62bbfd284272c928ceb64ddbde9", "type": "github"}, "original": {"owner": "edols<PERSON>", "repo": "flake-compat", "type": "github"}}, "flake-utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1685518550, "narHash": "sha256-o2d0KcvaXzTrPRIo0kOLV0/QXHhDQ5DTi+OxcjO8xqY=", "owner": "numtide", "repo": "flake-utils", "rev": "a1720a10a6cfe8234c0e93907ffe81be440f4cef", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "gitignore": {"inputs": {"nixpkgs": ["pre-commit-hooks", "nixpkgs"]}, "locked": {"lastModified": 1660459072, "narHash": "sha256-8DFJjXG8zqoONA1vXtgeKXy68KdJL5UaXR8NtVMUbx8=", "owner": "hercules-ci", "repo": "gitignore.nix", "rev": "a20de23b925fd8264fd7fad6454652e142fd7f73", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "gitignore.nix", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1689569955, "narHash": "sha256-RF7/33M4MAhHiuyfkq15zeOJGWSZiOH4woRqPo+Z+N0=", "owner": "NixOS", "repo": "nixpkgs", "rev": "7e212cc9752edacf28f3579440f9adf4c5e346ae", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixpkgs-unstable", "repo": "nixpkgs", "type": "github"}}, "nixpkgs-stable": {"locked": {"lastModified": 1685801374, "narHash": "sha256-otaSUoFEMM+LjBI1XL/xGB5ao6IwnZOXc47qhIgJe8U=", "owner": "NixOS", "repo": "nixpkgs", "rev": "c37ca420157f4abc31e26f436c1145f8951ff373", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-23.05", "repo": "nixpkgs", "type": "github"}}, "pre-commit-hooks": {"inputs": {"flake-compat": "flake-compat", "flake-utils": "flake-utils", "gitignore": "gitignore", "nixpkgs": ["nixpkgs"], "nixpkgs-stable": "nixpkgs-stable"}, "locked": {"lastModified": 1689553106, "narHash": "sha256-RFFf6BbpqQB0l1ehAbgri9g9MGZkAY9UdiNotD9fG8Y=", "owner": "cachix", "repo": "pre-commit-hooks.nix", "rev": "87589fa438dd6d5b8c7c1c6ab2ad69e4663bb51f", "type": "github"}, "original": {"owner": "cachix", "repo": "pre-commit-hooks.nix", "type": "github"}}, "root": {"inputs": {"devenv": "devenv", "nixpkgs": "nixpkgs", "pre-commit-hooks": "pre-commit-hooks"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}}, "root": "root", "version": 7}