<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>aps-environment</key>
	<string>development</string>
	<key>com.apple.developer.applesignin</key>
	<array>
		<string>Default</string>
	</array>
	<key>com.apple.developer.associated-domains</key>
  <array>
		<!-- not possible to do a wildcard, as we must host the verification files on the root domain, and Squarespace does not support this. -->
		<!-- see https://forum.squarespace.com/topic/229876-well-known-folder-with-file-vertification/ and https://developer.apple.com/documentation/technotes/tn3155-debugging-universal-links#Understand-applinks-configuration-and-rules-->
		<!-- <data android:scheme="http" android:host="*.micromentor.org" /> -->
		<!-- a workaround may be using subdomains like iqlaa.app.micromentor.org rather than iqlaa.micromentor.org -->
		<!-- https://stackoverflow.com/a/40466044 (discusses iOS universal links, not sure if this works on Android) -->
    <string>applinks:dev.micromentor.org</string>
    <string>applinks:test.micromentor.org</string>
    <string>applinks:app.micromentor.org</string>
    <string>applinks:iqlaa.micromentor.org</string>
    <string>applinks:ebrd.micromentor.org</string>
    <string>applinks:striveid.micromentor.org</string>
		<string>applinks:strivemx.micromentor.org</string>
		<string>applinks:orangecorners.micromentor.org</string>
		<string>applinks:finsus.micromentor.org</string>
		<string>applinks:finvero.micromentor.org</string>
		<string>applinks:banbajio.micromentor.org</string>
		<string>applinks:promover.micromentor.org</string>
		<string>applinks:proempleo.micromentor.org</string>
		<string>applinks:mastercard.micromentor.org</string>
		<!-- to test one of these before Apple parses the associated files, add ?mode=developer to the URL -->
  </array>
</dict>
</plist>
