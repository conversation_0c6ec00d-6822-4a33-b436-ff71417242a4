#!/bin/bash

# Usage of the script with named parameters
# sh ./build_app_bundles.sh --build_number 36 --build_name 1.0.1 --flavor dev

# Default values in case not all parameters are provided
BUILD_NUMBER=""
BUILD_NAME=""
FLAVOR=""

# Parse named parameters
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --build_number) BUILD_NUMBER="$2"; shift ;;
        --build_name) BUILD_NAME="$2"; shift ;;
        --flavor) FLAVOR="$2"; shift ;;
        *) echo "Unknown parameter: $1"; exit 1 ;;
    esac
    shift
done

# Ensure all parameters are provided
if [[ -z "$BUILD_NUMBER" || -z "$BUILD_NAME" || -z "$FLAVOR" ]]; then
    echo "Error: Missing one or more required parameters: --build_number, --build_name, or --flavor"
    exit 1
fi

# avoid getting local changes into the build, but don't erase them from a developer machine
git stash

# build the latest version of the app
sh ./build_runner.sh

# Build the iOS app with the specified parameters
flutter build ipa --flavor "$FLAVOR" --release --build-name="$BUILD_NAME" --build-number="$BUILD_NUMBER" --export-options-plist=ios/export.plist.dist

# Build the Android appbundle with the specified parameters
flutter build appbundle --build-name="$BUILD_NAME" --build-number "$BUILD_NUMBER" --release --flavor "$FLAVOR"
